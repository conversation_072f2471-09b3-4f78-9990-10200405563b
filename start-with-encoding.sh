#!/bin/bash

# 佣金系统启动脚本 - 支持UTF-8编码
# 使用方法: ./start-with-encoding.sh

AppName=ruoyi-admin.jar
LOG_DIR=/home/<USER>/ruoyi-admin/logs

# 确保日志目录存在
mkdir -p $LOG_DIR

# 设置环境变量
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# JVM参数 - 强制UTF-8编码
JVM_OPTS="-Dname=$AppName \
-Duser.timezone=Asia/Shanghai \
-Dfile.encoding=UTF-8 \
-Dsun.jnu.encoding=UTF-8 \
-Dconsole.encoding=UTF-8 \
-Dspring.banner.charset=UTF-8 \
-Xms512m \
-Xmx1024m \
-XX:MetaspaceSize=128m \
-XX:MaxMetaspaceSize=512m \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:+PrintGCDateStamps \
-XX:+PrintGCDetails \
-XX:NewRatio=1 \
-XX:SurvivorRatio=30 \
-XX:+UseParallelGC \
-XX:+UseParallelOldGC"

# 检查应用是否已经在运行
PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`

if [ x"$PID" != x"" ]; then
    echo "警告: $AppName 已经在运行中 (PID: $PID)"
    echo "如需重启，请先执行: kill $PID"
    exit 1
fi

# 检查jar文件是否存在
if [ ! -f "$AppName" ]; then
    echo "错误: jar文件不存在: $AppName"
    echo "请确保在正确的目录下运行此脚本"
    exit 1
fi

echo "=========================================="
echo "启动参数信息:"
echo "应用名称: $AppName"
echo "环境编码: LANG=$LANG, LC_ALL=$LC_ALL"
echo "JVM编码参数: -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"
echo "日志目录: $LOG_DIR"
echo "=========================================="

# 启动应用
echo "正在启动 $AppName ..."
nohup java $JVM_OPTS -jar $AppName > $LOG_DIR/startup.log 2>&1 &

# 等待一下让应用启动
sleep 3

# 检查启动结果
NEW_PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`

if [ x"$NEW_PID" != x"" ]; then
    echo "✓ $AppName 启动成功 (PID: $NEW_PID)"
    echo ""
    echo "查看日志命令:"
    echo "  实时查看应用日志:     tail -f $LOG_DIR/admin-app.log"
    echo "  实时查看佣金计算日志: tail -f $LOG_DIR/commission-calculation.log"
    echo "  查看启动日志:         tail -f $LOG_DIR/startup.log"
    echo ""
    echo "使用日志查看脚本:"
    echo "  ./view-commission-logs.sh -c -t    # 查看佣金计算日志"
    echo "  ./view-commission-logs.sh -a -t    # 查看应用总日志"
else
    echo "✗ $AppName 启动失败"
    echo "请查看启动日志: tail -f $LOG_DIR/startup.log"
    exit 1
fi 