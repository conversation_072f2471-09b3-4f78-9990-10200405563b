-- 调试查询：检查相关表的数据情况
-- 用于排查为什么 personalDiamonds、teamDiamonds、newRecruitsCount 为 null

-- 1. 检查 commission_payouts 表中的数据
SELECT 'commission_payouts' as table_name, creator_id, data_month, distributor_level, final_payout_usd
FROM commission_payouts 
WHERE creator_id = 88808201907081578 AND DATE_FORMAT(data_month,'%Y-%m') = '2025-05'
LIMIT 5;

-- 2. 检查 monthly_performance 表中的数据（个人钻石收入）
SELECT 'monthly_performance' as table_name, creator_id, data_month, diamonds as personal_diamonds
FROM monthly_performance 
WHERE creator_id = 88808201907081578 AND DATE_FORMAT(data_month,'%Y-%m') = '2025-05'
LIMIT 5;

-- 3. 检查 commission_distributor_qualifications 表中的数据（团队钻石收入）
SELECT 'commission_distributor_qualifications' as table_name, creator_id, data_month, 
       personal_diamonds, team_diamonds, achieved_level
FROM commission_distributor_qualifications 
WHERE creator_id = 88808201907081578 AND DATE_FORMAT(data_month,'%Y-%m') = '2025-05'
LIMIT 5;

-- 4. 检查 commission_recruitment_stats 表中的数据（拉新人数）
SELECT 'commission_recruitment_stats' as table_name, recruiter_id, data_month, new_recruits_count
FROM commission_recruitment_stats 
WHERE recruiter_id = 88808201907081578 AND DATE_FORMAT(data_month,'%Y-%m') = '2025-05'
LIMIT 5;

-- 5. 完整的关联查询测试（和修改后的Mapper SQL一致）
SELECT p.id, p.creator_id, p.data_month, p.distributor_level, p.final_payout_usd, p.created_at,
       c.nickname as creator_nickname, c.handle as creator_handle,
       COALESCE(mp.diamonds, 0) as personal_diamonds, 
       COALESCE(dq.team_diamonds, 0) as team_diamonds, 
       COALESCE(rs.new_recruits_count, 0) as new_recruits_count
FROM commission_payouts p
LEFT JOIN creators c ON p.creator_id = c.id
LEFT JOIN monthly_performance mp ON p.creator_id = mp.creator_id AND p.data_month = mp.data_month
LEFT JOIN commission_distributor_qualifications dq ON p.creator_id = dq.creator_id AND p.data_month = dq.data_month
LEFT JOIN commission_recruitment_stats rs ON p.creator_id = rs.recruiter_id AND p.data_month = rs.data_month
WHERE p.creator_id = 88808201907081578 
  AND DATE_FORMAT(p.data_month,'%Y-%m') = '2025-05';

-- 6. 检查各个表是否存在该用户的任何数据
SELECT 
    'summary' as table_name,
    COUNT(DISTINCT p.creator_id) as payouts_records,
    COUNT(DISTINCT mp.creator_id) as performance_records,
    COUNT(DISTINCT dq.creator_id) as qualifications_records,
    COUNT(DISTINCT rs.recruiter_id) as recruitment_records
FROM commission_payouts p
LEFT JOIN monthly_performance mp ON p.creator_id = mp.creator_id AND p.data_month = mp.data_month
LEFT JOIN commission_distributor_qualifications dq ON p.creator_id = dq.creator_id AND p.data_month = dq.data_month
LEFT JOIN commission_recruitment_stats rs ON p.creator_id = rs.recruiter_id AND p.data_month = rs.data_month
WHERE p.creator_id = 88808201907081578 
  AND DATE_FORMAT(p.data_month,'%Y-%m') = '2025-05'; 