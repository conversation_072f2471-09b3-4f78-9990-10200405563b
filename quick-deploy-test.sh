#!/bin/bash

# 快速部署和测试UTF-8编码修复
# 使用方法: ./quick-deploy-test.sh

REMOTE_HOST="*************"
REMOTE_USER="root"
REMOTE_DIR="/home/<USER>/ruoyi-admin"

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_warn() {
    echo -e "\033[33m[WARN]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

print_info "========== 快速部署UTF-8编码修复 =========="

# 1. 复制remote-start.sh到ruoyi-admin目录
if [ -f "./remote-start.sh" ]; then
    cp "./remote-start.sh" "./ruoyi-admin/"
    print_info "复制remote-start.sh到ruoyi-admin目录"
else
    print_error "remote-start.sh不存在，请先创建"
    exit 1
fi

# 2. 进入ruoyi-admin目录执行部署
cd ruoyi-admin

print_info "执行部署脚本..."
./deploy.sh

print_info "等待服务启动..."
sleep 10

# 3. 远程检查日志编码
print_info "检查远程日志编码..."
ssh "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
    cd /home/<USER>/ruoyi-admin
    
    echo "检查日志文件..."
    ls -la logs/
    
    echo ""
    echo "检查日志编码..."
    if [ -f "logs/commission-calculation.log" ]; then
        echo "佣金计算日志编码:"
        file -bi logs/commission-calculation.log
        echo ""
        echo "最新10行日志:"
        tail -10 logs/commission-calculation.log
    else
        echo "佣金计算日志文件不存在，可能需要先触发一次计算"
    fi
    
    echo ""
    echo "Java进程信息:"
    ps aux | grep ruoyi-admin.jar | grep -v grep
EOF

print_info "========== 部署完成 =========="
print_info "接下来的测试步骤:"
print_info "1. 访问应用并触发一次佣金计算"
print_info "2. 远程查看日志: ssh $REMOTE_USER@$REMOTE_HOST"
print_info "3. 进入目录: cd $REMOTE_DIR"
print_info "4. 查看日志: ./view-commission-logs.sh -c -t 50"
print_info "5. 如果中文显示正常，说明编码问题已解决" 