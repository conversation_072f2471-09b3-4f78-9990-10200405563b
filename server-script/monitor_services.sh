#!/bin/bash

# 配置文件名
CONFIG_FILE="service_monitor.conf"
# 记录文件修改时间的数据文件
TIMESTAMP_FILE="service_timestamps.dat"
# 日志目录
LOG_DIR="logs"

# 添加日志函数
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_DIR/monitor.log"
}

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    log "配置文件不存在，创建示例配置..."
    cat > "$CONFIG_FILE" << EOF
# 服务配置文件
# 格式：服务名称|JAR路径|启动命令|端口号
ruoyi-admin|/www/wwwroot/ruoyi_backend_service/ruoyi-admin/ruoyi-admin.jar|java -jar -Xms256m -Xmx1024m ruoyi-admin.jar --server.port=9131|9131
EOF
    log "请编辑 $CONFIG_FILE 配置文件后重新运行脚本"
    exit 1
fi

# 初始化
mkdir -p "$LOG_DIR"
touch "$TIMESTAMP_FILE"

# 获取进程ID
get_pid() {
    local port=$1
    lsof -i:$port -t 2>/dev/null
}

# 停止服务
stop_service() {
    local pid=$1
    local service_name=$2
    if [ ! -z "$pid" ]; then
        log "停止服务 $service_name (PID: $pid)"
        kill $pid
        sleep 5
        if ps -p $pid > /dev/null; then
            log "服务未能正常停止，强制终止"
            kill -9 $pid
        fi
    fi
}

# 启动服务
start_service() {
    local service_name=$1
    local jar_path=$2
    local start_cmd=$3
    local work_dir=$(dirname "$jar_path")
    
    log "启动服务 $service_name"
    log "工作目录: $work_dir"
    log "启动命令: $start_cmd"
    
    cd "$work_dir"
    nohup $start_cmd > "$LOG_DIR/$service_name.log" 2>&1 &
    
    sleep 10
    
    # 检查服务是否成功启动
    local port=$(echo $start_cmd | grep -o 'server.port=[0-9]\+' | cut -d'=' -f2)
    local new_pid=$(get_pid $port)
    if [ ! -z "$new_pid" ]; then
        log "服务成功启动，PID: $new_pid"
    else
        log "警告：服务可能未能成功启动"
    fi
}

# 检查和更新服务
check_and_update() {
    local service_name=$1
    local jar_path=$2
    local start_cmd=$3
    local port=$4
    
    log "检查服务: $service_name"
    log "JAR路径: $jar_path"
    
    # 检查JAR文件是否存在
    if [ ! -f "$jar_path" ]; then
        log "错误：JAR文件不存在: $jar_path"
        return
    fi
    
    # 获取当前文件修改时间
    local current_time=$(stat -c %Y "$jar_path")
    local last_time=$(grep "^$service_name:" "$TIMESTAMP_FILE" | cut -d: -f2 || echo 0)
    
    log "当前文件时间戳: $current_time"
    log "上次记录时间戳: $last_time"
    
    # 如果时间戳不同，说明文件已更新
    if [ "$current_time" != "$last_time" ]; then
        log "检测到服务 $service_name 有更新"
        
        # 获取当前运行的进程ID
        local pid=$(get_pid $port)
        
        if [ ! -z "$pid" ]; then
            log "当前运行的进程ID: $pid"
            stop_service "$pid" "$service_name"
        else
            log "没有找到运行中的进程"
        fi
        
        # 启动服务
        start_service "$service_name" "$jar_path" "$start_cmd"
        
        # 更新时间戳
        sed -i "/^$service_name:/d" "$TIMESTAMP_FILE"
        echo "$service_name:$current_time" >> "$TIMESTAMP_FILE"
        
        log "服务 $service_name 更新完成"
    else
        log "服务 $service_name 没有更新"
    fi
}

log "监控脚本启动"
log "配置文件: $CONFIG_FILE"
log "时间戳文件: $TIMESTAMP_FILE"
log "日志目录: $LOG_DIR"

# 主循环
while true; do
    log "开始检查服务更新..."
    
    # 读取配置文件中的每个服务
    while IFS='|' read -r service_name jar_path start_cmd port || [ -n "$service_name" ]; do
        # 跳过注释行和空行
        [[ $service_name =~ ^#.*$ ]] && continue
        [ -z "$service_name" ] && continue
        
        check_and_update "$service_name" "$jar_path" "$start_cmd" "$port"
    done < "$CONFIG_FILE"
    
    log "检查完成，等待下一次检查..."
    # 每60秒检查一次
    sleep 60
done