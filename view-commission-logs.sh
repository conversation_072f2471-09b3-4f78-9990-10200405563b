#!/bin/bash

# 佣金计算日志查看脚本
# 使用方法: ./view-commission-logs.sh [选项]

LOG_DIR="/home/<USER>/ruoyi-admin/logs"

# 函数：显示帮助信息
show_help() {
    echo "佣金计算日志查看工具"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -a, --all              查看 admin-app.log (应用总日志)"
    echo "  -c, --commission       查看 commission-calculation.log (佣金计算专用日志)"
    echo "  -e, --error           查看 sys-error.log (错误日志)"
    echo "  -i, --info            查看 sys-info.log (信息日志)"
    echo "  -l, --list            列出所有日志文件"
    echo "  -t, --tail NUM        实时查看最新日志 (默认100行)"
    echo "  -g, --grep PATTERN    搜索包含指定内容的日志"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -c -t 50           实时查看佣金计算日志最新50行"
    echo "  $0 -a -g '佣金计算'    在应用日志中搜索包含'佣金计算'的行"
    echo "  $0 -l                 列出所有可用的日志文件"
}

# 函数：列出日志文件
list_logs() {
    echo "可用的日志文件:"
    echo "==============="
    if [ -d "$LOG_DIR" ]; then
        ls -la "$LOG_DIR"/*.log 2>/dev/null | while read line; do
            echo "$line"
        done
    else
        echo "日志目录不存在: $LOG_DIR"
    fi
}

# 函数：查看日志
view_log() {
    local log_file="$1"
    local tail_lines="$2"
    local grep_pattern="$3"
    
    if [ ! -f "$log_file" ]; then
        echo "日志文件不存在: $log_file"
        return 1
    fi
    
    # 设置UTF-8环境
    export LANG=zh_CN.UTF-8
    export LC_ALL=zh_CN.UTF-8
    
    echo "查看日志文件: $log_file"
    echo "文件编码检测: $(file -bi "$log_file" | cut -d= -f2)"
    echo "========================="
    
    if [ -n "$grep_pattern" ]; then
        echo "搜索模式: $grep_pattern"
        if [ -n "$tail_lines" ]; then
            tail -n "$tail_lines" "$log_file" | grep --color=always "$grep_pattern"
        else
            grep --color=always "$grep_pattern" "$log_file"
        fi
    elif [ -n "$tail_lines" ]; then
        echo "实时查看最新 $tail_lines 行..."
        tail -f -n "$tail_lines" "$log_file"
    else
        # 使用more而不是less，并设置编码
        LESSCHARSET=utf-8 less "$log_file"
    fi
}

# 主逻辑
LOG_TYPE=""
TAIL_LINES=""
GREP_PATTERN=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -a|--all)
            LOG_TYPE="admin-app"
            shift
            ;;
        -c|--commission)
            LOG_TYPE="commission"
            shift
            ;;
        -e|--error)
            LOG_TYPE="error"
            shift
            ;;
        -i|--info)
            LOG_TYPE="info"
            shift
            ;;
        -l|--list)
            list_logs
            exit 0
            ;;
        -t|--tail)
            TAIL_LINES="${2:-100}"
            shift 2
            ;;
        -g|--grep)
            GREP_PATTERN="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定日志类型，显示帮助
if [ -z "$LOG_TYPE" ]; then
    echo "请指定要查看的日志类型"
    echo ""
    show_help
    exit 1
fi

# 根据日志类型设置文件路径
case $LOG_TYPE in
    "admin-app")
        LOG_FILE="$LOG_DIR/admin-app.log"
        ;;
    "commission")
        LOG_FILE="$LOG_DIR/commission-calculation.log"
        ;;
    "error")
        LOG_FILE="$LOG_DIR/sys-error.log"
        ;;
    "info")
        LOG_FILE="$LOG_DIR/sys-info.log"
        ;;
esac

# 查看日志
view_log "$LOG_FILE" "$TAIL_LINES" "$GREP_PATTERN" 