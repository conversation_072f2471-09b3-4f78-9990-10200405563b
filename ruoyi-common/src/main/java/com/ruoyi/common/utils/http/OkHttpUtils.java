package com.ruoyi.common.utils.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * HTTP请求工具类（使用Java原生的HttpURLConnection）
 *
 * <AUTHOR>
 */
public class OkHttpUtils {
    private static final Logger log = LoggerFactory.getLogger(OkHttpUtils.class);
    
    private static final int CONNECT_TIMEOUT = 10000; // 10秒
    private static final int READ_TIMEOUT = 10000;    // 10秒

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public static String get(String url) {
        return get(url, null);
    }

    /**
     * 发送GET请求
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应结果
     */
    public static String get(String url, Map<String, String> params) {
        StringBuilder sb = new StringBuilder(url);
        if (params != null && !params.isEmpty()) {
            sb.append("?");
            params.forEach((key, value) -> sb.append(key).append("=").append(value).append("&"));
            sb.deleteCharAt(sb.length() - 1);
        }
        
        HttpURLConnection conn = null;
        BufferedReader reader = null;
        
        try {
            URL requestUrl = new URL(sb.toString());
            conn = (HttpURLConnection) requestUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(CONNECT_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);
            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            
            // 获取响应
            int responseCode = conn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                
                String result = response.toString();
                log.info("请求成功 - 状态码: {}, 响应内容: {}", responseCode, result);
                return result;
            } else {
                reader = new BufferedReader(new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                
                String result = response.toString();
                log.error("请求失败 - 状态码: {}, 响应内容: {}", responseCode, result);
                return result;
            }
        } catch (Exception e) {
            log.error("请求异常 - URL: {}, 异常信息: {}", url, e.getMessage(), e);
            return "";
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (IOException e) {
                log.error("关闭资源异常", e);
            }
        }
    }

    /**
     * 发送POST请求（JSON格式）
     *
     * @param url  请求URL
     * @param json JSON字符串
     * @return 响应结果
     */
    public static String post(String url, String json) {
        log.info("POST请求 - URL: {}, 参数: {}", url, json);
        
        HttpURLConnection conn = null;
        OutputStream os = null;
        BufferedReader reader = null;
        
        try {
            URL requestUrl = new URL(url);
            conn = (HttpURLConnection) requestUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(CONNECT_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);
            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            
            // 写入请求体
            os = conn.getOutputStream();
            byte[] input = json.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
            os.flush();
            
            // 获取响应
            int responseCode = conn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                
                String result = response.toString();
                log.info("请求成功 - 状态码: {}, 响应内容: {}", responseCode, result);
                return result;
            } else {
                reader = new BufferedReader(new InputStreamReader(
                        conn.getErrorStream() != null ? conn.getErrorStream() : conn.getInputStream(), 
                        StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                
                String result = response.toString();
                log.error("请求失败 - 状态码: {}, 响应内容: {}", responseCode, result);
                return result;
            }
        } catch (Exception e) {
            log.error("请求异常 - URL: {}, 异常信息: {}", url, e.getMessage(), e);
            return "";
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (reader != null) {
                    reader.close();
                }
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (IOException e) {
                log.error("关闭资源异常", e);
            }
        }
    }

    /**
     * 发送POST请求（表单格式）
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应结果
     */
    public static String postForm(String url, Map<String, String> params) {
        StringBuilder formData = new StringBuilder();
        if (params != null && !params.isEmpty()) {
            params.forEach((key, value) -> {
                if (formData.length() > 0) {
                    formData.append("&");
                }
                formData.append(key).append("=").append(value);
            });
        }
        
        HttpURLConnection conn = null;
        OutputStream os = null;
        BufferedReader reader = null;
        
        try {
            URL requestUrl = new URL(url);
            conn = (HttpURLConnection) requestUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(CONNECT_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);
            conn.setRequestProperty("Accept", "*/*");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setDoOutput(true);
            conn.setDoInput(true);
            
            // 写入请求体
            os = conn.getOutputStream();
            byte[] input = formData.toString().getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
            os.flush();
            
            // 获取响应
            int responseCode = conn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                
                String result = response.toString();
                log.info("请求成功 - 状态码: {}, 响应内容: {}", responseCode, result);
                return result;
            } else {
                reader = new BufferedReader(new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                
                String result = response.toString();
                log.error("请求失败 - 状态码: {}, 响应内容: {}", responseCode, result);
                return result;
            }
        } catch (Exception e) {
            log.error("请求异常 - URL: {}, 异常信息: {}", url, e.getMessage(), e);
            return "";
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (reader != null) {
                    reader.close();
                }
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (IOException e) {
                log.error("关闭资源异常", e);
            }
        }
    }
}
