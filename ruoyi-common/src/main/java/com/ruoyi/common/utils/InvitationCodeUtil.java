package com.ruoyi.common.utils;

import java.security.SecureRandom;

/**
 * 邀请码生成工具类
 * 
 * <AUTHOR>
 */
public class InvitationCodeUtil {

    /**
     * 字符集合，不包含易混淆的字符1、0、I、O
     */
    private static final String CHARS = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    
    /**
     * 邀请码长度
     */
    private static final int CODE_LENGTH = 6;
    
    /**
     * 随机数生成器
     */
    private static final SecureRandom RANDOM = new SecureRandom();
    
    /**
     * 生成随机邀请码
     * 
     * @return 6位唯一邀请码
     */
    public static String generateInvitationCode() {
        StringBuilder sb = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            int randomIndex = RANDOM.nextInt(CHARS.length());
            sb.append(CHARS.charAt(randomIndex));
        }
        return sb.toString();
    }
} 