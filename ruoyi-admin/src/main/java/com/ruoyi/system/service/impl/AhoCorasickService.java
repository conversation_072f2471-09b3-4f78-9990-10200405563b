package com.ruoyi.system.service.impl;

import org.springframework.stereotype.Service;
import java.text.Normalizer;
import java.util.*;

@Service
public class AhoCorasickService {
    private TrieNode root;
    
    // 全角转半角映射表
    private static final Map<Character, Character> FULL_TO_HALF = new HashMap<>();
    
    static {
        // 初始化全角转半角映射
        char[] full = "！＂＃＄％＆＇（）＊＋，－．／０１２３４５６７８９：；＜＝＞？＠ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺ［＼］＾＿｀ａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ｛｜｝～".toCharArray();
        char[] half = "!\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~".toCharArray();
        for (int i = 0; i < full.length; i++) {
            FULL_TO_HALF.put(full[i], half[i]);
        }
    }

    public AhoCorasickService() {
        this.root = new TrieNode();
    }

    private static class TrieNode {
        Map<Character, TrieNode> children;
        boolean isEndOfWord;
        String word;
        TrieNode fail;
        
        TrieNode() {
            this.children = new HashMap<>();
            this.isEndOfWord = false;
            this.fail = null;
        }
    }

    /**
     * 标准化文本处理
     * @param text 输入文本
     * @return 标准化后的文本
     */
    private String normalizeText(String text) {
        if (text == null) {
            return "";
        }
        
        // 1. Unicode标准化
        String normalized = Normalizer.normalize(text, Normalizer.Form.NFKC);
        
        // 2. 全角转半角
        StringBuilder sb = new StringBuilder(normalized.length());
        for (char c : normalized.toCharArray()) {
            Character halfWidth = FULL_TO_HALF.get(c);
            sb.append(halfWidth != null ? halfWidth : c);
        }
        
        return sb.toString();
    }

    /**
     * 构建AC自动机
     * @param words 敏感词列表
     */
    public synchronized void buildMachine(Collection<String> words) {
        // 重置根节点
        this.root = new TrieNode();
        
        // 构建Trie树，对每个敏感词进行标准化处理
        for (String word : words) {
            if (word == null || word.trim().isEmpty()) {
                continue;
            }
            insert(normalizeText(word.trim()));
        }
        
        // 构建失败指针
        buildFailurePointers();
    }

    private void insert(String word) {
        TrieNode current = root;
        for (char ch : word.toCharArray()) {
            current.children.putIfAbsent(ch, new TrieNode());
            current = current.children.get(ch);
        }
        current.isEndOfWord = true;
        current.word = word;
    }

    private void buildFailurePointers() {
        Queue<TrieNode> queue = new LinkedList<>();
        
        // 将第一层节点的失败指针指向根节点
        for (TrieNode node : root.children.values()) {
            node.fail = root;
            queue.offer(node);
        }
        
        // BFS构建其余节点的失败指针
        while (!queue.isEmpty()) {
            TrieNode current = queue.poll();
            
            for (Map.Entry<Character, TrieNode> entry : current.children.entrySet()) {
                char ch = entry.getKey();
                TrieNode child = entry.getValue();
                
                queue.offer(child);
                
                TrieNode failNode = current.fail;
                while (failNode != null && !failNode.children.containsKey(ch)) {
                    failNode = failNode.fail;
                }
                
                child.fail = failNode == null ? root : failNode.children.get(ch);
            }
        }
    }

    /**
     * 查找文本中的所有敏感词
     * @param text 待检查的文本
     * @return 找到的敏感词集合
     */
    public Set<String> findAll(String text) {
        Set<String> found = new HashSet<>();
        if (text == null || text.isEmpty()) {
            return found;
        }

        // 对输入文本进行标准化处理
        String normalizedText = normalizeText(text);
        TrieNode current = root;
        int i = 0;
        
        while (i < normalizedText.length()) {
            char ch = normalizedText.charAt(i);
            
            // 查找下一个匹配节点
            while (current != root && !current.children.containsKey(ch)) {
                current = current.fail;
            }
            
            // 找到匹配的子节点
            current = current.children.getOrDefault(ch, root);
            
            // 检查是否找到敏感词
            TrieNode temp = current;
            while (temp != root) {
                if (temp.isEndOfWord) {
                    found.add(temp.word);
                }
                temp = temp.fail;
            }
            
            i++;
        }
        
        return found;
    }
}
