package com.ruoyi.web.controller.business;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.commission.ICommissionMonthlySummaryService;
import com.ruoyi.system.service.commission.ICommissionPayoutsService;
import com.ruoyi.system.domain.commission.CommissionMonthlySummary;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import com.ruoyi.system.domain.vo.CommissionCalculationSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 后台-佣金报表查询控制器 h:commission_report
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "后台-佣金报表查询 h:commission_report")
@RestController
@RequestMapping("/business/commission/report")
public class CommissionReportController extends BaseController
{
    @Autowired
    private ICommissionMonthlySummaryService commissionMonthlySummaryService;
    
    @Autowired
    private ICommissionPayoutsService commissionPayoutsService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");

    /**
     * 获取财务总览报表 h:commission_report
     */
    @ApiOperation(value = "获取财务总览报表 h:commission_report", notes = "获取指定月份的财务总览报表 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @GetMapping("/financial-overview/{dataMonth}")
    public AjaxResult getFinancialOverview(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            CommissionCalculationSummaryVO overview = commissionMonthlySummaryService.getFinancialOverviewReport(month);
            if (overview == null) {
                return error("未找到指定月份的财务数据，请先执行佣金计算");
            }
            return success(overview);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取财务总览报表失败：{}", e.getMessage(), e);
            return error("获取财务总览报表失败：" + e.getMessage());
        }
    }

    /**
     * 获取历史月度总览列表 h:commission_report
     */
    @ApiOperation(value = "获取历史月度总览列表 h:commission_report", notes = "获取历史月度财务总览列表 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @GetMapping("/summary/list")
    public TableDataInfo getSummaryList(CommissionMonthlySummary commissionMonthlySummary)
    {
        startPage();
        List<CommissionMonthlySummary> list = commissionMonthlySummaryService.selectCommissionMonthlySummaryList(commissionMonthlySummary);
        return getDataTable(list);
    }

    /**
     * 获取分销员收入明细列表 h:commission_report
     */
    @ApiOperation(value = "获取分销员收入明细列表 h:commission_report", notes = "获取分销员收入明细报表 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @GetMapping("/distributor-details")
    public TableDataInfo getDistributorDetails(CommissionPayouts commissionPayouts)
    {
        startPage();
        List<CommissionPayouts> list = commissionPayoutsService.selectCommissionPayoutsListWithCreator(commissionPayouts);
        return getDataTable(list);
    }

    /**
     * 获取单个分销员详细信息 h:commission_report
     */
    @ApiOperation(value = "获取分销员详细信息 h:commission_report", notes = "获取单个分销员的详细收入信息 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @GetMapping("/distributor/{creatorId}/{dataMonth}")
    public AjaxResult getDistributorDetail(
            @ApiParam(value = "分销员ID", required = true, example = "1001") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            CommissionPayouts payout = commissionPayoutsService.selectCommissionPayoutsByCreatorAndMonth(creatorId, month);
            if (payout == null) {
                return error("未找到指定分销员的收入数据");
            }
            return success(payout);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取分销员详细信息失败：{}", e.getMessage(), e);
            return error("获取分销员详细信息失败：" + e.getMessage());
        }
    }

    /**
     * 查询指定月份的分销员收入统计 h:commission_report
     */
    @ApiOperation(value = "查询月度分销员收入统计 h:commission_report", notes = "查询指定月份的所有分销员收入统计 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @GetMapping("/monthly-stats/{dataMonth}")
    public TableDataInfo getMonthlyDistributorStats(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            startPage();
            List<CommissionPayouts> list = commissionPayoutsService.selectCommissionPayoutsByMonth(month);
            return getDataTable(list);
        } catch (ParseException e) {
            logger.error("查询月度分销员收入统计失败：日期格式错误", e);
            return getDataTable(new java.util.ArrayList<>());
        } catch (Exception e) {
            logger.error("查询月度分销员收入统计失败：{}", e.getMessage(), e);
            return getDataTable(new java.util.ArrayList<>());
        }
    }

    /**
     * 导出财务总览报表 h:commission_report
     */
    @ApiOperation(value = "导出财务总览报表 h:commission_report", notes = "导出指定月份的财务总览报表 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @Log(title = "导出财务总览报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export/financial/{dataMonth}")
    public void exportFinancialReport(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth, 
            HttpServletResponse response)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            CommissionMonthlySummary summary = commissionMonthlySummaryService.selectCommissionMonthlySummaryByMonth(month);
            if (summary == null) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "未找到指定月份的财务数据");
                return;
            }
            
            List<CommissionMonthlySummary> list = java.util.Arrays.asList(summary);
            ExcelUtil<CommissionMonthlySummary> util = new ExcelUtil<CommissionMonthlySummary>(CommissionMonthlySummary.class);
            util.exportExcel(response, list, "财务总览报表_" + dataMonth);
        } catch (ParseException e) {
            logger.error("导出财务总览报表失败：日期格式错误", e);
        } catch (Exception e) {
            logger.error("导出财务总览报表失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 导出分销员收入明细报表 h:commission_report
     */
    @ApiOperation(value = "导出分销员收入明细报表 h:commission_report", notes = "导出分销员收入明细报表 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @Log(title = "导出分销员收入明细报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export/distributor-details")
    public void exportDistributorDetails(HttpServletResponse response, CommissionPayouts commissionPayouts)
    {
        try {
            List<CommissionPayouts> list = commissionPayoutsService.selectCommissionPayoutsListWithCreator(commissionPayouts);
            ExcelUtil<CommissionPayouts> util = new ExcelUtil<CommissionPayouts>(CommissionPayouts.class);
            util.exportExcel(response, list, "分销员收入明细报表");
        } catch (Exception e) {
            logger.error("导出分销员收入明细报表失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 导出月度财务总览列表 h:commission_report
     */
    @ApiOperation(value = "导出月度财务总览列表 h:commission_report", notes = "导出历史月度财务总览列表 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @Log(title = "导出月度财务总览列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export/summary-list")
    public void exportSummaryList(HttpServletResponse response, CommissionMonthlySummary commissionMonthlySummary)
    {
        try {
            List<CommissionMonthlySummary> list = commissionMonthlySummaryService.selectCommissionMonthlySummaryList(commissionMonthlySummary);
            ExcelUtil<CommissionMonthlySummary> util = new ExcelUtil<CommissionMonthlySummary>(CommissionMonthlySummary.class);
            util.exportExcel(response, list, "月度财务总览列表");
        } catch (Exception e) {
            logger.error("导出月度财务总览列表失败：{}", e.getMessage(), e);
        }
    }
} 