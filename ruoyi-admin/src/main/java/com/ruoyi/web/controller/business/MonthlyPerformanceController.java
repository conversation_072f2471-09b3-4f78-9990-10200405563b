package com.ruoyi.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.vo.MonthlyPerformanceVO;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 主播月度业绩Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Api(tags = "主播月度业绩管理")
@RestController
@RequestMapping("/business/performance")
public class MonthlyPerformanceController extends BaseController
{
    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;

    /**
     * 查询主播月度业绩列表（包含主播信息）
     */
    @ApiOperation(value = "查询主播月度业绩列表", notes = "权限: h:performance, 包含主播信息")
    @PreAuthorize("@ss.hasPermi('h:performance')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyPerformance monthlyPerformance)
    {
        startPage();
        List<MonthlyPerformanceVO> list = monthlyPerformanceService.selectMonthlyPerformanceWithCreatorList(monthlyPerformance);
        return getDataTable(list);
    }

    /**
     * 导出主播月度业绩列表
     */
    @ApiOperation(value = "导出主播月度业绩列表", notes = "权限: h:performance")
    @PreAuthorize("@ss.hasPermi('h:performance')")
    @Log(title = "主播月度业绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyPerformance monthlyPerformance)
    {
        List<MonthlyPerformance> list = monthlyPerformanceService.selectMonthlyPerformanceList(monthlyPerformance);
        ExcelUtil<MonthlyPerformance> util = new ExcelUtil<MonthlyPerformance>(MonthlyPerformance.class);
        util.exportExcel(response, list, "主播月度业绩数据");
    }

    /**
     * 获取主播月度业绩详细信息
     */
    @ApiOperation(value = "获取主播月度业绩详细信息", notes = "权限: h:performance")
    @PreAuthorize("@ss.hasPermi('h:performance')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "主播月度业绩ID", required = true) @PathVariable("id") Integer id)
    {
        return success(monthlyPerformanceService.selectMonthlyPerformanceById(id));
    }

    /**
     * 新增主播月度业绩
     */
    @ApiOperation(value = "新增主播月度业绩", notes = "权限: h:performance")
    @PreAuthorize("@ss.hasPermi('h:performance')")
    @Log(title = "主播月度业绩", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonthlyPerformance monthlyPerformance)
    {
        return toAjax(monthlyPerformanceService.insertMonthlyPerformance(monthlyPerformance));
    }

    /**
     * 修改主播月度业绩
     */
    @ApiOperation(value = "修改主播月度业绩", notes = "权限: h:performance")
    @PreAuthorize("@ss.hasPermi('h:performance')")
    @Log(title = "主播月度业绩", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonthlyPerformance monthlyPerformance)
    {
        return toAjax(monthlyPerformanceService.updateMonthlyPerformance(monthlyPerformance));
    }

    /**
     * 删除主播月度业绩
     */
    @ApiOperation(value = "删除主播月度业绩", notes = "权限: h:performance")
    @PreAuthorize("@ss.hasPermi('h:performance')")
    @Log(title = "主播月度业绩", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam(value = "需要删除的主播月度业绩ID,多个用逗号隔开", required = true) @PathVariable Integer[] ids)
    {
        return toAjax(monthlyPerformanceService.deleteMonthlyPerformanceByIds(ids));
    }
}
