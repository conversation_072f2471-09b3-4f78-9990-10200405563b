package com.ruoyi.web.controller.business;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.business.IHistoricalDataService;
import com.ruoyi.system.service.business.IRawImportService;
import com.ruoyi.system.domain.business.RawImport;
import com.ruoyi.system.domain.vo.CreateVirtualParentRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;

/**
 * 后台-历史数据导入控制器 h:historical
 *
 * <AUTHOR>
 */
@Api(tags = "历史数据导入管理")
@RestController
@RequestMapping("/h/historical")
public class HistoricalDataController extends BaseController
{
    @Autowired
    private IHistoricalDataService historicalDataService;

    @Autowired
    private IRawImportService rawImportService;

    /**
     * 导入Excel历史数据 h:historical
     */
    // @ApiOperation(value = "导入Excel历史数据 h:historical", notes = "上传Excel文件导入历史数据 h:historical")
    // @PreAuthorize("@ss.hasPermi('h:historical')")
    // @Log(title = "历史数据导入", businessType = BusinessType.IMPORT)
    // @PostMapping("/import")
    // public AjaxResult importData(@RequestParam("file") MultipartFile file)
    // {
    //     try
    //     {
    //         if (file.isEmpty())
    //         {
    //             return error("上传文件不能为空");
    //         }

    //         String fileName = file.getOriginalFilename();
    //         if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))
    //         {
    //             return error("只支持Excel文件格式(.xlsx, .xls)");
    //         }

    //         String updateBy = getUsername();
    //         // 默认使用当前时间作为创建时间，避免影响新人资格判定
    //         Map<String, Object> result = historicalDataService.importHistoricalData(file, updateBy, false);

    //         return success(result);
    //     }
    //     catch (Exception e)
    //     {
    //         logger.error("导入历史数据失败", e);
    //         return error("导入失败: " + e.getMessage());
    //     }
    // }

    /**
     * 导入Excel历史数据（使用历史创建时间）h:historical
     */
    @ApiOperation(value = "导入Excel历史数据（使用历史创建时间）h:historical",
                  notes = "上传Excel文件导入历史数据，新用户的创建时间设为数据月份，可能影响新人资格判定 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "历史数据导入（历史时间）", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importDataWithHistoricalTime(@RequestParam("file") MultipartFile file)
    {
        try
        {
            if (file.isEmpty())
            {
                return error("上传文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))
            {
                return error("只支持Excel文件格式(.xlsx, .xls)");
            }

            String updateBy = getUsername();
            // 使用历史数据月份作为创建时间，可能影响新人资格判定
            Map<String, Object> result = historicalDataService.importHistoricalData(file, updateBy, true);

            return success(result);
        }
        catch (Exception e)
        {
            logger.error("导入历史数据失败", e);
            return error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 验证Excel数据格式 h:historical
     */
    @ApiOperation(value = "验证Excel数据格式 h:historical", notes = "验证Excel文件格式和数据有效性 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @PostMapping("/validate")
    public AjaxResult validateData(@RequestParam("file") MultipartFile file)
    {
        try
        {
            if (file.isEmpty())
            {
                return error("上传文件不能为空");
            }
            
            Map<String, Object> result = historicalDataService.validateExcelData(file);
            return success(result);
        }
        catch (Exception e)
        {
            logger.error("验证Excel数据失败", e);
            return error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 创建虚拟上级并分配给组 h:historical
     */
    @ApiOperation(value = "创建虚拟上级 h:historical", notes = "创建虚拟上级并分配给指定组，昵称自动生成为：组名-虚拟 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "创建虚拟上级", businessType = BusinessType.INSERT)
    @PostMapping("/virtual-parent")
    public AjaxResult createVirtualParent(@Valid @RequestBody CreateVirtualParentRequest request)
    {
        try
        {
            String createBy = getUsername();
            String groupName = request.getGroupName();
            
            // 程序自动生成虚拟昵称和备注
            String virtualNickname = groupName + "-虚拟";
            String remark = "虚拟上级";
            
            Long virtualId = historicalDataService.createVirtualParentForGroup(
                groupName, 
                virtualNickname, 
                remark, 
                createBy);
            
            return success("创建成功，虚拟上级ID: " + virtualId + "，昵称: " + virtualNickname);
        }
        catch (Exception e)
        {
            logger.error("创建虚拟上级失败", e);
            return error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 重建创建者关系 h:historical
     */
    @ApiOperation(value = "重建创建者关系 h:historical", notes = "根据组关系重建整个创建者关系树 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "重建创建者关系", businessType = BusinessType.UPDATE)
    @PostMapping("/rebuild-relationships")
    public AjaxResult rebuildRelationships()
    {
        try
        {
            String updateBy = getUsername();
            Map<String, Object> result = historicalDataService.rebuildCreatorRelationships(updateBy);
            
            return success(result);
        }
        catch (Exception e)
        {
            logger.error("重建创建者关系失败", e);
            return error("重建失败: " + e.getMessage());
        }
    }

    /**
     * 自动生成关系 h:historical
     */
    @ApiOperation(value = "自动生成关系 h:historical", notes = "自动为孤儿组创建虚拟上级并建立层级关系 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "自动生成关系", businessType = BusinessType.INSERT)
    @PostMapping("/auto-generate-relationships")
    public AjaxResult autoGenerateRelationships()
    {
        try
        {
            String updateBy = getUsername();
            Map<String, Object> result = historicalDataService.autoGenerateRelationships(updateBy);
            return success(result);
        }
        catch (Exception e)
        {
            logger.error("自动生成关系失败", e);
            return error("自动生成关系失败: " + e.getMessage());
        }
    }

    /**
     * 清理重复的虚拟上级 h:historical
     */
    @ApiOperation(value = "清理重复虚拟上级 h:historical", notes = "清理系统中重复的虚拟上级，保留最早创建的 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "清理重复虚拟上级", businessType = BusinessType.DELETE)
    @PostMapping("/cleanup-duplicate-virtual-creators")
    public AjaxResult cleanupDuplicateVirtualCreators()
    {
        try
        {
            String updateBy = getUsername();
            Map<String, Object> result = historicalDataService.cleanupDuplicateVirtualCreators(updateBy);
            return success(result);
        }
        catch (Exception e)
        {
            logger.error("清理重复虚拟上级失败", e);
            return error("清理失败: " + e.getMessage());
        }
    }

    /**
     * 查询导入历史记录列表 h:historical
     */
    @ApiOperation(value = "获取导入历史记录列表 h:historical", notes = "分页查询导入历史记录 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/imports")
    public TableDataInfo list(RawImport rawImport)
    {
        startPage();
        List<RawImport> list = rawImportService.selectRawImportList(rawImport);
        return getDataTable(list);
    }

    /**
     * 导出导入历史记录 h:historical
     */
    @ApiOperation(value = "导出导入历史记录 h:historical", notes = "导出导入历史记录到Excel h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "导入历史记录", businessType = BusinessType.EXPORT)
    @PostMapping("/imports/export")
    public void export(HttpServletResponse response, RawImport rawImport)
    {
        List<RawImport> list = rawImportService.selectRawImportList(rawImport);
        ExcelUtil<RawImport> util = new ExcelUtil<RawImport>(RawImport.class);
        util.exportExcel(response, list, "导入历史记录");
    }

    /**
     * 获取导入历史记录详细信息 h:historical
     */
    @ApiOperation(value = "获取导入历史记录详情 h:historical", notes = "根据ID获取导入历史记录详细信息 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/imports/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rawImportService.selectRawImportById(id));
    }

    /**
     * 删除导入历史记录 h:historical
     */
    @ApiOperation(value = "删除导入历史记录 h:historical", notes = "根据ID删除导入历史记录 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "导入历史记录", businessType = BusinessType.DELETE)
    @PostMapping("/imports/delete/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rawImportService.deleteRawImportByIds(ids));
    }
} 