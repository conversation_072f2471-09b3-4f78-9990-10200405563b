package com.ruoyi.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.business.GroupCreatorRelation;
import com.ruoyi.system.domain.vo.GroupRelationVO;
import com.ruoyi.system.domain.vo.AssignGroupParentRequest;
import com.ruoyi.system.domain.vo.UpdateGroupTypeRequest;
import com.ruoyi.system.service.business.IGroupCreatorRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;

/**
 * 后台-组关系管理控制器 h:historical
 *
 * <AUTHOR>
 */
@Api(tags = "组关系管理")
@RestController
@RequestMapping("/h/group")
public class GroupRelationController extends BaseController
{
    @Autowired
    private IGroupCreatorRelationService groupCreatorRelationService;

    /**
     * 查询组与creators的关系列表 h:historical
     */
    @ApiOperation(value = "获取组关系列表 h:historical", notes = "获取所有组与creators的关系，包含未指定creatorID的组 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/relations")
    public TableDataInfo list()
    {
        startPage();
        List<GroupRelationVO> list = groupCreatorRelationService.selectGroupRelationList();
        return getDataTable(list);
    }

    /**
     * 查询组名与CreatorID关系列表 h:historical
     */
    @ApiOperation(value = "获取组创建者关系列表 h:historical", notes = "分页查询组名与CreatorID关系 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/list")
    public TableDataInfo listRelations(GroupCreatorRelation groupCreatorRelation)
    {
        startPage();
        List<GroupCreatorRelation> list = groupCreatorRelationService.selectGroupCreatorRelationList(groupCreatorRelation);
        return getDataTable(list);
    }

    /**
     * 导出组名与CreatorID关系列表 h:historical
     */
    @ApiOperation(value = "导出组关系列表 h:historical", notes = "导出组名与CreatorID关系到Excel h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "组名与CreatorID关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GroupCreatorRelation groupCreatorRelation)
    {
        List<GroupCreatorRelation> list = groupCreatorRelationService.selectGroupCreatorRelationList(groupCreatorRelation);
        ExcelUtil<GroupCreatorRelation> util = new ExcelUtil<GroupCreatorRelation>(GroupCreatorRelation.class);
        util.exportExcel(response, list, "组名与CreatorID关系");
    }

    /**
     * 获取组名与CreatorID关系详细信息 h:historical
     */
    @ApiOperation(value = "获取组关系详情 h:historical", notes = "根据组名获取组名与CreatorID关系详细信息 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/{groupName}")
    public AjaxResult getInfo(@PathVariable("groupName") String groupName)
    {
        return success(groupCreatorRelationService.selectGroupCreatorRelationByGroupName(groupName));
    }

    /**
     * 新增组名与CreatorID关系 h:historical
     */
    @ApiOperation(value = "新增组关系 h:historical", notes = "新增组名与CreatorID关系 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "组名与CreatorID关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GroupCreatorRelation groupCreatorRelation)
    {
        groupCreatorRelation.setCreateBy(getUsername());
        groupCreatorRelation.setUpdateBy(getUsername());
        return toAjax(groupCreatorRelationService.insertGroupCreatorRelation(groupCreatorRelation));
    }

    /**
     * 修改组名与CreatorID关系 h:historical
     */
    @ApiOperation(value = "修改组关系 h:historical", notes = "修改组名与CreatorID关系 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "组名与CreatorID关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GroupCreatorRelation groupCreatorRelation)
    {
        groupCreatorRelation.setUpdateBy(getUsername());
        return toAjax(groupCreatorRelationService.updateGroupCreatorRelation(groupCreatorRelation));
    }

    /**
     * 指定组对应的上级 h:historical
     */
    @ApiOperation(value = "指定组上级 h:historical", notes = "为指定组分配上级creator h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "指定组上级", businessType = BusinessType.UPDATE)
    @PostMapping("/assign-parent")
    public AjaxResult assignParent(@Valid @RequestBody AssignGroupParentRequest request)
    {
        try
        {
            String updateBy = getUsername();
            int result = groupCreatorRelationService.assignGroupParent(
                request.getGroupName(),
                request.getCreatorId(),
                updateBy);

            return toAjax(result);
        }
        catch (Exception e)
        {
            logger.error("指定组上级失败", e);
            return error("指定失败: " + e.getMessage());
        }
    }

    /**
     * 修改组类型 h:historical
     */
    @ApiOperation(value = "修改组类型 h:historical", notes = "修改指定组的类型（0-分销组，1-非分销组）h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "修改组类型", businessType = BusinessType.UPDATE)
    @PostMapping("/update-type")
    public AjaxResult updateGroupType(@Valid @RequestBody UpdateGroupTypeRequest request)
    {
        try
        {
            String updateBy = getUsername();
            int result = groupCreatorRelationService.updateGroupType(
                request.getGroupName(),
                request.getGroupType(),
                updateBy);

            return toAjax(result);
        }
        catch (Exception e)
        {
            logger.error("修改组类型失败", e);
            return error("修改失败: " + e.getMessage());
        }
    }

    /**
     * 删除组名与CreatorID关系 h:historical
     */
    @ApiOperation(value = "删除组关系 h:historical", notes = "批量删除组名与CreatorID关系 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "组名与CreatorID关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupNames}")
    public AjaxResult remove(@PathVariable String[] groupNames)
    {
        return toAjax(groupCreatorRelationService.deleteGroupCreatorRelationByGroupNames(groupNames));
    }
} 