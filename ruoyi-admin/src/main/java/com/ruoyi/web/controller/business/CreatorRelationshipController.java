package com.ruoyi.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.business.CreatorRelationship;
import com.ruoyi.system.service.business.ICreatorRelationshipService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 主播层级关系Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Api(tags = "主播层级关系管理")
@RestController
@RequestMapping("/h/relationship")
public class CreatorRelationshipController extends BaseController
{
    @Autowired
    private ICreatorRelationshipService creatorRelationshipService;

    @ApiOperation("查询主播层级关系列表")
    @PreAuthorize("@ss.hasPermi('h:relationship:list')")
    @GetMapping("/list")
    public TableDataInfo list(CreatorRelationship creatorRelationship)
    {
        startPage();
        List<CreatorRelationship> list = creatorRelationshipService.selectCreatorRelationshipList(creatorRelationship);
        return getDataTable(list);
    }

    @ApiOperation("导出主播层级关系列表")
    @PreAuthorize("@ss.hasPermi('h:relationship:export')")
    @Log(title = "主播层级关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CreatorRelationship creatorRelationship)
    {
        List<CreatorRelationship> list = creatorRelationshipService.selectCreatorRelationshipList(creatorRelationship);
        ExcelUtil<CreatorRelationship> util = new ExcelUtil<CreatorRelationship>(CreatorRelationship.class);
        util.exportExcel(response, list, "主播层级关系数据");
    }

    @ApiOperation("获取主播层级关系详细信息")
    @PreAuthorize("@ss.hasPermi('h:relationship:query')")
    @GetMapping(value = "/{ancestorId}/{descendantId}")
    public AjaxResult getInfo(@ApiParam(value = "祖先ID", required = true) @PathVariable("ancestorId") Long ancestorId,
                              @ApiParam(value = "后代ID", required = true) @PathVariable("descendantId") Long descendantId)
    {
        return success(creatorRelationshipService.selectCreatorRelationshipById(ancestorId, descendantId));
    }

    @ApiOperation("新增主播层级关系 (通常由重建任务管理，慎用)")
    @PreAuthorize("@ss.hasPermi('h:relationship:add')")
    @Log(title = "主播层级关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CreatorRelationship creatorRelationship)
    {
        return toAjax(creatorRelationshipService.insertCreatorRelationship(creatorRelationship));
    }

    // 一般不提供修改接口，因为层级关系由父ID决定，通过重建生成

    @ApiOperation("删除主播层级关系 (通常由重建任务管理，慎用)")
    @PreAuthorize("@ss.hasPermi('h:relationship:remove')")
    @Log(title = "主播层级关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ancestorId}/{descendantId}")
    public AjaxResult remove(@ApiParam(value = "祖先ID", required = true) @PathVariable("ancestorId") Long ancestorId,
                             @ApiParam(value = "后代ID", required = true) @PathVariable("descendantId") Long descendantId)
    {
        return toAjax(creatorRelationshipService.deleteCreatorRelationshipById(ancestorId, descendantId));
    }

    @ApiOperation("重建主播层级关系表")
    @PreAuthorize("@ss.hasPermi('h:relationship:rebuild')")
    @Log(title = "主播层级关系", businessType = BusinessType.UPDATE) // 使用UPDATE类型标记重建操作
    @PostMapping("/rebuild")
    public AjaxResult rebuildRelationships()
    {
        creatorRelationshipService.rebuildCreatorRelationships();
        return success("主播层级关系表重建任务已启动");
    }

    @ApiOperation("查询指定主播的所有后代")
    @PreAuthorize("@ss.hasPermi('h:relationship:query')")
    @GetMapping("/descendants/{creatorId}")
    public AjaxResult getDescendants(
            @ApiParam(value = "主播ID (作为祖先)", required = true) @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "最小深度 (可选)") @RequestParam(required = false) Integer minDepth,
            @ApiParam(value = "最大深度 (可选)") @RequestParam(required = false) Integer maxDepth)
    {
        List<CreatorRelationship> descendants = creatorRelationshipService.getDescendants(creatorId, minDepth, maxDepth);
        return success(descendants);
    }

    @ApiOperation("查询指定主播的所有祖先")
    @PreAuthorize("@ss.hasPermi('h:relationship:query')")
    @GetMapping("/ancestors/{creatorId}")
    public AjaxResult getAncestors(
            @ApiParam(value = "主播ID (作为后代)", required = true) @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "最小深度 (可选)") @RequestParam(required = false) Integer minDepth,
            @ApiParam(value = "最大深度 (可选)") @RequestParam(required = false) Integer maxDepth)
    {
        List<CreatorRelationship> ancestors = creatorRelationshipService.getAncestors(creatorId, minDepth, maxDepth);
        return success(ancestors);
    }

    @ApiOperation("查询指定主播的直接上级")
    @PreAuthorize("@ss.hasPermi('h:relationship:query')")
    @GetMapping("/parent/{creatorId}")
    public AjaxResult getDirectParent(@ApiParam(value = "主播ID", required = true) @PathVariable("creatorId") Long creatorId)
    {
        Long parentId = creatorRelationshipService.getDirectParentId(creatorId);
        return success(parentId);
    }

    @ApiOperation("查询指定主播的所有直接下级")
    @PreAuthorize("@ss.hasPermi('h:relationship:query')")
    @GetMapping("/children/{creatorId}")
    public AjaxResult getDirectChildren(@ApiParam(value = "主播ID", required = true) @PathVariable("creatorId") Long creatorId)
    {
        List<Long> childrenIds = creatorRelationshipService.getDirectChildrenIds(creatorId);
        return success(childrenIds);
    }
}
