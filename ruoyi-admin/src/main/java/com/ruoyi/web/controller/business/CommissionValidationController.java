package com.ruoyi.web.controller.business;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.commission.ICommissionCalculationService;
import com.ruoyi.system.domain.commission.CommissionCalculationLogs;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 佣金计算验证Controller
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/business/commission/validation")
@Api(tags = "佣金计算验证管理")
public class CommissionValidationController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(CommissionValidationController.class);
    
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
    
    @Autowired
    private ICommissionCalculationService commissionCalculationService;
    
    /**
     * 验证单个分销员计算结果
     */
    @ApiOperation(value = "验证单个分销员计算结果", notes = "验证指定分销员在指定月份的计算结果是否正确")
    @Log(title = "验证分销员计算", businessType = BusinessType.OTHER)
    @GetMapping("/distributor/{creatorId}/{dataMonth}")
    public AjaxResult validateDistributor(
            @ApiParam(value = "分销员ID", required = true, example = "12345") 
            @PathVariable Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = commissionCalculationService.validateSingleDistributorCalculation(creatorId, month);
            return success(result);
        } catch (Exception e) {
            logger.error("验证分销员 {} 在 {} 的计算结果失败：{}", creatorId, dataMonth, e.getMessage(), e);
            return error("验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取月度计算验证报告
     */
    @ApiOperation(value = "获取月度计算验证报告", notes = "获取指定月份的整体计算验证报告，包含汇总数据验证和统计信息")
    @Log(title = "获取验证报告", businessType = BusinessType.OTHER)
    @GetMapping("/report/{dataMonth}")
    public AjaxResult getValidationReport(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = commissionCalculationService.getCalculationValidationReport(month);
            return success(result);
        } catch (Exception e) {
            logger.error("获取 {} 月度验证报告失败：{}", dataMonth, e.getMessage(), e);
            return error("获取报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取分销员计算日志
     */
    @ApiOperation(value = "获取分销员计算日志", notes = "获取指定分销员在指定月份的详细计算日志")
    @Log(title = "获取计算日志", businessType = BusinessType.OTHER)
    @GetMapping("/logs/{creatorId}/{dataMonth}")
    public AjaxResult getDistributorLogs(
            @ApiParam(value = "分销员ID", required = true, example = "12345") 
            @PathVariable Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            List<CommissionCalculationLogs> logs = commissionCalculationService.getDistributorCalculationLogs(creatorId, month);
            return success(logs);
        } catch (Exception e) {
            logger.error("获取分销员 {} 在 {} 的计算日志失败：{}", creatorId, dataMonth, e.getMessage(), e);
            return error("获取日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取重新计算统计信息
     */
    @ApiOperation("获取重新计算统计信息")
    @GetMapping("/recalculation-statistics/{dataMonth}")
    public AjaxResult getRecalculationStatistics(
            @ApiParam(value = "数据月份", required = true, example = "2024-01-01")
            @PathVariable("dataMonth") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataMonth) {
        
        try {
            Map<String, Object> statistics = commissionCalculationService.getRecalculationStatistics(dataMonth);
            return AjaxResult.success("获取重新计算统计成功", statistics);
            
        } catch (Exception e) {
            logger.error("获取重新计算统计失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取重新计算统计失败: " + e.getMessage());
        }
    }

    /**
     * 查询历史计算日志
     */
    @ApiOperation("查询历史计算日志")
    @GetMapping("/logs-historical/{dataMonth}")
    public AjaxResult getHistoricalCalculationLogs(
            @ApiParam(value = "数据月份", required = true, example = "2024-01-01")
            @PathVariable("dataMonth") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataMonth,
            @ApiParam(value = "批次ID", example = "RECALC_1642334400000")
            @RequestParam(value = "batchId", required = false) String batchId) {
        
        try {
            Map<String, Object> result = commissionCalculationService.getHistoricalCalculationLogs(dataMonth, batchId);
            return AjaxResult.success("查询历史日志成功", result);
            
        } catch (Exception e) {
            logger.error("查询历史日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询历史日志失败: " + e.getMessage());
        }
    }

    /**
     * 查询重新计算历史记录
     */
    @ApiOperation("查询重新计算历史记录")
    @GetMapping("/recalculation-history/{dataMonth}")
    public AjaxResult getRecalculationHistory(
            @ApiParam(value = "数据月份", required = true, example = "2024-01-01")
            @PathVariable("dataMonth") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataMonth) {
        
        try {
            Map<String, Object> history = commissionCalculationService.getRecalculationHistory(dataMonth);
            return AjaxResult.success("查询重新计算历史成功", history);
            
        } catch (Exception e) {
            logger.error("查询重新计算历史失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询重新计算历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新有效计算日志
     */
    @ApiOperation("获取最新有效计算日志")
    @GetMapping("/logs-latest/{dataMonth}")
    public AjaxResult getLatestValidCalculationLogs(
            @ApiParam(value = "数据月份", required = true, example = "2024-01-01")
            @PathVariable("dataMonth") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dataMonth) {
        
        try {
            List<CommissionCalculationLogs> logs = commissionCalculationService.getLatestValidCalculationLogs(dataMonth);
            
            Map<String, Object> result = new HashMap<>();
            result.put("dataMonth", new SimpleDateFormat("yyyy-MM").format(dataMonth));
            result.put("logs", logs);
            result.put("totalCount", logs.size());
            
            return AjaxResult.success("获取最新有效日志成功", result);
            
        } catch (Exception e) {
            logger.error("获取最新有效日志失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取最新有效日志失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量验证分销员计算结果
     */
    @ApiOperation(value = "批量验证分销员计算结果", notes = "批量验证指定月份所有分销员的计算结果")
    @Log(title = "批量验证计算结果", businessType = BusinessType.OTHER)
    @PostMapping("/batch/{dataMonth}")
    public AjaxResult batchValidateDistributors(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable String dataMonth,
            @ApiParam(value = "分销员ID列表，如果为空则验证所有分销员") 
            @RequestBody(required = false) List<Long> creatorIds)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            
            // 如果没有指定分销员ID，则获取该月份所有有收入的分销员
            if (creatorIds == null || creatorIds.isEmpty()) {
                // 这里可以调用服务获取所有分销员ID
                return error("批量验证功能待完善");
            }
            
            // 批量验证逻辑
            return error("批量验证功能待完善");
            
        } catch (Exception e) {
            logger.error("批量验证 {} 月度计算结果失败：{}", dataMonth, e.getMessage(), e);
            return error("批量验证失败: " + e.getMessage());
        }
    }
} 