package com.ruoyi.web.controller.business;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.commission.ICommissionCalculationService;
import com.ruoyi.system.domain.commission.CommissionMonthlySummary;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 后台-佣金计算引擎控制器 h:commission_calculation
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "后台-佣金计算引擎 h:commission_calculation")
@RestController
@RequestMapping("/business/commission/calculation")
public class CommissionCalculationController extends BaseController
{
    @Autowired
    private ICommissionCalculationService commissionCalculationService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");

    /**
     * 执行月度佣金计算 h:commission_calculation
     */
    @ApiOperation(value = "执行月度佣金计算 h:commission_calculation", notes = "根据指定月份执行佣金计算 h:commission_calculation")
    @PreAuthorize("@ss.hasPermi('h:commission_calculation')")
    @Log(title = "月度佣金计算", businessType = BusinessType.INSERT)
    @PostMapping("/execute/{dataMonth}")
    public AjaxResult executeMonthlyCalculation(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = commissionCalculationService.executeMonthlyCalculation(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("执行月度佣金计算失败：{}", e.getMessage(), e);
            return error("执行月度佣金计算失败：" + e.getMessage());
        }
    }

    /**
     * 获取计算状态 h:commission_calculation
     */
    @ApiOperation(value = "获取计算状态 h:commission_calculation", notes = "获取月度佣金计算进度状态 h:commission_calculation")
    @PreAuthorize("@ss.hasPermi('h:commission_calculation')")
    @GetMapping("/status/{dataMonth}")
    public AjaxResult getCalculationStatus(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = commissionCalculationService.getCalculationStatus(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取计算状态失败：{}", e.getMessage(), e);
            return error("获取计算状态失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算月度佣金 h:commission_calculation
     */
    @ApiOperation(value = "重新计算月度佣金 h:commission_calculation", notes = "重新计算指定月份的佣金分成 h:commission_calculation")
    @PreAuthorize("@ss.hasPermi('h:commission_calculation')")
    @Log(title = "重新计算月度佣金", businessType = BusinessType.UPDATE)
    @PostMapping("/recalculate/{dataMonth}")
    public AjaxResult recalculateMonthlyCommission(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth,
            @ApiParam(value = "是否强制重新计算", example = "false")
            @RequestParam(value = "force", defaultValue = "false") boolean force)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = commissionCalculationService.recalculateMonthlyCommission(month, force);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("重新计算月度佣金失败：{}", e.getMessage(), e);
            return error("重新计算月度佣金失败：" + e.getMessage());
        }
    }

    /**
     * 预览计算结果 h:commission_calculation
     */
    @ApiOperation(value = "预览计算结果 h:commission_calculation", notes = "预览月度佣金计算结果，不保存 h:commission_calculation")
    @PreAuthorize("@ss.hasPermi('h:commission_calculation')")
    @PostMapping("/preview/{dataMonth}")
    public AjaxResult previewCalculation(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = commissionCalculationService.previewCalculation(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("预览计算结果失败：{}", e.getMessage(), e);
            return error("预览计算结果失败：" + e.getMessage());
        }
    }

    /**
     * 检查计算前置条件 h:commission_calculation
     */
    @ApiOperation(value = "检查计算前置条件 h:commission_calculation", notes = "检查执行佣金计算的前置条件 h:commission_calculation")
    @PreAuthorize("@ss.hasPermi('h:commission_calculation')")
    @GetMapping("/check-preconditions/{dataMonth}")
    public AjaxResult checkCalculationPreconditions(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = commissionCalculationService.checkCalculationPreconditions(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("检查计算前置条件失败：{}", e.getMessage(), e);
            return error("检查计算前置条件失败：" + e.getMessage());
        }
    }

    /**
     * 获取月度计算总览 h:commission_calculation
     */
    @ApiOperation(value = "获取月度计算总览 h:commission_calculation", notes = "获取指定月份的佣金计算总览信息 h:commission_calculation")
    @PreAuthorize("@ss.hasPermi('h:commission_calculation')")
    @GetMapping("/summary/{dataMonth}")
    public AjaxResult getMonthlyCalculationSummary(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            CommissionMonthlySummary summary = commissionCalculationService.getMonthlyCalculationSummary(month);
            return success(summary);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取月度计算总览失败：{}", e.getMessage(), e);
            return error("获取月度计算总览失败：" + e.getMessage());
        }
    }
} 