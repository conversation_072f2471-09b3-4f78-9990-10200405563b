package com.ruoyi.web.controller.business;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.business.IDashboardService;
import com.ruoyi.system.domain.dto.DashboardResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 后台-个人主仪表盘控制器 h:dashboard
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "个人主仪表盘管理")
@RestController
@RequestMapping("/api/dashboard")
public class DashboardController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);

    @Autowired
    private IDashboardService dashboardService;

    /**
     * 获取指定分销员在指定月份的完整仪表盘聚合数据 h:dashboard
     */
    @ApiOperation(value = "获取个人主仪表盘数据 h:dashboard", notes = "获取指定分销员在指定月份的完整仪表盘聚合数据 h:dashboard")
    @PreAuthorize("@ss.hasPermi('h:dashboard')")
    @GetMapping("/{creatorId}/{month}")
    public AjaxResult getDashboardData(
            @ApiParam(value = "分销员ID", required = true, example = "1001")
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "月份，格式为YYYY-MM", required = true, example = "2024-12")
            @PathVariable("month") String month)
    {
        logger.info("开始获取仪表盘数据，用户ID: {}, 月份: {}", creatorId, month);
        
        try {
            // 参数验证
            if (creatorId == null || creatorId <= 0) {
                logger.warn("无效的用户ID: {}", creatorId);
                return error("用户ID不能为空且必须大于0");
            }
            
            if (month == null || !month.matches("\\d{4}-\\d{2}")) {
                logger.warn("无效的月份格式: {}", month);
                return error("月份格式错误，请使用YYYY-MM格式");
            }
            
            // 调用服务获取数据
            DashboardResponse dashboardData = dashboardService.getDashboardData(creatorId, month);
            
            logger.info("仪表盘数据获取成功，用户ID: {}, 月份: {}", creatorId, month);
            return success(dashboardData);
            
        } catch (Exception e) {
            logger.error("获取仪表盘数据失败，用户ID: {}, 月份: {}, 错误: {}", creatorId, month, e.getMessage(), e);
            return error("获取仪表盘数据失败: " + e.getMessage());
        }
    }
}
