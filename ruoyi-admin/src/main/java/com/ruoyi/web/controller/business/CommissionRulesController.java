package com.ruoyi.web.controller.business;

import java.util.List;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.commission.CommissionLevelRules;
import com.ruoyi.system.domain.commission.CommissionMultilevelRules;
import com.ruoyi.system.domain.commission.CommissionRecruitmentBonusRules;
import com.ruoyi.system.service.commission.ICommissionLevelRulesService;
import com.ruoyi.system.service.commission.ICommissionMultilevelRulesService;
import com.ruoyi.system.service.commission.ICommissionRecruitmentBonusRulesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 后台-佣金规则管理控制器 h:commission_rules
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "后台-佣金规则管理 h:commission_rules")
@RestController
@RequestMapping("/business/commission/rules")
public class CommissionRulesController extends BaseController
{
    @Autowired
    private ICommissionLevelRulesService commissionLevelRulesService;

    @Autowired
    private ICommissionMultilevelRulesService commissionMultilevelRulesService;

    @Autowired
    private ICommissionRecruitmentBonusRulesService commissionRecruitmentBonusRulesService;

    // ========== 分销员等级规则管理 ==========

    /**
     * 查询分销员等级规则列表 h:commission_rules
     */
    @ApiOperation(value = "查询分销员等级规则列表 h:commission_rules", notes = "获取分销员等级规则分页列表 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @GetMapping("/level/list")
    public TableDataInfo levelRulesList(CommissionLevelRules commissionLevelRules)
    {
        startPage();
        List<CommissionLevelRules> list = commissionLevelRulesService.selectCommissionLevelRulesList(commissionLevelRules);
        return getDataTable(list);
    }

    /**
     * 获取分销员等级规则详细信息 h:commission_rules
     */
    @ApiOperation(value = "获取分销员等级规则详细信息 h:commission_rules", notes = "根据ID获取分销员等级规则详细信息 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @GetMapping(value = "/level/{id}")
    public AjaxResult getLevelRuleInfo(@PathVariable("id") Integer id)
    {
        return success(commissionLevelRulesService.selectCommissionLevelRulesById(id));
    }

    /**
     * 新增分销员等级规则 h:commission_rules
     */
    @ApiOperation(value = "新增分销员等级规则 h:commission_rules", notes = "新增一条分销员等级规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "分销员等级规则", businessType = BusinessType.INSERT)
    @PostMapping("/level")
    public AjaxResult addLevelRule(@RequestBody CommissionLevelRules commissionLevelRules)
    {
        return toAjax(commissionLevelRulesService.insertCommissionLevelRules(commissionLevelRules));
    }

    /**
     * 修改分销员等级规则 h:commission_rules
     */
    @ApiOperation(value = "修改分销员等级规则 h:commission_rules", notes = "根据ID修改分销员等级规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "分销员等级规则", businessType = BusinessType.UPDATE)
    @PutMapping("/level")
    public AjaxResult editLevelRule(@RequestBody CommissionLevelRules commissionLevelRules)
    {
        return toAjax(commissionLevelRulesService.updateCommissionLevelRules(commissionLevelRules));
    }

    /**
     * 删除分销员等级规则 h:commission_rules
     */
    @ApiOperation(value = "删除分销员等级规则 h:commission_rules", notes = "根据ID删除分销员等级规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "分销员等级规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/level/{ids}")
    public AjaxResult removeLevelRule(@PathVariable Integer[] ids)
    {
        return toAjax(commissionLevelRulesService.deleteCommissionLevelRulesByIds(ids));
    }

    // ========== 多级提成规则管理 ==========

    /**
     * 查询多级提成规则列表 h:commission_rules
     */
    @ApiOperation(value = "查询多级提成规则列表 h:commission_rules", notes = "获取多级提成规则分页列表 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @GetMapping("/multilevel/list")
    public TableDataInfo multilevelRulesList(CommissionMultilevelRules commissionMultilevelRules)
    {
        startPage();
        List<CommissionMultilevelRules> list = commissionMultilevelRulesService.selectCommissionMultilevelRulesList(commissionMultilevelRules);
        return getDataTable(list);
    }

    /**
     * 新增多级提成规则 h:commission_rules
     */
    @ApiOperation(value = "新增多级提成规则 h:commission_rules", notes = "新增一条多级提成规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "多级提成规则", businessType = BusinessType.INSERT)
    @PostMapping("/multilevel")
    public AjaxResult addMultilevelRule(@RequestBody CommissionMultilevelRules commissionMultilevelRules)
    {
        return toAjax(commissionMultilevelRulesService.insertCommissionMultilevelRules(commissionMultilevelRules));
    }

    /**
     * 修改多级提成规则 h:commission_rules
     */
    @ApiOperation(value = "修改多级提成规则 h:commission_rules", notes = "根据ID修改多级提成规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "多级提成规则", businessType = BusinessType.UPDATE)
    @PutMapping("/multilevel")
    public AjaxResult editMultilevelRule(@RequestBody CommissionMultilevelRules commissionMultilevelRules)
    {
        return toAjax(commissionMultilevelRulesService.updateCommissionMultilevelRules(commissionMultilevelRules));
    }

    // ========== 拉新奖励规则管理 ==========

    /**
     * 查询拉新奖励规则列表 h:commission_rules
     */
    @ApiOperation(value = "查询拉新奖励规则列表 h:commission_rules", notes = "获取拉新奖励规则分页列表 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @GetMapping("/recruitment/list")
    public TableDataInfo recruitmentRulesList(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules)
    {
        startPage();
        List<CommissionRecruitmentBonusRules> list = commissionRecruitmentBonusRulesService.selectCommissionRecruitmentBonusRulesList(commissionRecruitmentBonusRules);
        return getDataTable(list);
    }

    /**
     * 新增拉新奖励规则 h:commission_rules
     */
    @ApiOperation(value = "新增拉新奖励规则 h:commission_rules", notes = "新增一条拉新奖励规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "拉新奖励规则", businessType = BusinessType.INSERT)
    @PostMapping("/recruitment")
    public AjaxResult addRecruitmentRule(@RequestBody CommissionRecruitmentBonusRules commissionRecruitmentBonusRules)
    {
        return toAjax(commissionRecruitmentBonusRulesService.insertCommissionRecruitmentBonusRules(commissionRecruitmentBonusRules));
    }

    /**
     * 获取拉新奖励规则详细信息 h:commission_rules
     */
    @ApiOperation(value = "获取拉新奖励规则详细信息 h:commission_rules", notes = "根据ID获取拉新奖励规则详细信息 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @GetMapping(value = "/recruitment/{id}")
    public AjaxResult getRecruitmentRuleInfo(@PathVariable("id") Integer id)
    {
        return success(commissionRecruitmentBonusRulesService.selectCommissionRecruitmentBonusRulesById(id));
    }

    /**
     * 修改拉新奖励规则 h:commission_rules
     */
    @ApiOperation(value = "修改拉新奖励规则 h:commission_rules", notes = "根据ID修改拉新奖励规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "拉新奖励规则", businessType = BusinessType.UPDATE)
    @PutMapping("/recruitment")
    public AjaxResult editRecruitmentRule(@RequestBody CommissionRecruitmentBonusRules commissionRecruitmentBonusRules)
    {
        return toAjax(commissionRecruitmentBonusRulesService.updateCommissionRecruitmentBonusRules(commissionRecruitmentBonusRules));
    }

    /**
     * 删除拉新奖励规则 h:commission_rules
     */
    @ApiOperation(value = "删除拉新奖励规则 h:commission_rules", notes = "根据ID删除拉新奖励规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "拉新奖励规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/recruitment/{ids}")
    public AjaxResult removeRecruitmentRule(@PathVariable Integer[] ids)
    {
        return toAjax(commissionRecruitmentBonusRulesService.deleteCommissionRecruitmentBonusRulesByIds(ids));
    }

    // ========== 规则初始化 ==========

    /**
     * 初始化默认规则 h:commission_rules
     */
    @ApiOperation(value = "初始化默认规则 h:commission_rules", notes = "初始化系统默认的各类规则 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "佣金规则初始化", businessType = BusinessType.INSERT)
    @PostMapping("/init")
    public AjaxResult initRules()
    {
        try {
            int insertedCount = 0;

            // 检查并初始化分销员等级规则（基于合格拉新人数）
            if (!isLevelRuleExists("金牌")) {
                CommissionLevelRules goldLevel = new CommissionLevelRules();
                goldLevel.setLevelName("金牌");
                goldLevel.setMinQualifiedRecruits(10);  // 需要10个合格拉新
                goldLevel.setCommissionRate(new BigDecimal("0.0200"));
                goldLevel.setPayoutDepth(3);  // 享受3级收益
                goldLevel.setDisplayOrder(1);
                goldLevel.setIsActive(1);
                commissionLevelRulesService.insertCommissionLevelRules(goldLevel);
                insertedCount++;
            }

            if (!isLevelRuleExists("银牌")) {
                CommissionLevelRules silverLevel = new CommissionLevelRules();
                silverLevel.setLevelName("银牌");
                silverLevel.setMinQualifiedRecruits(5);  // 需要5个合格拉新
                silverLevel.setCommissionRate(new BigDecimal("0.0100"));
                silverLevel.setPayoutDepth(2);  // 享受2级收益
                silverLevel.setDisplayOrder(2);
                silverLevel.setIsActive(1);
                commissionLevelRulesService.insertCommissionLevelRules(silverLevel);
                insertedCount++;
            }

            if (!isLevelRuleExists("铜牌")) {
                CommissionLevelRules bronzeLevel = new CommissionLevelRules();
                bronzeLevel.setLevelName("铜牌");
                bronzeLevel.setMinQualifiedRecruits(2);  // 需要2个合格拉新
                bronzeLevel.setCommissionRate(new BigDecimal("0.0050"));
                bronzeLevel.setPayoutDepth(1);  // 享受1级收益
                bronzeLevel.setDisplayOrder(3);
                bronzeLevel.setIsActive(1);
                commissionLevelRulesService.insertCommissionLevelRules(bronzeLevel);
                insertedCount++;
            }

            // 检查并初始化多级提成规则
            if (!isMultilevelRuleExists(1)) {
                CommissionMultilevelRules level1Rule = new CommissionMultilevelRules();
                level1Rule.setDepth(1);
                level1Rule.setCommissionRate(new BigDecimal("0.0200"));
                level1Rule.setIsActive(1);
                commissionMultilevelRulesService.insertCommissionMultilevelRules(level1Rule);
                insertedCount++;
            }

            if (!isMultilevelRuleExists(2)) {
                CommissionMultilevelRules level2Rule = new CommissionMultilevelRules();
                level2Rule.setDepth(2);
                level2Rule.setCommissionRate(new BigDecimal("0.0100"));
                level2Rule.setIsActive(1);
                commissionMultilevelRulesService.insertCommissionMultilevelRules(level2Rule);
                insertedCount++;
            }

            if (!isMultilevelRuleExists(3)) {
                CommissionMultilevelRules level3Rule = new CommissionMultilevelRules();
                level3Rule.setDepth(3);
                level3Rule.setCommissionRate(new BigDecimal("0.0050"));
                level3Rule.setIsActive(1);
                commissionMultilevelRulesService.insertCommissionMultilevelRules(level3Rule);
                insertedCount++;
            }

            // 检查并初始化拉新奖励规则
            if (!isRecruitmentBonusRuleExists(5)) {
                CommissionRecruitmentBonusRules bonus1 = new CommissionRecruitmentBonusRules();
                bonus1.setMinNewRecruits(5);
                bonus1.setBonusUsd(new BigDecimal("10.0000"));
                bonus1.setIsActive(1);
                commissionRecruitmentBonusRulesService.insertCommissionRecruitmentBonusRules(bonus1);
                insertedCount++;
            }

            if (!isRecruitmentBonusRuleExists(10)) {
                CommissionRecruitmentBonusRules bonus2 = new CommissionRecruitmentBonusRules();
                bonus2.setMinNewRecruits(10);
                bonus2.setBonusUsd(new BigDecimal("20.0000"));
                bonus2.setIsActive(1);
                commissionRecruitmentBonusRulesService.insertCommissionRecruitmentBonusRules(bonus2);
                insertedCount++;
            }

            if (!isRecruitmentBonusRuleExists(30)) {
                CommissionRecruitmentBonusRules bonus3 = new CommissionRecruitmentBonusRules();
                bonus3.setMinNewRecruits(30);
                bonus3.setBonusUsd(new BigDecimal("50.0000"));
                bonus3.setIsActive(1);
                commissionRecruitmentBonusRulesService.insertCommissionRecruitmentBonusRules(bonus3);
                insertedCount++;
            }

            if (insertedCount > 0) {
                return success("规则初始化成功，新增 " + insertedCount + " 条规则");
            } else {
                return success("规则已存在，无需重复初始化");
            }
        } catch (Exception e) {
            logger.error("规则初始化失败", e);
            return error("规则初始化失败：" + e.getMessage());
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 检查等级规则是否已存在
     */
    private boolean isLevelRuleExists(String levelName) {
        CommissionLevelRules queryCondition = new CommissionLevelRules();
        queryCondition.setLevelName(levelName);
        List<CommissionLevelRules> existingRules = commissionLevelRulesService.selectCommissionLevelRulesList(queryCondition);
        return !existingRules.isEmpty();
    }

    /**
     * 检查多级提成规则是否已存在
     */
    private boolean isMultilevelRuleExists(Integer depth) {
        CommissionMultilevelRules queryCondition = new CommissionMultilevelRules();
        queryCondition.setDepth(depth);
        List<CommissionMultilevelRules> existingRules = commissionMultilevelRulesService.selectCommissionMultilevelRulesList(queryCondition);
        return !existingRules.isEmpty();
    }

    /**
     * 检查拉新奖励规则是否已存在
     */
    private boolean isRecruitmentBonusRuleExists(Integer minNewRecruits) {
        CommissionRecruitmentBonusRules queryCondition = new CommissionRecruitmentBonusRules();
        queryCondition.setMinNewRecruits(minNewRecruits);
        List<CommissionRecruitmentBonusRules> existingRules = commissionRecruitmentBonusRulesService.selectCommissionRecruitmentBonusRulesList(queryCondition);
        return !existingRules.isEmpty();
    }

    // ========== 数据导出 ==========

    /**
     * 导出分销员等级规则列表 h:commission_rules
     */
    @ApiOperation(value = "导出分销员等级规则列表 h:commission_rules", notes = "导出分销员等级规则列表 h:commission_rules")
    @PreAuthorize("@ss.hasPermi('h:commission_rules')")
    @Log(title = "分销员等级规则", businessType = BusinessType.EXPORT)
    @PostMapping("/level/export")
    public void exportLevelRules(HttpServletResponse response, CommissionLevelRules commissionLevelRules)
    {
        List<CommissionLevelRules> list = commissionLevelRulesService.selectCommissionLevelRulesList(commissionLevelRules);
        ExcelUtil<CommissionLevelRules> util = new ExcelUtil<CommissionLevelRules>(CommissionLevelRules.class);
        util.exportExcel(response, list, "分销员等级规则数据");
    }
} 