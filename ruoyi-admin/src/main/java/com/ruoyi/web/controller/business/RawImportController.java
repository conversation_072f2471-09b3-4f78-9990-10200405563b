package com.ruoyi.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.business.RawImport;
import com.ruoyi.system.service.business.IRawImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 原始Excel数据导入记录Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Api(tags = "原始Excel数据导入记录管理")
@RestController
@RequestMapping("/h/rawimport") // 统一使用 "h" 作为业务模块前缀
public class RawImportController extends BaseController
{
    @Autowired
    private IRawImportService rawImportService;

    @ApiOperation("查询原始Excel数据导入记录列表")
    @PreAuthorize("@ss.hasPermi('h:rawimport:list')")
    @GetMapping("/list")
    public AjaxResult list(RawImport rawImport)
    {
        startPage();
        List<RawImport> list = rawImportService.selectRawImportList(rawImport);
        return AjaxResult.success(getDataTable(list));
    }

    @ApiOperation("导出原始Excel数据导入记录列表")
    @PreAuthorize("@ss.hasPermi('h:rawimport:export')")
    @Log(title = "原始Excel数据导入记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RawImport rawImport)
    {
        List<RawImport> list = rawImportService.selectRawImportList(rawImport);
        ExcelUtil<RawImport> util = new ExcelUtil<RawImport>(RawImport.class);
        util.exportExcel(response, list, "原始导入数据");
    }

    @ApiOperation("获取原始Excel数据导入记录详细信息")
    @PreAuthorize("@ss.hasPermi('h:rawimport:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@ApiParam(value = "原始导入记录ID", required = true) @PathVariable("id") Long id)
    {
        return AjaxResult.success(rawImportService.selectRawImportById(id));
    }

    @ApiOperation("新增原始Excel数据导入记录 (通常不直接调用，通过导入接口)")
    @PreAuthorize("@ss.hasPermi('h:rawimport:add')")
    @Log(title = "原始Excel数据导入记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RawImport rawImport)
    {
        rawImport.setCreateBy(getUsername());
        return toAjax(rawImportService.insertRawImport(rawImport));
    }

    @ApiOperation("修改原始Excel数据导入记录")
    @PreAuthorize("@ss.hasPermi('h:rawimport:edit')")
    @Log(title = "原始Excel数据导入记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RawImport rawImport)
    {
        rawImport.setUpdateBy(getUsername());
        return toAjax(rawImportService.updateRawImport(rawImport));
    }

    @ApiOperation("删除原始Excel数据导入记录")
    @PreAuthorize("@ss.hasPermi('h:rawimport:remove')")
    @Log(title = "原始Excel数据导入记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam(value = "需要删除的原始导入记录ID数组", required = true) @PathVariable Long[] ids)
    {
        return toAjax(rawImportService.deleteRawImportByIds(ids));
    }

    @ApiOperation("导入原始Excel数据")
    @PreAuthorize("@ss.hasPermi('h:rawimport:import')")
    @Log(title = "原始Excel数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }
        try {
            String operName = SecurityUtils.getUsername();
            logger.info("用户 '{}' 开始执行Excel数据导入，文件名: {}", operName, file.getOriginalFilename());
            String message = rawImportService.importRawDataFromExcel(file, operName);
            logger.info("用户 '{}' 的Excel数据导入任务成功完成。消息: {}", operName, message);
            return AjaxResult.success(message);
        } catch (Exception e) {
            // 捕获更具体的异常，或者在日志中记录完整的堆栈信息
            logger.error("导入Excel数据时发生严重错误。文件名: {}, 错误: {}", file.getOriginalFilename(), e.getMessage(), e);
            // 返回给用户的错误信息应简洁明了，避免暴露内部实现细节
            return AjaxResult.error("导入Excel数据失败，请检查文件内容或联系管理员。详细错误: " + e.getMessage());
        }
    }
}
