package com.ruoyi.web.controller.business;

import java.util.List;
import java.util.Map;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.business.INewRecruitQualificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 后台-拉新资格判定管理控制器 h:new_recruit_qualification
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Api(tags = "后台-拉新资格判定管理 h:new_recruit_qualification")
@RestController
@RequestMapping("/business/new-recruit-qualification")
public class NewRecruitQualificationController extends BaseController
{
    @Autowired
    private INewRecruitQualificationService newRecruitQualificationService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");

    /**
     * 执行拉新资格判定 h:new_recruit_qualification
     */
    @ApiOperation(value = "执行拉新资格判定 h:new_recruit_qualification", notes = "根据指定月份执行拉新资格判定 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @Log(title = "拉新资格判定", businessType = BusinessType.INSERT)
    @PostMapping("/execute/{dataMonth}")
    public AjaxResult executeQualification(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-07") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = newRecruitQualificationService.executeNewRecruitQualification(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("执行拉新资格判定失败", e);
            return error("执行失败：" + e.getMessage());
        }
    }

    /**
     * 预览拉新资格判定结果 h:new_recruit_qualification
     */
    @ApiOperation(value = "预览拉新资格判定结果 h:new_recruit_qualification", notes = "预览指定月份的拉新资格判定结果，不保存到数据库 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @GetMapping("/preview/{dataMonth}")
    public AjaxResult previewQualification(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-07") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = newRecruitQualificationService.previewNewRecruitQualification(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("预览拉新资格判定失败", e);
            return error("预览失败：" + e.getMessage());
        }
    }

    /**
     * 检查单个用户的拉新资格 h:new_recruit_qualification
     */
    @ApiOperation(value = "检查单个用户的拉新资格 h:new_recruit_qualification", notes = "检查指定用户在指定月份的拉新资格 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @GetMapping("/check/{creatorId}/{dataMonth}")
    public AjaxResult checkSingleCreator(
            @ApiParam(value = "用户ID", required = true, example = "1001") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-07") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = newRecruitQualificationService.checkSingleCreatorQualification(creatorId, month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("检查用户拉新资格失败", e);
            return error("检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取拉新资格统计信息 h:new_recruit_qualification
     */
    @ApiOperation(value = "获取拉新资格统计信息 h:new_recruit_qualification", notes = "获取指定月份的拉新资格统计信息 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @GetMapping("/statistics/{dataMonth}")
    public AjaxResult getStatistics(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-07") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = newRecruitQualificationService.getQualificationStatistics(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取拉新资格统计信息失败", e);
            return error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取已合格拉新用户列表 h:new_recruit_qualification
     */
    @ApiOperation(value = "获取已合格拉新用户列表 h:new_recruit_qualification", notes = "获取已合格拉新用户列表 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @GetMapping("/qualified")
    public AjaxResult getQualifiedUsers(
            @ApiParam(value = "数据月份，格式：yyyy-MM，可选", example = "2025-07") 
            @RequestParam(value = "dataMonth", required = false) String dataMonth,
            @ApiParam(value = "招募人ID，可选", example = "1001") 
            @RequestParam(value = "recruiterId", required = false) Long recruiterId)
    {
        try {
            Date month = null;
            if (dataMonth != null && !dataMonth.trim().isEmpty()) {
                month = dateFormat.parse(dataMonth);
            }
            
            List<Map<String, Object>> result = newRecruitQualificationService.getQualifiedNewRecruits(month, recruiterId);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取已合格拉新用户列表失败", e);
            return error("获取列表失败：" + e.getMessage());
        }
    }

    /**
     * 重置指定月份的拉新资格判定结果 h:new_recruit_qualification
     */
    @ApiOperation(value = "重置拉新资格判定结果 h:new_recruit_qualification", notes = "重置指定月份的拉新资格判定结果 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @Log(title = "重置拉新资格判定", businessType = BusinessType.UPDATE)
    @PostMapping("/reset/{dataMonth}")
    public AjaxResult resetQualification(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-07") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = newRecruitQualificationService.resetQualificationForMonth(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("重置拉新资格判定失败", e);
            return error("重置失败：" + e.getMessage());
        }
    }

    /**
     * 获取拉新资格判定配置 h:new_recruit_qualification
     */
    @ApiOperation(value = "获取拉新资格判定配置 h:new_recruit_qualification", notes = "获取拉新资格判定的配置参数 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @GetMapping("/config")
    public AjaxResult getConfig()
    {
        try {
            Map<String, Object> result = newRecruitQualificationService.getQualificationConfig();
            return success(result);
        } catch (Exception e) {
            logger.error("获取拉新资格判定配置失败", e);
            return error("获取配置失败：" + e.getMessage());
        }
    }

    /**
     * 更新拉新资格判定配置 h:new_recruit_qualification
     */
    @ApiOperation(value = "更新拉新资格判定配置 h:new_recruit_qualification", notes = "更新拉新资格判定的配置参数 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @Log(title = "更新拉新资格判定配置", businessType = BusinessType.UPDATE)
    @PostMapping("/config")
    public AjaxResult updateConfig(
            @ApiParam(value = "账号创建天数阈值", example = "60")
            @RequestParam(value = "tenureDays", required = false) Integer tenureDays,
            @ApiParam(value = "最小有效直播天数", example = "24")
            @RequestParam(value = "minValidDays", required = false) Integer minValidDays)
    {
        try {
            Map<String, Object> config = new java.util.HashMap<>();
            if (tenureDays != null) {
                config.put("tenureDays", tenureDays);
            }
            if (minValidDays != null) {
                config.put("minValidDays", minValidDays);
            }

            Map<String, Object> result = newRecruitQualificationService.updateQualificationConfig(config);

            if ((Boolean) result.get("success")) {
                return success(result);
            } else {
                return error((String) result.get("message"));
            }
        } catch (Exception e) {
            logger.error("更新拉新资格判定配置失败", e);
            return error("更新配置失败：" + e.getMessage());
        }
    }

    /**
     * 测试接口：验证60天内有效直播天数统计逻辑 h:new_recruit_qualification
     */
    @ApiOperation(value = "测试60天内有效直播天数统计 h:new_recruit_qualification", notes = "用于验证跨月份的60天内有效直播天数统计逻辑 h:new_recruit_qualification")
    @PreAuthorize("@ss.hasPermi('h:new_recruit_qualification')")
    @GetMapping("/test/valid-days-calculation/{creatorId}/{dataMonth}")
    public AjaxResult testValidDaysCalculation(
            @ApiParam(value = "用户ID", required = true, example = "1001")
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-07")
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            Date month = dateFormat.parse(dataMonth);

            // 调用内部方法检查单个用户资格，获取详细的计算过程
            Map<String, Object> result = newRecruitQualificationService.checkSingleCreatorQualification(creatorId, month);

            // 添加测试说明
            Map<String, Object> testResult = new java.util.HashMap<>();
            testResult.put("testDescription", "验证60天内有效直播天数统计逻辑");
            testResult.put("creatorId", creatorId);
            testResult.put("dataMonth", dataMonth);
            testResult.put("qualificationResult", result);

            return success(testResult);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("测试60天内有效直播天数统计失败", e);
            return error("测试失败：" + e.getMessage());
        }
    }
}
