package com.ruoyi.web.controller.business;

import java.util.List;
import java.util.stream.Collectors;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.vo.UpdateVirtualCreatorRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;

/**
 * 后台-虚拟上级管理控制器 h:historical
 *
 * <AUTHOR>
 */
@Api(tags = "虚拟上级管理")
@RestController
@RequestMapping("/h/virtual")
public class VirtualCreatorController extends BaseController
{
    @Autowired
    private ICreatorService creatorService;

    /**
     * 查看所有虚拟创建者 h:historical
     */
    @ApiOperation(value = "获取虚拟创建者列表 h:historical", notes = "查看所有虚拟创建者 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/creators")
    public TableDataInfo list()
    {
        startPage();
        
        // 查询所有创建者
        Creator queryParam = new Creator();
        List<Creator> allCreators = creatorService.selectCreatorList(queryParam);
        
        // 过滤出虚拟创建者（ID以888开头）
        List<Creator> virtualCreators = allCreators.stream()
            .filter(creator -> creator.getId() != null && creator.getId().toString().startsWith("888"))
            .collect(Collectors.toList());
        
        return getDataTable(virtualCreators);
    }

    /**
     * 获取虚拟创建者详细信息 h:historical
     */
    @ApiOperation(value = "获取虚拟创建者详情 h:historical", notes = "根据ID获取虚拟创建者详细信息 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/creators/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        // 验证是否为虚拟创建者
        if (id == null || !id.toString().startsWith("888"))
        {
            return error("无效的虚拟创建者ID");
        }
        
        Creator creator = creatorService.selectCreatorById(id);
        if (creator == null)
        {
            return error("虚拟创建者不存在");
        }
        
        return success(creator);
    }

    /**
     * 修改虚拟创建者信息 h:historical
     */
    @ApiOperation(value = "修改虚拟创建者 h:historical", notes = "修改虚拟创建者信息 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "虚拟创建者", businessType = BusinessType.UPDATE)
    @PutMapping("/creators/{id}")
    public AjaxResult edit(@PathVariable("id") Long id, @Valid @RequestBody UpdateVirtualCreatorRequest request)
    {
        try
        {
            // 验证是否为虚拟创建者
            if (id == null || !id.toString().startsWith("888"))
            {
                return error("无效的虚拟创建者ID");
            }
            
            Creator existingCreator = creatorService.selectCreatorById(id);
            if (existingCreator == null)
            {
                return error("虚拟创建者不存在");
            }
            
            // 只允许修改昵称和备注
            existingCreator.setNickname(request.getNickname());
            existingCreator.setRemark(request.getRemark());
            existingCreator.setUpdateBy(getUsername());
            
            int result = creatorService.updateCreator(existingCreator);
            return toAjax(result);
        }
        catch (Exception e)
        {
            logger.error("修改虚拟创建者失败", e);
            return error("修改失败: " + e.getMessage());
        }
    }

    /**
     * 检查虚拟创建者使用情况 h:historical
     */
    @ApiOperation(value = "检查虚拟创建者使用情况 h:historical", notes = "检查虚拟创建者是否被使用 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/creators/{id}/usage")
    public AjaxResult checkUsage(@PathVariable("id") Long id)
    {
        try
        {
            // 验证是否为虚拟创建者
            if (id == null || !id.toString().startsWith("888"))
            {
                return error("无效的虚拟创建者ID");
            }
            
            // 查询是否有下级
            Creator queryParam = new Creator();
            queryParam.setParentId(id);
            List<Creator> children = creatorService.selectCreatorList(queryParam);
            
            // 构建使用情况信息
            java.util.Map<String, Object> usage = new java.util.HashMap<>();
            usage.put("hasChildren", !children.isEmpty());
            usage.put("childrenCount", children.size());
            usage.put("children", children);
            
            return success(usage);
        }
        catch (Exception e)
        {
            logger.error("检查虚拟创建者使用情况失败", e);
            return error("检查失败: " + e.getMessage());
        }
    }
} 