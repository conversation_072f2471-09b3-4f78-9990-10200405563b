package com.ruoyi.web.controller.business;

import java.util.List;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.CreatorTreeNode;
import com.ruoyi.system.domain.business.CreatorMonthlyTreeNode;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 后台-主播信息管理控制器 h:creator
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Api(tags = "后台-主播信息管理 h:creator")
@RestController
@RequestMapping("/business/creator")
public class CreatorController extends BaseController
{
    @Autowired
    private ICreatorService creatorService;

    /**
     * 查询主播信息列表 h:creator
     */
    @ApiOperation(value = "查询主播信息列表 h:creator", notes = "获取主播信息分页列表 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping("/list")
    public TableDataInfo list(Creator creator)
    {
        startPage();
        List<Creator> list = creatorService.selectCreatorList(creator);
        return getDataTable(list);
    }

    /**
     * 查询主播树状关系图 h:creator
     */
    @ApiOperation(value = "查询主播树状关系图 h:creator", notes = "获取主播树状关系图，根级是没有父节点的用户 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping("/tree")
    public AjaxResult tree()
    {
        List<CreatorTreeNode> tree = creatorService.selectCreatorTree();
        return AjaxResult.success("查询成功", tree);
    }

    /**
     * 查询主播月度数据树状关系图 h:creator
     */
    @ApiOperation(value = "查询主播月度数据树状关系图 h:creator", notes = "获取包含月度业绩数据的主播树状关系图 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping("/monthlyTree")
    public AjaxResult monthlyTree(
            @RequestParam("dataMonth") @DateTimeFormat(pattern = "yyyy-MM") Date dataMonth,
            @RequestParam(value = "creatorId", required = false) Long creatorId,
            @RequestParam(value = "nickname", required = false) String nickname)
    {
        List<CreatorMonthlyTreeNode> tree = creatorService.selectCreatorMonthlyTree(dataMonth, creatorId, nickname);
        return AjaxResult.success("查询成功", tree);
    }

    /**
     * 导出主播信息列表 h:creator
     */
    @ApiOperation(value = "导出主播信息列表 h:creator", notes = "导出主播信息列表 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @Log(title = "主播信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Creator creator)
    {
        List<Creator> list = creatorService.selectCreatorList(creator);
        ExcelUtil<Creator> util = new ExcelUtil<Creator>(Creator.class);
        util.exportExcel(response, list, "主播信息数据");
    }

    /**
     * 获取主播信息详细信息 h:creator
     */
    @ApiOperation(value = "获取主播信息详细信息 h:creator", notes = "根据ID获取主播信息详细信息 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(creatorService.selectCreatorById(id));
    }

    /**
     * 新增主播信息 h:creator
     */
    @ApiOperation(value = "新增主播信息 h:creator", notes = "新增一条主播信息 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @Log(title = "主播信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Creator creator)
    {
        creator.setCreateBy(getUsername());
        return toAjax(creatorService.insertCreator(creator));
    }

    /**
     * 修改主播信息 h:creator
     */
    @ApiOperation(value = "修改主播信息 h:creator", notes = "根据ID修改主播信息 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @Log(title = "主播信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Creator creator)
    {
        creator.setUpdateBy(getUsername());
        return toAjax(creatorService.updateCreator(creator));
    }

    /**
     * 删除主播信息 h:creator
     */
    @ApiOperation(value = "删除主播信息 h:creator", notes = "根据ID删除主播信息 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @Log(title = "主播信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(creatorService.deleteCreatorByIds(ids));
    }
}
