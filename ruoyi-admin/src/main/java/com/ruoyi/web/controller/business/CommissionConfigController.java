package com.ruoyi.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.commission.CommissionSettings;
import com.ruoyi.system.domain.commission.CommissionMonthlySettings;
import com.ruoyi.system.service.commission.ICommissionSettingsService;
import com.ruoyi.system.service.commission.ICommissionMonthlySettingsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 后台-佣金配置管理控制器 h:commission_config
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "后台-佣金配置管理 h:commission_config")
@RestController
@RequestMapping("/business/commission/config")
public class CommissionConfigController extends BaseController
{
    @Autowired
    private ICommissionSettingsService commissionSettingsService;

    @Autowired
    private ICommissionMonthlySettingsService commissionMonthlySettingsService;

    // ========== 全局佣金配置管理 ==========

    /**
     * 查询全局佣金配置列表 h:commission_config
     */
    @ApiOperation(value = "查询全局佣金配置列表 h:commission_config", notes = "获取全局佣金配置分页列表 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @GetMapping("/settings/list")
    public TableDataInfo settingsList(CommissionSettings commissionSettings)
    {
        startPage();
        List<CommissionSettings> list = commissionSettingsService.selectCommissionSettingsList(commissionSettings);
        return getDataTable(list);
    }

    /**
     * 获取全局佣金配置详细信息 h:commission_config
     */
    @ApiOperation(value = "获取全局佣金配置详细信息 h:commission_config", notes = "根据配置键获取全局佣金配置详细信息 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @GetMapping(value = "/settings/{settingKey}")
    public AjaxResult getSettingsInfo(@PathVariable("settingKey") String settingKey)
    {
        return success(commissionSettingsService.selectCommissionSettingsByKey(settingKey));
    }

    /**
     * 新增全局佣金配置 h:commission_config
     */
    @ApiOperation(value = "新增全局佣金配置 h:commission_config", notes = "新增一条全局佣金配置 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "全局佣金配置", businessType = BusinessType.INSERT)
    @PostMapping("/settings")
    public AjaxResult addSettings(@RequestBody CommissionSettings commissionSettings)
    {
        return toAjax(commissionSettingsService.insertCommissionSettings(commissionSettings));
    }

    /**
     * 修改全局佣金配置 h:commission_config
     */
    @ApiOperation(value = "修改全局佣金配置 h:commission_config", notes = "根据配置键修改全局佣金配置 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "全局佣金配置", businessType = BusinessType.UPDATE)
    @PutMapping("/settings")
    public AjaxResult editSettings(@RequestBody CommissionSettings commissionSettings)
    {
        return toAjax(commissionSettingsService.updateCommissionSettings(commissionSettings));
    }

    /**
     * 删除全局佣金配置 h:commission_config
     */
    @ApiOperation(value = "删除全局佣金配置 h:commission_config", notes = "根据配置键删除全局佣金配置 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "全局佣金配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/settings/{settingKeys}")
    public AjaxResult removeSettings(@PathVariable String[] settingKeys)
    {
        return toAjax(commissionSettingsService.deleteCommissionSettingsByKeys(settingKeys));
    }

    // ========== 月度佣金配置管理 ==========

    /**
     * 查询月度佣金配置列表 h:commission_config
     */
    @ApiOperation(value = "查询月度佣金配置列表 h:commission_config", notes = "获取月度佣金配置分页列表 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @GetMapping("/monthly/list")
    public TableDataInfo monthlyList(CommissionMonthlySettings commissionMonthlySettings)
    {
        startPage();
        List<CommissionMonthlySettings> list = commissionMonthlySettingsService.selectCommissionMonthlySettingsList(commissionMonthlySettings);
        return getDataTable(list);
    }

    /**
     * 新增月度佣金配置 h:commission_config
     */
    @ApiOperation(value = "新增月度佣金配置 h:commission_config", notes = "新增一条月度佣金配置 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "月度佣金配置", businessType = BusinessType.INSERT)
    @PostMapping("/monthly")
    public AjaxResult addMonthly(@RequestBody CommissionMonthlySettings commissionMonthlySettings)
    {
        commissionMonthlySettings.setCreateBy(getUsername());
        return toAjax(commissionMonthlySettingsService.insertCommissionMonthlySettings(commissionMonthlySettings));
    }

    /**
     * 修改月度佣金配置 h:commission_config
     */
    @ApiOperation(value = "修改月度佣金配置 h:commission_config", notes = "修改月度佣金配置 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "月度佣金配置", businessType = BusinessType.UPDATE)
    @PutMapping("/monthly")
    public AjaxResult editMonthly(@RequestBody CommissionMonthlySettings commissionMonthlySettings)
    {
        commissionMonthlySettings.setUpdateBy(getUsername());
        return toAjax(commissionMonthlySettingsService.updateCommissionMonthlySettings(commissionMonthlySettings));
    }

    // ========== 配置数据导出 ==========

    /**
     * 导出全局佣金配置列表 h:commission_config
     */
    @ApiOperation(value = "导出全局佣金配置列表 h:commission_config", notes = "导出全局佣金配置列表 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "全局佣金配置", businessType = BusinessType.EXPORT)
    @PostMapping("/settings/export")
    public void exportSettings(HttpServletResponse response, CommissionSettings commissionSettings)
    {
        List<CommissionSettings> list = commissionSettingsService.selectCommissionSettingsList(commissionSettings);
        ExcelUtil<CommissionSettings> util = new ExcelUtil<CommissionSettings>(CommissionSettings.class);
        util.exportExcel(response, list, "全局佣金配置数据");
    }

    /**
     * 导出月度佣金配置列表 h:commission_config
     */
    @ApiOperation(value = "导出月度佣金配置列表 h:commission_config", notes = "导出月度佣金配置列表 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "月度佣金配置", businessType = BusinessType.EXPORT)
    @PostMapping("/monthly/export")
    public void exportMonthly(HttpServletResponse response, CommissionMonthlySettings commissionMonthlySettings)
    {
        List<CommissionMonthlySettings> list = commissionMonthlySettingsService.selectCommissionMonthlySettingsList(commissionMonthlySettings);
        ExcelUtil<CommissionMonthlySettings> util = new ExcelUtil<CommissionMonthlySettings>(CommissionMonthlySettings.class);
        util.exportExcel(response, list, "月度佣金配置数据");
    }

    // ========== 配置初始化和批量操作 ==========

    /**
     * 初始化系统配置 h:commission_config
     */
    @ApiOperation(value = "初始化系统配置 h:commission_config", notes = "初始化默认的佣金配置项 h:commission_config")
    @PreAuthorize("@ss.hasPermi('h:commission_config')")
    @Log(title = "佣金配置初始化", businessType = BusinessType.INSERT)
    @PostMapping("/init")
    public AjaxResult initConfig()
    {
        try {
            // 初始化基础配置
            CommissionSettings baseDiamondThreshold = new CommissionSettings();
            baseDiamondThreshold.setSettingKey("base_diamond_threshold");
            baseDiamondThreshold.setSettingValue("5000");
            baseDiamondThreshold.setDescription("基础分成钻石门槛");
            commissionSettingsService.insertCommissionSettings(baseDiamondThreshold);

            CommissionSettings thresholdIncreaseRate = new CommissionSettings();
            thresholdIncreaseRate.setSettingKey("threshold_increase_rate");
            thresholdIncreaseRate.setSettingValue("0.012");
            thresholdIncreaseRate.setDescription("动态钻石门槛月度上浮比例 (1.2%)");
            commissionSettingsService.insertCommissionSettings(thresholdIncreaseRate);

            return success("配置初始化成功");
        } catch (Exception e) {
            logger.error("配置初始化失败", e);
            return error("配置初始化失败：" + e.getMessage());
        }
    }
} 