package com.ruoyi.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.commission.CommissionSettings;
import com.ruoyi.system.service.commission.ICommissionSettingsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 后台-全局佣金配置管理控制器 h:commission_settings
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
@Api(tags = "后台-全局佣金配置管理 h:commission_settings")
@RestController
@RequestMapping("/business/commission/settings")
public class CommissionSettingsController extends BaseController
{
    @Autowired
    private ICommissionSettingsService commissionSettingsService;

    /**
     * 查询全局佣金配置列表 h:commission_settings
     */
    @ApiOperation(value = "查询全局佣金配置列表 h:commission_settings", notes = "获取全局佣金配置分页列表 h:commission_settings")
    @PreAuthorize("@ss.hasPermi('h:commission_settings')")
    @GetMapping("/list")
    public TableDataInfo list(CommissionSettings commissionSettings)
    {
        startPage();
        List<CommissionSettings> list = commissionSettingsService.selectCommissionSettingsList(commissionSettings);
        return getDataTable(list);
    }

    /**
     * 导出全局佣金配置列表 h:commission_settings
     */
    @ApiOperation(value = "导出全局佣金配置列表 h:commission_settings", notes = "导出全局佣金配置列表 h:commission_settings")
    @PreAuthorize("@ss.hasPermi('h:commission_settings')")
    @Log(title = "全局佣金配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommissionSettings commissionSettings)
    {
        List<CommissionSettings> list = commissionSettingsService.selectCommissionSettingsList(commissionSettings);
        ExcelUtil<CommissionSettings> util = new ExcelUtil<CommissionSettings>(CommissionSettings.class);
        util.exportExcel(response, list, "全局佣金配置数据");
    }

    /**
     * 获取全局佣金配置详细信息 h:commission_settings
     */
    @ApiOperation(value = "获取全局佣金配置详细信息 h:commission_settings", notes = "根据配置键获取全局佣金配置详细信息 h:commission_settings")
    @PreAuthorize("@ss.hasPermi('h:commission_settings')")
    @GetMapping(value = "/{settingKey}")
    public AjaxResult getInfo(@PathVariable("settingKey") String settingKey)
    {
        return success(commissionSettingsService.selectCommissionSettingsByKey(settingKey));
    }

    /**
     * 新增全局佣金配置 h:commission_settings
     */
    @ApiOperation(value = "新增全局佣金配置 h:commission_settings", notes = "新增一条全局佣金配置 h:commission_settings")
    @PreAuthorize("@ss.hasPermi('h:commission_settings')")
    @Log(title = "全局佣金配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommissionSettings commissionSettings)
    {
        return toAjax(commissionSettingsService.insertCommissionSettings(commissionSettings));
    }

    /**
     * 修改全局佣金配置 h:commission_settings
     */
    @ApiOperation(value = "修改全局佣金配置 h:commission_settings", notes = "根据配置键修改全局佣金配置 h:commission_settings")
    @PreAuthorize("@ss.hasPermi('h:commission_settings')")
    @Log(title = "全局佣金配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommissionSettings commissionSettings)
    {
        return toAjax(commissionSettingsService.updateCommissionSettings(commissionSettings));
    }

    /**
     * 删除全局佣金配置 h:commission_settings
     */
    @ApiOperation(value = "删除全局佣金配置 h:commission_settings", notes = "根据配置键删除全局佣金配置 h:commission_settings")
    @PreAuthorize("@ss.hasPermi('h:commission_settings')")
    @Log(title = "全局佣金配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{settingKeys}")
    public AjaxResult remove(@PathVariable String[] settingKeys)
    {
        return toAjax(commissionSettingsService.deleteCommissionSettingsByKeys(settingKeys));
    }
} 