package com.ruoyi.web.controller.business;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.business.ICreatorRelationshipService;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.CreatorTreeNode;
import com.ruoyi.system.domain.vo.AssignCreatorParentRequest;
import com.ruoyi.common.exception.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;

/**
 * 后台-创建者关系管理控制器 h:historical
 *
 * <AUTHOR>
 */
@Api(tags = "创建者关系管理")
@RestController
@RequestMapping("/h/creator")
public class CreatorRelationController extends BaseController
{
    @Autowired
    private ICreatorService creatorService;

    @Autowired
    private ICreatorRelationshipService creatorRelationshipService;

    /**
     * 指定创建者的上级 h:historical
     */
    @ApiOperation(value = "指定创建者上级 h:historical", notes = "手动指定creator的parent_id h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @Log(title = "指定创建者上级", businessType = BusinessType.UPDATE)
    @PostMapping("/assign-parent")
    public AjaxResult assignParent(@Valid @RequestBody AssignCreatorParentRequest request)
    {
        try
        {
            Long creatorId = request.getCreatorId();
            Long parentId = request.getParentId();
            
            // 验证创建者存在
            Creator creator = creatorService.selectCreatorById(creatorId);
            if (creator == null)
            {
                return error("指定的创建者不存在");
            }
            
            // 验证上级存在（如果指定了上级）
            if (parentId != null)
            {
                Creator parent = creatorService.selectCreatorById(parentId);
                if (parent == null)
                {
                    return error("指定的上级创建者不存在");
                }
                
                // 检查循环关系：不能将自己设为上级
                if (creatorId.equals(parentId))
                {
                    return error("不能将自己设为上级");
                }
                
                // 检查循环关系：检查是否会形成循环
                if (wouldCreateCycle(creatorId, parentId))
                {
                    return error("指定的上级关系会形成循环引用");
                }
            }
            
            // 更新parent_id
            creator.setParentId(parentId);
            creator.setUpdateBy(getUsername());
            int result = creatorService.updateCreator(creator);
            
            if (result > 0)
            {
                // 重建关系表
                creatorRelationshipService.rebuildCreatorRelationships();
                return success("指定成功");
            }
            else
            {
                return error("指定失败");
            }
        }
        catch (Exception e)
        {
            logger.error("指定创建者上级失败", e);
            return error("指定失败: " + e.getMessage());
        }
    }

    /**
     * 查看关系树 h:historical
     */
    @ApiOperation(value = "查看关系树 h:historical", notes = "查看完整的创建者关系树 h:historical")
    @PreAuthorize("@ss.hasPermi('h:historical')")
    @GetMapping("/relationship-tree")
    public AjaxResult getRelationshipTree(@RequestParam(value = "rootCreatorId", required = false) Long rootCreatorId)
    {
        try
        {
            List<CreatorTreeNode> treeNodes;
            
            if (rootCreatorId != null)
            {
                // 查询指定根节点的树
                Creator rootCreator = creatorService.selectCreatorById(rootCreatorId);
                if (rootCreator == null)
                {
                    return error("指定的根创建者不存在");
                }
                // TODO: 实现单个根节点树查询
                treeNodes = creatorService.selectCreatorTree();
            }
            else
            {
                // 查询完整树
                treeNodes = creatorService.selectCreatorTree();
            }
            
            return success(treeNodes);
        }
        catch (Exception e)
        {
            logger.error("查询关系树失败", e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否会形成循环关系
     */
    private boolean wouldCreateCycle(Long creatorId, Long parentId)
    {
        try
        {
            // 检查parentId是否是creatorId的后代
            // 如果是，则会形成循环
            return isDescendant(parentId, creatorId);
        }
        catch (Exception e)
        {
            logger.warn("检查循环关系时发生异常", e);
            return false;
        }
    }

    /**
     * 检查一个创建者是否是另一个创建者的后代
     */
    private boolean isDescendant(Long potentialDescendant, Long ancestorId)
    {
        if (potentialDescendant == null || ancestorId == null)
        {
            return false;
        }
        
        if (potentialDescendant.equals(ancestorId))
        {
            return true;
        }
        
        // 查找potentialDescendant的所有祖先
        try
        {
            Creator current = creatorService.selectCreatorById(potentialDescendant);
            int maxDepth = 10; // 防止无限循环
            int currentDepth = 0;
            
            while (current != null && current.getParentId() != null && currentDepth < maxDepth)
            {
                if (current.getParentId().equals(ancestorId))
                {
                    return true;
                }
                current = creatorService.selectCreatorById(current.getParentId());
                currentDepth++;
            }
        }
        catch (Exception e)
        {
            logger.warn("检查后代关系时发生异常", e);
        }
        
        return false;
    }
} 