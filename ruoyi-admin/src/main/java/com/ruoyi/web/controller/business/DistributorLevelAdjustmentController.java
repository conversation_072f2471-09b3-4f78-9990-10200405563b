package com.ruoyi.web.controller.business;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.commission.IDistributorLevelAdjustmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 后台-分销员等级调整控制器 h:level_adjustment
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Api(tags = "后台-分销员等级调整 h:level_adjustment")
@RestController
@RequestMapping("/business/level/adjustment")
public class DistributorLevelAdjustmentController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(DistributorLevelAdjustmentController.class);
    
    @Autowired
    private IDistributorLevelAdjustmentService distributorLevelAdjustmentService;

    /**
     * 执行月度等级调整 h:level_adjustment
     */
    @ApiOperation(value = "执行月度等级调整 h:level_adjustment", notes = "根据指定月份执行分销员等级调整 h:level_adjustment")
    @PreAuthorize("@ss.hasPermi('h:level_adjustment')")
    @Log(title = "月度等级调整", businessType = BusinessType.UPDATE)
    @PostMapping("/execute/{dataMonth}")
    public AjaxResult executeMonthlyLevelAdjustment(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = distributorLevelAdjustmentService.executeMonthlyLevelAdjustment(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("执行月度等级调整失败：{}", e.getMessage(), e);
            return error("执行月度等级调整失败：" + e.getMessage());
        }
    }

    /**
     * 获取月度等级调整报告 h:level_adjustment
     */
    @ApiOperation(value = "获取月度等级调整报告 h:level_adjustment", notes = "获取指定月份的等级调整统计报告 h:level_adjustment")
    @PreAuthorize("@ss.hasPermi('h:level_adjustment')")
    @GetMapping("/report/{dataMonth}")
    public AjaxResult getMonthlyAdjustmentReport(
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = distributorLevelAdjustmentService.getMonthlyAdjustmentReport(month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取月度等级调整报告失败：{}", e.getMessage(), e);
            return error("获取月度等级调整报告失败：" + e.getMessage());
        }
    }

    /**
     * 验证分销员等级调整计算 h:level_adjustment
     */
    @ApiOperation(value = "验证分销员等级调整计算 h:level_adjustment", notes = "验证指定分销员的等级调整计算过程 h:level_adjustment")
    @PreAuthorize("@ss.hasPermi('h:level_adjustment')")
    @GetMapping("/validate/{creatorId}/{dataMonth}")
    public AjaxResult validateLevelAdjustmentCalculation(
            @ApiParam(value = "分销员ID", required = true, example = "1001") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = distributorLevelAdjustmentService.validateLevelAdjustmentCalculation(creatorId, month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("验证等级调整计算失败：{}", e.getMessage(), e);
            return error("验证等级调整计算失败：" + e.getMessage());
        }
    }

    /**
     * 检查分销员保护期状态 h:level_adjustment
     */
    @ApiOperation(value = "检查分销员保护期状态 h:level_adjustment", notes = "检查指定分销员的保护期状态 h:level_adjustment")
    @PreAuthorize("@ss.hasPermi('h:level_adjustment')")
    @GetMapping("/protection/{creatorId}/{dataMonth}")
    public AjaxResult checkProtectionPeriod(
            @ApiParam(value = "分销员ID", required = true, example = "1001") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = distributorLevelAdjustmentService.checkProtectionPeriod(creatorId, month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("检查保护期状态失败：{}", e.getMessage(), e);
            return error("检查保护期状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取等级调整配置参数 h:level_adjustment
     */
    @ApiOperation(value = "获取等级调整配置参数 h:level_adjustment", notes = "获取等级调整相关的配置参数 h:level_adjustment")
    @PreAuthorize("@ss.hasPermi('h:level_adjustment')")
    @GetMapping("/config")
    public AjaxResult getLevelAdjustmentConfig()
    {
        try {
            Map<String, Object> result = distributorLevelAdjustmentService.getLevelAdjustmentConfig();
            return success(result);
        } catch (Exception e) {
            logger.error("获取等级调整配置参数失败：{}", e.getMessage(), e);
            return error("获取等级调整配置参数失败：" + e.getMessage());
        }
    }

    /**
     * 获取分销员团队业绩数据 h:level_adjustment
     */
    @ApiOperation(value = "获取分销员团队业绩数据 h:level_adjustment", notes = "获取指定分销员的团队业绩数据 h:level_adjustment")
    @PreAuthorize("@ss.hasPermi('h:level_adjustment')")
    @GetMapping("/performance/{creatorId}/{dataMonth}")
    public AjaxResult getTeamPerformanceData(
            @ApiParam(value = "分销员ID", required = true, example = "1001") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = distributorLevelAdjustmentService.getTeamPerformanceData(creatorId, month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取团队业绩数据失败：{}", e.getMessage(), e);
            return error("获取团队业绩数据失败：" + e.getMessage());
        }
    }

    /**
     * 计算分销员月度增长率 h:level_adjustment
     */
    @ApiOperation(value = "计算分销员月度增长率 h:level_adjustment", notes = "计算指定分销员的月度环比增长率 h:level_adjustment")
    @PreAuthorize("@ss.hasPermi('h:level_adjustment')")
    @GetMapping("/growth-rate/{creatorId}/{dataMonth}")
    public AjaxResult calculateMonthlyGrowthRates(
            @ApiParam(value = "分销员ID", required = true, example = "1001") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            Date month = dateFormat.parse(dataMonth);
            Map<String, Object> result = distributorLevelAdjustmentService.calculateMonthlyGrowthRates(creatorId, month);
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("计算月度增长率失败：{}", e.getMessage(), e);
            return error("计算月度增长率失败：" + e.getMessage());
        }
    }
}
