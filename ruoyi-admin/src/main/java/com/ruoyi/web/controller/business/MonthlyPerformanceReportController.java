package com.ruoyi.web.controller.business;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.dto.MonthlyPerformanceReportResponse;
import com.ruoyi.system.service.business.IMonthlyPerformanceReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.regex.Pattern;

/**
 * 后台-分销员业绩分析报告控制器 h:reports
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "后台-分销员业绩分析报告 h:reports")
@RestController
@RequestMapping("/api/reports")
public class MonthlyPerformanceReportController extends BaseController
{
    @Autowired
    private IMonthlyPerformanceReportService monthlyPerformanceReportService;

    // 月份格式验证正则表达式
    private static final Pattern MONTH_PATTERN = Pattern.compile("^\\d{4}-\\d{2}$");

    /**
     * 获取分销员月度业绩分析报告 h:reports
     */
    @ApiOperation(value = "获取分销员月度业绩分析报告 h:reports", 
                  notes = "获取指定分销员在指定月份的完整业绩分析报告，包含过去6个月的趋势数据和洞察 h:reports")
    @PreAuthorize("@ss.hasPermi('h:reports')")
    @GetMapping("/monthly-performance/{creatorId}/{month}")
    public AjaxResult getMonthlyPerformanceReport(
            @ApiParam(value = "分销员ID", required = true, example = "123456") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "基准月份，格式为 YYYY-MM", required = true, example = "2024-12") 
            @PathVariable("month") String month)
    {
        try {
            // 参数验证
            if (creatorId == null || creatorId <= 0) {
                return error("分销员ID不能为空且必须大于0");
            }
            
            if (month == null || month.trim().isEmpty()) {
                return error("月份参数不能为空");
            }
            
            // 验证月份格式
            if (!MONTH_PATTERN.matcher(month.trim()).matches()) {
                return error("月份格式错误，请使用 YYYY-MM 格式，例如：2024-12");
            }
            
            // 验证月份范围
            String[] parts = month.trim().split("-");
            int year = Integer.parseInt(parts[0]);
            int monthNum = Integer.parseInt(parts[1]);
            
            if (year < 2020 || year > 2030) {
                return error("年份必须在2020-2030范围内");
            }
            
            if (monthNum < 1 || monthNum > 12) {
                return error("月份必须在01-12范围内");
            }
            
            // 调用服务获取报告数据
            MonthlyPerformanceReportResponse reportData = monthlyPerformanceReportService.getMonthlyPerformanceReport(creatorId, month.trim());
            
            return success(reportData);
            
        } catch (NumberFormatException e) {
            return error("月份格式错误，请使用 YYYY-MM 格式，例如：2024-12");
        } catch (Exception e) {
            logger.error("获取分销员业绩分析报告失败：creatorId={}, month={}, error={}", creatorId, month, e.getMessage(), e);
            return error("获取业绩分析报告失败：" + e.getMessage());
        }
    }
}
