package com.ruoyi.web.controller.business;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.CreatorMonthlyTreeNode;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.system.service.commission.ICommissionPayoutsService;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.vo.AnchorPerformanceVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 后台-主播视角控制器 h:anchor
 *
 * <AUTHOR>
 */
@Api(tags = "后台-主播视角管理 h:anchor")
@RestController
@RequestMapping("/business/anchor")
public class AnchorController extends BaseController
{
    @Autowired
    private ICreatorService creatorService;
    
    @Autowired
    private ICommissionPayoutsService commissionPayoutsService;
    
    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");

    /**
     * 查询主播月度数据树状关系图 h:anchor
     */
    @ApiOperation(value = "查询主播月度数据树状关系图 h:anchor", notes = "获取包含月度业绩数据的主播树状关系图 h:anchor")
//    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping("/monthlyTree")
    public AjaxResult monthlyTree(
            @RequestParam("dataMonth") @DateTimeFormat(pattern = "yyyy-MM") Date dataMonth,
            @RequestParam(value = "nickname", required = false) String nickname)
    {
        try {
            // 从登录用户信息中获取CreatorID
            LoginUser loginUser = getLoginUser();
            String username = loginUser.getUser().getUserName();
            logger.info("loginUser: {}", loginUser.getUser().getUserId());
            logger.info("username: {}", username);

            // 将username直接转换为creatorId
            Long creatorId;
            try {
                creatorId = Long.parseLong(username);
            } catch (NumberFormatException e) {
                return error("用户名格式错误，无法转换为主播ID");
            }
            
            List<CreatorMonthlyTreeNode> tree = creatorService.selectCreatorMonthlyTree(dataMonth, creatorId, nickname);
            return AjaxResult.success("查询成功", tree);
        } catch (Exception e) {
            logger.error("查询主播月度数据树状关系图失败：{}", e.getMessage(), e);
            return error("查询主播月度数据树状关系图失败：" + e.getMessage());
        }
    }

    /**
     * 获取主播信息详细信息 h:anchor
     */
    @ApiOperation(value = "获取主播信息详细信息 h:anchor", notes = "获取当前登录主播的详细信息 h:anchor")
//    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping("/info")
    public AjaxResult getInfo()
    {
        try {
            // 从登录用户信息中获取CreatorID
            LoginUser loginUser = getLoginUser();
            String username = loginUser.getUser().getUserName();
            
            // 将username直接转换为creatorId
            Long creatorId;
            try {
                creatorId = Long.parseLong(username);
            } catch (NumberFormatException e) {
                return error("用户名格式错误，无法转换为主播ID");
            }
            
            // 根据creatorId获取主播信息
            Creator currentCreator = creatorService.selectCreatorById(creatorId);
            if (currentCreator == null) {
                return error("未找到当前登录用户对应的主播信息");
            }
            
            return success(currentCreator);
        } catch (Exception e) {
            logger.error("获取主播信息详细信息失败：{}", e.getMessage(), e);
            return error("获取主播信息详细信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取单个分销员详细收入信息 h:anchor
     */
    @ApiOperation(value = "获取分销员详细收入信息 h:anchor", notes = "获取指定分销员的详细收入信息，包含直播时长、有效天数和环比数据 h:anchor")
//    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping("/distributor/{dataMonth}")
    public AjaxResult getDistributorDetail(
        
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
    {
        try {
            // 从登录用户信息中获取CreatorID
            LoginUser loginUser = getLoginUser();
            String username = loginUser.getUser().getUserName();
            
            // 将username直接转换为creatorId
            Long creatorId;
            try {
                creatorId = Long.parseLong(username);
            } catch (NumberFormatException e) {
                return error("用户名格式错误，无法转换为主播ID");
            }
            
            // 解析当前月份
            Date currentMonth = dateFormat.parse(dataMonth);
            
            // 计算上个月日期
            Calendar cal = Calendar.getInstance();
            cal.setTime(currentMonth);
            cal.add(Calendar.MONTH, -1);
            Date lastMonth = cal.getTime();
            
            // 获取分销员收入数据
            CommissionPayouts payout = commissionPayoutsService.selectCommissionPayoutsByCreatorAndMonth(creatorId, currentMonth);
            if (payout == null) {
                return error("未找到指定分销员的收入数据");
            }
            
            // 获取当月和上月的月度业绩数据
            MonthlyPerformance currentMonthPerformance = monthlyPerformanceService.selectMonthlyPerformanceByCreatorAndMonth(creatorId, currentMonth);
            MonthlyPerformance lastMonthPerformance = monthlyPerformanceService.selectMonthlyPerformanceByCreatorAndMonth(creatorId, lastMonth);
            
            // 创建返回对象
            AnchorPerformanceVO result = new AnchorPerformanceVO();
            result.setCommissionPayouts(payout);
            result.setCurrentMonthPerformance(currentMonthPerformance);
            result.setLastMonthPerformance(lastMonthPerformance);
            
            // 计算直播时长环比数据
            AnchorPerformanceVO.LiveDurationComparison comparison = calculateLiveDurationComparison(currentMonthPerformance, lastMonthPerformance);
            result.setLiveDurationComparison(comparison);
            
            return success(result);
        } catch (ParseException e) {
            return error("日期格式错误，请使用 yyyy-MM 格式");
        } catch (Exception e) {
            logger.error("获取分销员详细收入信息失败：{}", e.getMessage(), e);
            return error("获取分销员详细收入信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算直播时长环比数据
     * 
     * @param currentMonthPerformance 当月业绩数据
     * @param lastMonthPerformance 上月业绩数据
     * @return 环比数据
     */
    private AnchorPerformanceVO.LiveDurationComparison calculateLiveDurationComparison(
            MonthlyPerformance currentMonthPerformance, MonthlyPerformance lastMonthPerformance) {
        
        AnchorPerformanceVO.LiveDurationComparison comparison = new AnchorPerformanceVO.LiveDurationComparison();
        
        // 设置当月数据
        BigDecimal currentHours = (currentMonthPerformance != null && currentMonthPerformance.getLiveDurationHours() != null) 
                ? currentMonthPerformance.getLiveDurationHours() : BigDecimal.ZERO;
        Integer currentValidDays = (currentMonthPerformance != null && currentMonthPerformance.getValidDays() != null) 
                ? currentMonthPerformance.getValidDays() : 0;
        
        // 设置上月数据
        BigDecimal lastHours = (lastMonthPerformance != null && lastMonthPerformance.getLiveDurationHours() != null) 
                ? lastMonthPerformance.getLiveDurationHours() : BigDecimal.ZERO;
        Integer lastValidDays = (lastMonthPerformance != null && lastMonthPerformance.getValidDays() != null) 
                ? lastMonthPerformance.getValidDays() : 0;
        
        comparison.setCurrentMonthHours(currentHours);
        comparison.setCurrentMonthValidDays(currentValidDays);
        comparison.setLastMonthHours(lastHours);
        comparison.setLastMonthValidDays(lastValidDays);
        
        // 计算环比变化量
        BigDecimal changeAmount = currentHours.subtract(lastHours);
        comparison.setChangeAmount(changeAmount);
        
        // 计算环比变化百分比和趋势
        if (lastHours.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal changePercentage = changeAmount.divide(lastHours, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            comparison.setChangePercentage(changePercentage);
            
            // 设置变化趋势
            if (changeAmount.compareTo(BigDecimal.ZERO) > 0) {
                comparison.setChangeTrend("增加");
            } else if (changeAmount.compareTo(BigDecimal.ZERO) < 0) {
                comparison.setChangeTrend("减少");
            } else {
                comparison.setChangeTrend("持平");
            }
        } else {
            // 上月没有数据或为0
            if (currentHours.compareTo(BigDecimal.ZERO) > 0) {
                comparison.setChangePercentage(new BigDecimal("100")); // 100%增长
                comparison.setChangeTrend("新增");
            } else {
                comparison.setChangePercentage(BigDecimal.ZERO);
                comparison.setChangeTrend("无变化");
            }
        }
        
        return comparison;
    }
} 