package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Api(tags = "后端-文件存储接口")
@RestController
@RequestMapping("/system/oss")
public class OssController extends BaseController {

    @Value("${local.storage.upload-dir}")
    private String uploadDir;

    @Value("${local.storage.access-url}")
    private String accessUrl;

    @PostMapping("/upload")
    @PreAuthorize("@ss.hasPermi('h:oss')")
    @ApiOperation(value = "上传文件 h:oss", notes = "支持图片和视频文件的上传，返回可访问的URL地址")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "file", paramType = "form"),
        @ApiImplicitParam(name = "fileType", value = "文件类型(image/video)", required = true, dataType = "string", paramType = "query")
    })
    public AjaxResult uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("fileType") String fileType) {
        
        if (file.isEmpty()) {
            return AjaxResult.error("请选择要上传的文件");
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            return AjaxResult.error("文件名不能为空");
        }

        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        if ("image".equals(fileType)) {
            if (!isValidImageExtension(fileExtension)) {
                return AjaxResult.error("不支持的图片格式，仅支持：.jpg、.jpeg、.png、.gif");
            }
        } else if ("video".equals(fileType)) {
            if (!isValidVideoExtension(fileExtension)) {
                return AjaxResult.error("不支持的视频格式，仅支持：.mp4、.avi、.mov");
            }
        } else {
            return AjaxResult.error("不支持的文件类型，仅支持图片或视频");
        }

        try {
            // 生成唯一文件名
            String fileName = UUID.randomUUID().toString().replace("-", "") + fileExtension;
            String directory = fileType;
            String objectName = directory + "/" + fileName;
            
            // 创建目录
            Path dirPath = Paths.get(uploadDir, directory);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }
            
            // 保存文件
            Path filePath = dirPath.resolve(fileName);
            file.transferTo(filePath.toFile());
            
            // 生成访问URL
            String fileUrl = accessUrl + objectName;
            
            return AjaxResult.success("上传成功", fileUrl);
        } catch (IOException e) {
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

    private boolean isValidImageExtension(String extension) {
        extension = extension.toLowerCase();
        return ".jpg".equals(extension) || ".jpeg".equals(extension) 
            || ".png".equals(extension) || ".gif".equals(extension);
    }

    private boolean isValidVideoExtension(String extension) {
        extension = extension.toLowerCase();
        return ".mp4".equals(extension) || ".avi".equals(extension) || ".mov".equals(extension);
    }

    private String getContentType(String extension) {
        extension = extension.toLowerCase();
        switch (extension) {
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".mp4":
                return "video/mp4";
            case ".avi":
                return "video/x-msvideo";
            case ".mov":
                return "video/quicktime";
            default:
                return "application/octet-stream";
        }
    }
}
