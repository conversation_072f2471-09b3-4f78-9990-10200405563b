#!/bin/bash

# 部署配置
REMOTE_HOST="*************"
REMOTE_USER="root"
REMOTE_DIR="/home/<USER>/ruoyi-admin"
LOCAL_JAR="./target/ruoyi-admin.jar"
JAVA_PROCESS_NAME="ruoyi-admin.jar"

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_warn() {
    echo -e "\033[33m[WARN]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 检查本地jar文件是否存在
check_local_jar() {
    if [ ! -f "$LOCAL_JAR" ]; then
        print_error "本地jar文件不存在: $LOCAL_JAR"
        print_info "请先执行: mvn clean package -DskipTests"
        exit 1
    fi
    print_info "本地jar文件检查通过: $LOCAL_JAR"
}

# 复制jar文件到远程服务器
deploy_jar() {
    print_info "开始复制jar文件到远程服务器..."
    scp "$LOCAL_JAR" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/"
    
    if [ $? -eq 0 ]; then
        print_info "jar文件复制成功"
    else
        print_error "jar文件复制失败"
        exit 1
    fi
}

# 部署启动脚本和配置文件
deploy_start_script() {
    print_info "部署UTF-8编码的启动脚本和配置文件..."
    
    # 检查remote-start.sh是否存在
    if [ -f "./remote-start.sh" ]; then
        scp "./remote-start.sh" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/start.sh"
        if [ $? -eq 0 ]; then
            print_info "启动脚本部署成功"
            # 设置执行权限
            ssh "$REMOTE_USER@$REMOTE_HOST" "chmod +x $REMOTE_DIR/start.sh"
        else
            print_warn "启动脚本部署失败，将使用默认启动方式"
        fi
    else
        print_warn "remote-start.sh不存在，将使用默认启动方式"
    fi
    
    # 部署日志查看脚本
    if [ -f "../view-commission-logs.sh" ]; then
        scp "../view-commission-logs.sh" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/"
        ssh "$REMOTE_USER@$REMOTE_HOST" "chmod +x $REMOTE_DIR/view-commission-logs.sh"
        print_info "日志查看脚本部署成功"
    fi
}

# 远程重启服务
restart_service() {
    print_info "开始远程重启服务..."
    
    ssh "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
        cd /home/<USER>/ruoyi-admin
        
        # 查找并杀死现有的java进程
        echo "[远程] 查找现有的ruoyi-admin进程..."
        PID=$(ps aux | grep "ruoyi-admin.jar" | grep -v grep | awk '{print $2}')
        
        if [ -n "$PID" ]; then
            echo "[远程] 找到进程 PID: $PID，正在停止..."
            kill -9 $PID
            sleep 2
            echo "[远程] 进程已停止"
        else
            echo "[远程] 没有找到运行中的ruoyi-admin进程"
        fi
        
        # 检查start.sh是否存在
        if [ -f "./start.sh" ]; then
            echo "[远程] 使用start.sh启动服务..."
            chmod +x ./start.sh
            nohup ./start.sh > /dev/null 2>&1 &
        else
            # 如果没有start.sh，直接启动jar
            echo "[远程] 直接启动jar文件..."
            # 设置UTF-8环境
            export LANG=zh_CN.UTF-8
            export LC_ALL=zh_CN.UTF-8
            nohup java -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -Xms256m -Xmx1024m -jar ruoyi-admin.jar --server.port=6131 --spring.profiles.active=dev > ruoyi-admin.log 2>&1 &
        fi
        
        # 等待一下让服务启动
        sleep 3
        
        # 检查服务是否启动成功
        NEW_PID=$(ps aux | grep "ruoyi-admin.jar" | grep -v grep | awk '{print $2}')
        if [ -n "$NEW_PID" ]; then
            echo "[远程] 服务启动成功，新进程 PID: $NEW_PID"
        else
            echo "[远程] 服务启动失败，请检查日志"
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        print_info "远程服务重启成功"
    else
        print_error "远程服务重启失败"
        exit 1
    fi
}

# 检查服务状态
check_service_status() {
    print_info "检查远程服务状态..."
    
    ssh "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
        echo "[远程] 当前Java进程状态:"
        ps aux | grep java | grep -v grep
        echo ""
        echo "[远程] ruoyi-admin进程状态:"
        ps aux | grep "ruoyi-admin.jar" | grep -v grep
EOF
}

# 主函数
main() {
    print_info "========== 开始部署 ruoyi-admin ==========";
    
    # 检查本地jar文件
    check_local_jar
    
    # 复制jar文件
    deploy_jar
    
    # 部署启动脚本
    deploy_start_script
    
    # 重启服务
    restart_service
    
    # 检查服务状态
    check_service_status
    
    print_info "========== 部署完成 ==========";
    print_info "如果服务启动失败，请登录服务器查看日志:"
    print_info "ssh $REMOTE_USER@$REMOTE_HOST"
    print_info "cd $REMOTE_DIR && tail -f ruoyi-admin.log"
}

# 执行主函数
main "$@" 