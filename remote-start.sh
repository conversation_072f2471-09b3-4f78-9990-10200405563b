#!/bin/bash

# 远程服务器启动脚本 - 支持UTF-8编码
# 此文件应部署到远程服务器的 /home/<USER>/ruoyi-admin/ 目录下，命名为 start.sh

AppName="ruoyi-admin.jar"
ServerPort="6131"
LogFile="ruoyi-admin.log"

# 设置UTF-8环境变量
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# JVM参数 - 完整的UTF-8编码设置
JVM_OPTS="-Dfile.encoding=UTF-8 \
-Dsun.jnu.encoding=UTF-8 \
-Dconsole.encoding=UTF-8 \
-Dspring.banner.charset=UTF-8 \
-Duser.timezone=Asia/Shanghai \
-Dname=$AppName \
-Xms256m \
-Xmx1024m \
-XX:MetaspaceSize=128m \
-XX:MaxMetaspaceSize=512m \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:+PrintGCDateStamps \
-XX:+PrintGCDetails"

echo "=========================================="
echo "启动 ruoyi-admin 服务"
echo "时间: $(date)"
echo "编码: LANG=$LANG, LC_ALL=$LC_ALL"
echo "端口: $ServerPort"
echo "日志: $LogFile"
echo "=========================================="

# 检查jar文件是否存在
if [ ! -f "$AppName" ]; then
    echo "错误: jar文件不存在: $AppName"
    exit 1
fi

# 启动应用
echo "正在启动应用..."
java $JVM_OPTS -jar $AppName --server.port=$ServerPort --spring.profiles.active=dev

echo "应用启动完成" 