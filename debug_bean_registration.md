# UserSyncService Bean注册调试指南

## 问题分析
错误信息：`No bean named 'userSyncService' available`

这表明Spring容器中没有找到名为'userSyncService'的Bean。

## 可能的原因
1. 静态内部类的Bean命名规则不同
2. Bean没有被正确扫描
3. 包扫描路径问题

## 调试步骤

### 1. 检查所有已注册的Bean
在应用启动后，添加以下代码来列出所有Bean：

```java
@RestController
public class DebugController {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @GetMapping("/debug/beans")
    public Map<String, Object> listBeans() {
        Map<String, Object> result = new HashMap<>();
        
        // 列出所有Bean名称
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        result.put("totalBeans", beanNames.length);
        result.put("allBeanNames", Arrays.asList(beanNames));
        
        // 查找包含"sync"的Bean
        List<String> syncBeans = Arrays.stream(beanNames)
            .filter(name -> name.toLowerCase().contains("sync"))
            .collect(Collectors.toList());
        result.put("syncRelatedBeans", syncBeans);
        
        // 查找UserSyncService类型的Bean
        try {
            Map<String, Object> userSyncBeans = applicationContext.getBeansOfType(
                Class.forName("com.ruoyi.system.service.impl.business.HistoricalDataServiceImpl$UserSyncService")
            );
            result.put("userSyncServiceBeans", userSyncBeans.keySet());
        } catch (Exception e) {
            result.put("userSyncServiceBeans", "Error: " + e.getMessage());
        }
        
        return result;
    }
}
```

### 2. 检查Bean的实际名称
静态内部类的Bean名称可能是：
- `historicalDataServiceImpl.UserSyncService` 
- `userSyncService`
- `HistoricalDataServiceImpl$UserSyncService`

### 3. 验证Bean是否存在
```java
@PostConstruct
public void checkUserSyncService() {
    try {
        // 方法1：通过名称
        Object bean1 = SpringUtils.getBean("userSyncService");
        log.info("找到Bean (userSyncService): {}", bean1.getClass().getName());
    } catch (Exception e) {
        log.warn("未找到Bean (userSyncService): {}", e.getMessage());
    }
    
    try {
        // 方法2：通过类型
        UserSyncService bean2 = SpringUtils.getBean(UserSyncService.class);
        log.info("找到Bean (UserSyncService.class): {}", bean2.getClass().getName());
    } catch (Exception e) {
        log.warn("未找到Bean (UserSyncService.class): {}", e.getMessage());
    }
}
```

## 解决方案

已实施的解决方案包括：

1. **明确指定Bean名称**：`@Component("userSyncService")`
2. **多重获取策略**：
   - 首先尝试通过名称获取
   - 其次尝试通过类型获取  
   - 最后手动创建实例

3. **增强日志**：详细记录每种获取方法的结果

## 验证修复
部署更新后，观察日志输出：
- 如果看到"通过Bean名称获取UserSyncService成功" - 表示Bean注册正常
- 如果看到"通过类型获取UserSyncService成功" - 表示Bean存在但名称不匹配
- 如果看到"手动创建UserSyncService实例" - 表示Bean不存在，使用备用方案

## 注意事项
- 静态内部类需要是public的
- 确保@Component注解正确
- 验证包扫描路径包含该类 