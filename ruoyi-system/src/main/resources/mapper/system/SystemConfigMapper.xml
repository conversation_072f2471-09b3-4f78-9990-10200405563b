<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SystemConfigMapper">

    <resultMap type="com.ruoyi.system.domain.SystemConfig" id="SystemConfigResult">
        <id     property="configKey"     column="config_key"     />
        <result property="configValue"   column="config_value"   />
        <result property="description"   column="description"    />
        <result property="groupName"     column="group_name"     />
        <result property="createTime"    column="created_at"     />
        <result property="updateTime"    column="updated_at"     />
    </resultMap>

    <sql id="selectSystemConfigVo">
        select config_key, config_value, description, group_name, created_at, updated_at from system_config
    </sql>

    <select id="selectSystemConfigList" parameterType="com.ruoyi.system.domain.SystemConfig" resultMap="SystemConfigResult">
        <include refid="selectSystemConfigVo"/>
        <where>
            <if test="configKey != null and configKey != ''">
                AND config_key like concat('%', #{configKey}, '%')
            </if>
            <if test="groupName != null and groupName != ''">
                AND group_name = #{groupName}
            </if>
            <if test="description != null and description != ''">
                 AND description like concat('%', #{description}, '%')
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectSystemConfigByKey" parameterType="String" resultMap="SystemConfigResult">
        <include refid="selectSystemConfigVo"/>
        where config_key = #{configKey}
    </select>

    <update id="updateSystemConfig" parameterType="com.ruoyi.system.domain.SystemConfig">
        update system_config
        <set>
            <if test="configValue != null">
                config_value = #{configValue},
            </if>
            <!-- description 和 groupName 也可以考虑是否允许在此处更新 -->
            <if test="description != null">
                description = #{description}, 
            </if>
             <if test="groupName != null">
                group_name = #{groupName},
            </if>
            updated_at = now()
        </set>
        where config_key = #{configKey}
    </update>

</mapper>
