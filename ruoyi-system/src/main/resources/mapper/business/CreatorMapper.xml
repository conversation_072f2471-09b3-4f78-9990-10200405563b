<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.business.CreatorMapper">

    <resultMap type="com.ruoyi.system.domain.business.Creator" id="CreatorResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="firstQualifiedMonth"    column="first_qualified_month"    />
        <result property="qualifiedByRecruiterId"    column="qualified_by_recruiter_id"    />
        <result property="nickname"    column="nickname"    />
        <result property="handle"    column="handle"    />
        <result property="firstActiveMonth"    column="first_active_month"    />
        <result property="recruitedBy"    column="recruited_by"    />
        <result property="currentDistributorLevelId"    column="current_distributor_level_id"    />
        <result property="levelUpdatedAt"    column="level_updated_at"    />
        <result property="createTime"    column="created_at"  />
        <result property="updateTime"    column="updated_at"  />
        <result property="createBy"      column="create_by"   />
        <result property="updateBy"      column="update_by"   />
        <result property="remark"        column="remark"      />
    </resultMap>

    <sql id="selectCreatorVo">
        select id, parent_id, first_qualified_month, qualified_by_recruiter_id, nickname, handle, first_active_month, recruited_by, current_distributor_level_id, level_updated_at, created_at, updated_at, create_by, update_by, remark from creators
    </sql>

    <select id="selectCreatorList" parameterType="com.ruoyi.system.domain.business.Creator" resultMap="CreatorResult">
        <include refid="selectCreatorVo"/>
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="handle != null  and handle != ''"> and handle = #{handle}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(created_at,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(created_at,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectCreatorById" parameterType="java.lang.Long" resultMap="CreatorResult">
        <include refid="selectCreatorVo"/>
        where id = #{id}
    </select>

    <select id="selectCreatorByHandle" parameterType="String" resultMap="CreatorResult">
        <include refid="selectCreatorVo"/>
        where handle = #{handle}
    </select>

    <!-- 查询所有根节点主播（没有父节点的主播） -->
    <select id="selectRootCreators" resultMap="CreatorResult">
        <include refid="selectCreatorVo"/>
        where (parent_id is null or parent_id = 0)
        order by created_at desc
    </select>

    <!-- 查询指定主播的直属子节点 -->
    <select id="selectDirectChildren" parameterType="java.lang.Long" resultMap="CreatorResult">
        <include refid="selectCreatorVo"/>
        where parent_id = #{parentId}
        order by created_at desc
    </select>

    <!-- 根据条件查询根节点主播（支持按昵称、ID过滤） -->
    <select id="selectRootCreatorsWithFilter" resultMap="CreatorResult">
        <include refid="selectCreatorVo"/>
        <where>
            <choose>
                <!-- 如果指定了creatorId，直接查询该用户作为根节点 -->
                <when test="creatorId != null">
                    id = #{creatorId}
                </when>
                <!-- 否则查询真正的根节点用户 -->
                <otherwise>
                    (parent_id is null or parent_id = 0)
                </otherwise>
            </choose>
            <if test="nickname != null and nickname != ''">
                and nickname like concat('%', #{nickname}, '%')
            </if>
        </where>
        order by created_at desc
    </select>

        <insert id="insertCreator" parameterType="com.ruoyi.system.domain.business.Creator" useGeneratedKeys="false">
        insert into creators
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="firstQualifiedMonth != null">first_qualified_month,</if>
            <if test="qualifiedByRecruiterId != null">qualified_by_recruiter_id,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="handle != null and handle != ''">handle,</if>
            <if test="firstActiveMonth != null">first_active_month,</if>
            <if test="recruitedBy != null">recruited_by,</if>
            <if test="currentDistributorLevelId != null">current_distributor_level_id,</if>
            <if test="levelUpdatedAt != null">level_updated_at,</if>
            <if test="createTime != null">created_at,</if>
            <!-- updated_at will use DB default CURRENT_TIMESTAMP -->
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="firstQualifiedMonth != null">#{firstQualifiedMonth},</if>
            <if test="qualifiedByRecruiterId != null">#{qualifiedByRecruiterId},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="handle != null and handle != ''">#{handle},</if>
            <if test="firstActiveMonth != null">#{firstActiveMonth},</if>
            <if test="recruitedBy != null">#{recruitedBy},</if>
            <if test="currentDistributorLevelId != null">#{currentDistributorLevelId},</if>
            <if test="levelUpdatedAt != null">#{levelUpdatedAt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCreator" parameterType="com.ruoyi.system.domain.business.Creator">
        update creators
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="firstQualifiedMonth != null">first_qualified_month = #{firstQualifiedMonth},</if>
            <if test="qualifiedByRecruiterId != null">qualified_by_recruiter_id = #{qualifiedByRecruiterId},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="handle != null and handle != ''">handle = #{handle},</if>
            <if test="firstActiveMonth != null">first_active_month = #{firstActiveMonth},</if>
            <if test="recruitedBy != null">recruited_by = #{recruitedBy},</if>
            <if test="currentDistributorLevelId != null">current_distributor_level_id = #{currentDistributorLevelId},</if>
            <if test="levelUpdatedAt != null">level_updated_at = #{levelUpdatedAt},</if>
            <if test="createTime != null">created_at = #{createTime},</if>
            <if test="updateTime != null">updated_at = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if> <!-- Allow empty string for remark, check if column allows null or empty -->
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCreatorById" parameterType="java.lang.Long">
        delete from creators where id = #{id}
    </delete>

    <delete id="deleteCreatorByIds" parameterType="java.lang.String">
        delete from creators where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
