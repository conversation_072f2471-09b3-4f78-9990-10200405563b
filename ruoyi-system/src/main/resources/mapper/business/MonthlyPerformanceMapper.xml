<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.business.MonthlyPerformanceMapper">

    <resultMap type="com.ruoyi.system.domain.business.MonthlyPerformance" id="MonthlyPerformanceResult">
        <result property="id"    column="id"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupManager"    column="group_manager"    />
        <result property="isViolative"    column="is_violative"    />
        <result property="isRookie"    column="is_rookie"    />
        <result property="diamonds"    column="diamonds"    />
        <result property="validDays"    column="valid_days"    />
        <result property="liveDurationHours"    column="live_duration_hours"    />
        <result property="bonusEstimated"    column="bonus_estimated"    />
        <result property="bonusRookieM1Retention"    column="bonus_rookie_m1_retention"    />
        <result property="bonusRookieM2"    column="bonus_rookie_m2"    />
        <result property="bonusRookieHalfMilestone"    column="bonus_rookie_half_milestone"    />
        <result property="bonusRookieM1"    column="bonus_rookie_m1"    />
        <result property="bonusActiveness"    column="bonus_activeness"    />
        <result property="bonusRevenueScale"    column="bonus_revenue_scale"    />
        <result property="bonusNewCreatorNetwork"    column="bonus_new_creator_network"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.domain.vo.MonthlyPerformanceVO" id="MonthlyPerformanceWithCreatorResult">
        <!-- 月度业绩字段 -->
        <result property="id"    column="id"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupManager"    column="group_manager"    />
        <result property="creatorNetworkManager"    column="creator_network_manager"    />
        <result property="isViolative"    column="is_violative"    />
        <result property="isRookie"    column="is_rookie"    />
        <result property="diamonds"    column="diamonds"    />
        <result property="validDays"    column="valid_days"    />
        <result property="liveDurationHours"    column="live_duration_hours"    />
        <result property="bonusEstimated"    column="bonus_estimated"    />
        <result property="bonusRookieM1Retention"    column="bonus_rookie_m1_retention"    />
        <result property="bonusRookieM2"    column="bonus_rookie_m2"    />
        <result property="bonusRookieHalfMilestone"    column="bonus_rookie_half_milestone"    />
        <result property="bonusRookieM1"    column="bonus_rookie_m1"    />
        <result property="bonusActiveness"    column="bonus_activeness"    />
        <result property="bonusRevenueScale"    column="bonus_revenue_scale"    />
        <result property="bonusNewCreatorNetwork"    column="bonus_new_creator_network"    />
        <!-- Creator字段 -->
        <result property="nickname"    column="nickname"    />
        <result property="parentId"    column="parent_id"    />
        <result property="creatorCreatedAt"    column="creator_created_at"    />
        <result property="creatorRemark"    column="creator_remark"    />
        <result property="parentNickname"    column="parent_nickname"    />
    </resultMap>

    <sql id="selectMonthlyPerformanceVo">
        select id, creator_id, data_month, group_name, group_manager, is_violative, is_rookie, diamonds, valid_days, live_duration_hours, bonus_estimated, bonus_rookie_m1_retention, bonus_rookie_m2, bonus_rookie_half_milestone, bonus_rookie_m1, bonus_activeness, bonus_revenue_scale, bonus_new_creator_network from monthly_performance
    </sql>

    <select id="selectMonthlyPerformanceList" parameterType="com.ruoyi.system.domain.business.MonthlyPerformance" resultMap="MonthlyPerformanceResult">
        <include refid="selectMonthlyPerformanceVo"/>
        <where>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
            <if test="dataMonth != null "> and data_month = #{dataMonth}</if>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="groupManager != null  and groupManager != ''"> and group_manager like concat('%', #{groupManager}, '%')</if>
            <if test="isViolative != null "> and is_violative = #{isViolative}</if>
            <if test="isRookie != null "> and is_rookie = #{isRookie}</if>
        </where>
        order by data_month desc
    </select>

    <select id="selectMonthlyPerformanceById" parameterType="Integer" resultMap="MonthlyPerformanceResult">
        <include refid="selectMonthlyPerformanceVo"/>
        where id = #{id}
    </select>

    <select id="selectMonthlyPerformanceByCreatorAndMonth" resultMap="MonthlyPerformanceResult">
        <include refid="selectMonthlyPerformanceVo"/>
        where creator_id = #{creatorId} and data_month = #{dataMonth}
    </select>

    <select id="selectMonthlyPerformanceWithCreatorList" parameterType="com.ruoyi.system.domain.business.MonthlyPerformance" resultMap="MonthlyPerformanceWithCreatorResult">
        select 
            mp.id, 
            mp.creator_id, 
            mp.data_month, 
            mp.group_name, 
            mp.group_manager, 
            mp.creator_network_manager,
            mp.is_violative, 
            mp.is_rookie, 
            mp.diamonds, 
            mp.valid_days, 
            mp.live_duration_hours, 
            mp.bonus_estimated, 
            mp.bonus_rookie_m1_retention, 
            mp.bonus_rookie_m2, 
            mp.bonus_rookie_half_milestone, 
            mp.bonus_rookie_m1, 
            mp.bonus_activeness, 
            mp.bonus_revenue_scale, 
            mp.bonus_new_creator_network,
            c.nickname,
            c.parent_id,
            c.created_at as creator_created_at,
            c.remark as creator_remark,
            pc.nickname as parent_nickname
        from monthly_performance mp
        left join creators c on mp.creator_id = c.id
        left join creators pc on c.parent_id = pc.id
        <where>
            <if test="creatorId != null "> and mp.creator_id = #{creatorId}</if>
            <if test="dataMonth != null "> and mp.data_month = #{dataMonth}</if>
            <if test="groupName != null  and groupName != ''"> and mp.group_name like concat('%', #{groupName}, '%')</if>
            <if test="groupManager != null  and groupManager != ''"> and mp.group_manager like concat('%', #{groupManager}, '%')</if>
            <if test="isViolative != null "> and mp.is_violative = #{isViolative}</if>
            <if test="isRookie != null "> and mp.is_rookie = #{isRookie}</if>
        </where>
        order by mp.data_month desc
    </select>

    <insert id="insertMonthlyPerformance" parameterType="com.ruoyi.system.domain.business.MonthlyPerformance" useGeneratedKeys="true" keyProperty="id">
        insert into monthly_performance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">creator_id,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="groupName != null and groupName != ''">group_name,</if>
            <if test="groupManager != null and groupManager != ''">group_manager,</if>
            <if test="isViolative != null">is_violative,</if>
            <if test="isRookie != null">is_rookie,</if>
            <if test="diamonds != null">diamonds,</if>
            <if test="validDays != null">valid_days,</if>
            <if test="liveDurationHours != null">live_duration_hours,</if>
            <if test="bonusEstimated != null">bonus_estimated,</if>
            <if test="bonusRookieM1Retention != null">bonus_rookie_m1_retention,</if>
            <if test="bonusRookieM2 != null">bonus_rookie_m2,</if>
            <if test="bonusRookieHalfMilestone != null">bonus_rookie_half_milestone,</if>
            <if test="bonusRookieM1 != null">bonus_rookie_m1,</if>
            <if test="bonusActiveness != null">bonus_activeness,</if>
            <if test="bonusRevenueScale != null">bonus_revenue_scale,</if>
            <if test="bonusNewCreatorNetwork != null">bonus_new_creator_network,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">#{creatorId},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="groupName != null and groupName != ''">#{groupName},</if>
            <if test="groupManager != null and groupManager != ''">#{groupManager},</if>
            <if test="isViolative != null">#{isViolative},</if>
            <if test="isRookie != null">#{isRookie},</if>
            <if test="diamonds != null">#{diamonds},</if>
            <if test="validDays != null">#{validDays},</if>
            <if test="liveDurationHours != null">#{liveDurationHours},</if>
            <if test="bonusEstimated != null">#{bonusEstimated},</if>
            <if test="bonusRookieM1Retention != null">#{bonusRookieM1Retention},</if>
            <if test="bonusRookieM2 != null">#{bonusRookieM2},</if>
            <if test="bonusRookieHalfMilestone != null">#{bonusRookieHalfMilestone},</if>
            <if test="bonusRookieM1 != null">#{bonusRookieM1},</if>
            <if test="bonusActiveness != null">#{bonusActiveness},</if>
            <if test="bonusRevenueScale != null">#{bonusRevenueScale},</if>
            <if test="bonusNewCreatorNetwork != null">#{bonusNewCreatorNetwork},</if>
         </trim>
    </insert>

    <update id="updateMonthlyPerformance" parameterType="com.ruoyi.system.domain.business.MonthlyPerformance">
        update monthly_performance
        <trim prefix="SET" suffixOverrides=",">
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="dataMonth != null">data_month = #{dataMonth},</if>
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="groupManager != null and groupManager != ''">group_manager = #{groupManager},</if>
            <if test="isViolative != null">is_violative = #{isViolative},</if>
            <if test="isRookie != null">is_rookie = #{isRookie},</if>
            <if test="diamonds != null">diamonds = #{diamonds},</if>
            <if test="validDays != null">valid_days = #{validDays},</if>
            <if test="liveDurationHours != null">live_duration_hours = #{liveDurationHours},</if>
            <if test="bonusEstimated != null">bonus_estimated = #{bonusEstimated},</if>
            <if test="bonusRookieM1Retention != null">bonus_rookie_m1_retention = #{bonusRookieM1Retention},</if>
            <if test="bonusRookieM2 != null">bonus_rookie_m2 = #{bonusRookieM2},</if>
            <if test="bonusRookieHalfMilestone != null">bonus_rookie_half_milestone = #{bonusRookieHalfMilestone},</if>
            <if test="bonusRookieM1 != null">bonus_rookie_m1 = #{bonusRookieM1},</if>
            <if test="bonusActiveness != null">bonus_activeness = #{bonusActiveness},</if>
            <if test="bonusRevenueScale != null">bonus_revenue_scale = #{bonusRevenueScale},</if>
            <if test="bonusNewCreatorNetwork != null">bonus_new_creator_network = #{bonusNewCreatorNetwork},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthlyPerformanceById" parameterType="Integer">
        delete from monthly_performance where id = #{id}
    </delete>

    <delete id="deleteMonthlyPerformanceByIds" parameterType="String">
        delete from monthly_performance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCreatorIdsWithDataByMonth" resultType="java.lang.Long">
        select distinct creator_id from monthly_performance where data_month = #{dataMonth}
    </select>

    <select id="selectMonthlyPerformanceByCreatorIdsAndMonth" resultMap="MonthlyPerformanceResult">
        <include refid="selectMonthlyPerformanceVo"/>
        where data_month = #{dataMonth}
        <if test="creatorIds != null and creatorIds.size() > 0">
            and creator_id in
            <foreach collection="creatorIds" item="creatorId" open="(" separator="," close=")">
                #{creatorId}
            </foreach>
        </if>
    </select>
</mapper>
