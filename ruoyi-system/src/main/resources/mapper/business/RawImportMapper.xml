<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.business.RawImportMapper">

    <resultMap type="com.ruoyi.system.domain.business.RawImport" id="RawImportResult">
        <result property="id"                         column="id"                         />
        <result property="fileName"                   column="file_name"                  />
        <result property="importedAt"                 column="imported_at"                />
        <result property="dataMonth"                  column="data_month"                 />
        <result property="creatorId"                  column="creator_id"                 />
        <result property="creatorNickname"            column="creator_nickname"           />
        <result property="handle"                     column="handle"                     />
        <result property="creatorNetworkManager"      column="creator_network_manager"    />
        <result property="groupName"                  column="group_name"                 />
        <result property="groupManager"               column="group_manager"              />
        <result property="isViolativeCreators"        column="is_violative_creators"      />
        <result property="theCreatorWasRookie"        column="the_creator_was_rookie"     />
        <result property="diamonds"                   column="diamonds"                   />
        <result property="validDays"                  column="valid_days"                 />
        <result property="liveDurationH"              column="live_duration_h"            />
        <result property="estimatedBonus"             column="estimated_bonus"            />
        <result property="estBonusRookieM1Retention"  column="est_bonus_rookie_m1_retention" />
        <result property="estBonusRookieM2"           column="est_bonus_rookie_m2"        />
        <result property="estBonusRookieHalfMilestone" column="est_bonus_rookie_half_milestone" />
        <result property="estBonusRookieM1"           column="est_bonus_rookie_m1"        />
        <result property="estBonusActivenessTask"     column="est_bonus_activeness_task"  />
        <result property="estBonusRevenueScaleTask"   column="est_bonus_revenue_scale_task" />
        <result property="estBonusNewCreatorNetworkTask" column="est_bonus_new_creator_network_task" />
        <result property="createBy"                   column="create_by"                  />
        <result property="createTime"                 column="create_time"                />
        <result property="updateBy"                   column="update_by"                  />
        <result property="updateTime"                 column="update_time"                />
        <result property="remark"                     column="remark"                     />
    </resultMap>

    <sql id="selectRawImportVo">
        select id, file_name, imported_at, data_month, creator_id, creator_nickname, handle, creator_network_manager, group_name, group_manager, is_violative_creators, the_creator_was_rookie, diamonds, valid_days, live_duration_h, estimated_bonus, est_bonus_rookie_m1_retention, est_bonus_rookie_m2, est_bonus_rookie_half_milestone, est_bonus_rookie_m1, est_bonus_activeness_task, est_bonus_revenue_scale_task, est_bonus_new_creator_network_task, create_by, create_time, update_by, update_time, remark
        from raw_imports
    </sql>

    <select id="selectRawImportList" parameterType="com.ruoyi.system.domain.business.RawImport" resultMap="RawImportResult">
        <include refid="selectRawImportVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="dataMonth != null  and dataMonth != ''"> and data_month = #{dataMonth}</if>
            <if test="creatorId != null  and creatorId != ''"> and creator_id = #{creatorId}</if>
            <if test="handle != null  and handle != ''"> and handle = #{handle}</if>
            <if test="params != null and params.beginImportedAt != null and params.beginImportedAt != '' and params.endImportedAt != null and params.endImportedAt != ''">
                and imported_at between #{params.beginImportedAt} and #{params.endImportedAt}
            </if>
        </where>
        order by imported_at desc
    </select>

    <select id="selectRawImportById" parameterType="Long" resultMap="RawImportResult">
        <include refid="selectRawImportVo"/>
        where id = #{id}
    </select>

    <insert id="insertRawImport" parameterType="com.ruoyi.system.domain.business.RawImport" useGeneratedKeys="true" keyProperty="id">
        insert into raw_imports
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">file_name,</if>
            <if test="importedAt != null">imported_at,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="creatorNickname != null">creator_nickname,</if>
            <if test="handle != null">handle,</if>
            <if test="creatorNetworkManager != null">creator_network_manager,</if>
            <if test="groupName != null">group_name,</if>
            <if test="groupManager != null">group_manager,</if>
            <if test="isViolativeCreators != null">is_violative_creators,</if>
            <if test="theCreatorWasRookie != null">the_creator_was_rookie,</if>
            <if test="diamonds != null">diamonds,</if>
            <if test="validDays != null">valid_days,</if>
            <if test="liveDurationH != null">live_duration_h,</if>
            <if test="estimatedBonus != null">estimated_bonus,</if>
            <if test="estBonusRookieM1Retention != null">est_bonus_rookie_m1_retention,</if>
            <if test="estBonusRookieM2 != null">est_bonus_rookie_m2,</if>
            <if test="estBonusRookieHalfMilestone != null">est_bonus_rookie_half_milestone,</if>
            <if test="estBonusRookieM1 != null">est_bonus_rookie_m1,</if>
            <if test="estBonusActivenessTask != null">est_bonus_activeness_task,</if>
            <if test="estBonusRevenueScaleTask != null">est_bonus_revenue_scale_task,</if>
            <if test="estBonusNewCreatorNetworkTask != null">est_bonus_new_creator_network_task,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">#{fileName},</if>
            <if test="importedAt != null">#{importedAt},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorNickname != null">#{creatorNickname},</if>
            <if test="handle != null">#{handle},</if>
            <if test="creatorNetworkManager != null">#{creatorNetworkManager},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="groupManager != null">#{groupManager},</if>
            <if test="isViolativeCreators != null">#{isViolativeCreators},</if>
            <if test="theCreatorWasRookie != null">#{theCreatorWasRookie},</if>
            <if test="diamonds != null">#{diamonds},</if>
            <if test="validDays != null">#{validDays},</if>
            <if test="liveDurationH != null">#{liveDurationH},</if>
            <if test="estimatedBonus != null">#{estimatedBonus},</if>
            <if test="estBonusRookieM1Retention != null">#{estBonusRookieM1Retention},</if>
            <if test="estBonusRookieM2 != null">#{estBonusRookieM2},</if>
            <if test="estBonusRookieHalfMilestone != null">#{estBonusRookieHalfMilestone},</if>
            <if test="estBonusRookieM1 != null">#{estBonusRookieM1},</if>
            <if test="estBonusActivenessTask != null">#{estBonusActivenessTask},</if>
            <if test="estBonusRevenueScaleTask != null">#{estBonusRevenueScaleTask},</if>
            <if test="estBonusNewCreatorNetworkTask != null">#{estBonusNewCreatorNetworkTask},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertRawImport" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into raw_imports (
            file_name, imported_at, data_month, creator_id, creator_nickname, handle,
            creator_network_manager, group_name, group_manager, is_violative_creators,
            the_creator_was_rookie, diamonds, valid_days, live_duration_h, estimated_bonus,
            est_bonus_rookie_m1_retention, est_bonus_rookie_m2, est_bonus_rookie_half_milestone,
            est_bonus_rookie_m1, est_bonus_activeness_task, est_bonus_revenue_scale_task,
            est_bonus_new_creator_network_task, create_by, create_time, update_by, update_time, remark
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.fileName}, #{item.importedAt}, #{item.dataMonth}, #{item.creatorId}, #{item.creatorNickname}, #{item.handle},
            #{item.creatorNetworkManager}, #{item.groupName}, #{item.groupManager}, #{item.isViolativeCreators},
            #{item.theCreatorWasRookie}, #{item.diamonds}, #{item.validDays}, #{item.liveDurationH}, #{item.estimatedBonus},
            #{item.estBonusRookieM1Retention}, #{item.estBonusRookieM2}, #{item.estBonusRookieHalfMilestone},
            #{item.estBonusRookieM1}, #{item.estBonusActivenessTask}, #{item.estBonusRevenueScaleTask},
            #{item.estBonusNewCreatorNetworkTask}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark}
            )
        </foreach>
    </insert>

    <update id="updateRawImport" parameterType="com.ruoyi.system.domain.business.RawImport">
        update raw_imports
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="importedAt != null">imported_at = #{importedAt},</if>
            <if test="dataMonth != null">data_month = #{dataMonth},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="creatorNickname != null">creator_nickname = #{creatorNickname},</if>
            <if test="handle != null">handle = #{handle},</if>
            <if test="creatorNetworkManager != null">creator_network_manager = #{creatorNetworkManager},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="groupManager != null">group_manager = #{groupManager},</if>
            <if test="isViolativeCreators != null">is_violative_creators = #{isViolativeCreators},</if>
            <if test="theCreatorWasRookie != null">the_creator_was_rookie = #{theCreatorWasRookie},</if>
            <if test="diamonds != null">diamonds = #{diamonds},</if>
            <if test="validDays != null">valid_days = #{validDays},</if>
            <if test="liveDurationH != null">live_duration_h = #{liveDurationH},</if>
            <if test="estimatedBonus != null">estimated_bonus = #{estimatedBonus},</if>
            <if test="estBonusRookieM1Retention != null">est_bonus_rookie_m1_retention = #{estBonusRookieM1Retention},</if>
            <if test="estBonusRookieM2 != null">est_bonus_rookie_m2 = #{estBonusRookieM2},</if>
            <if test="estBonusRookieHalfMilestone != null">est_bonus_rookie_half_milestone = #{estBonusRookieHalfMilestone},</if>
            <if test="estBonusRookieM1 != null">est_bonus_rookie_m1 = #{estBonusRookieM1},</if>
            <if test="estBonusActivenessTask != null">est_bonus_activeness_task = #{estBonusActivenessTask},</if>
            <if test="estBonusRevenueScaleTask != null">est_bonus_revenue_scale_task = #{estBonusRevenueScaleTask},</if>
            <if test="estBonusNewCreatorNetworkTask != null">est_bonus_new_creator_network_task = #{estBonusNewCreatorNetworkTask},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRawImportById" parameterType="Long">
        delete from raw_imports where id = #{id}
    </delete>

    <delete id="deleteRawImportByIds" parameterType="String">
        delete from raw_imports where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
