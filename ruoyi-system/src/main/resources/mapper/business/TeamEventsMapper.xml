<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.business.TeamEventsMapper">
    
    <resultMap type="TeamEvents" id="TeamEventsResult">
        <result property="id"    column="id"    />
        <result property="actorCreatorId"    column="actor_creator_id"    />
        <result property="targetCreatorId"    column="target_creator_id"    />
        <result property="eventType"    column="event_type"    />
        <result property="eventTimestamp"    column="event_timestamp"    />
        <result property="details"    column="details"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTeamEventsVo">
        select id, actor_creator_id, target_creator_id, event_type, event_timestamp, details, create_by, create_time, update_by, update_time, remark from team_events
    </sql>

    <select id="selectTeamEventsList" parameterType="TeamEvents" resultMap="TeamEventsResult">
        <include refid="selectTeamEventsVo"/>
        <where>  
            <if test="actorCreatorId != null ">
                and actor_creator_id = #{actorCreatorId}
            </if>
            <if test="targetCreatorId != null ">
                and target_creator_id = #{targetCreatorId}
            </if>
            <if test="eventType != null and eventType != ''">
                and event_type = #{eventType}
            </if>
            <if test="eventTimestamp != null ">
                and event_timestamp = #{eventTimestamp}
            </if>
            <if test="details != null and details != ''">
                and details like concat('%', #{details}, '%')
            </if>
        </where>
        order by event_timestamp desc
    </select>
    
    <select id="selectTeamEventsById" parameterType="Long" resultMap="TeamEventsResult">
        <include refid="selectTeamEventsVo"/>
        where id = #{id}
    </select>

    <select id="selectTeamEventsByActorAndTimeRange" resultMap="TeamEventsResult">
        <include refid="selectTeamEventsVo"/>
        where actor_creator_id = #{actorCreatorId}
        <if test="startTime != null">
            and event_timestamp >= #{startTime}
        </if>
        <if test="endTime != null">
            and event_timestamp &lt;= #{endTime}
        </if>
        order by event_timestamp desc
    </select>

    <select id="selectTeamEventsByTypeAndTimeRange" resultMap="TeamEventsResult">
        <include refid="selectTeamEventsVo"/>
        where event_type = #{eventType}
        <if test="startTime != null">
            and event_timestamp >= #{startTime}
        </if>
        <if test="endTime != null">
            and event_timestamp &lt;= #{endTime}
        </if>
        order by event_timestamp desc
    </select>
        
    <insert id="insertTeamEvents" parameterType="TeamEvents" useGeneratedKeys="true" keyProperty="id">
        insert into team_events
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="actorCreatorId != null">actor_creator_id,</if>
            <if test="targetCreatorId != null">target_creator_id,</if>
            <if test="eventType != null and eventType != ''">event_type,</if>
            <if test="eventTimestamp != null">event_timestamp,</if>
            <if test="details != null">details,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="actorCreatorId != null">#{actorCreatorId},</if>
            <if test="targetCreatorId != null">#{targetCreatorId},</if>
            <if test="eventType != null and eventType != ''">#{eventType},</if>
            <if test="eventTimestamp != null">#{eventTimestamp},</if>
            <if test="details != null">#{details},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="insertTeamEventsBatch" parameterType="java.util.List">
        insert into team_events (actor_creator_id, target_creator_id, event_type, event_timestamp, details, create_by, create_time, update_by, update_time, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.actorCreatorId}, #{item.targetCreatorId}, #{item.eventType}, #{item.eventTimestamp}, #{item.details}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateTeamEvents" parameterType="TeamEvents">
        update team_events
        <trim prefix="SET" suffixOverrides=",">
            <if test="actorCreatorId != null">actor_creator_id = #{actorCreatorId},</if>
            <if test="targetCreatorId != null">target_creator_id = #{targetCreatorId},</if>
            <if test="eventType != null and eventType != ''">event_type = #{eventType},</if>
            <if test="eventTimestamp != null">event_timestamp = #{eventTimestamp},</if>
            <if test="details != null">details = #{details},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeamEventsById" parameterType="Long">
        delete from team_events where id = #{id}
    </delete>

    <delete id="deleteTeamEventsByIds" parameterType="String">
        delete from team_events where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTeamEventsByActorCreatorId" parameterType="Long">
        delete from team_events where actor_creator_id = #{actorCreatorId}
    </delete>

    <delete id="deleteTeamEventsByTimeRange">
        delete from team_events
        where event_timestamp >= #{startTime}
        and event_timestamp &lt;= #{endTime}
    </delete>
</mapper>
