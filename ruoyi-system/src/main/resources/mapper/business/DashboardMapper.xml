<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.business.DashboardMapper">

    <!-- 根据用户ID获取用户昵称 -->
    <select id="selectCreatorNickname" parameterType="Long" resultType="String">
        SELECT nickname 
        FROM creators 
        WHERE id = #{creatorId}
    </select>

    <!-- 获取用户在指定月份的等级 -->
    <select id="selectUserLevel" resultType="String">
        SELECT achieved_level 
        FROM commission_distributor_qualifications 
        WHERE creator_id = #{creatorId} 
        AND data_month = #{dataMonth}
    </select>

    <!-- 获取用户截至指定月份的年度累计总收入 -->
    <select id="selectTotalIncomeByYear" resultType="BigDecimal">
        SELECT COALESCE(SUM(final_payout_usd), 0) 
        FROM commission_payouts 
        WHERE creator_id = #{creatorId} 
        AND YEAR(data_month) = YEAR(#{dataMonth}) 
        AND data_month &lt;= #{dataMonth}
    </select>

    <!-- 获取用户指定月份的当月总收入 -->
    <select id="selectMonthlyIncome" resultType="BigDecimal">
        SELECT COALESCE(final_payout_usd, 0) 
        FROM commission_payouts 
        WHERE creator_id = #{creatorId} 
        AND data_month = #{dataMonth}
    </select>

    <!-- 获取用户上个月的收入 -->
    <select id="selectPreviousMonthIncome" resultType="BigDecimal">
        SELECT COALESCE(final_payout_usd, 0) 
        FROM commission_payouts 
        WHERE creator_id = #{creatorId} 
        AND data_month = DATE_SUB(#{dataMonth}, INTERVAL 1 MONTH)
    </select>

    <!-- 获取用户过去4个月的收入数据（用于计算月均环比增长率） -->
    <select id="selectPast4MonthsIncome" resultType="Map">
        SELECT 
            data_month,
            final_payout_usd as income
        FROM commission_payouts 
        WHERE creator_id = #{creatorId} 
        AND data_month &gt;= DATE_SUB(#{dataMonth}, INTERVAL 3 MONTH)
        AND data_month &lt;= #{dataMonth}
        ORDER BY data_month ASC
    </select>

    <!-- 获取保级目标增长率配置 -->
    <select id="selectRetentionTargetGrowthRate" resultType="BigDecimal">
        SELECT CAST(setting_value AS DECIMAL(7,4)) 
        FROM commission_settings 
        WHERE setting_key = 'retention_target_avg_growth_rate'
    </select>

    <!-- 获取用户个人收入（基于source_type分类） -->
    <select id="selectPersonalIncome" resultType="BigDecimal">
        SELECT COALESCE(SUM(calculated_amount_usd), 0) 
        FROM commission_payout_breakdowns 
        WHERE creator_id = #{creatorId} 
        AND YEAR(data_month) = YEAR(#{dataMonth}) 
        AND data_month &lt;= #{dataMonth}
        AND source_type IN ('LEVEL_COMMISSION')
    </select>

    <!-- 获取用户团队收入（基于source_type分类） -->
    <select id="selectTeamIncome" resultType="BigDecimal">
        SELECT COALESCE(SUM(calculated_amount_usd), 0) 
        FROM commission_payout_breakdowns 
        WHERE creator_id = #{creatorId} 
        AND YEAR(data_month) = YEAR(#{dataMonth}) 
        AND data_month &lt;= #{dataMonth}
        AND source_type IN ('MULTI_LEVEL_COMMISSION', 'RECRUITMENT_BONUS')
    </select>

    <!-- 获取用户团队总人数 -->
    <select id="selectTotalTeamMembers" parameterType="Long" resultType="Integer">
        SELECT COUNT(descendant_id) 
        FROM creator_relationships 
        WHERE ancestor_id = #{creatorId} 
        AND depth > 0
    </select>

</mapper>
