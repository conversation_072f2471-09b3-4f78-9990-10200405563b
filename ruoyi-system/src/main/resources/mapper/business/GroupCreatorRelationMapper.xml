<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.business.GroupCreatorRelationMapper">

    <resultMap type="com.ruoyi.system.domain.business.GroupCreatorRelation" id="GroupCreatorRelationResult">
        <result property="groupName"    column="group_name"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="groupType"    column="group_type"    />
        <result property="createTime"   column="created_at"    />
        <result property="updateTime"   column="updated_at"    />
        <result property="createBy"     column="create_by"     />
        <result property="updateBy"     column="update_by"     />
    </resultMap>

    <resultMap type="com.ruoyi.system.domain.vo.GroupRelationVO" id="GroupRelationVOResult">
        <result property="groupName"    column="group_name"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="groupType"    column="group_type"    />
        <result property="nickname"     column="nickname"      />
        <result property="handle"       column="handle"        />
        <result property="createdAt"    column="created_at"    />
        <result property="hasParent"    column="has_parent"    />
    </resultMap>

    <sql id="selectGroupCreatorRelationVo">
        select group_name, creator_id, group_type, created_at, updated_at, create_by, update_by from group_creator_relation
    </sql>

    <select id="selectGroupCreatorRelationList" parameterType="com.ruoyi.system.domain.business.GroupCreatorRelation" resultMap="GroupCreatorRelationResult">
        <include refid="selectGroupCreatorRelationVo"/>
        <where>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="creatorId != null "> and creator_id = #{creatorId}</if>
            <if test="groupType != null "> and group_type = #{groupType}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(created_at,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(created_at,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="selectGroupCreatorRelationByGroupName" parameterType="String" resultMap="GroupCreatorRelationResult">
        <include refid="selectGroupCreatorRelationVo"/>
        where group_name = #{groupName}
    </select>

    <select id="selectGroupCreatorRelationByCreatorId" parameterType="java.lang.Long" resultMap="GroupCreatorRelationResult">
        <include refid="selectGroupCreatorRelationVo"/>
        where creator_id = #{creatorId}
    </select>

    <select id="selectGroupRelationList" resultMap="GroupRelationVOResult">
        SELECT DISTINCT
            mp.group_name,
            gcr.creator_id,
            COALESCE(gcr.group_type, 0) as group_type,
            c.nickname,
            c.handle,
            gcr.created_at,
            CASE WHEN gcr.creator_id IS NOT NULL THEN 1 ELSE 0 END as has_parent
        FROM monthly_performance mp
        LEFT JOIN group_creator_relation gcr ON mp.group_name = gcr.group_name
        LEFT JOIN creators c ON gcr.creator_id = c.id
        WHERE mp.group_name IS NOT NULL AND mp.group_name != ''
        ORDER BY mp.group_name, gcr.created_at DESC
    </select>

    <insert id="insertGroupCreatorRelation" parameterType="com.ruoyi.system.domain.business.GroupCreatorRelation">
        insert into group_creator_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="groupType != null">group_type,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">#{groupName},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="groupType != null">#{groupType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateGroupCreatorRelation" parameterType="com.ruoyi.system.domain.business.GroupCreatorRelation">
        update group_creator_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="groupType != null">group_type = #{groupType},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where group_name = #{groupName}
    </update>

    <delete id="deleteGroupCreatorRelationByGroupName" parameterType="String">
        delete from group_creator_relation where group_name = #{groupName}
    </delete>

    <delete id="deleteGroupCreatorRelationByCreatorId" parameterType="java.lang.Long">
        delete from group_creator_relation where creator_id = #{creatorId}
    </delete>

    <delete id="deleteGroupCreatorRelationByGroupNames" parameterType="String">
        delete from group_creator_relation where group_name in
        <foreach item="groupName" collection="array" open="(" separator="," close=")">
            #{groupName}
        </foreach>
    </delete>
</mapper> 