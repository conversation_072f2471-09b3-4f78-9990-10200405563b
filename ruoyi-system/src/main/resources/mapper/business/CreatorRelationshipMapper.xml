<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.business.CreatorRelationshipMapper">

    <resultMap type="com.ruoyi.system.domain.business.CreatorRelationship" id="CreatorRelationshipResult">
        <result property="ancestorId"    column="ancestor_id"    />
        <result property="descendantId"    column="descendant_id"    />
        <result property="depth"    column="depth"    />
    </resultMap>

    <sql id="selectCreatorRelationshipVo">
        select ancestor_id, descendant_id, depth from creator_relationships
    </sql>

    <select id="selectCreatorRelationshipList" parameterType="com.ruoyi.system.domain.business.CreatorRelationship" resultMap="CreatorRelationshipResult">
        <include refid="selectCreatorRelationshipVo"/>
        <where>
            <if test="ancestorId != null "> and ancestor_id = #{ancestorId}</if>
            <if test="descendantId != null "> and descendant_id = #{descendantId}</if>
            <if test="depth != null "> and depth = #{depth}</if>
        </where>
        order by ancestor_id, descendant_id, depth
    </select>

    <select id="selectCreatorRelationshipById" resultMap="CreatorRelationshipResult">
        <include refid="selectCreatorRelationshipVo"/>
        where ancestor_id = #{ancestorId} and descendant_id = #{descendantId}
    </select>

    <insert id="insertCreatorRelationship" parameterType="com.ruoyi.system.domain.business.CreatorRelationship">
        insert into creator_relationships(ancestor_id, descendant_id, depth)
        values(#{ancestorId}, #{descendantId}, #{depth})
    </insert>

    <insert id="batchInsertCreatorRelationship" parameterType="java.util.List">
        insert into creator_relationships(ancestor_id, descendant_id, depth) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ancestorId}, #{item.descendantId}, #{item.depth})
        </foreach>
    </insert>

    <delete id="deleteCreatorRelationshipById">
        delete from creator_relationships where ancestor_id = #{ancestorId} and descendant_id = #{descendantId}
    </delete>

    <delete id="deleteCreatorRelationshipByAncestorId" parameterType="Long">
        delete from creator_relationships where ancestor_id = #{ancestorId}
    </delete>

    <delete id="deleteCreatorRelationshipByDescendantId" parameterType="Long">
        delete from creator_relationships where descendant_id = #{descendantId}
    </delete>

    <delete id="deleteAllCreatorRelationships">
        delete from creator_relationships
    </delete>

    <select id="selectDirectChildrenIds" parameterType="Long" resultType="Long">
        select descendant_id from creator_relationships
        where ancestor_id = #{creatorId} and depth = 1
    </select>

    <select id="selectDescendantsByDepth" resultMap="CreatorRelationshipResult">
        <include refid="selectCreatorRelationshipVo"/>
        where ancestor_id = #{ancestorId}
        <if test="depth != null">
            and depth = #{depth}
        </if>
        <if test="depth == null">
            and depth &gt; 0 <!-- Exclude self if depth is not specified -->
        </if>
        order by depth, descendant_id
    </select>

    <select id="selectAncestorsByDepth" resultMap="CreatorRelationshipResult">
        <include refid="selectCreatorRelationshipVo"/>
        where descendant_id = #{descendantId}
        <if test="depth != null">
            and depth = #{depth}
        </if>
        <if test="depth == null">
            and depth &gt; 0 <!-- Exclude self if depth is not specified -->
        </if>
        order by depth desc, ancestor_id
    </select>

</mapper>
