<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionMultilevelRulesMapper">
    
    <resultMap type="CommissionMultilevelRules" id="CommissionMultilevelRulesResult">
        <result property="id"    column="id"    />
        <result property="depth"    column="depth"    />
        <result property="commissionRate"    column="commission_rate"    />
        <result property="isActive"    column="is_active"    />
    </resultMap>

    <sql id="selectCommissionMultilevelRulesVo">
        select id, depth, commission_rate, is_active 
        from commission_multilevel_rules
    </sql>

    <select id="selectCommissionMultilevelRulesList" parameterType="CommissionMultilevelRules" resultMap="CommissionMultilevelRulesResult">
        <include refid="selectCommissionMultilevelRulesVo"/>
        <where>  
            <if test="depth != null">
                and depth = #{depth}
            </if>
            <if test="isActive != null">
                and is_active = #{isActive}
            </if>
        </where>
        order by depth asc
    </select>

    <select id="selectActiveCommissionMultilevelRules" resultMap="CommissionMultilevelRulesResult">
        <include refid="selectCommissionMultilevelRulesVo"/>
        where is_active = 1
        order by depth asc
    </select>
    
    <select id="selectCommissionMultilevelRulesById" parameterType="Integer" resultMap="CommissionMultilevelRulesResult">
        <include refid="selectCommissionMultilevelRulesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCommissionMultilevelRules" parameterType="CommissionMultilevelRules" useGeneratedKeys="true" keyProperty="id">
        insert into commission_multilevel_rules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="depth != null">depth,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="isActive != null">is_active,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="depth != null">#{depth},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="isActive != null">#{isActive},</if>
         </trim>
    </insert>

    <update id="updateCommissionMultilevelRules" parameterType="CommissionMultilevelRules">
        update commission_multilevel_rules
        <trim prefix="SET" suffixOverrides=",">
            <if test="depth != null">depth = #{depth},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommissionMultilevelRulesById" parameterType="Integer">
        delete from commission_multilevel_rules where id = #{id}
    </delete>

    <delete id="deleteCommissionMultilevelRulesByIds" parameterType="Integer">
        delete from commission_multilevel_rules where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 