<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionDynamicThresholdsMapper">
    
    <resultMap type="CommissionDynamicThresholds" id="CommissionDynamicThresholdsResult">
        <result property="creatorId"    column="creator_id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="lastMonthDiamonds"    column="last_month_diamonds"    />
        <result property="calculatedThreshold"    column="calculated_threshold"    />
        <result property="baseThreshold"    column="base_threshold"    />
        <result property="thresholdIncreaseRate"    column="threshold_increase_rate"    />
    </resultMap>

    <sql id="selectCommissionDynamicThresholdsVo">
        select creator_id, data_month, last_month_diamonds, calculated_threshold, base_threshold, threshold_increase_rate from commission_dynamic_thresholds
    </sql>

    <select id="selectCommissionDynamicThresholdsList" parameterType="CommissionDynamicThresholds" resultMap="CommissionDynamicThresholdsResult">
        <include refid="selectCommissionDynamicThresholdsVo"/>
        <where>  
            <if test="dataMonth != null"> and data_month = #{dataMonth}</if>
            <if test="creatorId != null"> and creator_id = #{creatorId}</if>
            <if test="calculatedThreshold != null"> and calculated_threshold = #{calculatedThreshold}</if>
        </where>
        order by data_month desc, creator_id
    </select>
    
    <select id="selectCommissionDynamicThresholdsById" parameterType="Long" resultMap="CommissionDynamicThresholdsResult">
        <include refid="selectCommissionDynamicThresholdsVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionDynamicThresholdsByMonth" parameterType="java.util.Date" resultMap="CommissionDynamicThresholdsResult">
        <include refid="selectCommissionDynamicThresholdsVo"/>
        where data_month = #{dataMonth}
        order by distributor_id, created_at desc
    </select>

    <select id="selectCommissionDynamicThresholdsByDistributor" resultMap="CommissionDynamicThresholdsResult">
        <include refid="selectCommissionDynamicThresholdsVo"/>
        where creator_id = #{creatorId} and data_month = #{dataMonth}
        limit 1
    </select>

    <select id="selectCommissionDynamicThresholdsByDistributorAndMonth" resultMap="CommissionDynamicThresholdsResult">
        <include refid="selectCommissionDynamicThresholdsVo"/>
        where creator_id = #{creatorId} and data_month = #{dataMonth}
    </select>

    <select id="selectCommissionDynamicThresholdsByThreshold" resultMap="CommissionDynamicThresholdsResult">
        <include refid="selectCommissionDynamicThresholdsVo"/>
        where data_month = #{dataMonth} and calculated_threshold = #{threshold}
        order by creator_id
    </select>

    <select id="checkCommissionDynamicThresholdExists" resultType="int">
        select count(1) from commission_dynamic_thresholds 
        where creator_id = #{creatorId} and data_month = #{dataMonth}
    </select>
        
    <insert id="insertCommissionDynamicThresholds" parameterType="CommissionDynamicThresholds">
        insert into commission_dynamic_thresholds
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">creator_id,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="lastMonthDiamonds != null">last_month_diamonds,</if>
            <if test="calculatedThreshold != null">calculated_threshold,</if>
            <if test="baseThreshold != null">base_threshold,</if>
            <if test="thresholdIncreaseRate != null">threshold_increase_rate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">#{creatorId},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="lastMonthDiamonds != null">#{lastMonthDiamonds},</if>
            <if test="calculatedThreshold != null">#{calculatedThreshold},</if>
            <if test="baseThreshold != null">#{baseThreshold},</if>
            <if test="thresholdIncreaseRate != null">#{thresholdIncreaseRate},</if>
         </trim>
    </insert>

    <insert id="batchInsertCommissionDynamicThresholds" parameterType="java.util.List">
        insert into commission_dynamic_thresholds(creator_id, data_month, last_month_diamonds, calculated_threshold, base_threshold, threshold_increase_rate)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.creatorId}, #{item.dataMonth}, #{item.lastMonthDiamonds}, #{item.calculatedThreshold}, #{item.baseThreshold}, #{item.thresholdIncreaseRate})
        </foreach>
    </insert>

    <update id="updateCommissionDynamicThresholds" parameterType="CommissionDynamicThresholds">
        update commission_dynamic_thresholds
        <trim prefix="SET" suffixOverrides=",">
            <if test="lastMonthDiamonds != null">last_month_diamonds = #{lastMonthDiamonds},</if>
            <if test="calculatedThreshold != null">calculated_threshold = #{calculatedThreshold},</if>
            <if test="baseThreshold != null">base_threshold = #{baseThreshold},</if>
            <if test="thresholdIncreaseRate != null">threshold_increase_rate = #{thresholdIncreaseRate},</if>
        </trim>
        where creator_id = #{creatorId} and data_month = #{dataMonth}
    </update>

    <delete id="deleteCommissionDynamicThresholdsByCreatorAndMonth">
        delete from commission_dynamic_thresholds where creator_id = #{creatorId} and data_month = #{dataMonth}
    </delete>

    <delete id="deleteCommissionDynamicThresholdsByCreatorIds" parameterType="String">
        delete from commission_dynamic_thresholds where creator_id in 
        <foreach item="creatorId" collection="array" open="(" separator="," close=")">
            #{creatorId}
        </foreach>
    </delete>

    <delete id="deleteCommissionDynamicThresholdsByMonth" parameterType="java.util.Date">
        delete from commission_dynamic_thresholds where data_month = #{dataMonth}
    </delete>

</mapper> 