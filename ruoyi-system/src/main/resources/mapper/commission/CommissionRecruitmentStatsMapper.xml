<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionRecruitmentStatsMapper">
    
    <resultMap type="CommissionRecruitmentStats" id="CommissionRecruitmentStatsResult">
        <result property="recruiterId"    column="recruiter_id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="newRecruitsCount"    column="new_recruits_count"    />
        <result property="qualifiedBonusUsd"    column="qualified_bonus_usd"    />
    </resultMap>

    <sql id="selectCommissionRecruitmentStatsVo">
        select recruiter_id, data_month, new_recruits_count, qualified_bonus_usd from commission_recruitment_stats
    </sql>

    <select id="selectCommissionRecruitmentStatsList" parameterType="CommissionRecruitmentStats" resultMap="CommissionRecruitmentStatsResult">
        <include refid="selectCommissionRecruitmentStatsVo"/>
        <where>  
            <if test="dataMonth != null"> and data_month = #{dataMonth}</if>
            <if test="recruiterId != null"> and recruiter_id = #{recruiterId}</if>
            <if test="newRecruitsCount != null"> and new_recruits_count = #{newRecruitsCount}</if>
        </where>
        order by data_month desc, qualified_bonus_usd desc
    </select>
    
    <select id="selectCommissionRecruitmentStatsById" parameterType="Long" resultMap="CommissionRecruitmentStatsResult">
        <include refid="selectCommissionRecruitmentStatsVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionRecruitmentStatsByMonth" parameterType="java.util.Date" resultMap="CommissionRecruitmentStatsResult">
        <include refid="selectCommissionRecruitmentStatsVo"/>
        where data_month = #{dataMonth}
        order by qualified_bonus_usd desc, recruiter_id
    </select>

    <select id="selectCommissionRecruitmentStatsByDistributor" resultMap="CommissionRecruitmentStatsResult">
        <include refid="selectCommissionRecruitmentStatsVo"/>
        where recruiter_id = #{recruiterId} and data_month = #{dataMonth}
        limit 1
    </select>

    <select id="selectCommissionRecruitmentStatsByDistributorAndMonth" resultMap="CommissionRecruitmentStatsResult">
        <include refid="selectCommissionRecruitmentStatsVo"/>
        where recruiter_id = #{recruiterId} and data_month = #{dataMonth}
    </select>

    <select id="selectTopRecruiters" resultMap="CommissionRecruitmentStatsResult">
        <include refid="selectCommissionRecruitmentStatsVo"/>
        where data_month = #{dataMonth}
        order by new_recruits_count desc, qualified_bonus_usd desc
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="selectCommissionRecruitmentStatsSummary" resultType="java.util.Map">
        select 
            count(distinct recruiter_id) as total_recruiters,
            sum(new_recruits_count) as total_new_recruits,
            sum(qualified_bonus_usd) as total_bonus_earned
        from commission_recruitment_stats 
        where data_month = #{dataMonth}
    </select>

    <select id="checkCommissionRecruitmentStatsExists" resultType="int">
        select count(1) from commission_recruitment_stats 
        where recruiter_id = #{recruiterId} and data_month = #{dataMonth}
    </select>
        
    <insert id="insertCommissionRecruitmentStats" parameterType="CommissionRecruitmentStats">
        insert into commission_recruitment_stats
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recruiterId != null">recruiter_id,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="newRecruitsCount != null">new_recruits_count,</if>
            <if test="qualifiedBonusUsd != null">qualified_bonus_usd,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recruiterId != null">#{recruiterId},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="newRecruitsCount != null">#{newRecruitsCount},</if>
            <if test="qualifiedBonusUsd != null">#{qualifiedBonusUsd},</if>
         </trim>
    </insert>

    <insert id="batchInsertCommissionRecruitmentStats" parameterType="java.util.List">
        insert into commission_recruitment_stats(recruiter_id, data_month, new_recruits_count, qualified_bonus_usd)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.recruiterId}, #{item.dataMonth}, #{item.newRecruitsCount}, #{item.qualifiedBonusUsd})
        </foreach>
    </insert>

    <update id="updateCommissionRecruitmentStats" parameterType="CommissionRecruitmentStats">
        update commission_recruitment_stats
        <trim prefix="SET" suffixOverrides=",">
            <if test="newRecruitsCount != null">new_recruits_count = #{newRecruitsCount},</if>
            <if test="qualifiedBonusUsd != null">qualified_bonus_usd = #{qualifiedBonusUsd},</if>
        </trim>
        where recruiter_id = #{recruiterId} and data_month = #{dataMonth}
    </update>

    <delete id="deleteCommissionRecruitmentStatsByRecruiterAndMonth">
        delete from commission_recruitment_stats where recruiter_id = #{recruiterId} and data_month = #{dataMonth}
    </delete>

    <delete id="deleteCommissionRecruitmentStatsByRecruiterIds" parameterType="String">
        delete from commission_recruitment_stats where recruiter_id in 
        <foreach item="recruiterId" collection="array" open="(" separator="," close=")">
            #{recruiterId}
        </foreach>
    </delete>

    <delete id="deleteCommissionRecruitmentStatsByMonth" parameterType="java.util.Date">
        delete from commission_recruitment_stats where data_month = #{dataMonth}
    </delete>

</mapper> 