<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionMonthlySettingsMapper">
    
    <resultMap type="CommissionMonthlySettings" id="CommissionMonthlySettingsResult">
        <result property="dataMonth"    column="data_month"    />
        <result property="manualIncomeUsd"    column="manual_income_usd"    />
        <result property="payoutCapRate"    column="payout_cap_rate"    />
        <result property="diamondToUsdRate"    column="diamond_to_usd_rate"    />
        <result property="createTime"    column="created_at"    />
        <result property="updateTime"    column="updated_at"    />
    </resultMap>

    <sql id="selectCommissionMonthlySettingsVo">
        select data_month, manual_income_usd, payout_cap_rate, diamond_to_usd_rate, created_at, updated_at 
        from commission_monthly_settings
    </sql>

    <select id="selectCommissionMonthlySettingsList" parameterType="CommissionMonthlySettings" resultMap="CommissionMonthlySettingsResult">
        <include refid="selectCommissionMonthlySettingsVo"/>
        <where>  
            <if test="dataMonth != null">
                and date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
            </if>
            <if test="manualIncomeUsd != null">
                and manual_income_usd = #{manualIncomeUsd}
            </if>
        </where>
        order by data_month desc
    </select>
    
    <select id="selectCommissionMonthlySettingsByMonth" parameterType="Date" resultMap="CommissionMonthlySettingsResult">
        <include refid="selectCommissionMonthlySettingsVo"/>
        where date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
        order by created_at desc
        limit 1
    </select>
        
    <insert id="insertCommissionMonthlySettings" parameterType="CommissionMonthlySettings">
        insert into commission_monthly_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">data_month,</if>
            <if test="manualIncomeUsd != null">manual_income_usd,</if>
            <if test="payoutCapRate != null">payout_cap_rate,</if>
            <if test="diamondToUsdRate != null">diamond_to_usd_rate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="manualIncomeUsd != null">#{manualIncomeUsd},</if>
            <if test="payoutCapRate != null">#{payoutCapRate},</if>
            <if test="diamondToUsdRate != null">#{diamondToUsdRate},</if>
         </trim>
    </insert>

    <update id="updateCommissionMonthlySettings" parameterType="CommissionMonthlySettings">
        update commission_monthly_settings
        <trim prefix="SET" suffixOverrides=",">
            <if test="manualIncomeUsd != null">manual_income_usd = #{manualIncomeUsd},</if>
            <if test="payoutCapRate != null">payout_cap_rate = #{payoutCapRate},</if>
            <if test="diamondToUsdRate != null">diamond_to_usd_rate = #{diamondToUsdRate},</if>
            updated_at = CURRENT_TIMESTAMP,
        </trim>
        where data_month = #{dataMonth}
    </update>

    <delete id="deleteCommissionMonthlySettingsByMonth" parameterType="Date">
        delete from commission_monthly_settings where data_month = #{dataMonth}
    </delete>

    <delete id="deleteCommissionMonthlySettingsByMonths" parameterType="Date">
        delete from commission_monthly_settings where data_month in 
        <foreach item="dataMonth" collection="array" open="(" separator="," close=")">
            #{dataMonth}
        </foreach>
    </delete>

</mapper> 