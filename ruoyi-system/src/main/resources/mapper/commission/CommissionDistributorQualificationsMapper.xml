<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionDistributorQualificationsMapper">
    
    <resultMap type="CommissionDistributorQualifications" id="CommissionDistributorQualificationsResult">
        <result property="creatorId"    column="creator_id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="isQualified"    column="is_qualified"    />
        <result property="achievedLevel"    column="achieved_level"    />
        <result property="personalDiamonds"    column="personal_diamonds"    />
        <result property="teamDiamonds"    column="team_diamonds"    />
        <result property="directDownlinesCount"    column="direct_downlines_count"    />
        <result property="dynamicThreshold"    column="dynamic_threshold"    />
        <result property="thresholdMet"    column="threshold_met"    />
    </resultMap>

    <sql id="selectCommissionDistributorQualificationsVo">
        select creator_id, data_month, is_qualified, achieved_level, personal_diamonds, team_diamonds, direct_downlines_count, dynamic_threshold, threshold_met from commission_distributor_qualifications
    </sql>

    <select id="selectCommissionDistributorQualificationsList" parameterType="CommissionDistributorQualifications" resultMap="CommissionDistributorQualificationsResult">
        <include refid="selectCommissionDistributorQualificationsVo"/>
        <where>  
            <if test="creatorId != null"> and creator_id = #{creatorId}</if>
            <if test="dataMonth != null"> and data_month = #{dataMonth}</if>
            <if test="achievedLevel != null  and achievedLevel != ''"> and achieved_level = #{achievedLevel}</if>
            <if test="isQualified != null"> and is_qualified = #{isQualified}</if>
        </where>
        order by data_month desc, achieved_level
    </select>
    
    <select id="selectCommissionDistributorQualificationsById" parameterType="Long" resultMap="CommissionDistributorQualificationsResult">
        <include refid="selectCommissionDistributorQualificationsVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionDistributorQualificationsByDistributor" resultMap="CommissionDistributorQualificationsResult">
        <include refid="selectCommissionDistributorQualificationsVo"/>
        where creator_id = #{creatorId} and data_month = #{dataMonth}
        limit 1
    </select>

    <select id="selectCommissionDistributorQualificationsByMonth" parameterType="java.util.Date" resultMap="CommissionDistributorQualificationsResult">
        <include refid="selectCommissionDistributorQualificationsVo"/>
        where data_month = #{dataMonth}
        order by creator_id, achieved_level desc
    </select>

    <select id="selectQualifiedDistributorsByLevel" resultMap="CommissionDistributorQualificationsResult">
        <include refid="selectCommissionDistributorQualificationsVo"/>
        where data_month = #{dataMonth} and achieved_level = #{achievedLevel} and is_qualified = 1
        order by creator_id
    </select>

    <select id="checkCommissionDistributorQualificationExists" resultType="int">
        select count(1) from commission_distributor_qualifications 
        where creator_id = #{creatorId} and data_month = #{dataMonth}
    </select>
        
    <insert id="insertCommissionDistributorQualifications" parameterType="CommissionDistributorQualifications">
        insert into commission_distributor_qualifications
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">creator_id,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="isQualified != null">is_qualified,</if>
            <if test="achievedLevel != null and achievedLevel != ''">achieved_level,</if>
            <if test="personalDiamonds != null">personal_diamonds,</if>
            <if test="teamDiamonds != null">team_diamonds,</if>
            <if test="directDownlinesCount != null">direct_downlines_count,</if>
            <if test="dynamicThreshold != null">dynamic_threshold,</if>
            <if test="thresholdMet != null">threshold_met,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">#{creatorId},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="isQualified != null">#{isQualified},</if>
            <if test="achievedLevel != null and achievedLevel != ''">#{achievedLevel},</if>
            <if test="personalDiamonds != null">#{personalDiamonds},</if>
            <if test="teamDiamonds != null">#{teamDiamonds},</if>
            <if test="directDownlinesCount != null">#{directDownlinesCount},</if>
            <if test="dynamicThreshold != null">#{dynamicThreshold},</if>
            <if test="thresholdMet != null">#{thresholdMet},</if>
         </trim>
    </insert>

    <update id="updateCommissionDistributorQualifications" parameterType="CommissionDistributorQualifications">
        update commission_distributor_qualifications
        <trim prefix="SET" suffixOverrides=",">
            <if test="isQualified != null">is_qualified = #{isQualified},</if>
            <if test="achievedLevel != null and achievedLevel != ''">achieved_level = #{achievedLevel},</if>
            <if test="personalDiamonds != null">personal_diamonds = #{personalDiamonds},</if>
            <if test="teamDiamonds != null">team_diamonds = #{teamDiamonds},</if>
            <if test="directDownlinesCount != null">direct_downlines_count = #{directDownlinesCount},</if>
            <if test="dynamicThreshold != null">dynamic_threshold = #{dynamicThreshold},</if>
            <if test="thresholdMet != null">threshold_met = #{thresholdMet},</if>
        </trim>
        where creator_id = #{creatorId} and data_month = #{dataMonth}
    </update>

    <delete id="deleteCommissionDistributorQualificationsByCreatorAndMonth">
        delete from commission_distributor_qualifications where creator_id = #{creatorId} and data_month = #{dataMonth}
    </delete>

    <delete id="deleteCommissionDistributorQualificationsByCreatorIds" parameterType="String">
        delete from commission_distributor_qualifications where creator_id in 
        <foreach item="creatorId" collection="array" open="(" separator="," close=")">
            #{creatorId}
        </foreach>
    </delete>

</mapper> 