<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionCalculationLogsMapper">
    
    <resultMap type="CommissionCalculationLogs" id="CommissionCalculationLogsResult">
        <result property="id"    column="id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="calculationType"    column="calculation_type"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="logLevel"    column="log_level"    />
        <result property="message"    column="message"    />
        <result property="details"    column="details"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectCommissionCalculationLogsVo">
        select id, data_month, calculation_type, creator_id, log_level, message, details, created_at from commission_calculation_logs
    </sql>

    <select id="selectCommissionCalculationLogsList" parameterType="CommissionCalculationLogs" resultMap="CommissionCalculationLogsResult">
        <include refid="selectCommissionCalculationLogsVo"/>
        <where>  
            <if test="dataMonth != null"> and data_month = #{dataMonth}</if>
            <if test="calculationType != null  and calculationType != ''"> and calculation_type = #{calculationType}</if>
            <if test="creatorId != null"> and creator_id = #{creatorId}</if>
            <if test="logLevel != null  and logLevel != ''"> and log_level = #{logLevel}</if>
            <if test="message != null  and message != ''"> and message like concat('%', #{message}, '%')</if>
        </where>
        order by created_at desc
    </select>
    
    <select id="selectCommissionCalculationLogsById" parameterType="Long" resultMap="CommissionCalculationLogsResult">
        <include refid="selectCommissionCalculationLogsVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionCalculationLogsByMonth" parameterType="java.util.Date" resultMap="CommissionCalculationLogsResult">
        <include refid="selectCommissionCalculationLogsVo"/>
        where data_month = #{dataMonth}
        order by created_at desc
    </select>

    <select id="selectCommissionCalculationLogsByMonthAndType" resultMap="CommissionCalculationLogsResult">
        <include refid="selectCommissionCalculationLogsVo"/>
        where data_month = #{dataMonth} and calculation_type = #{calculationType}
        order by created_at desc
    </select>

    <select id="selectCommissionCalculationLogsByCreatorAndMonth" resultMap="CommissionCalculationLogsResult">
        <include refid="selectCommissionCalculationLogsVo"/>
        where creator_id = #{creatorId} and data_month = #{dataMonth}
        order by created_at desc
    </select>
        
    <insert id="insertCommissionCalculationLogs" parameterType="CommissionCalculationLogs" useGeneratedKeys="true" keyProperty="id">
        insert into commission_calculation_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">data_month,</if>
            <if test="calculationType != null and calculationType != ''">calculation_type,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="logLevel != null and logLevel != ''">log_level,</if>
            <if test="message != null and message != ''">message,</if>
            <if test="details != null">details,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="calculationType != null and calculationType != ''">#{calculationType},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="logLevel != null and logLevel != ''">#{logLevel},</if>
            <if test="message != null and message != ''">#{message},</if>
            <if test="details != null">#{details},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <insert id="batchInsertCommissionCalculationLogs" parameterType="java.util.List">
        insert into commission_calculation_logs(data_month, calculation_type, creator_id, log_level, message, details, created_at)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dataMonth}, #{item.calculationType}, #{item.creatorId}, #{item.logLevel}, #{item.message}, #{item.details}, #{item.createdAt})
        </foreach>
    </insert>

    <update id="updateCommissionCalculationLogs" parameterType="CommissionCalculationLogs">
        update commission_calculation_logs
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataMonth != null">data_month = #{dataMonth},</if>
            <if test="calculationType != null and calculationType != ''">calculation_type = #{calculationType},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="logLevel != null and logLevel != ''">log_level = #{logLevel},</if>
            <if test="message != null and message != ''">message = #{message},</if>
            <if test="details != null">details = #{details},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommissionCalculationLogsById" parameterType="Long">
        delete from commission_calculation_logs where id = #{id}
    </delete>

    <delete id="deleteCommissionCalculationLogsByIds" parameterType="String">
        delete from commission_calculation_logs where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCommissionCalculationLogsBeforeDate" parameterType="java.util.Date">
        delete from commission_calculation_logs where created_at &lt; #{beforeDate}
    </delete>

</mapper> 