<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionConfigSnapshotsMapper">
    
    <resultMap type="CommissionConfigSnapshots" id="CommissionConfigSnapshotsResult">
        <result property="id"    column="id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="configType"    column="config_type"    />
        <result property="configData"    column="config_data"    />
        <result property="createdAt"    column="created_at"    />
    </resultMap>

    <sql id="selectCommissionConfigSnapshotsVo">
        select id, data_month, config_type, config_data, created_at from commission_config_snapshots
    </sql>

    <select id="selectCommissionConfigSnapshotsList" parameterType="CommissionConfigSnapshots" resultMap="CommissionConfigSnapshotsResult">
        <include refid="selectCommissionConfigSnapshotsVo"/>
        <where>  
            <if test="dataMonth != null"> and data_month = #{dataMonth}</if>
            <if test="configType != null  and configType != ''"> and config_type = #{configType}</if>
        </where>
        order by data_month desc, created_at desc
    </select>
    
    <select id="selectCommissionConfigSnapshotsById" parameterType="Long" resultMap="CommissionConfigSnapshotsResult">
        <include refid="selectCommissionConfigSnapshotsVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionConfigSnapshotsByMonth" parameterType="java.util.Date" resultMap="CommissionConfigSnapshotsResult">
        <include refid="selectCommissionConfigSnapshotsVo"/>
        where data_month = #{dataMonth}
        order by config_type, created_at desc
    </select>

    <select id="selectCommissionConfigSnapshotsByMonthAndType" resultMap="CommissionConfigSnapshotsResult">
        <include refid="selectCommissionConfigSnapshotsVo"/>
        where data_month = #{dataMonth} and config_type = #{configType}
        order by created_at desc
        limit 1
    </select>

    <select id="selectLatestCommissionConfigSnapshot" parameterType="String" resultMap="CommissionConfigSnapshotsResult">
        <include refid="selectCommissionConfigSnapshotsVo"/>
        where config_type = #{configType}
        order by data_month desc, created_at desc
        limit 1
    </select>

    <select id="checkCommissionConfigSnapshotExists" resultType="int">
        select count(1) from commission_config_snapshots 
        where data_month = #{dataMonth} and config_type = #{configType}
    </select>
        
    <insert id="insertCommissionConfigSnapshots" parameterType="CommissionConfigSnapshots" useGeneratedKeys="true" keyProperty="id">
        insert into commission_config_snapshots
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">data_month,</if>
            <if test="configType != null and configType != ''">config_type,</if>
            <if test="configData != null and configData != ''">config_data,</if>
            <if test="createdAt != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="configType != null and configType != ''">#{configType},</if>
            <if test="configData != null and configData != ''">#{configData},</if>
            <if test="createdAt != null">#{createdAt},</if>
         </trim>
    </insert>

    <insert id="batchInsertCommissionConfigSnapshots" parameterType="java.util.List">
        insert into commission_config_snapshots(data_month, config_type, config_data, created_at)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dataMonth}, #{item.configType}, #{item.configData}, #{item.createdAt})
        </foreach>
    </insert>

    <update id="updateCommissionConfigSnapshots" parameterType="CommissionConfigSnapshots">
        update commission_config_snapshots
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataMonth != null">data_month = #{dataMonth},</if>
            <if test="configType != null and configType != ''">config_type = #{configType},</if>
            <if test="configData != null and configData != ''">config_data = #{configData},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommissionConfigSnapshotsById" parameterType="Long">
        delete from commission_config_snapshots where id = #{id}
    </delete>

    <delete id="deleteCommissionConfigSnapshotsByIds" parameterType="String">
        delete from commission_config_snapshots where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCommissionConfigSnapshotsBeforeDate" parameterType="java.util.Date">
        delete from commission_config_snapshots where created_at &lt; #{beforeDate}
    </delete>

</mapper> 