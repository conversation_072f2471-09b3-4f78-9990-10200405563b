<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionPayoutBreakdownsMapper">
    
    <resultMap type="CommissionPayoutBreakdowns" id="CommissionPayoutBreakdownsResult">
        <result property="id"    column="id"    />
        <result property="payoutId"    column="payout_id"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="sourceType"    column="source_type"    />
        <result property="baseAmountDiamonds"    column="base_amount_diamonds"    />
        <result property="calculatedAmountDiamonds"    column="calculated_amount_diamonds"    />
        <result property="calculatedAmountUsd"    column="calculated_amount_usd"    />
        <result property="createTime"    column="created_at"    />
        <result property="updateTime"    column="updated_at"    />
    </resultMap>

    <sql id="selectCommissionPayoutBreakdownsVo">
        select id, payout_id, creator_id, data_month, source_type, 
               base_amount_diamonds, calculated_amount_diamonds, calculated_amount_usd, 
               created_at, updated_at
        from commission_payout_breakdowns
    </sql>

    <select id="selectCommissionPayoutBreakdownsById" parameterType="Long" resultMap="CommissionPayoutBreakdownsResult">
        <include refid="selectCommissionPayoutBreakdownsVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionPayoutBreakdownsList" parameterType="CommissionPayoutBreakdowns" resultMap="CommissionPayoutBreakdownsResult">
        <include refid="selectCommissionPayoutBreakdownsVo"/>
        <where>  
            <if test="payoutId != null">
                and payout_id = #{payoutId}
            </if>
            <if test="creatorId != null">
                and creator_id = #{creatorId}
            </if>
            <if test="dataMonth != null">
                and date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
            </if>
            <if test="sourceType != null and sourceType != ''">
                and source_type = #{sourceType}
            </if>
        </where>
        order by payout_id, source_type
    </select>

    <select id="selectCommissionPayoutBreakdownsByPayoutId" parameterType="Long" resultMap="CommissionPayoutBreakdownsResult">
        <include refid="selectCommissionPayoutBreakdownsVo"/>
        where payout_id = #{payoutId}
        order by source_type
    </select>

    <select id="selectCommissionPayoutBreakdownsByCreatorAndMonth" resultMap="CommissionPayoutBreakdownsResult">
        <include refid="selectCommissionPayoutBreakdownsVo"/>
        where creator_id = #{creatorId} 
        and date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
        order by source_type
    </select>

    <select id="selectCommissionPayoutBreakdownsByMonth" parameterType="Date" resultMap="CommissionPayoutBreakdownsResult">
        <include refid="selectCommissionPayoutBreakdownsVo"/>
        where date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
        order by creator_id, source_type
    </select>
        
    <insert id="insertCommissionPayoutBreakdowns" parameterType="CommissionPayoutBreakdowns" useGeneratedKeys="true" keyProperty="id">
        insert into commission_payout_breakdowns
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payoutId != null">payout_id,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="baseAmountDiamonds != null">base_amount_diamonds,</if>
            <if test="calculatedAmountDiamonds != null">calculated_amount_diamonds,</if>
            <if test="calculatedAmountUsd != null">calculated_amount_usd,</if>
            <if test="createTime != null">created_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payoutId != null">#{payoutId},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="baseAmountDiamonds != null">#{baseAmountDiamonds},</if>
            <if test="calculatedAmountDiamonds != null">#{calculatedAmountDiamonds},</if>
            <if test="calculatedAmountUsd != null">#{calculatedAmountUsd},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertCommissionPayoutBreakdowns" parameterType="java.util.List">
        insert into commission_payout_breakdowns 
        (payout_id, creator_id, data_month, source_type, base_amount_diamonds, 
         calculated_amount_diamonds, calculated_amount_usd, created_at)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.payoutId}, #{item.creatorId}, #{item.dataMonth}, #{item.sourceType}, 
             #{item.baseAmountDiamonds}, #{item.calculatedAmountDiamonds}, #{item.calculatedAmountUsd}, 
             #{item.createTime})
        </foreach>
    </insert>

    <update id="updateCommissionPayoutBreakdowns" parameterType="CommissionPayoutBreakdowns">
        update commission_payout_breakdowns
        <trim prefix="SET" suffixOverrides=",">
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="baseAmountDiamonds != null">base_amount_diamonds = #{baseAmountDiamonds},</if>
            <if test="calculatedAmountDiamonds != null">calculated_amount_diamonds = #{calculatedAmountDiamonds},</if>
            <if test="calculatedAmountUsd != null">calculated_amount_usd = #{calculatedAmountUsd},</if>
            <if test="updateTime != null">updated_at = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommissionPayoutBreakdownsById" parameterType="Long">
        delete from commission_payout_breakdowns where id = #{id}
    </delete>

    <delete id="deleteCommissionPayoutBreakdownsByIds" parameterType="Long">
        delete from commission_payout_breakdowns where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCommissionPayoutBreakdownsByPayoutId" parameterType="Long">
        delete from commission_payout_breakdowns where payout_id = #{payoutId}
    </delete>

    <delete id="deleteCommissionPayoutBreakdownsByMonth" parameterType="Date">
        delete from commission_payout_breakdowns 
        where date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
    </delete>

</mapper> 