<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionRecruitmentBonusRulesMapper">
    
    <resultMap type="CommissionRecruitmentBonusRules" id="CommissionRecruitmentBonusRulesResult">
        <result property="id"    column="id"    />
        <result property="minNewRecruits"    column="min_new_recruits"    />
        <result property="bonusUsd"    column="bonus_usd"    />
        <result property="isActive"    column="is_active"    />
    </resultMap>

    <sql id="selectCommissionRecruitmentBonusRulesVo">
        select id, min_new_recruits, bonus_usd, is_active 
        from commission_recruitment_bonus_rules
    </sql>

    <select id="selectCommissionRecruitmentBonusRulesList" parameterType="CommissionRecruitmentBonusRules" resultMap="CommissionRecruitmentBonusRulesResult">
        <include refid="selectCommissionRecruitmentBonusRulesVo"/>
        <where>  
            <if test="minNewRecruits != null">
                and min_new_recruits = #{minNewRecruits}
            </if>
            <if test="isActive != null">
                and is_active = #{isActive}
            </if>
        </where>
        order by min_new_recruits asc
    </select>

    <select id="selectActiveCommissionRecruitmentBonusRules" resultMap="CommissionRecruitmentBonusRulesResult">
        <include refid="selectCommissionRecruitmentBonusRulesVo"/>
        where is_active = 1
        order by min_new_recruits desc
    </select>
    
    <select id="selectCommissionRecruitmentBonusRulesById" parameterType="Integer" resultMap="CommissionRecruitmentBonusRulesResult">
        <include refid="selectCommissionRecruitmentBonusRulesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCommissionRecruitmentBonusRules" parameterType="CommissionRecruitmentBonusRules" useGeneratedKeys="true" keyProperty="id">
        insert into commission_recruitment_bonus_rules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="minNewRecruits != null">min_new_recruits,</if>
            <if test="bonusUsd != null">bonus_usd,</if>
            <if test="isActive != null">is_active,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="minNewRecruits != null">#{minNewRecruits},</if>
            <if test="bonusUsd != null">#{bonusUsd},</if>
            <if test="isActive != null">#{isActive},</if>
         </trim>
    </insert>

    <update id="updateCommissionRecruitmentBonusRules" parameterType="CommissionRecruitmentBonusRules">
        update commission_recruitment_bonus_rules
        <trim prefix="SET" suffixOverrides=",">
            <if test="minNewRecruits != null">min_new_recruits = #{minNewRecruits},</if>
            <if test="bonusUsd != null">bonus_usd = #{bonusUsd},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommissionRecruitmentBonusRulesById" parameterType="Integer">
        delete from commission_recruitment_bonus_rules where id = #{id}
    </delete>

    <delete id="deleteCommissionRecruitmentBonusRulesByIds" parameterType="Integer">
        delete from commission_recruitment_bonus_rules where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 