<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionMonthlySummaryMapper">
    
    <resultMap type="CommissionMonthlySummary" id="CommissionMonthlySummaryResult">
        <result property="dataMonth"    column="data_month"    />
        <result property="budgetUsd"    column="budget_usd"    />
        <result property="payoutCapUsd"    column="payout_cap_usd"    />
        <result property="actualPayoutUsd"    column="actual_payout_usd"    />
        <result property="payoutToBudgetRatio"    column="payout_to_budget_ratio"    />
        <result property="exceededAmountUsd"    column="exceeded_amount_usd"    />
        <result property="totalBonusEstimated"    column="total_bonus_estimated"    />
        <result property="calculationStatus"    column="calculation_status"    />
        <result property="calculatedAt"    column="calculated_at"    />
    </resultMap>

    <sql id="selectCommissionMonthlySummaryVo">
        select data_month, budget_usd, payout_cap_usd, actual_payout_usd, 
               payout_to_budget_ratio, exceeded_amount_usd, total_bonus_estimated, 
               calculation_status, calculated_at 
        from commission_monthly_summary
    </sql>

    <select id="selectCommissionMonthlySummaryList" parameterType="CommissionMonthlySummary" resultMap="CommissionMonthlySummaryResult">
        <include refid="selectCommissionMonthlySummaryVo"/>
        <where>  
            <if test="dataMonth != null">
                and date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
            </if>
            <if test="calculationStatus != null and calculationStatus != ''">
                and calculation_status = #{calculationStatus}
            </if>
            <if test="budgetUsd != null">
                and budget_usd = #{budgetUsd}
            </if>
        </where>
        order by data_month desc
    </select>

    <select id="selectTotalBonusEstimatedByMonth" parameterType="Date" resultType="BigDecimal">
        SELECT COALESCE(SUM(bonus_estimated), 0) as total_bonus_estimated
        FROM monthly_performance 
        WHERE date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
    </select>

    <select id="selectCommissionMonthlySummaryByMonth" parameterType="Date" resultMap="CommissionMonthlySummaryResult">
        <include refid="selectCommissionMonthlySummaryVo"/>
        where date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
        order by calculated_at desc
        limit 1
    </select>
        
    <insert id="insertCommissionMonthlySummary" parameterType="CommissionMonthlySummary">
        insert into commission_monthly_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">data_month,</if>
            <if test="budgetUsd != null">budget_usd,</if>
            <if test="payoutCapUsd != null">payout_cap_usd,</if>
            <if test="actualPayoutUsd != null">actual_payout_usd,</if>
            <if test="payoutToBudgetRatio != null">payout_to_budget_ratio,</if>
            <if test="exceededAmountUsd != null">exceeded_amount_usd,</if>
            <if test="totalBonusEstimated != null">total_bonus_estimated,</if>
            <if test="calculationStatus != null">calculation_status,</if>
            calculated_at,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="budgetUsd != null">#{budgetUsd},</if>
            <if test="payoutCapUsd != null">#{payoutCapUsd},</if>
            <if test="actualPayoutUsd != null">#{actualPayoutUsd},</if>
            <if test="payoutToBudgetRatio != null">#{payoutToBudgetRatio},</if>
            <if test="exceededAmountUsd != null">#{exceededAmountUsd},</if>
            <if test="totalBonusEstimated != null">#{totalBonusEstimated},</if>
            <if test="calculationStatus != null">#{calculationStatus},</if>
            CURRENT_TIMESTAMP,
         </trim>
    </insert>

    <update id="updateCommissionMonthlySummary" parameterType="CommissionMonthlySummary">
        update commission_monthly_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="budgetUsd != null">budget_usd = #{budgetUsd},</if>
            <if test="payoutCapUsd != null">payout_cap_usd = #{payoutCapUsd},</if>
            <if test="actualPayoutUsd != null">actual_payout_usd = #{actualPayoutUsd},</if>
            <if test="payoutToBudgetRatio != null">payout_to_budget_ratio = #{payoutToBudgetRatio},</if>
            <if test="exceededAmountUsd != null">exceeded_amount_usd = #{exceededAmountUsd},</if>
            <if test="totalBonusEstimated != null">total_bonus_estimated = #{totalBonusEstimated},</if>
            <if test="calculationStatus != null">calculation_status = #{calculationStatus},</if>
            calculated_at = CURRENT_TIMESTAMP,
        </trim>
        where data_month = #{dataMonth}
    </update>

    <delete id="deleteCommissionMonthlySummaryByMonth" parameterType="Date">
        delete from commission_monthly_summary where data_month = #{dataMonth}
    </delete>

    <delete id="deleteCommissionMonthlySummaryByMonths" parameterType="Date">
        delete from commission_monthly_summary where data_month in 
        <foreach item="dataMonth" collection="array" open="(" separator="," close=")">
            #{dataMonth}
        </foreach>
    </delete>

</mapper> 