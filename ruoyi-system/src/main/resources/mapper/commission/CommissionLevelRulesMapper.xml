<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionLevelRulesMapper">
    
    <resultMap type="CommissionLevelRules" id="CommissionLevelRulesResult">
        <result property="id"    column="id"    />
        <result property="levelName"    column="level_name"    />
        <result property="minQualifiedRecruits"    column="min_qualified_recruits"    />
        <result property="commissionRate"    column="commission_rate"    />
        <result property="payoutDepth"    column="payout_depth"    />
        <result property="displayOrder"    column="display_order"    />
        <result property="isActive"    column="is_active"    />
    </resultMap>

    <sql id="selectCommissionLevelRulesVo">
        select id, level_name, min_qualified_recruits, commission_rate, payout_depth, display_order, is_active
        from commission_level_rules
    </sql>

    <select id="selectCommissionLevelRulesList" parameterType="CommissionLevelRules" resultMap="CommissionLevelRulesResult">
        <include refid="selectCommissionLevelRulesVo"/>
        <where>
            <if test="levelName != null and levelName != ''">
                and level_name like concat('%', #{levelName}, '%')
            </if>
            <if test="minQualifiedRecruits != null">
                and min_qualified_recruits = #{minQualifiedRecruits}
            </if>
            <if test="isActive != null">
                and is_active = #{isActive}
            </if>
        </where>
        order by display_order asc, id asc
    </select>

    <select id="selectActiveCommissionLevelRules" resultMap="CommissionLevelRulesResult">
        <include refid="selectCommissionLevelRulesVo"/>
        where is_active = 1
        order by min_qualified_recruits desc, id asc
    </select>
    
    <select id="selectCommissionLevelRulesById" parameterType="Integer" resultMap="CommissionLevelRulesResult">
        <include refid="selectCommissionLevelRulesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCommissionLevelRules" parameterType="CommissionLevelRules" useGeneratedKeys="true" keyProperty="id">
        insert into commission_level_rules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="levelName != null and levelName != ''">level_name,</if>
            <if test="minQualifiedRecruits != null">min_qualified_recruits,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="payoutDepth != null">payout_depth,</if>
            <if test="displayOrder != null">display_order,</if>
            <if test="isActive != null">is_active,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="levelName != null and levelName != ''">#{levelName},</if>
            <if test="minQualifiedRecruits != null">#{minQualifiedRecruits},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="payoutDepth != null">#{payoutDepth},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
            <if test="isActive != null">#{isActive},</if>
         </trim>
    </insert>

    <update id="updateCommissionLevelRules" parameterType="CommissionLevelRules">
        update commission_level_rules
        <trim prefix="SET" suffixOverrides=",">
            <if test="levelName != null and levelName != ''">level_name = #{levelName},</if>
            <if test="minQualifiedRecruits != null">min_qualified_recruits = #{minQualifiedRecruits},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="payoutDepth != null">payout_depth = #{payoutDepth},</if>
            <if test="displayOrder != null">display_order = #{displayOrder},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommissionLevelRulesById" parameterType="Integer">
        delete from commission_level_rules where id = #{id}
    </delete>

    <delete id="deleteCommissionLevelRulesByIds" parameterType="Integer">
        delete from commission_level_rules where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 