<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionSettingsMapper">
    
    <resultMap type="CommissionSettings" id="CommissionSettingsResult">
        <result property="settingKey"    column="setting_key"    />
        <result property="settingValue"    column="setting_value"    />
        <result property="description"    column="description"    />
        <result property="updateTime"    column="updated_at"    />
    </resultMap>

    <sql id="selectCommissionSettingsVo">
        select setting_key, setting_value, description, updated_at from commission_settings
    </sql>

    <select id="selectCommissionSettingsList" parameterType="CommissionSettings" resultMap="CommissionSettingsResult">
        <include refid="selectCommissionSettingsVo"/>
        <where>  
            <if test="settingKey != null and settingKey != ''">
                and setting_key like concat('%', #{settingKey}, '%')
            </if>
            <if test="settingValue != null and settingValue != ''">
                and setting_value like concat('%', #{settingValue}, '%')
            </if>
            <if test="description != null and description != ''">
                and description like concat('%', #{description}, '%')
            </if>
        </where>
        order by updated_at desc
    </select>
    
    <select id="selectCommissionSettingsByKey" parameterType="String" resultMap="CommissionSettingsResult">
        <include refid="selectCommissionSettingsVo"/>
        where setting_key = #{settingKey}
    </select>
        
    <insert id="insertCommissionSettings" parameterType="CommissionSettings">
        insert into commission_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="settingKey != null and settingKey != ''">setting_key,</if>
            <if test="settingValue != null and settingValue != ''">setting_value,</if>
            <if test="description != null">description,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="settingKey != null and settingKey != ''">#{settingKey},</if>
            <if test="settingValue != null and settingValue != ''">#{settingValue},</if>
            <if test="description != null">#{description},</if>
         </trim>
    </insert>

    <update id="updateCommissionSettings" parameterType="CommissionSettings">
        update commission_settings
        <trim prefix="SET" suffixOverrides=",">
            <if test="settingValue != null and settingValue != ''">setting_value = #{settingValue},</if>
            <if test="description != null">description = #{description},</if>
            updated_at = CURRENT_TIMESTAMP,
        </trim>
        where setting_key = #{settingKey}
    </update>

    <delete id="deleteCommissionSettingsByKey" parameterType="String">
        delete from commission_settings where setting_key = #{settingKey}
    </delete>

    <delete id="deleteCommissionSettingsByKeys" parameterType="String">
        delete from commission_settings where setting_key in 
        <foreach item="settingKey" collection="array" open="(" separator="," close=")">
            #{settingKey}
        </foreach>
    </delete>

</mapper> 