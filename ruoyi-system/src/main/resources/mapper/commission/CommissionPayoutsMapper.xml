<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.commission.CommissionPayoutsMapper">
    
    <resultMap type="CommissionPayouts" id="CommissionPayoutsResult">
        <result property="id"    column="id"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="dataMonth"    column="data_month"    />
        <result property="distributorLevel"    column="distributor_level"    />
        <result property="finalPayoutUsd"    column="final_payout_usd"    />
        <result property="createTime"    column="created_at"    />
    </resultMap>

    <resultMap type="CommissionPayouts" id="CommissionPayoutsWithCreatorResult" extends="CommissionPayoutsResult">
        <result property="creatorNickname"    column="creator_nickname"    />
        <result property="creatorHandle"    column="creator_handle"    />
        <result property="personalDiamonds"    column="personal_diamonds"    />
        <result property="teamDiamonds"    column="team_diamonds"    />
        <result property="newRecruitsCount"    column="new_recruits_count"    />
    </resultMap>

    <sql id="selectCommissionPayoutsVo">
        select id, creator_id, data_month, distributor_level, final_payout_usd, created_at
        from commission_payouts
    </sql>

    <sql id="selectCommissionPayoutsWithCreatorVo">
        select p.id, p.creator_id, p.data_month, p.distributor_level, p.final_payout_usd, p.created_at,
               c.nickname as creator_nickname, c.handle as creator_handle,
               coalesce(mp.diamonds, 0) as personal_diamonds, 
               coalesce(level_breakdown.base_amount_diamonds - coalesce(mp.diamonds, 0), 0) as team_diamonds, 
               coalesce(recruit_breakdown.base_amount_diamonds, 0) as new_recruits_count
        from commission_payouts p
        left join creators c on p.creator_id = c.id
        left join monthly_performance mp on p.creator_id = mp.creator_id and p.data_month = mp.data_month
        left join (
            select payout_id, creator_id, data_month, base_amount_diamonds
            from commission_payout_breakdowns 
            where source_type = 'LEVEL_COMMISSION'
        ) level_breakdown on p.id = level_breakdown.payout_id
        left join (
            select payout_id, creator_id, data_month, base_amount_diamonds
            from commission_payout_breakdowns 
            where source_type = 'RECRUITMENT_BONUS'
        ) recruit_breakdown on p.id = recruit_breakdown.payout_id
    </sql>

    <select id="selectCommissionPayoutsById" parameterType="Long" resultMap="CommissionPayoutsResult">
        <include refid="selectCommissionPayoutsVo"/>
        where id = #{id}
    </select>

    <select id="selectCommissionPayoutsByCreatorAndMonth" resultMap="CommissionPayoutsWithCreatorResult">
        <include refid="selectCommissionPayoutsWithCreatorVo"/>
        where p.creator_id = #{creatorId} 
        and date_format(p.data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
        order by p.created_at desc
        limit 1
    </select>

    <select id="selectCommissionPayoutsList" parameterType="CommissionPayouts" resultMap="CommissionPayoutsResult">
        <include refid="selectCommissionPayoutsVo"/>
        <where>  
            <if test="creatorId != null">
                and creator_id = #{creatorId}
            </if>
            <if test="dataMonth != null">
                and date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
            </if>
            <if test="distributorLevel != null and distributorLevel != ''">
                and distributor_level = #{distributorLevel}
            </if>
            <if test="finalPayoutUsd != null">
                and final_payout_usd = #{finalPayoutUsd}
            </if>
        </where>
        order by data_month desc, final_payout_usd desc
    </select>

    <select id="selectCommissionPayoutsListWithCreator" parameterType="CommissionPayouts" resultMap="CommissionPayoutsWithCreatorResult">
        <include refid="selectCommissionPayoutsWithCreatorVo"/>
        <where>  
            <if test="creatorId != null">
                and p.creator_id = #{creatorId}
            </if>
            <if test="dataMonth != null">
                and date_format(p.data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
            </if>
            <if test="distributorLevel != null and distributorLevel != ''">
                and p.distributor_level = #{distributorLevel}
            </if>
            <if test="finalPayoutUsd != null">
                and p.final_payout_usd = #{finalPayoutUsd}
            </if>
            <if test="creatorNickname != null and creatorNickname != ''">
                and c.nickname like concat('%', #{creatorNickname}, '%')
            </if>
        </where>
        order by p.data_month desc, p.final_payout_usd desc
    </select>

    <select id="selectCommissionPayoutsByMonth" parameterType="Date" resultMap="CommissionPayoutsResult">
        <include refid="selectCommissionPayoutsVo"/>
        where date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
        order by final_payout_usd desc
    </select>
        
    <insert id="insertCommissionPayouts" parameterType="CommissionPayouts" useGeneratedKeys="true" keyProperty="id">
        insert into commission_payouts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">creator_id,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="distributorLevel != null">distributor_level,</if>
            <if test="finalPayoutUsd != null">final_payout_usd,</if>
            created_at,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">#{creatorId},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="distributorLevel != null">#{distributorLevel},</if>
            <if test="finalPayoutUsd != null">#{finalPayoutUsd},</if>
            CURRENT_TIMESTAMP,
         </trim>
    </insert>

    <!-- 新增插入或更新方法，处理重复记录 -->
    <insert id="insertOrUpdateCommissionPayouts" parameterType="CommissionPayouts" useGeneratedKeys="true" keyProperty="id">
        insert into commission_payouts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">creator_id,</if>
            <if test="dataMonth != null">data_month,</if>
            <if test="distributorLevel != null">distributor_level,</if>
            <if test="finalPayoutUsd != null">final_payout_usd,</if>
            created_at,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="creatorId != null">#{creatorId},</if>
            <if test="dataMonth != null">#{dataMonth},</if>
            <if test="distributorLevel != null">#{distributorLevel},</if>
            <if test="finalPayoutUsd != null">#{finalPayoutUsd},</if>
            CURRENT_TIMESTAMP,
         </trim>
        ON DUPLICATE KEY UPDATE 
            distributor_level = VALUES(distributor_level),
            final_payout_usd = VALUES(final_payout_usd),
            created_at = CURRENT_TIMESTAMP
    </insert>

    <update id="updateCommissionPayouts" parameterType="CommissionPayouts">
        update commission_payouts
        <trim prefix="SET" suffixOverrides=",">
            <if test="distributorLevel != null">distributor_level = #{distributorLevel},</if>
            <if test="finalPayoutUsd != null">final_payout_usd = #{finalPayoutUsd},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommissionPayoutsById" parameterType="Long">
        delete from commission_payouts where id = #{id}
    </delete>

    <delete id="deleteCommissionPayoutsByIds" parameterType="Long">
        delete from commission_payouts where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCommissionPayoutsByMonth" parameterType="Date">
        delete from commission_payouts
        where date_format(data_month,'%Y-%m') = date_format(#{dataMonth},'%Y-%m')
    </delete>

    <select id="selectCommissionPayoutsByCreatorAndMonthRange" resultMap="CommissionPayoutsResult">
        <include refid="selectCommissionPayoutsVo"/>
        where creator_id = #{creatorId}
        and data_month between #{startMonth} and #{endMonth}
        order by data_month asc
    </select>

    <select id="selectTotalIncomeByCreatorAndYear" resultType="java.math.BigDecimal">
        select coalesce(sum(final_payout_usd), 0)
        from commission_payouts
        where creator_id = #{creatorId}
        and year(data_month) = #{year}
        and data_month &lt;= #{endMonth}
    </select>

</mapper>