package com.ruoyi.system.mapper.commission;

import java.util.List;
import java.util.Date;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.commission.CommissionDistributorQualifications;

/**
 * 分销员资格记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface CommissionDistributorQualificationsMapper 
{
    /**
     * 查询分销员资格记录列表
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 分销员资格记录集合
     */
    public List<CommissionDistributorQualifications> selectCommissionDistributorQualificationsList(CommissionDistributorQualifications commissionDistributorQualifications);

    /**
     * 根据分销员ID和月份查询资格记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员资格记录
     */
    public CommissionDistributorQualifications selectCommissionDistributorQualificationsByDistributor(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据月份查询所有分销员资格记录
     * 
     * @param dataMonth 数据月份
     * @return 分销员资格记录集合
     */
    public List<CommissionDistributorQualifications> selectCommissionDistributorQualificationsByMonth(Date dataMonth);

    /**
     * 根据等级查询合格分销员
     * 
     * @param dataMonth 数据月份
     * @param achievedLevel 达成等级
     * @return 分销员资格记录集合
     */
    public List<CommissionDistributorQualifications> selectQualifiedDistributorsByLevel(@Param("dataMonth") Date dataMonth, @Param("achievedLevel") String achievedLevel);

    /**
     * 检查分销员资格记录是否存在
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 存在数量
     */
    public int checkCommissionDistributorQualificationExists(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 新增分销员资格记录
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    public int insertCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications);

    /**
     * 修改分销员资格记录
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    public int updateCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications);

    /**
     * 删除分销员资格记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionDistributorQualificationsByCreatorAndMonth(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 批量删除分销员资格记录
     * 
     * @param creatorIds 需要删除的分销员ID集合
     * @return 结果
     */
    public int deleteCommissionDistributorQualificationsByCreatorIds(Long[] creatorIds);
}
