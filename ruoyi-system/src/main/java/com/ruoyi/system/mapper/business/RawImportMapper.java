package com.ruoyi.system.mapper.business;

import java.util.List;
import com.ruoyi.system.domain.business.RawImport;

/**
 * 原始Excel数据导入记录Mapper接口
 * 对应表: raw_imports
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface RawImportMapper
{
    /**
     * 查询原始Excel数据导入记录
     *
     * @param id 原始Excel数据导入记录主键
     * @return 原始Excel数据导入记录
     */
    public RawImport selectRawImportById(Long id);

    /**
     * 查询原始Excel数据导入记录列表
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 原始Excel数据导入记录集合
     */
    public List<RawImport> selectRawImportList(RawImport rawImport);

    /**
     * 新增原始Excel数据导入记录
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 结果
     */
    public int insertRawImport(RawImport rawImport);

    /**
     * 批量新增原始Excel数据导入记录
     *
     * @param rawImportList 原始Excel数据导入记录列表
     * @return 结果
     */
    public int batchInsertRawImport(List<RawImport> rawImportList);

    /**
     * 修改原始Excel数据导入记录
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 结果
     */
    public int updateRawImport(RawImport rawImport);

    /**
     * 删除原始Excel数据导入记录
     *
     * @param id 原始Excel数据导入记录主键
     * @return 结果
     */
    public int deleteRawImportById(Long id);

    /**
     * 批量删除原始Excel数据导入记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRawImportByIds(Long[] ids);
}
