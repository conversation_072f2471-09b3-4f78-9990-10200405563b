package com.ruoyi.system.mapper.commission;

import java.util.List;
import com.ruoyi.system.domain.commission.CommissionLevelRules;

/**
 * 分销员等级规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface CommissionLevelRulesMapper 
{
    /**
     * 查询分销员等级规则
     * 
     * @param id 分销员等级规则主键
     * @return 分销员等级规则
     */
    public CommissionLevelRules selectCommissionLevelRulesById(Integer id);

    /**
     * 查询分销员等级规则列表
     * 
     * @param commissionLevelRules 分销员等级规则
     * @return 分销员等级规则集合
     */
    public List<CommissionLevelRules> selectCommissionLevelRulesList(CommissionLevelRules commissionLevelRules);

    /**
     * 新增分销员等级规则
     * 
     * @param commissionLevelRules 分销员等级规则
     * @return 结果
     */
    public int insertCommissionLevelRules(CommissionLevelRules commissionLevelRules);

    /**
     * 修改分销员等级规则
     * 
     * @param commissionLevelRules 分销员等级规则
     * @return 结果
     */
    public int updateCommissionLevelRules(CommissionLevelRules commissionLevelRules);

    /**
     * 删除分销员等级规则
     * 
     * @param id 分销员等级规则主键
     * @return 结果
     */
    public int deleteCommissionLevelRulesById(Integer id);

    /**
     * 批量删除分销员等级规则
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommissionLevelRulesByIds(Integer[] ids);

    /**
     * 查询启用的分销员等级规则列表
     * 
     * @return 启用的分销员等级规则集合
     */
    public List<CommissionLevelRules> selectActiveCommissionLevelRules();
} 