package com.ruoyi.system.mapper.business;

import java.util.List;
import java.util.Date;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.business.TeamEvents;

/**
 * 团队动态与成就事件日志Mapper接口
 * 对应表: team_events
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface TeamEventsMapper
{
    /**
     * 查询团队动态与成就事件日志
     *
     * @param id 团队动态与成就事件日志主键
     * @return 团队动态与成就事件日志
     */
    public TeamEvents selectTeamEventsById(Long id);

    /**
     * 查询团队动态与成就事件日志列表
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 团队动态与成就事件日志集合
     */
    public List<TeamEvents> selectTeamEventsList(TeamEvents teamEvents);

    /**
     * 根据关联人ID和时间范围查询团队事件
     *
     * @param actorCreatorId 事件关联人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 团队事件列表
     */
    public List<TeamEvents> selectTeamEventsByActorAndTimeRange(@Param("actorCreatorId") Long actorCreatorId, 
                                                                @Param("startTime") Date startTime, 
                                                                @Param("endTime") Date endTime);

    /**
     * 根据事件类型和时间范围查询团队事件
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 团队事件列表
     */
    public List<TeamEvents> selectTeamEventsByTypeAndTimeRange(@Param("eventType") String eventType, 
                                                               @Param("startTime") Date startTime, 
                                                               @Param("endTime") Date endTime);

    /**
     * 新增团队动态与成就事件日志
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 结果
     */
    public int insertTeamEvents(TeamEvents teamEvents);

    /**
     * 批量新增团队动态与成就事件日志
     *
     * @param teamEventsList 团队动态与成就事件日志列表
     * @return 结果
     */
    public int insertTeamEventsBatch(List<TeamEvents> teamEventsList);

    /**
     * 修改团队动态与成就事件日志
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 结果
     */
    public int updateTeamEvents(TeamEvents teamEvents);

    /**
     * 删除团队动态与成就事件日志
     *
     * @param id 团队动态与成就事件日志主键
     * @return 结果
     */
    public int deleteTeamEventsById(Long id);

    /**
     * 批量删除团队动态与成就事件日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTeamEventsByIds(Long[] ids);

    /**
     * 根据关联人ID删除团队事件
     *
     * @param actorCreatorId 事件关联人ID
     * @return 结果
     */
    public int deleteTeamEventsByActorCreatorId(Long actorCreatorId);

    /**
     * 根据时间范围删除团队事件（性能优化版本）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 删除的记录数
     */
    public int deleteTeamEventsByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
