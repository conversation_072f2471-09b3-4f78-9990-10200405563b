package com.ruoyi.system.mapper.commission;

import java.util.List;
import com.ruoyi.system.domain.commission.CommissionSettings;

/**
 * 全局佣金配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface CommissionSettingsMapper 
{
    /**
     * 查询全局佣金配置
     * 
     * @param settingKey 全局佣金配置主键
     * @return 全局佣金配置
     */
    public CommissionSettings selectCommissionSettingsByKey(String settingKey);

    /**
     * 查询全局佣金配置列表
     * 
     * @param commissionSettings 全局佣金配置
     * @return 全局佣金配置集合
     */
    public List<CommissionSettings> selectCommissionSettingsList(CommissionSettings commissionSettings);

    /**
     * 新增全局佣金配置
     * 
     * @param commissionSettings 全局佣金配置
     * @return 结果
     */
    public int insertCommissionSettings(CommissionSettings commissionSettings);

    /**
     * 修改全局佣金配置
     * 
     * @param commissionSettings 全局佣金配置
     * @return 结果
     */
    public int updateCommissionSettings(CommissionSettings commissionSettings);

    /**
     * 删除全局佣金配置
     * 
     * @param settingKey 全局佣金配置主键
     * @return 结果
     */
    public int deleteCommissionSettingsByKey(String settingKey);

    /**
     * 批量删除全局佣金配置
     * 
     * @param settingKeys 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommissionSettingsByKeys(String[] settingKeys);
} 