package com.ruoyi.system.mapper.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionDynamicThresholds;
import org.apache.ibatis.annotations.Param;

/**
 * 分销员动态门槛计算记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface CommissionDynamicThresholdsMapper 
{
    /**
     * 查询分销员动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员动态门槛计算记录
     */
    public CommissionDynamicThresholds selectCommissionDynamicThresholdsByCreatorAndMonth(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 查询分销员动态门槛计算记录列表
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 分销员动态门槛计算记录集合
     */
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsList(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 根据数据月份查询动态门槛记录列表
     * 
     * @param dataMonth 数据月份
     * @return 分销员动态门槛计算记录集合
     */
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsByMonth(Date dataMonth);

    /**
     * 根据分销员ID查询历史动态门槛记录
     * 
     * @param creatorId 分销员ID
     * @return 分销员动态门槛计算记录集合
     */
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsByCreator(Long creatorId);

    /**
     * 查询指定月份门槛变化统计
     * 
     * @param dataMonth 数据月份
     * @return 门槛变化统计信息
     */
    public List<CommissionDynamicThresholds> selectThresholdChangeStatsByMonth(Date dataMonth);

    /**
     * 新增分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    public int insertCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 批量新增分销员动态门槛计算记录
     * 
     * @param thresholdsList 分销员动态门槛计算记录列表
     * @return 结果
     */
    public int batchInsertCommissionDynamicThresholds(@Param("list") List<CommissionDynamicThresholds> thresholdsList);

    /**
     * 插入或更新分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    public int insertOrUpdateCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 修改分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    public int updateCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 删除分销员动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionDynamicThresholdsByCreatorAndMonth(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 根据分销员ID删除动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @return 结果
     */
    public int deleteCommissionDynamicThresholdsByCreator(Long creatorId);

    /**
     * 根据数据月份删除动态门槛计算记录
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionDynamicThresholdsByMonth(Date dataMonth);

    /**
     * 检查指定分销员和月份的记录是否存在
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 是否存在 (count > 0)
     */
    public int checkCommissionDynamicThresholdExists(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 清理指定日期之前的历史门槛记录
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    public int deleteCommissionDynamicThresholdsBeforeDate(@Param("beforeDate") Date beforeDate);
} 