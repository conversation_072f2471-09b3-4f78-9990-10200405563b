package com.ruoyi.system.mapper.business;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.business.Creator;
import org.apache.ibatis.annotations.Param;

/**
 * 主播信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface CreatorMapper
{
    /**
     * 查询主播信息
     *
     * @param id 主播信息主键
     * @return 主播信息
     */
    public Creator selectCreatorById(Long id);

    /**
     * 查询主播信息列表
     *
     * @param creator 主播信息
     * @return 主播信息集合
     */
    public List<Creator> selectCreatorList(Creator creator);

    /**
     * 新增主播信息
     *
     * @param creator 主播信息
     * @return 结果
     */
    public int insertCreator(Creator creator);

    /**
     * 修改主播信息
     *
     * @param creator 主播信息
     * @return 结果
     */
    public int updateCreator(Creator creator);

    /**
     * 删除主播信息
     *
     * @param id 主播信息主键
     * @return 结果
     */
    public int deleteCreatorById(Long id);

    /**
     * 批量删除主播信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCreatorByIds(Long[] ids);

    /**
     * 根据Handle查询主播信息
     *
     * @param handle 主播handle
     * @return 主播信息
     */
    public Creator selectCreatorByHandle(String handle);

    /**
     * 查询所有根节点主播（没有父节点的主播）
     * 
     * @return 根节点主播列表
     */
    public List<Creator> selectRootCreators();

    /**
     * 查询指定主播的直属子节点
     * 
     * @param parentId 父节点ID
     * @return 直属子节点列表
     */
    public List<Creator> selectDirectChildren(Long parentId);

    /**
     * 根据条件查询根节点主播（支持按昵称、ID过滤）
     * 
     * @param creatorId 主播ID（可选）
     * @param nickname 主播昵称（可选）
     * @return 根节点主播列表
     */
    public List<Creator> selectRootCreatorsWithFilter(@Param("creatorId") Long creatorId, @Param("nickname") String nickname);
}
