package com.ruoyi.system.mapper.business;

import java.util.List;
import com.ruoyi.system.domain.business.CreatorRelationship;
import org.apache.ibatis.annotations.Param;

/**
 * 主播层级关系Mapper接口
 * 对应表: creator_relationships
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface CreatorRelationshipMapper
{
    /**
     * 查询主播层级关系
     *
     * @param ancestorId 祖先ID
     * @param descendantId 后代ID
     * @return 主播层级关系
     */
    public CreatorRelationship selectCreatorRelationshipById(@Param("ancestorId") Long ancestorId, @Param("descendantId") Long descendantId);

    /**
     * 查询主播层级关系列表
     *
     * @param creatorRelationship 主播层级关系
     * @return 主播层级关系集合
     */
    public List<CreatorRelationship> selectCreatorRelationshipList(CreatorRelationship creatorRelationship);

    /**
     * 新增主播层级关系
     *
     * @param creatorRelationship 主播层级关系
     * @return 结果
     */
    public int insertCreatorRelationship(CreatorRelationship creatorRelationship);

    /**
     * 批量新增主播层级关系
     *
     * @param relationships 关系列表
     * @return 结果
     */
    public int batchInsertCreatorRelationship(List<CreatorRelationship> relationships);

    /**
     * 删除主播层级关系
     *
     * @param ancestorId 祖先ID
     * @param descendantId 后代ID
     * @return 结果
     */
    public int deleteCreatorRelationshipById(@Param("ancestorId") Long ancestorId, @Param("descendantId") Long descendantId);

    /**
     * 根据祖先ID删除关系
     * @param ancestorId 祖先ID
     * @return 结果
     */
    public int deleteCreatorRelationshipByAncestorId(Long ancestorId);

    /**
     * 根据后代ID删除关系
     * @param descendantId 后代ID
     * @return 结果
     */
    public int deleteCreatorRelationshipByDescendantId(Long descendantId);

    /**
     * 清空主播层级关系表
     *
     * @return 结果
     */
    public int deleteAllCreatorRelationships();

    /**
     * 查询指定主播的所有直接下级
     * @param creatorId 主播ID
     * @return 直接下级ID列表
     */
    public List<Long> selectDirectChildrenIds(Long creatorId);

    /**
     * 查询指定主播的所有后代（指定深度）
     * @param ancestorId 祖先ID
     * @param depth 深度
     * @return 后代列表
     */
    public List<CreatorRelationship> selectDescendantsByDepth(@Param("ancestorId") Long ancestorId, @Param("depth") Integer depth);

    /**
     * 查询指定主播的所有祖先（指定深度）
     * @param descendantId 后代ID
     * @param depth 深度
     * @return 祖先列表
     */
    public List<CreatorRelationship> selectAncestorsByDepth(@Param("descendantId") Long descendantId, @Param("depth") Integer depth);
}
