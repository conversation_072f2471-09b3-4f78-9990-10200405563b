package com.ruoyi.system.mapper.commission;

import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import org.apache.ibatis.annotations.Param;

/**
 * 分销员月度实收金额Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface CommissionPayoutsMapper 
{
    /**
     * 查询分销员月度实收金额
     * 
     * @param id 分销员月度实收金额主键
     * @return 分销员月度实收金额
     */
    public CommissionPayouts selectCommissionPayoutsById(Long id);

    /**
     * 根据分销员ID和月份查询实收金额
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员月度实收金额
     */
    public CommissionPayouts selectCommissionPayoutsByCreatorAndMonth(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 查询分销员月度实收金额列表
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 分销员月度实收金额集合
     */
    public List<CommissionPayouts> selectCommissionPayoutsList(CommissionPayouts commissionPayouts);

    /**
     * 查询分销员月度实收金额列表（带主播信息）
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 分销员月度实收金额集合
     */
    public List<CommissionPayouts> selectCommissionPayoutsListWithCreator(CommissionPayouts commissionPayouts);

    /**
     * 查询指定月份的分销员收入统计
     * 
     * @param dataMonth 数据月份
     * @return 分销员月度实收金额集合
     */
    public List<CommissionPayouts> selectCommissionPayoutsByMonth(Date dataMonth);

    /**
     * 新增分销员月度实收金额
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    public int insertCommissionPayouts(CommissionPayouts commissionPayouts);

    /**
     * 新增或更新分销员月度实收金额（处理重复记录）
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    public int insertOrUpdateCommissionPayouts(CommissionPayouts commissionPayouts);

    /**
     * 修改分销员月度实收金额
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    public int updateCommissionPayouts(CommissionPayouts commissionPayouts);

    /**
     * 删除分销员月度实收金额
     * 
     * @param id 分销员月度实收金额主键
     * @return 结果
     */
    public int deleteCommissionPayoutsById(Long id);

    /**
     * 批量删除分销员月度实收金额
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommissionPayoutsByIds(Long[] ids);

    /**
     * 根据月份删除分销员月度实收金额
     *
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionPayoutsByMonth(Date dataMonth);

    /**
     * 查询指定分销员在指定月份范围内的收入数据（用于业绩趋势分析）
     *
     * @param creatorId 分销员ID
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 分销员月度实收金额集合
     */
    public List<CommissionPayouts> selectCommissionPayoutsByCreatorAndMonthRange(@Param("creatorId") Long creatorId, @Param("startMonth") Date startMonth, @Param("endMonth") Date endMonth);

    /**
     * 查询指定分销员在指定年份的累计收入
     *
     * @param creatorId 分销员ID
     * @param year 年份
     * @param endMonth 截止月份
     * @return 累计收入金额
     */
    public BigDecimal selectTotalIncomeByCreatorAndYear(@Param("creatorId") Long creatorId, @Param("year") Integer year, @Param("endMonth") Date endMonth);
} 