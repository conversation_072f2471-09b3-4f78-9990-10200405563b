package com.ruoyi.system.mapper.business;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

/**
 * 个人主仪表盘数据访问层
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DashboardMapper
{
    /**
     * 根据用户ID获取用户昵称
     * 
     * @param creatorId 用户ID
     * @return 用户昵称
     */
    String selectCreatorNickname(@Param("creatorId") Long creatorId);

    /**
     * 获取用户在指定月份的等级
     * 
     * @param creatorId 用户ID
     * @param dataMonth 数据月份
     * @return 用户等级
     */
    String selectUserLevel(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 获取用户截至指定月份的年度累计总收入
     * 
     * @param creatorId 用户ID
     * @param dataMonth 数据月份
     * @return 年度累计总收入
     */
    BigDecimal selectTotalIncomeByYear(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 获取用户指定月份的当月总收入
     * 
     * @param creatorId 用户ID
     * @param dataMonth 数据月份
     * @return 当月总收入
     */
    BigDecimal selectMonthlyIncome(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 获取用户上个月的收入
     * 
     * @param creatorId 用户ID
     * @param dataMonth 数据月份
     * @return 上月收入
     */
    BigDecimal selectPreviousMonthIncome(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 获取用户过去4个月的收入数据（用于计算月均环比增长率）
     * 
     * @param creatorId 用户ID
     * @param dataMonth 数据月份
     * @return 过去4个月的收入数据列表
     */
    List<Map<String, Object>> selectPast4MonthsIncome(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 获取保级目标增长率配置
     * 
     * @return 保级目标增长率
     */
    BigDecimal selectRetentionTargetGrowthRate();

    /**
     * 获取用户个人收入（基于source_type分类）
     * 
     * @param creatorId 用户ID
     * @param dataMonth 数据月份
     * @return 个人收入
     */
    BigDecimal selectPersonalIncome(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 获取用户团队收入（基于source_type分类）
     * 
     * @param creatorId 用户ID
     * @param dataMonth 数据月份
     * @return 团队收入
     */
    BigDecimal selectTeamIncome(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 获取用户团队总人数
     * 
     * @param creatorId 用户ID
     * @return 团队总人数
     */
    Integer selectTotalTeamMembers(@Param("creatorId") Long creatorId);
}
