package com.ruoyi.system.mapper.commission;

import java.util.List;
import com.ruoyi.system.domain.commission.CommissionMultilevelRules;

/**
 * 多级提成规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface CommissionMultilevelRulesMapper 
{
    /**
     * 查询多级提成规则
     * 
     * @param id 多级提成规则主键
     * @return 多级提成规则
     */
    public CommissionMultilevelRules selectCommissionMultilevelRulesById(Integer id);

    /**
     * 查询多级提成规则列表
     * 
     * @param commissionMultilevelRules 多级提成规则
     * @return 多级提成规则集合
     */
    public List<CommissionMultilevelRules> selectCommissionMultilevelRulesList(CommissionMultilevelRules commissionMultilevelRules);

    /**
     * 查询启用的多级提成规则列表
     * 
     * @return 多级提成规则集合
     */
    public List<CommissionMultilevelRules> selectActiveCommissionMultilevelRules();

    /**
     * 新增多级提成规则
     * 
     * @param commissionMultilevelRules 多级提成规则
     * @return 结果
     */
    public int insertCommissionMultilevelRules(CommissionMultilevelRules commissionMultilevelRules);

    /**
     * 修改多级提成规则
     * 
     * @param commissionMultilevelRules 多级提成规则
     * @return 结果
     */
    public int updateCommissionMultilevelRules(CommissionMultilevelRules commissionMultilevelRules);

    /**
     * 删除多级提成规则
     * 
     * @param id 多级提成规则主键
     * @return 结果
     */
    public int deleteCommissionMultilevelRulesById(Integer id);

    /**
     * 批量删除多级提成规则
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommissionMultilevelRulesByIds(Integer[] ids);
} 