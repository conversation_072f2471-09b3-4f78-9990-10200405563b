package com.ruoyi.system.mapper.business;

import java.util.Date;
import java.util.List;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.vo.MonthlyPerformanceVO;
import org.apache.ibatis.annotations.Param;

/**
 * 主播月度业绩Mapper接口
 * 对应表: monthly_performance
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface MonthlyPerformanceMapper
{
    /**
     * 查询主播月度业绩
     *
     * @param id 主播月度业绩主键
     * @return 主播月度业绩
     */
    public MonthlyPerformance selectMonthlyPerformanceById(Integer id);

    /**
     * 查询主播月度业绩列表
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 主播月度业绩集合
     */
    public List<MonthlyPerformance> selectMonthlyPerformanceList(MonthlyPerformance monthlyPerformance);

    /**
     * 查询主播月度业绩列表（关联查询主播信息）
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 主播月度业绩VO集合（包含主播信息）
     */
    public List<MonthlyPerformanceVO> selectMonthlyPerformanceWithCreatorList(MonthlyPerformance monthlyPerformance);

    /**
     * 根据主播ID和数据月份查询主播月度业绩
     *
     * @param creatorId 主播ID
     * @param dataMonth 数据月份
     * @return 主播月度业绩
     */
    public MonthlyPerformance selectMonthlyPerformanceByCreatorAndMonth(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);

    /**
     * 新增主播月度业绩
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 结果
     */
    public int insertMonthlyPerformance(MonthlyPerformance monthlyPerformance);

    /**
     * 修改主播月度业绩
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 结果
     */
    public int updateMonthlyPerformance(MonthlyPerformance monthlyPerformance);

    /**
     * 删除主播月度业绩
     *
     * @param id 主播月度业绩主键
     * @return 结果
     */
    public int deleteMonthlyPerformanceById(Integer id);

    /**
     * 批量删除主播月度业绩
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonthlyPerformanceByIds(Integer[] ids);

    /**
     * 查询在指定月份有数据的主播ID列表
     *
     * @param dataMonth 数据月份
     * @return 主播ID列表
     */
    public List<Long> selectCreatorIdsWithDataByMonth(@Param("dataMonth") Date dataMonth);

    /**
     * 批量查询指定创作者在指定月份的业绩数据
     *
     * @param creatorIds 创作者ID列表
     * @param dataMonth 数据月份
     * @return 主播月度业绩集合
     */
    public List<MonthlyPerformance> selectMonthlyPerformanceByCreatorIdsAndMonth(@Param("creatorIds") List<Long> creatorIds, @Param("dataMonth") Date dataMonth);
}
