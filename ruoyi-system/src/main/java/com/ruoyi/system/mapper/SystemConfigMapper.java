package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SystemConfig;

/**
 * 全局系统配置Mapper接口
 *
 * <AUTHOR>
 */
public interface SystemConfigMapper
{
    /**
     * 查询全局系统配置列表
     *
     * @param systemConfig 全局系统配置
     * @return 全局系统配置集合
     */
    public List<SystemConfig> selectSystemConfigList(SystemConfig systemConfig);

    /**
     * 根据配置键查询全局系统配置
     *
     * @param configKey 配置键
     * @return 全局系统配置
     */
    public SystemConfig selectSystemConfigByKey(String configKey);

    /**
     * 修改全局系统配置
     *
     * @param systemConfig 全局系统配置
     * @return 结果
     */
    public int updateSystemConfig(SystemConfig systemConfig);

}
