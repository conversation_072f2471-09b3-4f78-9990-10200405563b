package com.ruoyi.system.mapper.commission;

import java.util.List;
import com.ruoyi.system.domain.commission.CommissionRecruitmentBonusRules;

/**
 * 拉新奖励规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface CommissionRecruitmentBonusRulesMapper 
{
    /**
     * 查询拉新奖励规则
     * 
     * @param id 拉新奖励规则主键
     * @return 拉新奖励规则
     */
    public CommissionRecruitmentBonusRules selectCommissionRecruitmentBonusRulesById(Integer id);

    /**
     * 查询拉新奖励规则列表
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 拉新奖励规则集合
     */
    public List<CommissionRecruitmentBonusRules> selectCommissionRecruitmentBonusRulesList(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules);

    /**
     * 查询启用的拉新奖励规则列表
     * 
     * @return 拉新奖励规则集合
     */
    public List<CommissionRecruitmentBonusRules> selectActiveCommissionRecruitmentBonusRules();

    /**
     * 新增拉新奖励规则
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 结果
     */
    public int insertCommissionRecruitmentBonusRules(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules);

    /**
     * 修改拉新奖励规则
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 结果
     */
    public int updateCommissionRecruitmentBonusRules(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules);

    /**
     * 删除拉新奖励规则
     * 
     * @param id 拉新奖励规则主键
     * @return 结果
     */
    public int deleteCommissionRecruitmentBonusRulesById(Integer id);

    /**
     * 批量删除拉新奖励规则
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommissionRecruitmentBonusRulesByIds(Integer[] ids);
} 