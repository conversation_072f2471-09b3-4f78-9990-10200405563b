package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionPayoutBreakdownsMapper;
import com.ruoyi.system.domain.commission.CommissionPayoutBreakdowns;
import com.ruoyi.system.service.commission.ICommissionPayoutBreakdownsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 分销员收入构成明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionPayoutBreakdownsServiceImpl implements ICommissionPayoutBreakdownsService
{
    @Autowired
    private CommissionPayoutBreakdownsMapper commissionPayoutBreakdownsMapper;

    /**
     * 查询分销员收入构成明细
     * 
     * @param id 分销员收入构成明细主键
     * @return 分销员收入构成明细
     */
    @Override
    public CommissionPayoutBreakdowns selectCommissionPayoutBreakdownsById(Long id)
    {
        return commissionPayoutBreakdownsMapper.selectCommissionPayoutBreakdownsById(id);
    }

    /**
     * 查询分销员收入构成明细列表
     * 
     * @param commissionPayoutBreakdowns 分销员收入构成明细
     * @return 分销员收入构成明细
     */
    @Override
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsList(CommissionPayoutBreakdowns commissionPayoutBreakdowns)
    {
        return commissionPayoutBreakdownsMapper.selectCommissionPayoutBreakdownsList(commissionPayoutBreakdowns);
    }

    /**
     * 根据支付记录ID查询收入构成明细
     * 
     * @param payoutId 支付记录ID
     * @return 分销员收入构成明细集合
     */
    @Override
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsByPayoutId(Long payoutId)
    {
        return commissionPayoutBreakdownsMapper.selectCommissionPayoutBreakdownsByPayoutId(payoutId);
    }

    /**
     * 根据分销员ID和月份查询收入构成明细
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员收入构成明细集合
     */
    @Override
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsByCreatorAndMonth(Long creatorId, Date dataMonth)
    {
        return commissionPayoutBreakdownsMapper.selectCommissionPayoutBreakdownsByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 查询指定月份的收入构成统计
     * 
     * @param dataMonth 数据月份
     * @return 分销员收入构成明细集合
     */
    @Override
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsByMonth(Date dataMonth)
    {
        return commissionPayoutBreakdownsMapper.selectCommissionPayoutBreakdownsByMonth(dataMonth);
    }

    /**
     * 新增分销员收入构成明细
     * 
     * @param commissionPayoutBreakdowns 分销员收入构成明细
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertCommissionPayoutBreakdowns(CommissionPayoutBreakdowns commissionPayoutBreakdowns)
    {
        commissionPayoutBreakdowns.setCreateTime(DateUtils.getNowDate());
        return commissionPayoutBreakdownsMapper.insertCommissionPayoutBreakdowns(commissionPayoutBreakdowns);
    }

    /**
     * 批量新增分销员收入构成明细
     * 
     * @param breakdownsList 分销员收入构成明细集合
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int batchInsertCommissionPayoutBreakdowns(List<CommissionPayoutBreakdowns> breakdownsList)
    {
        if (breakdownsList == null || breakdownsList.isEmpty()) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        for (CommissionPayoutBreakdowns breakdown : breakdownsList) {
            breakdown.setCreateTime(now);
        }
        
        return commissionPayoutBreakdownsMapper.batchInsertCommissionPayoutBreakdowns(breakdownsList);
    }

    /**
     * 修改分销员收入构成明细
     * 
     * @param commissionPayoutBreakdowns 分销员收入构成明细
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int updateCommissionPayoutBreakdowns(CommissionPayoutBreakdowns commissionPayoutBreakdowns)
    {
        commissionPayoutBreakdowns.setUpdateTime(DateUtils.getNowDate());
        return commissionPayoutBreakdownsMapper.updateCommissionPayoutBreakdowns(commissionPayoutBreakdowns);
    }

    /**
     * 批量删除分销员收入构成明细
     * 
     * @param ids 需要删除的分销员收入构成明细主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionPayoutBreakdownsByIds(Long[] ids)
    {
        return commissionPayoutBreakdownsMapper.deleteCommissionPayoutBreakdownsByIds(ids);
    }

    /**
     * 删除分销员收入构成明细信息
     * 
     * @param id 分销员收入构成明细主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionPayoutBreakdownsById(Long id)
    {
        return commissionPayoutBreakdownsMapper.deleteCommissionPayoutBreakdownsById(id);
    }

    /**
     * 根据支付记录ID删除收入构成明细
     * 
     * @param payoutId 支付记录ID
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionPayoutBreakdownsByPayoutId(Long payoutId)
    {
        return commissionPayoutBreakdownsMapper.deleteCommissionPayoutBreakdownsByPayoutId(payoutId);
    }

    /**
     * 根据月份删除收入构成明细
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionPayoutBreakdownsByMonth(Date dataMonth)
    {
        return commissionPayoutBreakdownsMapper.deleteCommissionPayoutBreakdownsByMonth(dataMonth);
    }
} 