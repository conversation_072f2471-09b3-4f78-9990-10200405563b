package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionDynamicThresholds;

/**
 * 分销员动态门槛计算记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionDynamicThresholdsService 
{
    /**
     * 查询分销员动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员动态门槛计算记录
     */
    public CommissionDynamicThresholds selectCommissionDynamicThresholdsByCreatorAndMonth(Long creatorId, Date dataMonth);

    /**
     * 查询分销员动态门槛计算记录列表
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 分销员动态门槛计算记录集合
     */
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsList(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 根据数据月份查询动态门槛记录列表
     * 
     * @param dataMonth 数据月份
     * @return 分销员动态门槛计算记录集合
     */
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsByMonth(Date dataMonth);

    /**
     * 根据分销员ID查询历史动态门槛记录
     * 
     * @param creatorId 分销员ID
     * @return 分销员动态门槛计算记录集合
     */
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsByCreator(Long creatorId);

    /**
     * 新增分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    public int insertCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 批量新增分销员动态门槛计算记录
     * 
     * @param thresholdsList 分销员动态门槛计算记录列表
     * @return 结果
     */
    public int batchInsertCommissionDynamicThresholds(List<CommissionDynamicThresholds> thresholdsList);

    /**
     * 插入或更新分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    public int insertOrUpdateCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 修改分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    public int updateCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds);

    /**
     * 删除分销员动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionDynamicThresholdsByCreatorAndMonth(Long creatorId, Date dataMonth);

    /**
     * 根据分销员ID删除动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @return 结果
     */
    public int deleteCommissionDynamicThresholdsByCreator(Long creatorId);

    /**
     * 根据数据月份删除动态门槛计算记录
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionDynamicThresholdsByMonth(Date dataMonth);

    /**
     * 检查指定分销员和月份的记录是否存在
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 是否存在
     */
    public boolean checkCommissionDynamicThresholdExists(Long creatorId, Date dataMonth);

    /**
     * 清理指定日期之前的历史门槛记录
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    public int deleteCommissionDynamicThresholdsBeforeDate(Date beforeDate);
} 