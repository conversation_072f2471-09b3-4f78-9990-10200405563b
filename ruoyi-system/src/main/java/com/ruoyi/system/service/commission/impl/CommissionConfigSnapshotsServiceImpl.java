package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionConfigSnapshotsMapper;
import com.ruoyi.system.domain.commission.CommissionConfigSnapshots;
import com.ruoyi.system.service.commission.ICommissionConfigSnapshotsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 月度配置快照Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionConfigSnapshotsServiceImpl implements ICommissionConfigSnapshotsService 
{
    @Autowired
    private CommissionConfigSnapshotsMapper commissionConfigSnapshotsMapper;

    /**
     * 查询月度配置快照
     * 
     * @param id 月度配置快照主键
     * @return 月度配置快照
     */
    @Override
    public CommissionConfigSnapshots selectCommissionConfigSnapshotsById(Long id)
    {
        return commissionConfigSnapshotsMapper.selectCommissionConfigSnapshotsById(id);
    }

    /**
     * 查询月度配置快照列表
     * 
     * @param commissionConfigSnapshots 月度配置快照
     * @return 月度配置快照
     */
    @Override
    public List<CommissionConfigSnapshots> selectCommissionConfigSnapshotsList(CommissionConfigSnapshots commissionConfigSnapshots)
    {
        return commissionConfigSnapshotsMapper.selectCommissionConfigSnapshotsList(commissionConfigSnapshots);
    }

    /**
     * 根据数据月份查询配置快照列表
     * 
     * @param dataMonth 数据月份
     * @return 月度配置快照集合
     */
    @Override
    public List<CommissionConfigSnapshots> selectCommissionConfigSnapshotsByMonth(Date dataMonth)
    {
        return commissionConfigSnapshotsMapper.selectCommissionConfigSnapshotsByMonth(dataMonth);
    }

    /**
     * 根据数据月份和配置类型查询快照
     * 
     * @param dataMonth 数据月份
     * @param configType 配置类型
     * @return 月度配置快照
     */
    @Override
    public CommissionConfigSnapshots selectCommissionConfigSnapshotsByMonthAndType(Date dataMonth, String configType)
    {
        return commissionConfigSnapshotsMapper.selectCommissionConfigSnapshotsByMonthAndType(dataMonth, configType);
    }

    /**
     * 新增月度配置快照
     * 
     * @param commissionConfigSnapshots 月度配置快照
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertCommissionConfigSnapshots(CommissionConfigSnapshots commissionConfigSnapshots)
    {
        commissionConfigSnapshots.setCreatedAt(DateUtils.getNowDate());
        return commissionConfigSnapshotsMapper.insertCommissionConfigSnapshots(commissionConfigSnapshots);
    }

    /**
     * 批量新增月度配置快照
     * 
     * @param snapshotsList 月度配置快照列表
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int batchInsertCommissionConfigSnapshots(List<CommissionConfigSnapshots> snapshotsList)
    {
        if (snapshotsList == null || snapshotsList.isEmpty()) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        for (CommissionConfigSnapshots snapshot : snapshotsList) {
            if (snapshot.getCreatedAt() == null) {
                snapshot.setCreatedAt(now);
            }
        }
        
        return commissionConfigSnapshotsMapper.batchInsertCommissionConfigSnapshots(snapshotsList);
    }

    /**
     * 修改月度配置快照
     * 
     * @param commissionConfigSnapshots 月度配置快照
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int updateCommissionConfigSnapshots(CommissionConfigSnapshots commissionConfigSnapshots)
    {
        return commissionConfigSnapshotsMapper.updateCommissionConfigSnapshots(commissionConfigSnapshots);
    }

    /**
     * 批量删除月度配置快照
     * 
     * @param ids 需要删除的月度配置快照主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionConfigSnapshotsByIds(Long[] ids)
    {
        return commissionConfigSnapshotsMapper.deleteCommissionConfigSnapshotsByIds(ids);
    }

    /**
     * 删除月度配置快照信息
     * 
     * @param id 月度配置快照主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionConfigSnapshotsById(Long id)
    {
        return commissionConfigSnapshotsMapper.deleteCommissionConfigSnapshotsById(id);
    }

    /**
     * 检查指定月份和类型的快照是否存在
     * 
     * @param dataMonth 数据月份
     * @param configType 配置类型
     * @return 是否存在
     */
    @Override
    public boolean checkCommissionConfigSnapshotExists(Date dataMonth, String configType)
    {
        int count = commissionConfigSnapshotsMapper.checkCommissionConfigSnapshotExists(dataMonth, configType);
        return count > 0;
    }

    /**
     * 清理指定日期之前的历史快照
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionConfigSnapshotsBeforeDate(Date beforeDate)
    {
        return commissionConfigSnapshotsMapper.deleteCommissionConfigSnapshotsBeforeDate(beforeDate);
    }
} 