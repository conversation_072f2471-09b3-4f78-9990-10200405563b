package com.ruoyi.system.service.commission.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionMultilevelRulesMapper;
import com.ruoyi.system.domain.commission.CommissionMultilevelRules;
import com.ruoyi.system.service.commission.ICommissionMultilevelRulesService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 多级提成规则Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class CommissionMultilevelRulesServiceImpl implements ICommissionMultilevelRulesService 
{
    @Autowired
    private CommissionMultilevelRulesMapper commissionMultilevelRulesMapper;

    /**
     * 查询多级提成规则
     * 
     * @param id 多级提成规则主键
     * @return 多级提成规则
     */
    @Override
    public CommissionMultilevelRules selectCommissionMultilevelRulesById(Integer id)
    {
        return commissionMultilevelRulesMapper.selectCommissionMultilevelRulesById(id);
    }

    /**
     * 查询多级提成规则列表
     * 
     * @param commissionMultilevelRules 多级提成规则
     * @return 多级提成规则
     */
    @Override
    public List<CommissionMultilevelRules> selectCommissionMultilevelRulesList(CommissionMultilevelRules commissionMultilevelRules)
    {
        return commissionMultilevelRulesMapper.selectCommissionMultilevelRulesList(commissionMultilevelRules);
    }

    /**
     * 查询启用的多级提成规则列表
     * 
     * @return 多级提成规则
     */
    @Override
    public List<CommissionMultilevelRules> selectActiveCommissionMultilevelRules()
    {
        return commissionMultilevelRulesMapper.selectActiveCommissionMultilevelRules();
    }

    /**
     * 新增多级提成规则
     * 
     * @param commissionMultilevelRules 多级提成规则
     * @return 结果
     */
    @Override
    
    public int insertCommissionMultilevelRules(CommissionMultilevelRules commissionMultilevelRules)
    {
        return commissionMultilevelRulesMapper.insertCommissionMultilevelRules(commissionMultilevelRules);
    }

    /**
     * 修改多级提成规则
     * 
     * @param commissionMultilevelRules 多级提成规则
     * @return 结果
     */
    @Override
    public int updateCommissionMultilevelRules(CommissionMultilevelRules commissionMultilevelRules)
    {
        return commissionMultilevelRulesMapper.updateCommissionMultilevelRules(commissionMultilevelRules);
    }

    /**
     * 批量删除多级提成规则
     * 
     * @param ids 需要删除的多级提成规则主键
     * @return 结果
     */
    @Override
    public int deleteCommissionMultilevelRulesByIds(Integer[] ids)
    {
        return commissionMultilevelRulesMapper.deleteCommissionMultilevelRulesByIds(ids);
    }

    /**
     * 删除多级提成规则信息
     * 
     * @param id 多级提成规则主键
     * @return 结果
     */
    @Override
    public int deleteCommissionMultilevelRulesById(Integer id)
    {
        return commissionMultilevelRulesMapper.deleteCommissionMultilevelRulesById(id);
    }
} 