package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionDynamicThresholdsMapper;
import com.ruoyi.system.domain.commission.CommissionDynamicThresholds;
import com.ruoyi.system.service.commission.ICommissionDynamicThresholdsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 分销员动态门槛计算记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionDynamicThresholdsServiceImpl implements ICommissionDynamicThresholdsService 
{
    @Autowired
    private CommissionDynamicThresholdsMapper commissionDynamicThresholdsMapper;

    /**
     * 查询分销员动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员动态门槛计算记录
     */
    @Override
    public CommissionDynamicThresholds selectCommissionDynamicThresholdsByCreatorAndMonth(Long creatorId, Date dataMonth)
    {
        return commissionDynamicThresholdsMapper.selectCommissionDynamicThresholdsByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 查询分销员动态门槛计算记录列表
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 分销员动态门槛计算记录
     */
    @Override
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsList(CommissionDynamicThresholds commissionDynamicThresholds)
    {
        return commissionDynamicThresholdsMapper.selectCommissionDynamicThresholdsList(commissionDynamicThresholds);
    }

    /**
     * 根据数据月份查询动态门槛记录列表
     * 
     * @param dataMonth 数据月份
     * @return 分销员动态门槛计算记录集合
     */
    @Override
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsByMonth(Date dataMonth)
    {
        return commissionDynamicThresholdsMapper.selectCommissionDynamicThresholdsByMonth(dataMonth);
    }

    /**
     * 根据分销员ID查询历史动态门槛记录
     * 
     * @param creatorId 分销员ID
     * @return 分销员动态门槛计算记录集合
     */
    @Override
    public List<CommissionDynamicThresholds> selectCommissionDynamicThresholdsByCreator(Long creatorId)
    {
        return commissionDynamicThresholdsMapper.selectCommissionDynamicThresholdsByCreator(creatorId);
    }

    /**
     * 新增分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds)
    {
        commissionDynamicThresholds.setCreateTime(DateUtils.getNowDate());
        return commissionDynamicThresholdsMapper.insertCommissionDynamicThresholds(commissionDynamicThresholds);
    }

    /**
     * 批量新增分销员动态门槛计算记录
     * 
     * @param thresholdsList 分销员动态门槛计算记录列表
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int batchInsertCommissionDynamicThresholds(List<CommissionDynamicThresholds> thresholdsList)
    {
        if (thresholdsList == null || thresholdsList.isEmpty()) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        for (CommissionDynamicThresholds threshold : thresholdsList) {
            if (threshold.getCreateTime() == null) {
                threshold.setCreateTime(now);
            }
        }
        
        return commissionDynamicThresholdsMapper.batchInsertCommissionDynamicThresholds(thresholdsList);
    }

    /**
     * 插入或更新分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertOrUpdateCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds)
    {
        if (commissionDynamicThresholds.getCreateTime() == null) {
            commissionDynamicThresholds.setCreateTime(DateUtils.getNowDate());
        }
        commissionDynamicThresholds.setUpdateTime(DateUtils.getNowDate());
        
        return commissionDynamicThresholdsMapper.insertOrUpdateCommissionDynamicThresholds(commissionDynamicThresholds);
    }

    /**
     * 修改分销员动态门槛计算记录
     * 
     * @param commissionDynamicThresholds 分销员动态门槛计算记录
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int updateCommissionDynamicThresholds(CommissionDynamicThresholds commissionDynamicThresholds)
    {
        commissionDynamicThresholds.setUpdateTime(DateUtils.getNowDate());
        return commissionDynamicThresholdsMapper.updateCommissionDynamicThresholds(commissionDynamicThresholds);
    }

    /**
     * 删除分销员动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionDynamicThresholdsByCreatorAndMonth(Long creatorId, Date dataMonth)
    {
        return commissionDynamicThresholdsMapper.deleteCommissionDynamicThresholdsByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 根据分销员ID删除动态门槛计算记录
     * 
     * @param creatorId 分销员ID
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionDynamicThresholdsByCreator(Long creatorId)
    {
        return commissionDynamicThresholdsMapper.deleteCommissionDynamicThresholdsByCreator(creatorId);
    }

    /**
     * 根据数据月份删除动态门槛计算记录
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int deleteCommissionDynamicThresholdsByMonth(Date dataMonth)
    {
        return commissionDynamicThresholdsMapper.deleteCommissionDynamicThresholdsByMonth(dataMonth);
    }

    /**
     * 检查指定分销员和月份的记录是否存在
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 是否存在
     */
    @Override
    public boolean checkCommissionDynamicThresholdExists(Long creatorId, Date dataMonth)
    {
        int count = commissionDynamicThresholdsMapper.checkCommissionDynamicThresholdExists(creatorId, dataMonth);
        return count > 0;
    }

    /**
     * 清理指定日期之前的历史门槛记录
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public int deleteCommissionDynamicThresholdsBeforeDate(Date beforeDate)
    {
        return commissionDynamicThresholdsMapper.deleteCommissionDynamicThresholdsBeforeDate(beforeDate);
    }
} 