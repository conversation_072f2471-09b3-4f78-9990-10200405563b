package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionDistributorQualifications;

/**
 * 分销员资格记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface ICommissionDistributorQualificationsService 
{
    /**
     * 查询分销员资格记录列表
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 分销员资格记录集合
     */
    public List<CommissionDistributorQualifications> selectCommissionDistributorQualificationsList(CommissionDistributorQualifications commissionDistributorQualifications);

    /**
     * 根据分销员ID和月份查询资格记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员资格记录
     */
    public CommissionDistributorQualifications selectCommissionDistributorQualificationsByDistributor(Long creatorId, Date dataMonth);

    /**
     * 根据月份查询所有分销员资格记录
     * 
     * @param dataMonth 数据月份
     * @return 分销员资格记录集合
     */
    public List<CommissionDistributorQualifications> selectCommissionDistributorQualificationsByMonth(Date dataMonth);

    /**
     * 根据等级查询合格分销员
     * 
     * @param dataMonth 数据月份
     * @param achievedLevel 达成等级
     * @return 分销员资格记录集合
     */
    public List<CommissionDistributorQualifications> selectQualifiedDistributorsByLevel(Date dataMonth, String achievedLevel);

    /**
     * 检查分销员资格记录是否存在
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 是否存在
     */
    public boolean checkCommissionDistributorQualificationExists(Long creatorId, Date dataMonth);

    /**
     * 新增分销员资格记录
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    public int insertCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications);

    /**
     * 新增或更新分销员资格记录
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    public int insertOrUpdateCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications);

    /**
     * 修改分销员资格记录
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    public int updateCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications);

    /**
     * 删除分销员资格记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionDistributorQualificationsByCreatorAndMonth(Long creatorId, Date dataMonth);

    /**
     * 批量删除分销员资格记录
     * 
     * @param creatorIds 需要删除的分销员ID集合
     * @return 结果
     */
    public int deleteCommissionDistributorQualificationsByCreatorIds(Long[] creatorIds);

    /**
     * 根据月份删除所有分销员资格记录
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionDistributorQualificationsByMonth(Date dataMonth);
}
