package com.ruoyi.system.service.business;

import java.util.List;
import com.ruoyi.system.domain.business.GroupCreatorRelation;
import com.ruoyi.system.domain.vo.GroupRelationVO;

/**
 * 组名与CreatorID关系Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IGroupCreatorRelationService
{
    /**
     * 查询组名与CreatorID关系
     *
     * @param groupName 组名与CreatorID关系主键
     * @return 组名与CreatorID关系
     */
    public GroupCreatorRelation selectGroupCreatorRelationByGroupName(String groupName);

    /**
     * 查询组名与CreatorID关系列表
     *
     * @param groupCreatorRelation 组名与CreatorID关系
     * @return 组名与CreatorID关系集合
     */
    public List<GroupCreatorRelation> selectGroupCreatorRelationList(GroupCreatorRelation groupCreatorRelation);

    /**
     * 新增组名与CreatorID关系
     *
     * @param groupCreatorRelation 组名与CreatorID关系
     * @return 结果
     */
    public int insertGroupCreatorRelation(GroupCreatorRelation groupCreatorRelation);

    /**
     * 修改组名与CreatorID关系
     *
     * @param groupCreatorRelation 组名与CreatorID关系
     * @return 结果
     */
    public int updateGroupCreatorRelation(GroupCreatorRelation groupCreatorRelation);

    /**
     * 批量删除组名与CreatorID关系
     *
     * @param groupNames 需要删除的组名与CreatorID关系主键集合
     * @return 结果
     */
    public int deleteGroupCreatorRelationByGroupNames(String[] groupNames);

    /**
     * 删除组名与CreatorID关系信息
     *
     * @param groupName 组名与CreatorID关系主键
     * @return 结果
     */
    public int deleteGroupCreatorRelationByGroupName(String groupName);

    /**
     * 查询所有组与creators的关系，包含未指定creatorID的组
     *
     * @return 组关系列表
     */
    public List<GroupRelationVO> selectGroupRelationList();

    /**
     * 指定组对应的上级
     *
     * @param groupName 组名
     * @param creatorId 创建者ID
     * @param updateBy 更新者
     * @return 结果
     */
    public int assignGroupParent(String groupName, Long creatorId, String updateBy);

    /**
     * 根据创建者ID查询组关系
     *
     * @param creatorId 创建者ID
     * @return 组关系
     */
    public GroupCreatorRelation selectGroupCreatorRelationByCreatorId(Long creatorId);

    /**
     * 删除指定创建者的组关系
     *
     * @param creatorId 创建者ID
     * @return 结果
     */
    public int deleteGroupCreatorRelationByCreatorId(Long creatorId);

    /**
     * 修改组类型
     *
     * @param groupName 组名
     * @param groupType 组类型：0-分销组，1-非分销组
     * @param updateBy 更新者
     * @return 结果
     */
    public int updateGroupType(String groupName, Integer groupType, String updateBy);
}