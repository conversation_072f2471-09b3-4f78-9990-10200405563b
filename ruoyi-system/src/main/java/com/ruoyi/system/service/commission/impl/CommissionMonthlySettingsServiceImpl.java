package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionMonthlySettingsMapper;
import com.ruoyi.system.domain.commission.CommissionMonthlySettings;
import com.ruoyi.system.service.commission.ICommissionMonthlySettingsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 月度佣金配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionMonthlySettingsServiceImpl implements ICommissionMonthlySettingsService 
{
    @Autowired
    private CommissionMonthlySettingsMapper commissionMonthlySettingsMapper;

    /**
     * 查询月度佣金配置
     * 
     * @param dataMonth 月度佣金配置主键
     * @return 月度佣金配置
     */
    @Override
    public CommissionMonthlySettings selectCommissionMonthlySettingsByMonth(Date dataMonth)
    {
        return commissionMonthlySettingsMapper.selectCommissionMonthlySettingsByMonth(dataMonth);
    }

    /**
     * 查询月度佣金配置列表
     * 
     * @param commissionMonthlySettings 月度佣金配置
     * @return 月度佣金配置
     */
    @Override
    public List<CommissionMonthlySettings> selectCommissionMonthlySettingsList(CommissionMonthlySettings commissionMonthlySettings)
    {
        return commissionMonthlySettingsMapper.selectCommissionMonthlySettingsList(commissionMonthlySettings);
    }

    /**
     * 新增月度佣金配置
     * 
     * @param commissionMonthlySettings 月度佣金配置
     * @return 结果
     */
    @Override
    public int insertCommissionMonthlySettings(CommissionMonthlySettings commissionMonthlySettings)
    {
        commissionMonthlySettings.setCreateTime(DateUtils.getNowDate());
        return commissionMonthlySettingsMapper.insertCommissionMonthlySettings(commissionMonthlySettings);
    }

    /**
     * 修改月度佣金配置
     * 
     * @param commissionMonthlySettings 月度佣金配置
     * @return 结果
     */
    @Override
    public int updateCommissionMonthlySettings(CommissionMonthlySettings commissionMonthlySettings)
    {
        commissionMonthlySettings.setUpdateTime(DateUtils.getNowDate());
        return commissionMonthlySettingsMapper.updateCommissionMonthlySettings(commissionMonthlySettings);
    }

    /**
     * 批量删除月度佣金配置
     * 
     * @param dataMonths 需要删除的月度佣金配置主键
     * @return 结果
     */
    @Override
    public int deleteCommissionMonthlySettingsByMonths(Date[] dataMonths)
    {
        return commissionMonthlySettingsMapper.deleteCommissionMonthlySettingsByMonths(dataMonths);
    }

    /**
     * 删除月度佣金配置信息
     * 
     * @param dataMonth 月度佣金配置主键
     * @return 结果
     */
    @Override
    public int deleteCommissionMonthlySettingsByMonth(Date dataMonth)
    {
        return commissionMonthlySettingsMapper.deleteCommissionMonthlySettingsByMonth(dataMonth);
    }
} 