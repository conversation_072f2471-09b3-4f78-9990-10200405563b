package com.ruoyi.system.service.impl.business;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import com.ruoyi.system.domain.dto.MonthlyPerformanceReportResponse;
import com.ruoyi.system.mapper.commission.CommissionPayoutsMapper;
import com.ruoyi.system.service.business.IMonthlyPerformanceReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 分销员月度业绩分析报告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class MonthlyPerformanceReportServiceImpl implements IMonthlyPerformanceReportService
{
    private static final Logger log = LoggerFactory.getLogger(MonthlyPerformanceReportServiceImpl.class);

    @Autowired
    private CommissionPayoutsMapper commissionPayoutsMapper;

    private final SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 获取指定分销员在指定月份的完整业绩分析报告
     * 
     * @param creatorId 分销员ID
     * @param month 基准月份，格式为 YYYY-MM
     * @return 业绩分析报告
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public MonthlyPerformanceReportResponse getMonthlyPerformanceReport(Long creatorId, String month)
    {
        try {
            log.info("开始生成分销员 {} 在 {} 月份的业绩分析报告", creatorId, month);
            
            // 解析基准月份
            Date selectedMonth = monthFormat.parse(month);
            Calendar cal = Calendar.getInstance();
            cal.setTime(selectedMonth);
            
            // 计算查询范围：需要查询7个月的数据（包含基准月份前6个月）
            Calendar startCal = (Calendar) cal.clone();
            startCal.add(Calendar.MONTH, -6);
            Date startMonth = startCal.getTime();
            Date endMonth = selectedMonth;
            
            log.info("查询收入数据范围：{} 到 {}", dateFormat.format(startMonth), dateFormat.format(endMonth));
            
            // 查询7个月的收入数据
            List<CommissionPayouts> payoutsList = commissionPayoutsMapper.selectCommissionPayoutsByCreatorAndMonthRange(
                creatorId, startMonth, endMonth);
            
            log.info("查询到 {} 条收入记录", payoutsList.size());
            
            // 构建响应对象
            MonthlyPerformanceReportResponse response = new MonthlyPerformanceReportResponse();
            response.setSelectedMonth(month);
            
            // 1. 计算顶部摘要
            MonthlyPerformanceReportResponse.Summary summary = calculateSummary(creatorId, selectedMonth, payoutsList);
            response.setSummary(summary);
            
            // 2. 生成趋势数据
            List<MonthlyPerformanceReportResponse.TrendData> trendData = generateTrendData(payoutsList, startMonth);
            response.setTrendData(trendData);
            
            // 3. 生成数据洞察
            MonthlyPerformanceReportResponse.Insights insights = generateInsights(trendData);
            response.setInsights(insights);
            
            log.info("业绩分析报告生成完成");
            return response;
            
        } catch (Exception e) {
            log.error("生成业绩分析报告失败：{}", e.getMessage(), e);
            throw new RuntimeException("生成业绩分析报告失败：" + e.getMessage());
        }
    }

    /**
     * 计算顶部摘要信息
     */
    private MonthlyPerformanceReportResponse.Summary calculateSummary(Long creatorId, Date selectedMonth, List<CommissionPayouts> payoutsList)
    {
        MonthlyPerformanceReportResponse.Summary summary = new MonthlyPerformanceReportResponse.Summary();
        
        // 计算年度累计总收入
        Calendar cal = Calendar.getInstance();
        cal.setTime(selectedMonth);
        int year = cal.get(Calendar.YEAR);
        
        BigDecimal totalIncomeToDate = commissionPayoutsMapper.selectTotalIncomeByCreatorAndYear(creatorId, year, selectedMonth);
        summary.setTotalIncomeToDate(totalIncomeToDate != null ? totalIncomeToDate : BigDecimal.ZERO);
        
        // 计算环比增长率
        BigDecimal selectedMonthGrowth = calculateMonthOverMonthGrowth(payoutsList, selectedMonth);
        summary.setSelectedMonthGrowth(selectedMonthGrowth);
        
        return summary;
    }

    /**
     * 计算环比增长率
     */
    private BigDecimal calculateMonthOverMonthGrowth(List<CommissionPayouts> payoutsList, Date selectedMonth)
    {
        // 创建月份到收入的映射
        Map<String, BigDecimal> monthIncomeMap = new HashMap<>();
        for (CommissionPayouts payout : payoutsList) {
            String monthKey = monthFormat.format(payout.getDataMonth());
            monthIncomeMap.put(monthKey, payout.getFinalPayoutUsd());
        }
        
        String selectedMonthKey = monthFormat.format(selectedMonth);
        BigDecimal selectedMonthIncome = monthIncomeMap.get(selectedMonthKey);
        
        if (selectedMonthIncome == null || selectedMonthIncome.compareTo(BigDecimal.ZERO) == 0) {
            return null; // 当月无收入，无法计算增长率
        }
        
        // 计算上月
        Calendar cal = Calendar.getInstance();
        cal.setTime(selectedMonth);
        cal.add(Calendar.MONTH, -1);
        String previousMonthKey = monthFormat.format(cal.getTime());
        BigDecimal previousMonthIncome = monthIncomeMap.get(previousMonthKey);
        
        if (previousMonthIncome == null || previousMonthIncome.compareTo(BigDecimal.ZERO) == 0) {
            return null; // 上月无收入，无法计算增长率
        }
        
        // 计算增长率：(当月收入 / 上月收入) - 1
        return selectedMonthIncome.divide(previousMonthIncome, 4, RoundingMode.HALF_UP)
                .subtract(BigDecimal.ONE);
    }

    /**
     * 生成趋势数据
     */
    private List<MonthlyPerformanceReportResponse.TrendData> generateTrendData(List<CommissionPayouts> payoutsList, Date startMonth)
    {
        // 创建月份到收入的映射
        Map<String, BigDecimal> monthIncomeMap = new HashMap<>();
        for (CommissionPayouts payout : payoutsList) {
            String monthKey = monthFormat.format(payout.getDataMonth());
            monthIncomeMap.put(monthKey, payout.getFinalPayoutUsd());
        }
        
        List<MonthlyPerformanceReportResponse.TrendData> trendDataList = new ArrayList<>();
        
        // 生成6个月的数据点
        Calendar cal = Calendar.getInstance();
        cal.setTime(startMonth);
        cal.add(Calendar.MONTH, 1); // 从第二个月开始（第一个月用于计算增长率）
        
        BigDecimal previousIncome = null;
        String previousMonthKey = monthFormat.format(startMonth);
        if (monthIncomeMap.containsKey(previousMonthKey)) {
            previousIncome = monthIncomeMap.get(previousMonthKey);
        }
        
        for (int i = 0; i < 6; i++) {
            MonthlyPerformanceReportResponse.TrendData trendData = new MonthlyPerformanceReportResponse.TrendData();
            
            String monthKey = monthFormat.format(cal.getTime());
            trendData.setMonth(monthKey);
            
            BigDecimal currentIncome = monthIncomeMap.getOrDefault(monthKey, BigDecimal.ZERO);
            trendData.setIncome(currentIncome);
            
            // 计算环比增长率
            if (i == 0) {
                // 第一个数据点的增长率为null
                trendData.setGrowth(null);
            } else {
                BigDecimal growth = calculateGrowthRate(currentIncome, previousIncome);
                trendData.setGrowth(growth);
            }
            
            trendDataList.add(trendData);
            
            // 为下一次循环准备
            previousIncome = currentIncome;
            cal.add(Calendar.MONTH, 1);
        }
        
        return trendDataList;
    }

    /**
     * 计算增长率
     */
    private BigDecimal calculateGrowthRate(BigDecimal currentIncome, BigDecimal previousIncome)
    {
        if (previousIncome == null || previousIncome.compareTo(BigDecimal.ZERO) == 0) {
            return null; // 上期无收入，无法计算增长率
        }
        
        if (currentIncome == null) {
            currentIncome = BigDecimal.ZERO;
        }
        
        // 计算增长率：(当期收入 / 上期收入) - 1
        return currentIncome.divide(previousIncome, 4, RoundingMode.HALF_UP)
                .subtract(BigDecimal.ONE);
    }

    /**
     * 生成数据洞察
     */
    private MonthlyPerformanceReportResponse.Insights generateInsights(List<MonthlyPerformanceReportResponse.TrendData> trendDataList)
    {
        MonthlyPerformanceReportResponse.Insights insights = new MonthlyPerformanceReportResponse.Insights();
        
        // 找出最佳表现月份
        MonthlyPerformanceReportResponse.BestMonth bestMonth = findBestMonth(trendDataList);
        insights.setBestMonth(bestMonth);
        
        // 分析增长趋势
        MonthlyPerformanceReportResponse.GrowthTrend growthTrend = analyzeGrowthTrend(trendDataList);
        insights.setGrowthTrend(growthTrend);
        
        return insights;
    }

    /**
     * 找出最佳表现月份
     */
    private MonthlyPerformanceReportResponse.BestMonth findBestMonth(List<MonthlyPerformanceReportResponse.TrendData> trendDataList)
    {
        MonthlyPerformanceReportResponse.BestMonth bestMonth = new MonthlyPerformanceReportResponse.BestMonth();
        
        BigDecimal maxIncome = BigDecimal.ZERO;
        String bestMonthStr = null;
        
        for (MonthlyPerformanceReportResponse.TrendData trendData : trendDataList) {
            if (trendData.getIncome() != null && trendData.getIncome().compareTo(maxIncome) > 0) {
                maxIncome = trendData.getIncome();
                bestMonthStr = trendData.getMonth();
            }
        }
        
        bestMonth.setMonth(bestMonthStr);
        bestMonth.setIncome(maxIncome);
        
        return bestMonth;
    }

    /**
     * 分析增长趋势
     */
    private MonthlyPerformanceReportResponse.GrowthTrend analyzeGrowthTrend(List<MonthlyPerformanceReportResponse.TrendData> trendDataList)
    {
        MonthlyPerformanceReportResponse.GrowthTrend growthTrend = new MonthlyPerformanceReportResponse.GrowthTrend();
        
        // 统计正增长月份数量
        int positiveMonths = 0;
        int validGrowthCount = 0;
        
        for (MonthlyPerformanceReportResponse.TrendData trendData : trendDataList) {
            if (trendData.getGrowth() != null) {
                validGrowthCount++;
                if (trendData.getGrowth().compareTo(BigDecimal.ZERO) > 0) {
                    positiveMonths++;
                }
            }
        }
        
        // 根据正增长月份比例判断趋势
        if (validGrowthCount == 0) {
            growthTrend.setType("STABLE");
            growthTrend.setMessage("数据不足，无法判断趋势");
        } else if (positiveMonths >= validGrowthCount * 0.8) { // 80%以上为正增长
            growthTrend.setType("UPWARD");
            growthTrend.setMessage("持续上升趋势，表现优秀");
        } else if (positiveMonths <= validGrowthCount * 0.2) { // 20%以下为正增长
            growthTrend.setType("DOWNWARD");
            growthTrend.setMessage("下降趋势，需要关注");
        } else {
            growthTrend.setType("STABLE");
            growthTrend.setMessage("波动趋势，整体稳定");
        }
        
        return growthTrend;
    }
}
