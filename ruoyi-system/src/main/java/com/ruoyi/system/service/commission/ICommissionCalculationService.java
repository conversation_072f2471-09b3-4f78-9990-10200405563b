package com.ruoyi.system.service.commission;

import java.util.Date;
import java.util.Map;
import java.util.List;
import com.ruoyi.system.domain.commission.CommissionMonthlySummary;
import com.ruoyi.system.domain.commission.CommissionCalculationLogs;

/**
 * 佣金计算核心业务Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionCalculationService 
{
    /**
     * 执行月度佣金计算
     * 
     * @param dataMonth 数据月份
     * @return 计算结果信息
     */
    public Map<String, Object> executeMonthlyCalculation(Date dataMonth);

    /**
     * 获取计算状态
     * 
     * @param dataMonth 数据月份
     * @return 计算状态信息
     */
    public Map<String, Object> getCalculationStatus(Date dataMonth);

    /**
     * 重新计算指定月份的佣金
     * 
     * @param dataMonth 数据月份
     * @param force 是否强制重新计算
     * @return 计算结果信息
     */
    public Map<String, Object> recalculateMonthlyCommission(Date dataMonth, boolean force);

    /**
     * 预览计算结果（不保存）
     * 
     * @param dataMonth 数据月份
     * @return 预览结果信息
     */
    public Map<String, Object> previewCalculation(Date dataMonth);

    /**
     * 检查计算前置条件
     * 
     * @param dataMonth 数据月份
     * @return 检查结果
     */
    public Map<String, Object> checkCalculationPreconditions(Date dataMonth);

    /**
     * 获取月度计算总览
     * 
     * @param dataMonth 数据月份
     * @return 计算总览
     */
    public CommissionMonthlySummary getMonthlyCalculationSummary(Date dataMonth);

    /**
     * 单个分销员计算验证（用于调试特定主播）
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 详细计算结果
     */
    public Map<String, Object> validateSingleDistributorCalculation(Long creatorId, Date dataMonth);

    /**
     * 获取月度计算详细报告
     * 
     * @param dataMonth 数据月份
     * @return 计算验证报告
     */
    public Map<String, Object> getCalculationValidationReport(Date dataMonth);

    /**
     * 获取分销员计算日志
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 计算日志列表
     */
    public List<CommissionCalculationLogs> getDistributorCalculationLogs(Long creatorId, Date dataMonth);

    /**
     * 获取重新计算的统计信息
     * 
     * @param dataMonth 数据月份
     * @return 统计信息
     */
    public Map<String, Object> getRecalculationStatistics(Date dataMonth);

    /**
     * 查询历史计算日志（被标记为历史的日志）
     * 
     * @param dataMonth 数据月份
     * @param batchId 批次ID（可选）
     * @return 历史日志列表
     */
    public Map<String, Object> getHistoricalCalculationLogs(Date dataMonth, String batchId);

    /**
     * 查询重新计算历史记录
     * 
     * @param dataMonth 数据月份
     * @return 重新计算历史
     */
    public Map<String, Object> getRecalculationHistory(Date dataMonth);

    /**
     * 获取最新的有效计算日志（排除历史日志）
     * 
     * @param dataMonth 数据月份
     * @return 最新计算日志列表
     */
    public List<CommissionCalculationLogs> getLatestValidCalculationLogs(Date dataMonth);
} 