package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionCalculationLogs;

/**
 * 佣金计算日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionCalculationLogsService 
{
    /**
     * 查询佣金计算日志
     * 
     * @param id 佣金计算日志主键
     * @return 佣金计算日志
     */
    public CommissionCalculationLogs selectCommissionCalculationLogsById(Long id);

    /**
     * 查询佣金计算日志列表
     * 
     * @param commissionCalculationLogs 佣金计算日志
     * @return 佣金计算日志集合
     */
    public List<CommissionCalculationLogs> selectCommissionCalculationLogsList(CommissionCalculationLogs commissionCalculationLogs);

    /**
     * 根据数据月份查询计算日志列表
     * 
     * @param dataMonth 数据月份
     * @return 佣金计算日志集合
     */
    public List<CommissionCalculationLogs> selectCommissionCalculationLogsByMonth(Date dataMonth);

    /**
     * 根据分销员ID和数据月份查询日志列表
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 佣金计算日志集合
     */
    public List<CommissionCalculationLogs> selectCommissionCalculationLogsByCreatorAndMonth(Long creatorId, Date dataMonth);

    /**
     * 新增佣金计算日志
     * 
     * @param commissionCalculationLogs 佣金计算日志
     * @return 结果
     */
    public int insertCommissionCalculationLogs(CommissionCalculationLogs commissionCalculationLogs);

    /**
     * 批量新增佣金计算日志
     * 
     * @param logsList 佣金计算日志列表
     * @return 结果
     */
    public int batchInsertCommissionCalculationLogs(List<CommissionCalculationLogs> logsList);

    /**
     * 修改佣金计算日志
     * 
     * @param commissionCalculationLogs 佣金计算日志
     * @return 结果
     */
    public int updateCommissionCalculationLogs(CommissionCalculationLogs commissionCalculationLogs);

    /**
     * 批量删除佣金计算日志
     * 
     * @param ids 需要删除的佣金计算日志主键集合
     * @return 结果
     */
    public int deleteCommissionCalculationLogsByIds(Long[] ids);

    /**
     * 删除佣金计算日志信息
     * 
     * @param id 佣金计算日志主键
     * @return 结果
     */
    public int deleteCommissionCalculationLogsById(Long id);

    /**
     * 清理指定日期之前的历史日志
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    public int deleteCommissionCalculationLogsBeforeDate(Date beforeDate);
} 