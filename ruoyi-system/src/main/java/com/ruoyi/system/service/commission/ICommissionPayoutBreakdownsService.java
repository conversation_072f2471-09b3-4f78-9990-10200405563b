package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionPayoutBreakdowns;

/**
 * 分销员收入构成明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionPayoutBreakdownsService 
{
    /**
     * 查询分销员收入构成明细
     * 
     * @param id 分销员收入构成明细主键
     * @return 分销员收入构成明细
     */
    public CommissionPayoutBreakdowns selectCommissionPayoutBreakdownsById(Long id);

    /**
     * 查询分销员收入构成明细列表
     * 
     * @param commissionPayoutBreakdowns 分销员收入构成明细
     * @return 分销员收入构成明细集合
     */
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsList(CommissionPayoutBreakdowns commissionPayoutBreakdowns);

    /**
     * 根据支付记录ID查询收入构成明细
     * 
     * @param payoutId 支付记录ID
     * @return 分销员收入构成明细集合
     */
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsByPayoutId(Long payoutId);

    /**
     * 根据分销员ID和月份查询收入构成明细
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员收入构成明细集合
     */
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsByCreatorAndMonth(Long creatorId, Date dataMonth);

    /**
     * 查询指定月份的收入构成统计
     * 
     * @param dataMonth 数据月份
     * @return 分销员收入构成明细集合
     */
    public List<CommissionPayoutBreakdowns> selectCommissionPayoutBreakdownsByMonth(Date dataMonth);

    /**
     * 新增分销员收入构成明细
     * 
     * @param commissionPayoutBreakdowns 分销员收入构成明细
     * @return 结果
     */
    public int insertCommissionPayoutBreakdowns(CommissionPayoutBreakdowns commissionPayoutBreakdowns);

    /**
     * 批量新增分销员收入构成明细
     * 
     * @param breakdownsList 分销员收入构成明细集合
     * @return 结果
     */
    public int batchInsertCommissionPayoutBreakdowns(List<CommissionPayoutBreakdowns> breakdownsList);

    /**
     * 修改分销员收入构成明细
     * 
     * @param commissionPayoutBreakdowns 分销员收入构成明细
     * @return 结果
     */
    public int updateCommissionPayoutBreakdowns(CommissionPayoutBreakdowns commissionPayoutBreakdowns);

    /**
     * 批量删除分销员收入构成明细
     * 
     * @param ids 需要删除的分销员收入构成明细主键集合
     * @return 结果
     */
    public int deleteCommissionPayoutBreakdownsByIds(Long[] ids);

    /**
     * 删除分销员收入构成明细信息
     * 
     * @param id 分销员收入构成明细主键
     * @return 结果
     */
    public int deleteCommissionPayoutBreakdownsById(Long id);

    /**
     * 根据支付记录ID删除收入构成明细
     * 
     * @param payoutId 支付记录ID
     * @return 结果
     */
    public int deleteCommissionPayoutBreakdownsByPayoutId(Long payoutId);

    /**
     * 根据月份删除收入构成明细
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionPayoutBreakdownsByMonth(Date dataMonth);
} 