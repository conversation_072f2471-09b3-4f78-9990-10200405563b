package com.ruoyi.system.service.impl.business;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.business.TeamEventsMapper;
import com.ruoyi.system.domain.business.TeamEvents;
import com.ruoyi.system.service.business.ITeamEventsService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * 团队动态与成就事件日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class TeamEventsServiceImpl implements ITeamEventsService
{
    @Autowired
    private TeamEventsMapper teamEventsMapper;

    /**
     * 查询团队动态与成就事件日志
     *
     * @param id 团队动态与成就事件日志主键
     * @return 团队动态与成就事件日志
     */
    @Override
    public TeamEvents selectTeamEventsById(Long id)
    {
        return teamEventsMapper.selectTeamEventsById(id);
    }

    /**
     * 查询团队动态与成就事件日志列表
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 团队动态与成就事件日志
     */
    @Override
    public List<TeamEvents> selectTeamEventsList(TeamEvents teamEvents)
    {
        return teamEventsMapper.selectTeamEventsList(teamEvents);
    }

    /**
     * 根据关联人ID和时间范围查询团队事件
     *
     * @param actorCreatorId 事件关联人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 团队事件列表
     */
    @Override
    public List<TeamEvents> selectTeamEventsByActorAndTimeRange(Long actorCreatorId, Date startTime, Date endTime)
    {
        return teamEventsMapper.selectTeamEventsByActorAndTimeRange(actorCreatorId, startTime, endTime);
    }

    /**
     * 根据事件类型和时间范围查询团队事件
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 团队事件列表
     */
    @Override
    public List<TeamEvents> selectTeamEventsByTypeAndTimeRange(String eventType, Date startTime, Date endTime)
    {
        return teamEventsMapper.selectTeamEventsByTypeAndTimeRange(eventType, startTime, endTime);
    }

    /**
     * 新增团队动态与成就事件日志
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int insertTeamEvents(TeamEvents teamEvents)
    {
        teamEvents.setCreateTime(DateUtils.getNowDate());
        return teamEventsMapper.insertTeamEvents(teamEvents);
    }

    /**
     * 批量新增团队动态与成就事件日志
     *
     * @param teamEventsList 团队动态与成就事件日志列表
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int insertTeamEventsBatch(List<TeamEvents> teamEventsList)
    {
        if (teamEventsList == null || teamEventsList.isEmpty()) {
            return 0;
        }
        
        Date now = DateUtils.getNowDate();
        for (TeamEvents teamEvents : teamEventsList) {
            if (teamEvents.getCreateTime() == null) {
                teamEvents.setCreateTime(now);
            }
        }
        
        return teamEventsMapper.insertTeamEventsBatch(teamEventsList);
    }

    /**
     * 修改团队动态与成就事件日志
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int updateTeamEvents(TeamEvents teamEvents)
    {
        teamEvents.setUpdateTime(DateUtils.getNowDate());
        return teamEventsMapper.updateTeamEvents(teamEvents);
    }

    /**
     * 批量删除团队动态与成就事件日志
     *
     * @param ids 需要删除的团队动态与成就事件日志主键
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int deleteTeamEventsByIds(Long[] ids)
    {
        return teamEventsMapper.deleteTeamEventsByIds(ids);
    }

    /**
     * 删除团队动态与成就事件日志信息
     *
     * @param id 团队动态与成就事件日志主键
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int deleteTeamEventsById(Long id)
    {
        return teamEventsMapper.deleteTeamEventsById(id);
    }

    /**
     * 根据关联人ID删除团队事件
     *
     * @param actorCreatorId 事件关联人ID
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int deleteTeamEventsByActorCreatorId(Long actorCreatorId)
    {
        return teamEventsMapper.deleteTeamEventsByActorCreatorId(actorCreatorId);
    }

    /**
     * 根据时间范围删除团队事件（性能优化版本）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 删除的记录数
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int deleteTeamEventsByTimeRange(Date startTime, Date endTime)
    {
        return teamEventsMapper.deleteTeamEventsByTimeRange(startTime, endTime);
    }

    /**
     * 创建等级提升事件
     *
     * @param actorCreatorId 事件关联人ID（上级）
     * @param targetCreatorId 事件目标者ID（晋升者）
     * @param oldLevel 原等级
     * @param newLevel 新等级
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    @Override
    public int createLevelUpEvent(Long actorCreatorId, Long targetCreatorId, String oldLevel, String newLevel, Date eventTimestamp)
    {
        TeamEvents teamEvents = new TeamEvents();
        teamEvents.setActorCreatorId(actorCreatorId);
        teamEvents.setTargetCreatorId(targetCreatorId);
        teamEvents.setEventType("LEVEL_UP");
        teamEvents.setEventTimestamp(eventTimestamp);
        
        JSONObject details = new JSONObject();
        details.put("old_level", oldLevel);
        details.put("new_level", newLevel);
        teamEvents.setDetails(details.toJSONString());
        
        return insertTeamEvents(teamEvents);
    }

    /**
     * 创建新人加入事件
     *
     * @param actorCreatorId 事件关联人ID（上级）
     * @param targetCreatorId 事件目标者ID（新人）
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    @Override
    public int createNewRecruitEvent(Long actorCreatorId, Long targetCreatorId, Date eventTimestamp)
    {
        TeamEvents teamEvents = new TeamEvents();
        teamEvents.setActorCreatorId(actorCreatorId);
        teamEvents.setTargetCreatorId(targetCreatorId);
        teamEvents.setEventType("NEW_RECRUIT");
        teamEvents.setEventTimestamp(eventTimestamp);
        
        JSONObject details = new JSONObject();
        details.put("recruit_id", targetCreatorId);
        teamEvents.setDetails(details.toJSONString());
        
        return insertTeamEvents(teamEvents);
    }

    /**
     * 创建业绩里程碑事件
     *
     * @param actorCreatorId 事件关联人ID
     * @param milestoneValue 里程碑值
     * @param currentValue 当前值
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    @Override
    public int createPerformanceMilestoneEvent(Long actorCreatorId, Long milestoneValue, Long currentValue, Date eventTimestamp)
    {
        TeamEvents teamEvents = new TeamEvents();
        teamEvents.setActorCreatorId(actorCreatorId);
        teamEvents.setEventType("PERFORMANCE_MILESTONE");
        teamEvents.setEventTimestamp(eventTimestamp);
        
        JSONObject details = new JSONObject();
        details.put("milestone_value", milestoneValue);
        details.put("current_value", currentValue);
        teamEvents.setDetails(details.toJSONString());
        
        return insertTeamEvents(teamEvents);
    }

    /**
     * 创建即将达标事件
     *
     * @param actorCreatorId 事件关联人ID
     * @param achievementType 达标类型（拉新/业绩）
     * @param currentValue 当前值
     * @param targetValue 目标值
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    @Override
    public int createApproachingMilestoneEvent(Long actorCreatorId, String achievementType, Long currentValue, Long targetValue, Date eventTimestamp)
    {
        TeamEvents teamEvents = new TeamEvents();
        teamEvents.setActorCreatorId(actorCreatorId);
        teamEvents.setEventType("APPROACHING_MILESTONE");
        teamEvents.setEventTimestamp(eventTimestamp);
        
        JSONObject details = new JSONObject();
        details.put("achievement_type", achievementType);
        details.put("current_value", currentValue);
        details.put("target_value", targetValue);
        teamEvents.setDetails(details.toJSONString());
        
        return insertTeamEvents(teamEvents);
    }
}
