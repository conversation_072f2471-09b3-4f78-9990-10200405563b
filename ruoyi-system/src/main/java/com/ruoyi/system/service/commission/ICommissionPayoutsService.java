package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionPayouts;

/**
 * 分销员月度实收金额Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionPayoutsService 
{
    /**
     * 查询分销员月度实收金额
     * 
     * @param id 分销员月度实收金额主键
     * @return 分销员月度实收金额
     */
    public CommissionPayouts selectCommissionPayoutsById(Long id);

    /**
     * 根据分销员ID和月份查询实收金额
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员月度实收金额
     */
    public CommissionPayouts selectCommissionPayoutsByCreatorAndMonth(Long creatorId, Date dataMonth);

    /**
     * 查询分销员月度实收金额列表
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 分销员月度实收金额集合
     */
    public List<CommissionPayouts> selectCommissionPayoutsList(CommissionPayouts commissionPayouts);

    /**
     * 查询分销员月度实收金额列表（带主播信息）
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 分销员月度实收金额集合
     */
    public List<CommissionPayouts> selectCommissionPayoutsListWithCreator(CommissionPayouts commissionPayouts);

    /**
     * 查询指定月份的分销员收入统计
     * 
     * @param dataMonth 数据月份
     * @return 分销员月度实收金额集合
     */
    public List<CommissionPayouts> selectCommissionPayoutsByMonth(Date dataMonth);

    /**
     * 新增分销员月度实收金额
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    public int insertCommissionPayouts(CommissionPayouts commissionPayouts);

    /**
     * 新增或更新分销员月度实收金额（处理重复记录）
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    public int insertOrUpdateCommissionPayouts(CommissionPayouts commissionPayouts);

    /**
     * 修改分销员月度实收金额
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    public int updateCommissionPayouts(CommissionPayouts commissionPayouts);

    /**
     * 批量删除分销员月度实收金额
     * 
     * @param ids 需要删除的分销员月度实收金额主键集合
     * @return 结果
     */
    public int deleteCommissionPayoutsByIds(Long[] ids);

    /**
     * 删除分销员月度实收金额信息
     * 
     * @param id 分销员月度实收金额主键
     * @return 结果
     */
    public int deleteCommissionPayoutsById(Long id);

    /**
     * 根据月份删除分销员月度实收金额
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionPayoutsByMonth(Date dataMonth);
} 