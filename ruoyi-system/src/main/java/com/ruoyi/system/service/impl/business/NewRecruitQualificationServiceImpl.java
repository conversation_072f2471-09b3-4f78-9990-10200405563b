package com.ruoyi.system.service.impl.business;

import java.util.*;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.service.business.INewRecruitQualificationService;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.system.service.commission.ICommissionSettingsService;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.commission.CommissionSettings;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 拉新用户资格判定Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class NewRecruitQualificationServiceImpl implements INewRecruitQualificationService
{
    private static final Logger log = LoggerFactory.getLogger(NewRecruitQualificationServiceImpl.class);
    
    @Autowired
    private ICreatorService creatorService;

    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;

    @Autowired
    private ICommissionSettingsService commissionSettingsService;

    // 配置参数键名
    private static final String CONFIG_KEY_TENURE_DAYS = "new_recruit_tenure_days";
    private static final String CONFIG_KEY_MIN_VALID_DAYS = "new_recruit_min_valid_days";

    // 默认值（当配置不存在时使用）
    private static final int DEFAULT_TENURE_DAYS = 60;  // 账号创建时间在60天内
    private static final int DEFAULT_MIN_VALID_DAYS = 24;  // 60天内有效直播24天
    
    /**
     * 执行全量拉新资格判定
     * 作为佣金计算的前置任务，判定所有符合条件的新用户并标记为合格拉新
     * 
     * @param dataMonth 考核月份
     * @return 判定结果统计信息
     */
    @Override
    @Transactional
    public Map<String, Object> executeNewRecruitQualification(Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        String monthStr = new SimpleDateFormat("yyyy-MM").format(dataMonth);
        
        try {
            log.info("=== 开始执行 {} 月度拉新资格判定 ===", monthStr);
            
            // 第一步：获取候选用户列表（first_qualified_month为NULL的用户）
            List<Creator> candidates = getCandidateCreators();
            log.info("找到 {} 个候选用户需要进行资格判定", candidates.size());
            
            if (candidates.isEmpty()) {
                result.put("success", true);
                result.put("message", "没有需要判定的候选用户");
                result.put("qualifiedCount", 0);
                result.put("totalCandidates", 0);
                return result;
            }
            
            // 第二步：逐个判定用户资格并统计结果
            int qualifiedCount = 0;
            int processedCount = 0;
            List<Map<String, Object>> qualifiedUsers = new ArrayList<>();

            log.info("=== 开始逐个判定用户资格 ===");
            for (Creator candidate : candidates) {
                try {
                    log.info("正在处理用户 {} (昵称: {}, Handle: {}, 创建时间: {}, 上级ID: {})",
                        candidate.getId(),
                        candidate.getNickname(),
                        candidate.getHandle(),
                        candidate.getCreateTime(),
                        candidate.getParentId());

                    Map<String, Object> qualificationResult = checkAndMarkQualification(candidate, dataMonth);
                    processedCount++;

                    // 详细记录判定结果
                    boolean qualified = (Boolean) qualificationResult.get("qualified");
                    log.info("用户 {} 判定结果: {} - {}",
                        candidate.getId(),
                        qualified ? "符合条件" : "不符合条件",
                        qualificationResult.get("qualificationReason"));

                    if (qualified) {
                        qualifiedCount++;
                        qualifiedUsers.add(qualificationResult);
                        log.info("✓ 用户 {} 符合拉新资格，已标记为合格", candidate.getId());
                    } else {
                        log.info("✗ 用户 {} 不符合拉新资格: {}",
                            candidate.getId(),
                            qualificationResult.get("qualificationReason"));
                    }

                } catch (Exception e) {
                    log.error("判定用户 {} 的拉新资格时发生错误: {}", candidate.getId(), e.getMessage(), e);
                }
            }

            log.info("=== 用户资格判定完成 - 处理总数: {}, 符合条件: {} ===", processedCount, qualifiedCount);
            
            log.info("=== 完成 {} 月度拉新资格判定 ===", monthStr);
            log.info("处理用户数: {}, 合格用户数: {}", processedCount, qualifiedCount);
            
            result.put("success", true);
            result.put("message", "拉新资格判定执行成功");
            result.put("dataMonth", monthStr);
            result.put("totalCandidates", processedCount);
            result.put("qualifiedCount", qualifiedCount);
            result.put("qualifiedUsers", qualifiedUsers);
            
        } catch (Exception e) {
            log.error("执行拉新资格判定失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "判定执行失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 预览拉新资格判定结果（不保存到数据库）
     * 用于调试和验证判定逻辑
     * 
     * @param dataMonth 考核月份
     * @return 预览结果信息
     */
    @Override
    public Map<String, Object> previewNewRecruitQualification(Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        String monthStr = new SimpleDateFormat("yyyy-MM").format(dataMonth);
        
        try {
            log.info("=== 开始预览 {} 月度拉新资格判定 ===", monthStr);
            
            // 获取候选用户列表
            List<Creator> candidates = getCandidateCreators();
            log.info("找到 {} 个候选用户", candidates.size());
            
            List<Map<String, Object>> previewResults = new ArrayList<>();
            int qualifiedCount = 0;
            
            for (Creator candidate : candidates) {
                Map<String, Object> qualificationCheck = checkQualificationOnly(candidate, dataMonth);
                previewResults.add(qualificationCheck);
                
                if ((Boolean) qualificationCheck.get("qualified")) {
                    qualifiedCount++;
                }
            }
            
            result.put("success", true);
            result.put("dataMonth", monthStr);
            result.put("totalCandidates", candidates.size());
            result.put("qualifiedCount", qualifiedCount);
            result.put("previewResults", previewResults);
            
        } catch (Exception e) {
            log.error("预览拉新资格判定失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "预览失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查单个用户的拉新资格
     * 
     * @param creatorId 用户ID
     * @param dataMonth 考核月份
     * @return 资格检查结果
     */
    @Override
    public Map<String, Object> checkSingleCreatorQualification(Long creatorId, Date dataMonth) {
        try {
            Creator creator = creatorService.selectCreatorById(creatorId);
            if (creator == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }
            
            Map<String, Object> qualificationCheck = checkQualificationOnly(creator, dataMonth);
            qualificationCheck.put("success", true);
            return qualificationCheck;
            
        } catch (Exception e) {
            log.error("检查用户 {} 的拉新资格失败：{}", creatorId, e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "检查失败：" + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取候选用户列表（first_qualified_month为NULL的用户）
     */
    private List<Creator> getCandidateCreators() {
        log.info("=== 开始获取候选用户列表 ===");

        Creator queryCondition = new Creator();
        List<Creator> allCreators = creatorService.selectCreatorList(queryCondition);
        log.info("从数据库查询到总用户数: {}", allCreators.size());

        // 过滤出first_qualified_month为NULL的用户
        List<Creator> candidates = new ArrayList<>();
        int totalUsers = 0;
        int alreadyQualifiedUsers = 0;
        int candidateUsers = 0;

        for (Creator creator : allCreators) {
            totalUsers++;
            if (creator.getFirstQualifiedMonth() == null) {
                candidates.add(creator);
                candidateUsers++;
                log.debug("候选用户: ID={}, 昵称={}, Handle={}, 创建时间={}, 上级ID={}",
                    creator.getId(),
                    creator.getNickname(),
                    creator.getHandle(),
                    creator.getCreateTime(),
                    creator.getParentId());
            } else {
                alreadyQualifiedUsers++;
                log.debug("已合格用户: ID={}, 昵称={}, 合格月份={}",
                    creator.getId(),
                    creator.getNickname(),
                    creator.getFirstQualifiedMonth());
            }
        }

        log.info("用户统计 - 总用户数: {}, 已合格用户: {}, 候选用户: {}",
            totalUsers, alreadyQualifiedUsers, candidateUsers);
        log.info("=== 候选用户列表获取完成 ===");

        return candidates;
    }
    
    /**
     * 检查用户资格并标记（如果符合条件）
     */
    @Transactional
    private Map<String, Object> checkAndMarkQualification(Creator creator, Date dataMonth) {
        Map<String, Object> result = checkQualificationOnly(creator, dataMonth);
        
        // 如果符合资格，则标记用户
        if ((Boolean) result.get("qualified")) {
            markCreatorAsQualified(creator, dataMonth);
        }
        
        return result;
    }
    
    /**
     * 仅检查用户资格（不保存到数据库）
     */
    private Map<String, Object> checkQualificationOnly(Creator creator, Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        result.put("creatorId", creator.getId());
        result.put("nickname", creator.getNickname());
        result.put("handle", creator.getHandle());
        result.put("parentId", creator.getParentId());

        log.info("--- 开始检查用户 {} 的拉新资格 ---", creator.getId());

        try {
            // 获取配置参数
            Map<String, Integer> config = getQualificationConfigFromDB();
            int tenureDays = config.get("tenureDays");
            int minValidDays = config.get("minValidDays");

            log.info("配置参数 - 账号创建天数阈值: {}天, 最小有效直播天数: {}天", tenureDays, minValidDays);

            // 条件1：检查账号创建时间是否在指定天数内
            log.info("检查条件1: 账号创建时间是否在{}天内", tenureDays);
            boolean tenureQualified = checkTenureQualification(creator, dataMonth, tenureDays);
            result.put("tenureQualified", tenureQualified);
            result.put("createdAt", creator.getCreateTime());
            result.put("tenureDaysConfig", tenureDays);

            log.info("条件1结果: {} - 用户创建时间: {}",
                tenureQualified ? "符合" : "不符合",
                creator.getCreateTime());

            // 条件2：检查指定天数内有效直播天数是否达到要求
            log.info("检查条件2: {}天内有效直播天数是否达到{}天", tenureDays, minValidDays);
            Map<String, Object> validDaysCheck = checkValidDaysQualificationIn60Days(creator.getId(), dataMonth, tenureDays, minValidDays);
            boolean validDaysQualified = (Boolean) validDaysCheck.get("qualified");
            result.put("validDaysQualified", validDaysQualified);
            result.put("validDaysIn60Days", validDaysCheck.get("validDays"));
            result.put("requiredValidDays", minValidDays);

            log.info("条件2结果: {} - 实际有效直播天数: {}, 要求: {}天",
                validDaysQualified ? "符合" : "不符合",
                validDaysCheck.get("validDays"),
                minValidDays);

            // 综合判定
            boolean qualified = tenureQualified && validDaysQualified;
            result.put("qualified", qualified);

            if (qualified) {
                String reason = String.format("符合拉新资格：账号创建时间符合要求且%d天内有效直播天数达标", tenureDays);
                result.put("qualificationReason", reason);
                log.info("✓ 最终判定: 符合拉新资格");
            } else {
                List<String> reasons = new ArrayList<>();
                if (!tenureQualified) {
                    reasons.add("账号创建时间超过" + tenureDays + "天");
                }
                if (!validDaysQualified) {
                    reasons.add(tenureDays + "天内有效直播天数不足" + minValidDays + "天");
                }
                String reason = "不符合拉新资格：" + String.join("，", reasons);
                result.put("qualificationReason", reason);
                log.info("✗ 最终判定: 不符合拉新资格 - {}", reason);
            }

        } catch (Exception e) {
            log.error("检查用户 {} 资格时发生错误: {}", creator.getId(), e.getMessage(), e);
            result.put("qualified", false);
            result.put("qualificationReason", "检查过程中发生错误：" + e.getMessage());
        }

        log.info("--- 用户 {} 资格检查完成 ---", creator.getId());
        return result;
    }
    
    /**
     * 检查账号创建时间资格
     */
    private boolean checkTenureQualification(Creator creator, Date dataMonth, int tenureDays) {
        log.debug("检查账号创建时间资格 - 用户ID: {}, 考核月份: {}, 天数阈值: {}天",
            creator.getId(), new SimpleDateFormat("yyyy-MM").format(dataMonth), tenureDays);

        if (creator.getCreateTime() == null) {
            log.warn("用户 {} 的创建时间为空，不符合条件", creator.getId());
            return false;
        }

        // 计算考核月的最后一天
        Calendar cal = Calendar.getInstance();
        cal.setTime(dataMonth);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        Date monthEndDate = cal.getTime();

        // 计算指定天数前的日期
        cal.setTime(monthEndDate);
        cal.add(Calendar.DAY_OF_YEAR, -tenureDays);
        Date tenureThresholdDate = cal.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("时间计算详情:");
        log.info("  考核月份: {}", new SimpleDateFormat("yyyy-MM").format(dataMonth));
        log.info("  考核月最后一天: {}", sdf.format(monthEndDate));
        log.info("  {}天前的阈值日期: {}", tenureDays, sdf.format(tenureThresholdDate));
        log.info("  用户创建时间: {}", sdf.format(creator.getCreateTime()));

        // 用户创建时间必须大于或等于阈值日期
        boolean qualified = creator.getCreateTime().compareTo(tenureThresholdDate) >= 0;

        log.info("账号创建时间判定: {} (用户创建时间 {} 阈值日期)",
            qualified ? "符合" : "不符合",
            qualified ? ">=" : "<");

        return qualified;
    }
    
    /**
     * 检查指定天数内有效直播天数资格
     * 统计用户在过去指定天数内的总有效直播天数是否达到要求
     */
    private Map<String, Object> checkValidDaysQualificationIn60Days(Long creatorId, Date dataMonth, int tenureDays, int minValidDays) {
        Map<String, Object> result = new HashMap<>();

        log.info("=== 开始检查用户 {} 在{}天内的有效直播天数 ===", creatorId, tenureDays);
        log.info("传入的dataMonth参数: {}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dataMonth));

        try {
            // 计算指定天数的时间范围
            Calendar cal = Calendar.getInstance();
            cal.setTime(dataMonth);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            Date endDate = cal.getTime();

            // 重新创建Calendar对象来计算开始日期，避免修改endDate的Calendar
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(endDate);
            startCal.add(Calendar.DAY_OF_YEAR, -tenureDays + 1); // +1是因为包含结束日期当天
            Date startDate = startCal.getTime();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            log.info("有效直播天数检查时间范围: {} 至 {}", sdf.format(startDate), sdf.format(endDate));
            log.info("考核月份: {}, 往前推{}天", new SimpleDateFormat("yyyy-MM").format(dataMonth), tenureDays);

            // 查询用户在指定天数内的所有业绩数据
            int totalValidDays = 0;
            List<String> monthsChecked = new ArrayList<>();

            // 从开始日期到结束日期，按月查询业绩数据
            Calendar monthCal = Calendar.getInstance();
            monthCal.setTime(startDate);

            log.info("开始按月查询业绩数据...");
            while (monthCal.getTime().before(endDate) || monthCal.getTime().equals(endDate)) {
                // 设置为当月第一天的00:00:00，确保与数据库存储格式一致
                monthCal.set(Calendar.DAY_OF_MONTH, 1);
                monthCal.set(Calendar.HOUR_OF_DAY, 0);
                monthCal.set(Calendar.MINUTE, 0);
                monthCal.set(Calendar.SECOND, 0);
                monthCal.set(Calendar.MILLISECOND, 0);
                Date monthToCheck = monthCal.getTime();
                String monthStr = new SimpleDateFormat("yyyy-MM").format(monthToCheck);

                log.info("查询月份: {} (精确时间: {})", monthStr, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(monthToCheck));
                MonthlyPerformance performance = getCreatorPerformance(creatorId, monthToCheck);

                if (performance != null && performance.getValidDays() != null) {
                    // 计算该月在60天范围内的有效天数
                    int monthValidDays = calculateValidDaysInRange(performance, startDate, endDate, monthToCheck);
                    totalValidDays += monthValidDays;

                    monthsChecked.add(monthStr + ":" + monthValidDays + "天");
                    log.info("月份 {} - 找到业绩数据，原始有效天数: {}天，范围内有效天数: {}天",
                        monthStr, performance.getValidDays(), monthValidDays);
                } else {
                    monthsChecked.add(monthStr + ":0天");
                    log.info("月份 {} - 未找到业绩数据或有效天数为空", monthStr);
                }

                // 移动到下一个月
                monthCal.add(Calendar.MONTH, 1);
            }

            log.info("有效直播天数统计:");
            log.info("  检查月份详情: {}", monthsChecked);
            log.info("  总有效直播天数: {}天", totalValidDays);
            log.info("  要求最小天数: {}天", minValidDays);

            boolean qualified = totalValidDays >= minValidDays;
            result.put("qualified", qualified);
            result.put("validDays", totalValidDays);
            result.put("monthsChecked", monthsChecked);
            result.put("dateRange", sdf.format(startDate) + " 至 " + sdf.format(endDate));
            result.put("reason", qualified ? tenureDays + "天内有效直播天数达标" : tenureDays + "天内有效直播天数不足");

            log.info("有效直播天数判定: {} ({}天 {} {}天)",
                qualified ? "符合" : "不符合",
                totalValidDays,
                qualified ? ">=" : "<",
                minValidDays);

        } catch (Exception e) {
            log.error("检查用户 {} 的60天内有效直播天数失败: {}", creatorId, e.getMessage(), e);
            result.put("qualified", false);
            result.put("validDays", 0);
            result.put("reason", "检查过程中发生错误：" + e.getMessage());
        }

        return result;
    }

    /**
     * 计算指定月份在日期范围内的有效天数
     * 如果月份完全在范围内，返回该月的全部有效天数
     * 如果月份部分在范围内，按比例计算
     */
    private int calculateValidDaysInRange(MonthlyPerformance performance, Date startDate, Date endDate, Date monthDate) {
        String monthStr = new SimpleDateFormat("yyyy-MM").format(monthDate);
        log.debug("计算月份 {} 在范围内的有效天数", monthStr);

        if (performance.getValidDays() == null) {
            log.debug("月份 {} 的有效天数为null，返回0", monthStr);
            return 0;
        }

        try {
            Calendar cal = Calendar.getInstance();

            // 计算该月的开始和结束日期
            cal.setTime(monthDate);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            Date monthStart = cal.getTime();

            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            Date monthEnd = cal.getTime();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            log.debug("月份 {} 时间范围: {} 至 {}", monthStr, sdf.format(monthStart), sdf.format(monthEnd));
            log.debug("检查范围: {} 至 {}", sdf.format(startDate), sdf.format(endDate));

            // 计算重叠的日期范围
            Date overlapStart = startDate.after(monthStart) ? startDate : monthStart;
            Date overlapEnd = endDate.before(monthEnd) ? endDate : monthEnd;

            log.debug("重叠范围: {} 至 {}", sdf.format(overlapStart), sdf.format(overlapEnd));

            if (overlapStart.after(overlapEnd)) {
                log.debug("月份 {} 与检查范围没有重叠，返回0", monthStr);
                return 0; // 没有重叠
            }

            // 如果整个月都在范围内，返回全部有效天数
            if (!startDate.after(monthStart) && !endDate.before(monthEnd)) {
                log.debug("月份 {} 完全在检查范围内，返回全部有效天数: {}", monthStr, performance.getValidDays());
                return performance.getValidDays();
            }

            // 计算重叠天数占该月总天数的比例
            long monthDays = (monthEnd.getTime() - monthStart.getTime()) / (24 * 60 * 60 * 1000) + 1;
            long overlapDays = (overlapEnd.getTime() - overlapStart.getTime()) / (24 * 60 * 60 * 1000) + 1;

            double ratio = (double) overlapDays / monthDays;
            int validDaysInRange = (int) Math.round(performance.getValidDays() * ratio);

            log.info("月份 {} 有效天数按比例计算:", monthStr);
            log.info("  原始有效天数: {}天", performance.getValidDays());
            log.info("  月份总天数: {}天", monthDays);
            log.info("  重叠天数: {}天", overlapDays);
            log.info("  重叠比例: {}", String.format("%.2f", ratio));
            log.info("  范围内有效天数: {}天", validDaysInRange);

            return validDaysInRange;

        } catch (Exception e) {
            log.error("计算月份 {} 在范围内的有效天数失败: {}", monthStr, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取用户月度业绩数据
     */
    private MonthlyPerformance getCreatorPerformance(Long creatorId, Date dataMonth) {
        log.info("查询用户 {} 在 {} 的月度业绩数据", creatorId, new SimpleDateFormat("yyyy-MM").format(dataMonth));
        log.info("传入的dataMonth详细信息: {}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dataMonth));

        MonthlyPerformance queryCondition = new MonthlyPerformance();
        queryCondition.setCreatorId(creatorId);
        queryCondition.setDataMonth(dataMonth);

        List<MonthlyPerformance> performances = monthlyPerformanceService.selectMonthlyPerformanceList(queryCondition);

        if (performances.isEmpty()) {
            log.info("用户 {} 在 {} 没有业绩数据", creatorId, new SimpleDateFormat("yyyy-MM").format(dataMonth));
            return null;
        } else {
            MonthlyPerformance performance = performances.get(0);
            log.info("用户 {} 在 {} 的业绩数据: 有效天数={}, 钻石数={}",
                creatorId,
                new SimpleDateFormat("yyyy-MM").format(dataMonth),
                performance.getValidDays(),
                performance.getDiamonds());
            return performance;
        }
    }
    
    /**
     * 标记用户为合格拉新
     */
    @Transactional
    private void markCreatorAsQualified(Creator creator, Date dataMonth) {
        // 设置首次合格月份为当前考核月份的第一天
        Calendar cal = Calendar.getInstance();
        cal.setTime(dataMonth);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date firstQualifiedMonth = cal.getTime();
        
        // 更新用户记录
        Creator updateCreator = new Creator();
        updateCreator.setId(creator.getId());
        updateCreator.setFirstQualifiedMonth(firstQualifiedMonth);
        updateCreator.setQualifiedByRecruiterId(creator.getParentId()); // 快照当前的parent_id
        updateCreator.setUpdateTime(DateUtils.getNowDate());
        
        int updateResult = creatorService.updateCreator(updateCreator);
        
        if (updateResult > 0) {
            log.info("成功标记用户 {} 为合格拉新，合格月份: {}, 招募人ID: {}", 
                creator.getId(), 
                new SimpleDateFormat("yyyy-MM").format(firstQualifiedMonth),
                creator.getParentId());
        } else {
            log.error("标记用户 {} 为合格拉新失败", creator.getId());
        }
    }
    
    /**
     * 获取指定月份的拉新资格统计信息
     *
     * @param dataMonth 考核月份
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getQualificationStatistics(Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        String monthStr = new SimpleDateFormat("yyyy-MM").format(dataMonth);

        try {
            // 统计指定月份合格的用户数量
            Calendar cal = Calendar.getInstance();
            cal.setTime(dataMonth);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            Date monthStart = cal.getTime();

            Creator queryCondition = new Creator();
            List<Creator> allCreators = creatorService.selectCreatorList(queryCondition);

            int qualifiedInMonth = 0;
            int totalQualified = 0;
            Map<Long, Integer> recruiterStats = new HashMap<>();

            for (Creator creator : allCreators) {
                if (creator.getFirstQualifiedMonth() != null) {
                    totalQualified++;

                    // 统计指定月份合格的用户
                    if (isSameMonth(creator.getFirstQualifiedMonth(), monthStart)) {
                        qualifiedInMonth++;

                        // 按招募人统计
                        Long recruiterId = creator.getQualifiedByRecruiterId();
                        if (recruiterId != null) {
                            recruiterStats.put(recruiterId, recruiterStats.getOrDefault(recruiterId, 0) + 1);
                        }
                    }
                }
            }

            result.put("success", true);
            result.put("dataMonth", monthStr);
            result.put("qualifiedInMonth", qualifiedInMonth);
            result.put("totalQualified", totalQualified);
            result.put("recruiterStats", recruiterStats);

        } catch (Exception e) {
            log.error("获取拉新资格统计信息失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取统计信息失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取已合格拉新用户列表
     *
     * @param dataMonth 考核月份（可选，为null时返回所有已合格用户）
     * @param recruiterId 招募人ID（可选，为null时返回所有招募人的数据）
     * @return 已合格拉新用户列表
     */
    @Override
    public List<Map<String, Object>> getQualifiedNewRecruits(Date dataMonth, Long recruiterId) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            Creator queryCondition = new Creator();
            List<Creator> allCreators = creatorService.selectCreatorList(queryCondition);

            for (Creator creator : allCreators) {
                if (creator.getFirstQualifiedMonth() != null) {
                    // 按月份过滤
                    if (dataMonth != null) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(dataMonth);
                        cal.set(Calendar.DAY_OF_MONTH, 1);
                        Date monthStart = cal.getTime();

                        if (!isSameMonth(creator.getFirstQualifiedMonth(), monthStart)) {
                            continue;
                        }
                    }

                    // 按招募人过滤
                    if (recruiterId != null && !recruiterId.equals(creator.getQualifiedByRecruiterId())) {
                        continue;
                    }

                    Map<String, Object> qualifiedUser = new HashMap<>();
                    qualifiedUser.put("creatorId", creator.getId());
                    qualifiedUser.put("nickname", creator.getNickname());
                    qualifiedUser.put("handle", creator.getHandle());
                    qualifiedUser.put("parentId", creator.getParentId());
                    qualifiedUser.put("firstQualifiedMonth", creator.getFirstQualifiedMonth());
                    qualifiedUser.put("qualifiedByRecruiterId", creator.getQualifiedByRecruiterId());
                    qualifiedUser.put("createdAt", creator.getCreateTime());

                    result.add(qualifiedUser);
                }
            }

        } catch (Exception e) {
            log.error("获取已合格拉新用户列表失败：{}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 重置指定月份的拉新资格判定结果
     * 将指定月份标记为合格的用户重置为未判定状态
     *
     * @param dataMonth 考核月份
     * @return 重置结果信息
     */
    @Override
    @Transactional
    public Map<String, Object> resetQualificationForMonth(Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        String monthStr = new SimpleDateFormat("yyyy-MM").format(dataMonth);

        try {
            log.info("=== 开始重置 {} 月度拉新资格判定结果 ===", monthStr);

            Calendar cal = Calendar.getInstance();
            cal.setTime(dataMonth);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            Date monthStart = cal.getTime();

            Creator queryCondition = new Creator();
            List<Creator> allCreators = creatorService.selectCreatorList(queryCondition);

            int resetCount = 0;
            for (Creator creator : allCreators) {
                if (creator.getFirstQualifiedMonth() != null &&
                    isSameMonth(creator.getFirstQualifiedMonth(), monthStart)) {

                    // 重置用户的合格标记
                    Creator updateCreator = new Creator();
                    updateCreator.setId(creator.getId());
                    updateCreator.setFirstQualifiedMonth(null);
                    updateCreator.setQualifiedByRecruiterId(null);
                    updateCreator.setUpdateTime(DateUtils.getNowDate());

                    int updateResult = creatorService.updateCreator(updateCreator);
                    if (updateResult > 0) {
                        resetCount++;
                        log.info("重置用户 {} 的拉新资格标记", creator.getId());
                    }
                }
            }

            log.info("=== 完成重置 {} 月度拉新资格判定结果，重置用户数: {} ===", monthStr, resetCount);

            result.put("success", true);
            result.put("message", "重置成功");
            result.put("dataMonth", monthStr);
            result.put("resetCount", resetCount);

        } catch (Exception e) {
            log.error("重置拉新资格判定结果失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "重置失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取拉新资格判定的配置参数
     *
     * @return 配置参数信息
     */
    @Override
    public Map<String, Object> getQualificationConfig() {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Integer> config = getQualificationConfigFromDB();
            result.put("success", true);
            result.put("tenureDays", config.get("tenureDays"));
            result.put("minValidDays", config.get("minValidDays"));
            result.put("description", "拉新用户资格判定配置参数");
            Map<String, String> configKeys = new HashMap<>();
            configKeys.put("tenureDays", CONFIG_KEY_TENURE_DAYS);
            configKeys.put("minValidDays", CONFIG_KEY_MIN_VALID_DAYS);
            result.put("configKeys", configKeys);
        } catch (Exception e) {
            log.error("获取拉新资格判定配置失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取配置失败：" + e.getMessage());
            result.put("tenureDays", DEFAULT_TENURE_DAYS);
            result.put("minValidDays", DEFAULT_MIN_VALID_DAYS);
        }

        return result;
    }

    /**
     * 更新拉新资格判定的配置参数
     *
     * @param config 配置参数
     * @return 更新结果
     */
    @Override
    public Map<String, Object> updateQualificationConfig(Map<String, Object> config) {
        Map<String, Object> result = new HashMap<>();

        try {
            int updatedCount = 0;
            List<String> updatedItems = new ArrayList<>();

            // 更新账号创建天数阈值
            if (config.containsKey("tenureDays")) {
                Object tenureDaysObj = config.get("tenureDays");
                if (tenureDaysObj != null) {
                    String tenureDaysValue = tenureDaysObj.toString();
                    try {
                        Integer.parseInt(tenureDaysValue); // 验证是否为有效整数

                        CommissionSettings setting = commissionSettingsService.selectCommissionSettingsByKey(CONFIG_KEY_TENURE_DAYS);
                        if (setting != null) {
                            setting.setSettingValue(tenureDaysValue);
                            setting.setUpdateTime(DateUtils.getNowDate());
                            commissionSettingsService.updateCommissionSettings(setting);
                        } else {
                            setting = new CommissionSettings();
                            setting.setSettingKey(CONFIG_KEY_TENURE_DAYS);
                            setting.setSettingValue(tenureDaysValue);
                            setting.setDescription("新人资格-账号最大创建天数");
                            setting.setCreateTime(DateUtils.getNowDate());
                            commissionSettingsService.insertCommissionSettings(setting);
                        }
                        updatedCount++;
                        updatedItems.add("账号创建天数阈值: " + tenureDaysValue + "天");
                    } catch (NumberFormatException e) {
                        result.put("success", false);
                        result.put("message", "账号创建天数阈值必须是有效的整数");
                        return result;
                    }
                }
            }

            // 更新最小有效直播天数
            if (config.containsKey("minValidDays")) {
                Object minValidDaysObj = config.get("minValidDays");
                if (minValidDaysObj != null) {
                    String minValidDaysValue = minValidDaysObj.toString();
                    try {
                        Integer.parseInt(minValidDaysValue); // 验证是否为有效整数

                        CommissionSettings setting = commissionSettingsService.selectCommissionSettingsByKey(CONFIG_KEY_MIN_VALID_DAYS);
                        if (setting != null) {
                            setting.setSettingValue(minValidDaysValue);
                            setting.setUpdateTime(DateUtils.getNowDate());
                            commissionSettingsService.updateCommissionSettings(setting);
                        } else {
                            setting = new CommissionSettings();
                            setting.setSettingKey(CONFIG_KEY_MIN_VALID_DAYS);
                            setting.setSettingValue(minValidDaysValue);
                            setting.setDescription("新人资格-月度最低有效直播天数");
                            setting.setCreateTime(DateUtils.getNowDate());
                            commissionSettingsService.insertCommissionSettings(setting);
                        }
                        updatedCount++;
                        updatedItems.add("最小有效直播天数: " + minValidDaysValue + "天");
                    } catch (NumberFormatException e) {
                        result.put("success", false);
                        result.put("message", "最小有效直播天数必须是有效的整数");
                        return result;
                    }
                }
            }

            if (updatedCount > 0) {
                result.put("success", true);
                result.put("message", "配置更新成功");
                result.put("updatedCount", updatedCount);
                result.put("updatedItems", updatedItems);
                log.info("拉新资格判定配置更新成功: {}", String.join(", ", updatedItems));
            } else {
                result.put("success", false);
                result.put("message", "没有有效的配置项需要更新");
            }

        } catch (Exception e) {
            log.error("更新拉新资格判定配置失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "更新配置失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 判断两个日期是否为同一个月
     */
    private boolean isSameMonth(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
    }

    /**
     * 获取拉新资格判定配置参数
     * 从数据库中读取配置，如果不存在则使用默认值
     */
    private Map<String, Integer> getQualificationConfigFromDB() {
        Map<String, Integer> config = new HashMap<>();

        log.info("=== 开始加载拉新资格判定配置 ===");

        try {
            // 获取账号创建天数阈值
            log.info("查询配置项: {}", CONFIG_KEY_TENURE_DAYS);
            CommissionSettings tenureDaysSetting = commissionSettingsService.selectCommissionSettingsByKey(CONFIG_KEY_TENURE_DAYS);
            int tenureDays = DEFAULT_TENURE_DAYS;

            if (tenureDaysSetting != null && tenureDaysSetting.getSettingValue() != null) {
                log.info("找到配置项 {} = {}", CONFIG_KEY_TENURE_DAYS, tenureDaysSetting.getSettingValue());
                try {
                    tenureDays = Integer.parseInt(tenureDaysSetting.getSettingValue());
                    log.info("成功解析账号创建天数阈值: {}天", tenureDays);
                } catch (NumberFormatException e) {
                    log.warn("配置项 {} 的值 {} 不是有效的整数，使用默认值 {}天",
                        CONFIG_KEY_TENURE_DAYS, tenureDaysSetting.getSettingValue(), DEFAULT_TENURE_DAYS);
                }
            } else {
                log.warn("未找到配置项 {}，使用默认值 {}天", CONFIG_KEY_TENURE_DAYS, DEFAULT_TENURE_DAYS);
            }
            config.put("tenureDays", tenureDays);

            // 获取最小有效直播天数
            log.info("查询配置项: {}", CONFIG_KEY_MIN_VALID_DAYS);
            CommissionSettings minValidDaysSetting = commissionSettingsService.selectCommissionSettingsByKey(CONFIG_KEY_MIN_VALID_DAYS);
            int minValidDays = DEFAULT_MIN_VALID_DAYS;

            if (minValidDaysSetting != null && minValidDaysSetting.getSettingValue() != null) {
                log.info("找到配置项 {} = {}", CONFIG_KEY_MIN_VALID_DAYS, minValidDaysSetting.getSettingValue());
                try {
                    minValidDays = Integer.parseInt(minValidDaysSetting.getSettingValue());
                    log.info("成功解析最小有效直播天数: {}天", minValidDays);
                } catch (NumberFormatException e) {
                    log.warn("配置项 {} 的值 {} 不是有效的整数，使用默认值 {}天",
                        CONFIG_KEY_MIN_VALID_DAYS, minValidDaysSetting.getSettingValue(), DEFAULT_MIN_VALID_DAYS);
                }
            } else {
                log.warn("未找到配置项 {}，使用默认值 {}天", CONFIG_KEY_MIN_VALID_DAYS, DEFAULT_MIN_VALID_DAYS);
            }
            config.put("minValidDays", minValidDays);

            log.info("✓ 拉新资格判定配置加载完成:");
            log.info("  账号创建天数阈值: {}天", tenureDays);
            log.info("  最小有效直播天数: {}天", minValidDays);

        } catch (Exception e) {
            log.error("从数据库加载拉新资格判定配置失败，使用默认值: {}", e.getMessage(), e);
            config.put("tenureDays", DEFAULT_TENURE_DAYS);
            config.put("minValidDays", DEFAULT_MIN_VALID_DAYS);
            log.info("使用默认配置: 账号创建天数阈值={}天, 最小有效直播天数={}天",
                DEFAULT_TENURE_DAYS, DEFAULT_MIN_VALID_DAYS);
        }

        log.info("=== 配置加载完成 ===");
        return config;
    }
}
