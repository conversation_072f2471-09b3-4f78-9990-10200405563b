package com.ruoyi.system.service.business;

import org.springframework.web.multipart.MultipartFile;
import java.util.Map;

/**
 * 历史数据Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IHistoricalDataService
{
    /**
     * 导入Excel历史数据
     *
     * @param file Excel文件
     * @param updateBy 更新者
     * @return 导入结果统计
     */
    public Map<String, Object> importHistoricalData(MultipartFile file, String updateBy) throws Exception;

    /**
     * 导入Excel历史数据（支持创建时间策略选择）
     *
     * @param file Excel文件
     * @param updateBy 更新者
     * @param useHistoricalCreateTime 是否使用历史创建时间
     *        - true: 新creator的创建时间设为数据月份，可能影响新人资格判定
     *        - false: 新creator的创建时间设为当前时间，不影响新人资格判定
     * @return 导入结果统计
     */
    public Map<String, Object> importHistoricalData(MultipartFile file, String updateBy, boolean useHistoricalCreateTime) throws Exception;

    /**
     * 创建虚拟上级并分配给组
     *
     * @param groupName 组名
     * @param virtualNickname 虚拟上级昵称
     * @param remark 备注
     * @param createBy 创建者
     * @return 虚拟上级ID
     */
    public Long createVirtualParentForGroup(String groupName, String virtualNickname, String remark, String createBy);

    /**
     * 重建创建者关系
     * 根据group_creator_relation重建整个关系树
     *
     * @param updateBy 更新者
     * @return 重建统计信息
     */
    public Map<String, Object> rebuildCreatorRelationships(String updateBy);

    /**
     * 生成唯一的虚拟ID
     * 格式：888 + 10位时间戳 + 4位随机数 = 17位数字
     *
     * @return 虚拟ID
     */
    public Long generateVirtualCreatorId();

    /**
     * 验证Excel数据格式
     *
     * @param file Excel文件
     * @return 验证结果
     */
    public Map<String, Object> validateExcelData(MultipartFile file) throws Exception;

    /**
     * 自动为孤儿组创建虚拟上级并建立层级关系
     * 
     * @param updateBy 更新者
     * @return 处理结果统计
     */
    public Map<String, Object> autoGenerateRelationships(String updateBy);

    /**
     * 清理重复的虚拟上级
     * 
     * @param updateBy 更新者
     * @return 清理结果统计
     */
    public Map<String, Object> cleanupDuplicateVirtualCreators(String updateBy);
} 