package com.ruoyi.system.service.impl.business;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.business.GroupCreatorRelationMapper;
import com.ruoyi.system.mapper.business.CreatorMapper;
import com.ruoyi.system.domain.business.GroupCreatorRelation;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.vo.GroupRelationVO;
import com.ruoyi.system.service.business.IGroupCreatorRelationService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;

/**
 * 组名与CreatorID关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class GroupCreatorRelationServiceImpl implements IGroupCreatorRelationService
{
    @Autowired
    private GroupCreatorRelationMapper groupCreatorRelationMapper;

    @Autowired
    private CreatorMapper creatorMapper;

    /**
     * 查询组名与CreatorID关系
     *
     * @param groupName 组名与CreatorID关系主键
     * @return 组名与CreatorID关系
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public GroupCreatorRelation selectGroupCreatorRelationByGroupName(String groupName)
    {
        return groupCreatorRelationMapper.selectGroupCreatorRelationByGroupName(groupName);
    }

    /**
     * 查询组名与CreatorID关系列表
     *
     * @param groupCreatorRelation 组名与CreatorID关系
     * @return 组名与CreatorID关系
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<GroupCreatorRelation> selectGroupCreatorRelationList(GroupCreatorRelation groupCreatorRelation)
    {
        return groupCreatorRelationMapper.selectGroupCreatorRelationList(groupCreatorRelation);
    }

    /**
     * 新增组名与CreatorID关系
     *
     * @param groupCreatorRelation 组名与CreatorID关系
     * @return 结果
     */
    @Override
    public int insertGroupCreatorRelation(GroupCreatorRelation groupCreatorRelation)
    {
        groupCreatorRelation.setCreateTime(DateUtils.getNowDate());
        return groupCreatorRelationMapper.insertGroupCreatorRelation(groupCreatorRelation);
    }

    /**
     * 修改组名与CreatorID关系
     *
     * @param groupCreatorRelation 组名与CreatorID关系
     * @return 结果
     */
    @Override
    public int updateGroupCreatorRelation(GroupCreatorRelation groupCreatorRelation)
    {
        groupCreatorRelation.setUpdateTime(DateUtils.getNowDate());
        return groupCreatorRelationMapper.updateGroupCreatorRelation(groupCreatorRelation);
    }

    /**
     * 批量删除组名与CreatorID关系
     *
     * @param groupNames 需要删除的组名与CreatorID关系主键
     * @return 结果
     */
    @Override
    public int deleteGroupCreatorRelationByGroupNames(String[] groupNames)
    {
        return groupCreatorRelationMapper.deleteGroupCreatorRelationByGroupNames(groupNames);
    }

    /**
     * 删除组名与CreatorID关系信息
     *
     * @param groupName 组名与CreatorID关系主键
     * @return 结果
     */
    @Override
    public int deleteGroupCreatorRelationByGroupName(String groupName)
    {
        return groupCreatorRelationMapper.deleteGroupCreatorRelationByGroupName(groupName);
    }

    /**
     * 查询所有组与creators的关系，包含未指定creatorID的组
     *
     * @return 组关系列表
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<GroupRelationVO> selectGroupRelationList()
    {
        return groupCreatorRelationMapper.selectGroupRelationList();
    }

    /**
     * 指定组对应的上级
     *
     * @param groupName 组名
     * @param creatorId 创建者ID
     * @param updateBy 更新者
     * @return 结果
     */
    @Override
    public int assignGroupParent(String groupName, Long creatorId, String updateBy)
    {
        // 验证创建者是否存在
        Creator creator = creatorMapper.selectCreatorById(creatorId);
        if (creator == null)
        {
            throw new ServiceException("指定的创建者不存在");
        }

        // 检查是否已有关系
        GroupCreatorRelation existingRelation = groupCreatorRelationMapper.selectGroupCreatorRelationByGroupName(groupName);
        if (existingRelation != null)
        {
            // 更新现有关系
            existingRelation.setCreatorId(creatorId);
            existingRelation.setUpdateBy(updateBy);
            return updateGroupCreatorRelation(existingRelation);
        }
        else
        {
            // 创建新关系
            GroupCreatorRelation newRelation = new GroupCreatorRelation();
            newRelation.setGroupName(groupName);
            newRelation.setCreatorId(creatorId);
            newRelation.setCreateBy(updateBy);
            newRelation.setUpdateBy(updateBy);
            return insertGroupCreatorRelation(newRelation);
        }
    }

    /**
     * 根据创建者ID查询组关系
     *
     * @param creatorId 创建者ID
     * @return 组关系
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public GroupCreatorRelation selectGroupCreatorRelationByCreatorId(Long creatorId)
    {
        return groupCreatorRelationMapper.selectGroupCreatorRelationByCreatorId(creatorId);
    }

    /**
     * 删除指定创建者的组关系
     *
     * @param creatorId 创建者ID
     * @return 结果
     */
    @Override
    public int deleteGroupCreatorRelationByCreatorId(Long creatorId)
    {
        return groupCreatorRelationMapper.deleteGroupCreatorRelationByCreatorId(creatorId);
    }

    /**
     * 修改组类型
     *
     * @param groupName 组名
     * @param groupType 组类型：0-分销组，1-非分销组
     * @param updateBy 更新者
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int updateGroupType(String groupName, Integer groupType, String updateBy)
    {
        // 检查组是否存在
        GroupCreatorRelation existingRelation = groupCreatorRelationMapper.selectGroupCreatorRelationByGroupName(groupName);

        if (existingRelation != null)
        {
            // 如果组关系已存在，更新组类型
            GroupCreatorRelation updateRelation = new GroupCreatorRelation();
            updateRelation.setGroupName(groupName);
            updateRelation.setGroupType(groupType);
            updateRelation.setUpdateBy(updateBy);
            return updateGroupCreatorRelation(updateRelation);
        }
        else
        {
            // 如果组关系不存在，创建新的关系记录（只设置组名和组类型）
            GroupCreatorRelation newRelation = new GroupCreatorRelation();
            newRelation.setGroupName(groupName);
            newRelation.setGroupType(groupType);
            newRelation.setCreateBy(updateBy);
            newRelation.setUpdateBy(updateBy);
            return insertGroupCreatorRelation(newRelation);
        }
    }
}