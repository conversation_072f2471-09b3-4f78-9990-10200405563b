package com.ruoyi.system.service.business;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.CreatorTreeNode;
import com.ruoyi.system.domain.business.CreatorMonthlyTreeNode;

/**
 * 主播信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface ICreatorService
{
    /**
     * 查询主播信息
     *
     * @param id 主播信息主键
     * @return 主播信息
     */
    public Creator selectCreatorById(Long id);

    /**
     * 查询主播信息列表
     *
     * @param creator 主播信息
     * @return 主播信息集合
     */
    public List<Creator> selectCreatorList(Creator creator);

    /**
     * 新增主播信息
     *
     * @param creator 主播信息
     * @return 结果
     */
    public int insertCreator(Creator creator);

    /**
     * 修改主播信息
     *
     * @param creator 主播信息
     * @return 结果
     */
    public int updateCreator(Creator creator);

    /**
     * 批量删除主播信息
     *
     * @param ids 需要删除的主播信息主键集合
     * @return 结果
     */
    public int deleteCreatorByIds(Long[] ids);

    /**
     * 删除主播信息信息
     *
     * @param id 主播信息主键
     * @return 结果
     */
    public int deleteCreatorById(Long id);

    /**
     * 根据Handle查询主播信息
     *
     * @param handle 主播handle
     * @return 主播信息
     */
    public Creator selectCreatorByHandle(String handle);

    /**
     * 保存或更新主播信息
     * <p>
     * 如果主播ID为空，则执行插入操作；否则执行更新操作。
     * </p>
     *
     * @param creator 主播信息
     * @return 结果 (通常是影响的行数，或者返回保存/更新后的Creator对象，这里遵循若依风格返回int)
     */
    public int saveOrUpdateCreator(Creator creator);

    /**
     * 查询主播树状结构
     * 返回以根节点为起点的完整树状关系图
     *
     * @return 主播树状结构列表
     */
    public List<CreatorTreeNode> selectCreatorTree();

    /**
     * 查询主播月度数据树状结构
     * 返回包含月度业绩数据的完整树状关系图
     *
     * @param dataMonth 数据月份 (必传)
     * @param creatorId 主播ID (可选)
     * @param nickname 主播昵称 (可选)
     * @return 主播月度树状结构列表
     */
    public List<CreatorMonthlyTreeNode> selectCreatorMonthlyTree(Date dataMonth, Long creatorId, String nickname);
}
