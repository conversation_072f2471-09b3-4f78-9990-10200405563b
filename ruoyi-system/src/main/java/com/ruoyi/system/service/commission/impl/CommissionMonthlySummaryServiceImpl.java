package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionMonthlySummaryMapper;
import com.ruoyi.system.domain.commission.CommissionMonthlySummary;
import com.ruoyi.system.domain.vo.CommissionCalculationSummaryVO;
import com.ruoyi.system.service.commission.ICommissionMonthlySummaryService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 佣金月度计算总览Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionMonthlySummaryServiceImpl implements ICommissionMonthlySummaryService
{
    @Autowired
    private CommissionMonthlySummaryMapper commissionMonthlySummaryMapper;

    /**
     * 查询佣金月度计算总览
     * 
     * @param dataMonth 佣金月度计算总览主键
     * @return 佣金月度计算总览
     */
    @Override
    public CommissionMonthlySummary selectCommissionMonthlySummaryByMonth(Date dataMonth)
    {
        return commissionMonthlySummaryMapper.selectCommissionMonthlySummaryByMonth(dataMonth);
    }

    /**
     * 查询佣金月度计算总览列表
     * 
     * @param commissionMonthlySummary 佣金月度计算总览
     * @return 佣金月度计算总览
     */
    @Override
    public List<CommissionMonthlySummary> selectCommissionMonthlySummaryList(CommissionMonthlySummary commissionMonthlySummary)
    {
        return commissionMonthlySummaryMapper.selectCommissionMonthlySummaryList(commissionMonthlySummary);
    }

    /**
     * 获取财务总览报表VO
     * 
     * @param dataMonth 数据月份
     * @return 财务总览报表VO
     */
    @Override
    public CommissionCalculationSummaryVO getFinancialOverviewReport(Date dataMonth)
    {
        CommissionMonthlySummary summary = selectCommissionMonthlySummaryByMonth(dataMonth);
        
        CommissionCalculationSummaryVO vo = new CommissionCalculationSummaryVO();
        if (summary != null) {
            vo.setDataMonth(summary.getDataMonth());
            vo.setBudgetUsd(summary.getBudgetUsd());
            vo.setPayoutCapUsd(summary.getPayoutCapUsd());
            vo.setActualPayoutUsd(summary.getActualPayoutUsd());
            vo.setPayoutToBudgetRatio(summary.getPayoutToBudgetRatio());
            vo.setExceededAmountUsd(summary.getExceededAmountUsd());
            vo.setTotalBonusEstimated(summary.getTotalBonusEstimated() != null ? summary.getTotalBonusEstimated() : BigDecimal.ZERO);
            vo.setCalculationStatus(summary.getCalculationStatus());
            vo.setCalculatedAt(summary.getCalculatedAt());
        } else {
            vo.setDataMonth(dataMonth);
            vo.setBudgetUsd(BigDecimal.ZERO);
            vo.setPayoutCapUsd(BigDecimal.ZERO);
            vo.setActualPayoutUsd(BigDecimal.ZERO);
            vo.setPayoutToBudgetRatio(BigDecimal.ZERO);
            vo.setExceededAmountUsd(BigDecimal.ZERO);
            vo.setTotalBonusEstimated(BigDecimal.ZERO);
            vo.setCalculationStatus("PENDING");
        }
        
        return vo;
    }

    /**
     * 根据月份查询主播奖金估计累计值
     * 
     * @param dataMonth 数据月份
     * @return 当月主播奖金估计累计值
     */
    @Override
    public BigDecimal selectTotalBonusEstimatedByMonth(Date dataMonth)
    {
        return commissionMonthlySummaryMapper.selectTotalBonusEstimatedByMonth(dataMonth);
    }

    /**
     * 新增佣金月度计算总览
     * 
     * @param commissionMonthlySummary 佣金月度计算总览
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertCommissionMonthlySummary(CommissionMonthlySummary commissionMonthlySummary)
    {
        commissionMonthlySummary.setCreateTime(DateUtils.getNowDate());
        return commissionMonthlySummaryMapper.insertCommissionMonthlySummary(commissionMonthlySummary);
    }

    /**
     * 修改佣金月度计算总览
     * 
     * @param commissionMonthlySummary 佣金月度计算总览
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int updateCommissionMonthlySummary(CommissionMonthlySummary commissionMonthlySummary)
    {
        commissionMonthlySummary.setUpdateTime(DateUtils.getNowDate());
        return commissionMonthlySummaryMapper.updateCommissionMonthlySummary(commissionMonthlySummary);
    }

    /**
     * 批量删除佣金月度计算总览
     * 
     * @param dataMonths 需要删除的佣金月度计算总览主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionMonthlySummaryByMonths(Date[] dataMonths)
    {
        return commissionMonthlySummaryMapper.deleteCommissionMonthlySummaryByMonths(dataMonths);
    }

    /**
     * 删除佣金月度计算总览信息
     * 
     * @param dataMonth 佣金月度计算总览主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionMonthlySummaryByMonth(Date dataMonth)
    {
        return commissionMonthlySummaryMapper.deleteCommissionMonthlySummaryByMonth(dataMonth);
    }
} 