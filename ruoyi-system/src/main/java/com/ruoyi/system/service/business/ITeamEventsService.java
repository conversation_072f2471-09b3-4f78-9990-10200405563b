package com.ruoyi.system.service.business;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.business.TeamEvents;

/**
 * 团队动态与成就事件日志Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface ITeamEventsService
{
    /**
     * 查询团队动态与成就事件日志
     *
     * @param id 团队动态与成就事件日志主键
     * @return 团队动态与成就事件日志
     */
    public TeamEvents selectTeamEventsById(Long id);

    /**
     * 查询团队动态与成就事件日志列表
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 团队动态与成就事件日志集合
     */
    public List<TeamEvents> selectTeamEventsList(TeamEvents teamEvents);

    /**
     * 根据关联人ID和时间范围查询团队事件
     *
     * @param actorCreatorId 事件关联人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 团队事件列表
     */
    public List<TeamEvents> selectTeamEventsByActorAndTimeRange(Long actorCreatorId, Date startTime, Date endTime);

    /**
     * 根据事件类型和时间范围查询团队事件
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 团队事件列表
     */
    public List<TeamEvents> selectTeamEventsByTypeAndTimeRange(String eventType, Date startTime, Date endTime);

    /**
     * 新增团队动态与成就事件日志
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 结果
     */
    public int insertTeamEvents(TeamEvents teamEvents);

    /**
     * 批量新增团队动态与成就事件日志
     *
     * @param teamEventsList 团队动态与成就事件日志列表
     * @return 结果
     */
    public int insertTeamEventsBatch(List<TeamEvents> teamEventsList);

    /**
     * 修改团队动态与成就事件日志
     *
     * @param teamEvents 团队动态与成就事件日志
     * @return 结果
     */
    public int updateTeamEvents(TeamEvents teamEvents);

    /**
     * 批量删除团队动态与成就事件日志
     *
     * @param ids 需要删除的团队动态与成就事件日志主键集合
     * @return 结果
     */
    public int deleteTeamEventsByIds(Long[] ids);

    /**
     * 删除团队动态与成就事件日志信息
     *
     * @param id 团队动态与成就事件日志主键
     * @return 结果
     */
    public int deleteTeamEventsById(Long id);

    /**
     * 根据关联人ID删除团队事件
     *
     * @param actorCreatorId 事件关联人ID
     * @return 结果
     */
    public int deleteTeamEventsByActorCreatorId(Long actorCreatorId);

    /**
     * 根据时间范围删除团队事件（性能优化版本）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 删除的记录数
     */
    public int deleteTeamEventsByTimeRange(Date startTime, Date endTime);

    /**
     * 创建等级提升事件
     *
     * @param actorCreatorId 事件关联人ID（上级）
     * @param targetCreatorId 事件目标者ID（晋升者）
     * @param oldLevel 原等级
     * @param newLevel 新等级
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    public int createLevelUpEvent(Long actorCreatorId, Long targetCreatorId, String oldLevel, String newLevel, Date eventTimestamp);

    /**
     * 创建新人加入事件
     *
     * @param actorCreatorId 事件关联人ID（上级）
     * @param targetCreatorId 事件目标者ID（新人）
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    public int createNewRecruitEvent(Long actorCreatorId, Long targetCreatorId, Date eventTimestamp);

    /**
     * 创建业绩里程碑事件
     *
     * @param actorCreatorId 事件关联人ID
     * @param milestoneValue 里程碑值
     * @param currentValue 当前值
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    public int createPerformanceMilestoneEvent(Long actorCreatorId, Long milestoneValue, Long currentValue, Date eventTimestamp);

    /**
     * 创建即将达标事件
     *
     * @param actorCreatorId 事件关联人ID
     * @param achievementType 达标类型（拉新/业绩）
     * @param currentValue 当前值
     * @param targetValue 目标值
     * @param eventTimestamp 事件时间
     * @return 结果
     */
    public int createApproachingMilestoneEvent(Long actorCreatorId, String achievementType, Long currentValue, Long targetValue, Date eventTimestamp);
}
