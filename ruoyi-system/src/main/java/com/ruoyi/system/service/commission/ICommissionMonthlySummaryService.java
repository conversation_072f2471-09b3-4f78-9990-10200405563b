package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import com.ruoyi.system.domain.commission.CommissionMonthlySummary;
import com.ruoyi.system.domain.vo.CommissionCalculationSummaryVO;

/**
 * 佣金月度计算总览Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionMonthlySummaryService 
{
    /**
     * 查询佣金月度计算总览
     * 
     * @param dataMonth 佣金月度计算总览主键
     * @return 佣金月度计算总览
     */
    public CommissionMonthlySummary selectCommissionMonthlySummaryByMonth(Date dataMonth);

    /**
     * 查询佣金月度计算总览列表
     * 
     * @param commissionMonthlySummary 佣金月度计算总览
     * @return 佣金月度计算总览集合
     */
    public List<CommissionMonthlySummary> selectCommissionMonthlySummaryList(CommissionMonthlySummary commissionMonthlySummary);

    /**
     * 获取财务总览报表VO
     * 
     * @param dataMonth 数据月份
     * @return 财务总览报表VO
     */
    public CommissionCalculationSummaryVO getFinancialOverviewReport(Date dataMonth);

    /**
     * 根据月份查询主播奖金估计累计值
     * 
     * @param dataMonth 数据月份
     * @return 当月主播奖金估计累计值
     */
    public BigDecimal selectTotalBonusEstimatedByMonth(Date dataMonth);

    /**
     * 新增佣金月度计算总览
     * 
     * @param commissionMonthlySummary 佣金月度计算总览
     * @return 结果
     */
    public int insertCommissionMonthlySummary(CommissionMonthlySummary commissionMonthlySummary);

    /**
     * 修改佣金月度计算总览
     * 
     * @param commissionMonthlySummary 佣金月度计算总览
     * @return 结果
     */
    public int updateCommissionMonthlySummary(CommissionMonthlySummary commissionMonthlySummary);

    /**
     * 批量删除佣金月度计算总览
     * 
     * @param dataMonths 需要删除的佣金月度计算总览主键集合
     * @return 结果
     */
    public int deleteCommissionMonthlySummaryByMonths(Date[] dataMonths);

    /**
     * 删除佣金月度计算总览信息
     * 
     * @param dataMonth 佣金月度计算总览主键
     * @return 结果
     */
    public int deleteCommissionMonthlySummaryByMonth(Date dataMonth);
} 