package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.commission.CommissionDistributorQualificationsMapper;
import com.ruoyi.system.domain.commission.CommissionDistributorQualifications;
import com.ruoyi.system.service.commission.ICommissionDistributorQualificationsService;

/**
 * 分销员资格记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionDistributorQualificationsServiceImpl implements ICommissionDistributorQualificationsService 
{
    @Autowired
    private CommissionDistributorQualificationsMapper commissionDistributorQualificationsMapper;

    /**
     * 查询分销员资格记录列表
     * 
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 分销员资格记录
     */
    @Override
    public List<CommissionDistributorQualifications> selectCommissionDistributorQualificationsList(CommissionDistributorQualifications commissionDistributorQualifications)
    {
        return commissionDistributorQualificationsMapper.selectCommissionDistributorQualificationsList(commissionDistributorQualifications);
    }

    /**
     * 根据分销员ID和月份查询资格记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员资格记录
     */
    @Override
    public CommissionDistributorQualifications selectCommissionDistributorQualificationsByDistributor(Long creatorId, Date dataMonth)
    {
        return commissionDistributorQualificationsMapper.selectCommissionDistributorQualificationsByDistributor(creatorId, dataMonth);
    }

    /**
     * 根据月份查询所有分销员资格记录
     * 
     * @param dataMonth 数据月份
     * @return 分销员资格记录集合
     */
    @Override
    public List<CommissionDistributorQualifications> selectCommissionDistributorQualificationsByMonth(Date dataMonth)
    {
        return commissionDistributorQualificationsMapper.selectCommissionDistributorQualificationsByMonth(dataMonth);
    }

    /**
     * 根据等级查询合格分销员
     * 
     * @param dataMonth 数据月份
     * @param achievedLevel 达成等级
     * @return 分销员资格记录集合
     */
    @Override
    public List<CommissionDistributorQualifications> selectQualifiedDistributorsByLevel(Date dataMonth, String achievedLevel)
    {
        return commissionDistributorQualificationsMapper.selectQualifiedDistributorsByLevel(dataMonth, achievedLevel);
    }

    /**
     * 检查分销员资格记录是否存在
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 是否存在
     */
    @Override
    public boolean checkCommissionDistributorQualificationExists(Long creatorId, Date dataMonth)
    {
        return commissionDistributorQualificationsMapper.checkCommissionDistributorQualificationExists(creatorId, dataMonth) > 0;
    }

    /**
     * 新增分销员资格记录
     *
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int insertCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications)
    {
        commissionDistributorQualifications.setCreateTime(DateUtils.getNowDate());
        return commissionDistributorQualificationsMapper.insertCommissionDistributorQualifications(commissionDistributorQualifications);
    }

    /**
     * 新增或更新分销员资格记录
     *
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    @Transactional
    public int insertOrUpdateCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications)
    {
        boolean exists = checkCommissionDistributorQualificationExists(
            commissionDistributorQualifications.getCreatorId(), 
            commissionDistributorQualifications.getDataMonth());
        
        if (exists) {
            commissionDistributorQualifications.setUpdateTime(DateUtils.getNowDate());
            return commissionDistributorQualificationsMapper.updateCommissionDistributorQualifications(commissionDistributorQualifications);
        } else {
            commissionDistributorQualifications.setCreateTime(DateUtils.getNowDate());
            return commissionDistributorQualificationsMapper.insertCommissionDistributorQualifications(commissionDistributorQualifications);
        }
    }

    /**
     * 修改分销员资格记录
     *
     * @param commissionDistributorQualifications 分销员资格记录
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int updateCommissionDistributorQualifications(CommissionDistributorQualifications commissionDistributorQualifications)
    {
        commissionDistributorQualifications.setUpdateTime(DateUtils.getNowDate());
        return commissionDistributorQualificationsMapper.updateCommissionDistributorQualifications(commissionDistributorQualifications);
    }

    /**
     * 删除分销员资格记录
     *
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int deleteCommissionDistributorQualificationsByCreatorAndMonth(Long creatorId, Date dataMonth)
    {
        return commissionDistributorQualificationsMapper.deleteCommissionDistributorQualificationsByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 批量删除分销员资格记录
     *
     * @param creatorIds 需要删除的分销员ID集合
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int deleteCommissionDistributorQualificationsByCreatorIds(Long[] creatorIds)
    {
        return commissionDistributorQualificationsMapper.deleteCommissionDistributorQualificationsByCreatorIds(creatorIds);
    }

    /**
     * 根据月份删除所有分销员资格记录
     *
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public int deleteCommissionDistributorQualificationsByMonth(Date dataMonth)
    {
        CommissionDistributorQualifications query = new CommissionDistributorQualifications();
        query.setDataMonth(dataMonth);
        List<CommissionDistributorQualifications> qualifications = selectCommissionDistributorQualificationsList(query);
        
        if (qualifications.isEmpty()) {
            return 0;
        }
        
        Long[] creatorIds = qualifications.stream()
            .map(CommissionDistributorQualifications::getCreatorId)
            .toArray(Long[]::new);
        
        return deleteCommissionDistributorQualificationsByCreatorIds(creatorIds);
    }
}
