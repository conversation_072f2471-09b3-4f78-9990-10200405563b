package com.ruoyi.system.service.impl.business;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.service.business.IDashboardService;
import com.ruoyi.system.mapper.business.DashboardMapper;
import com.ruoyi.system.domain.dto.DashboardResponse;
import com.ruoyi.system.domain.dto.UserInfo;
import com.ruoyi.system.domain.dto.LevelMaintenance;
import com.ruoyi.system.domain.dto.PerformanceAnalytics;
import com.ruoyi.system.domain.dto.IncomeComponents;
import com.ruoyi.system.domain.dto.PerformanceKPIs;

/**
 * 个人主仪表盘Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class DashboardServiceImpl implements IDashboardService
{
    private static final Logger log = LoggerFactory.getLogger(DashboardServiceImpl.class);

    @Autowired
    private DashboardMapper dashboardMapper;

    /**
     * 获取指定分销员在指定月份的完整仪表盘聚合数据
     * 
     * @param creatorId 分销员ID
     * @param month 月份，格式为 YYYY-MM
     * @return 仪表盘数据
     */
    @Override
    public DashboardResponse getDashboardData(Long creatorId, String month)
    {
        log.info("开始获取仪表盘数据，用户ID: {}, 月份: {}", creatorId, month);

        // 参数验证
        if (creatorId == null || creatorId <= 0) {
            throw new ServiceException("用户ID不能为空且必须大于0");
        }

        if (month == null || month.trim().isEmpty()) {
            throw new ServiceException("月份不能为空");
        }

        if (!month.matches("\\d{4}-\\d{2}")) {
            throw new ServiceException("月份格式错误，请使用YYYY-MM格式");
        }

        try {
            // 解析月份字符串为Date对象
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date dataMonth = sdf.parse(month + "-01");

            DashboardResponse response = new DashboardResponse();
            
            // 1. 获取用户信息
            UserInfo userInfo = getUserInfo(creatorId);
            response.setUser(userInfo);
            
            // 2. 获取用户等级
            String level = dashboardMapper.selectUserLevel(creatorId, dataMonth);
            response.setLevel(level != null ? level.trim() : "");

            // 3. 设置选择的月份
            response.setSelectedMonth(month);

            // 4. 获取收入数据
            BigDecimal totalIncome = dashboardMapper.selectTotalIncomeByYear(creatorId, dataMonth);
            BigDecimal monthlyIncome = dashboardMapper.selectMonthlyIncome(creatorId, dataMonth);

            // 确保收入数据不为负数
            totalIncome = (totalIncome != null && totalIncome.compareTo(BigDecimal.ZERO) >= 0) ?
                         totalIncome : BigDecimal.ZERO;
            monthlyIncome = (monthlyIncome != null && monthlyIncome.compareTo(BigDecimal.ZERO) >= 0) ?
                           monthlyIncome : BigDecimal.ZERO;

            response.setTotalIncome(totalIncome);
            response.setMonthlyIncome(monthlyIncome);
            
            // 5. 计算月度收入环比增长率
            BigDecimal monthlyIncomeGrowth = calculateMonthlyIncomeGrowth(creatorId, dataMonth);
            response.setMonthlyIncomeGrowth(monthlyIncomeGrowth);
            
            // 6. 获取保级状态
            LevelMaintenance levelMaintenance = getLevelMaintenance(creatorId, dataMonth);
            response.setLevelMaintenance(levelMaintenance);
            
            // 7. 获取绩效分析
            PerformanceAnalytics performanceAnalytics = getPerformanceAnalytics(creatorId, dataMonth, totalIncome);
            response.setPerformanceAnalytics(performanceAnalytics);
            
            log.info("仪表盘数据获取完成，用户ID: {}", creatorId);
            return response;
            
        } catch (ParseException e) {
            log.error("月份格式解析错误: {}", month, e);
            throw new ServiceException("月份格式错误，请使用YYYY-MM格式");
        } catch (Exception e) {
            log.error("获取仪表盘数据失败，用户ID: {}, 月份: {}", creatorId, month, e);
            throw new ServiceException("获取仪表盘数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    private UserInfo getUserInfo(Long creatorId)
    {
        if (creatorId == null || creatorId <= 0) {
            throw new ServiceException("用户ID不能为空且必须大于0");
        }

        String nickname = dashboardMapper.selectCreatorNickname(creatorId);
        if (nickname == null || nickname.trim().isEmpty()) {
            throw new ServiceException("用户不存在或用户昵称为空");
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setName(nickname.trim());
        return userInfo;
    }

    /**
     * 计算月度收入环比增长率
     */
    private BigDecimal calculateMonthlyIncomeGrowth(Long creatorId, Date dataMonth)
    {
        BigDecimal currentIncome = dashboardMapper.selectMonthlyIncome(creatorId, dataMonth);
        BigDecimal previousIncome = dashboardMapper.selectPreviousMonthIncome(creatorId, dataMonth);

        if (currentIncome == null) currentIncome = BigDecimal.ZERO;
        if (previousIncome == null || previousIncome.compareTo(BigDecimal.ZERO) == 0) {
            return null; // 上月收入为0时返回null
        }

        // 计算环比增长率: (本月收入 / 上月收入) - 1
        return currentIncome.divide(previousIncome, 4, RoundingMode.HALF_UP)
                           .subtract(BigDecimal.ONE);
    }

    /**
     * 获取保级状态
     */
    private LevelMaintenance getLevelMaintenance(Long creatorId, Date dataMonth)
    {
        LevelMaintenance levelMaintenance = new LevelMaintenance();

        // 获取保级目标增长率
        BigDecimal targetGrowthRate = dashboardMapper.selectRetentionTargetGrowthRate();
        if (targetGrowthRate == null) {
            targetGrowthRate = new BigDecimal("0.15"); // 默认15%
        }
        levelMaintenance.setTargetGrowthRate(targetGrowthRate);

        // 计算实际月均环比增长率（防守算法）
        BigDecimal actualGrowthRate = calculateActualGrowthRate(creatorId, dataMonth);

        // 计算保级任务完成度百分比
        BigDecimal progressPercentage = null;
        if (actualGrowthRate != null && targetGrowthRate.compareTo(BigDecimal.ZERO) > 0) {
            progressPercentage = actualGrowthRate.divide(targetGrowthRate, 4, RoundingMode.HALF_UP);
        }
        levelMaintenance.setProgressPercentage(progressPercentage);

        return levelMaintenance;
    }

    /**
     * 计算实际月均环比增长率（防守算法）
     * 基于过去4个月的业绩计算
     */
    private BigDecimal calculateActualGrowthRate(Long creatorId, Date dataMonth)
    {
        List<Map<String, Object>> past4MonthsData = dashboardMapper.selectPast4MonthsIncome(creatorId, dataMonth);

        if (past4MonthsData == null || past4MonthsData.size() < 2) {
            return null; // 数据不足，无法计算
        }

        // 计算各月环比增长率
        BigDecimal totalGrowthRate = BigDecimal.ZERO;
        int validGrowthCount = 0;

        for (int i = 1; i < past4MonthsData.size(); i++) {
            BigDecimal currentIncome = (BigDecimal) past4MonthsData.get(i).get("income");
            BigDecimal previousIncome = (BigDecimal) past4MonthsData.get(i-1).get("income");

            if (currentIncome != null && previousIncome != null &&
                previousIncome.compareTo(BigDecimal.ZERO) > 0) {

                BigDecimal growthRate = currentIncome.divide(previousIncome, 4, RoundingMode.HALF_UP)
                                                   .subtract(BigDecimal.ONE);
                totalGrowthRate = totalGrowthRate.add(growthRate);
                validGrowthCount++;
            }
        }

        if (validGrowthCount == 0) {
            return null;
        }

        // 计算平均增长率
        return totalGrowthRate.divide(new BigDecimal(validGrowthCount), 4, RoundingMode.HALF_UP);
    }

    /**
     * 获取绩效分析
     */
    private PerformanceAnalytics getPerformanceAnalytics(Long creatorId, Date dataMonth, BigDecimal totalIncome)
    {
        PerformanceAnalytics analytics = new PerformanceAnalytics();

        // 设置总绩效，确保不为负数
        BigDecimal safeTotal = (totalIncome != null && totalIncome.compareTo(BigDecimal.ZERO) >= 0) ?
                              totalIncome : BigDecimal.ZERO;
        analytics.setTotalPerformanceUSD(safeTotal);

        // 获取收入构成
        IncomeComponents components = new IncomeComponents();
        BigDecimal personalIncome = dashboardMapper.selectPersonalIncome(creatorId, dataMonth);
        BigDecimal teamIncome = dashboardMapper.selectTeamIncome(creatorId, dataMonth);

        // 确保收入数据不为负数
        personalIncome = (personalIncome != null && personalIncome.compareTo(BigDecimal.ZERO) >= 0) ?
                        personalIncome : BigDecimal.ZERO;
        teamIncome = (teamIncome != null && teamIncome.compareTo(BigDecimal.ZERO) >= 0) ?
                    teamIncome : BigDecimal.ZERO;

        components.setPersonalIncomeUSD(personalIncome);
        components.setTeamIncomeUSD(teamIncome);
        analytics.setComponents(components);

        // 计算关键绩效指标
        PerformanceKPIs kpis = new PerformanceKPIs();

        // 计算个人收入占比
        BigDecimal personalIncomeRatio = null;
        if (safeTotal.compareTo(BigDecimal.ZERO) > 0) {
            personalIncomeRatio = personalIncome.divide(safeTotal, 4, RoundingMode.HALF_UP);
            // 确保占比在0-1之间
            if (personalIncomeRatio.compareTo(BigDecimal.ONE) > 0) {
                personalIncomeRatio = BigDecimal.ONE;
            } else if (personalIncomeRatio.compareTo(BigDecimal.ZERO) < 0) {
                personalIncomeRatio = BigDecimal.ZERO;
            }
        }
        kpis.setPersonalIncomeRatio(personalIncomeRatio);

        // 获取团队总人数
        Integer totalTeamMembers = dashboardMapper.selectTotalTeamMembers(creatorId);
        kpis.setTotalTeamMembers((totalTeamMembers != null && totalTeamMembers >= 0) ? totalTeamMembers : 0);

        analytics.setKpis(kpis);

        return analytics;
    }
}
