package com.ruoyi.system.service.commission.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionRecruitmentBonusRulesMapper;
import com.ruoyi.system.domain.commission.CommissionRecruitmentBonusRules;
import com.ruoyi.system.service.commission.ICommissionRecruitmentBonusRulesService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 拉新奖励规则Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class CommissionRecruitmentBonusRulesServiceImpl implements ICommissionRecruitmentBonusRulesService 
{
    @Autowired
    private CommissionRecruitmentBonusRulesMapper commissionRecruitmentBonusRulesMapper;

    /**
     * 查询拉新奖励规则
     * 
     * @param id 拉新奖励规则主键
     * @return 拉新奖励规则
     */
    @Override
    public CommissionRecruitmentBonusRules selectCommissionRecruitmentBonusRulesById(Integer id)
    {
        return commissionRecruitmentBonusRulesMapper.selectCommissionRecruitmentBonusRulesById(id);
    }

    /**
     * 查询拉新奖励规则列表
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 拉新奖励规则
     */
    @Override
    public List<CommissionRecruitmentBonusRules> selectCommissionRecruitmentBonusRulesList(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules)
    {
        return commissionRecruitmentBonusRulesMapper.selectCommissionRecruitmentBonusRulesList(commissionRecruitmentBonusRules);
    }

    /**
     * 查询启用的拉新奖励规则列表
     * 
     * @return 拉新奖励规则
     */
    @Override
    public List<CommissionRecruitmentBonusRules> selectActiveCommissionRecruitmentBonusRules()
    {
        return commissionRecruitmentBonusRulesMapper.selectActiveCommissionRecruitmentBonusRules();
    }

    /**
     * 新增拉新奖励规则
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 结果
     */
    @Override
    public int insertCommissionRecruitmentBonusRules(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules)
    {
        return commissionRecruitmentBonusRulesMapper.insertCommissionRecruitmentBonusRules(commissionRecruitmentBonusRules);
    }

    /**
     * 修改拉新奖励规则
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 结果
     */
    @Override
    public int updateCommissionRecruitmentBonusRules(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules)
    {
        return commissionRecruitmentBonusRulesMapper.updateCommissionRecruitmentBonusRules(commissionRecruitmentBonusRules);
    }

    /**
     * 批量删除拉新奖励规则
     * 
     * @param ids 需要删除的拉新奖励规则主键
     * @return 结果
     */
    @Override
    public int deleteCommissionRecruitmentBonusRulesByIds(Integer[] ids)
    {
        return commissionRecruitmentBonusRulesMapper.deleteCommissionRecruitmentBonusRulesByIds(ids);
    }

    /**
     * 删除拉新奖励规则信息
     * 
     * @param id 拉新奖励规则主键
     * @return 结果
     */
    @Override
    public int deleteCommissionRecruitmentBonusRulesById(Integer id)
    {
        return commissionRecruitmentBonusRulesMapper.deleteCommissionRecruitmentBonusRulesById(id);
    }
} 