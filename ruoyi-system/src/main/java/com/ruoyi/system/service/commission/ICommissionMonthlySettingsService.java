package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionMonthlySettings;

/**
 * 月度佣金配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionMonthlySettingsService 
{
    /**
     * 查询月度佣金配置
     * 
     * @param dataMonth 月度佣金配置主键
     * @return 月度佣金配置
     */
    public CommissionMonthlySettings selectCommissionMonthlySettingsByMonth(Date dataMonth);

    /**
     * 查询月度佣金配置列表
     * 
     * @param commissionMonthlySettings 月度佣金配置
     * @return 月度佣金配置集合
     */
    public List<CommissionMonthlySettings> selectCommissionMonthlySettingsList(CommissionMonthlySettings commissionMonthlySettings);

    /**
     * 新增月度佣金配置
     * 
     * @param commissionMonthlySettings 月度佣金配置
     * @return 结果
     */
    public int insertCommissionMonthlySettings(CommissionMonthlySettings commissionMonthlySettings);

    /**
     * 修改月度佣金配置
     * 
     * @param commissionMonthlySettings 月度佣金配置
     * @return 结果
     */
    public int updateCommissionMonthlySettings(CommissionMonthlySettings commissionMonthlySettings);

    /**
     * 批量删除月度佣金配置
     * 
     * @param dataMonths 需要删除的月度佣金配置主键集合
     * @return 结果
     */
    public int deleteCommissionMonthlySettingsByMonths(Date[] dataMonths);

    /**
     * 删除月度佣金配置信息
     * 
     * @param dataMonth 月度佣金配置主键
     * @return 结果
     */
    public int deleteCommissionMonthlySettingsByMonth(Date dataMonth);
} 