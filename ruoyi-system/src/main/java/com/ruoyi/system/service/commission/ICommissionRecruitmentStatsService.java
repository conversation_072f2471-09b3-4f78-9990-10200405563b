package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionRecruitmentStats;

/**
 * 招募统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionRecruitmentStatsService 
{
    /**
     * 查询招募统计
     * 
     * @param recruiterId 招募人ID
     * @param dataMonth 数据月份
     * @return 招募统计
     */
    public CommissionRecruitmentStats selectCommissionRecruitmentStatsByRecruiterAndMonth(Long recruiterId, Date dataMonth);

    /**
     * 查询招募统计列表
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 招募统计集合
     */
    public List<CommissionRecruitmentStats> selectCommissionRecruitmentStatsList(CommissionRecruitmentStats commissionRecruitmentStats);

    /**
     * 根据数据月份查询招募统计列表
     * 
     * @param dataMonth 数据月份
     * @return 招募统计集合
     */
    public List<CommissionRecruitmentStats> selectCommissionRecruitmentStatsByMonth(Date dataMonth);

    /**
     * 根据招募人ID查询历史招募统计
     * 
     * @param recruiterId 招募人ID
     * @return 招募统计集合
     */
    public List<CommissionRecruitmentStats> selectCommissionRecruitmentStatsByRecruiter(Long recruiterId);

    /**
     * 查询月度招募排行榜
     * 
     * @param dataMonth 数据月份
     * @param limit 返回条数限制
     * @return 招募统计集合（按新招募人数降序）
     */
    public List<CommissionRecruitmentStats> selectTopRecruitmentStatsByMonth(Date dataMonth, Integer limit);

    /**
     * 新增招募统计
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 结果
     */
    public int insertCommissionRecruitmentStats(CommissionRecruitmentStats commissionRecruitmentStats);

    /**
     * 批量新增招募统计
     * 
     * @param statsList 招募统计列表
     * @return 结果
     */
    public int batchInsertCommissionRecruitmentStats(List<CommissionRecruitmentStats> statsList);

    /**
     * 插入或更新招募统计
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 结果
     */
    public int insertOrUpdateCommissionRecruitmentStats(CommissionRecruitmentStats commissionRecruitmentStats);

    /**
     * 修改招募统计
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 结果
     */
    public int updateCommissionRecruitmentStats(CommissionRecruitmentStats commissionRecruitmentStats);

    /**
     * 删除招募统计
     * 
     * @param recruiterId 招募人ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionRecruitmentStatsByRecruiterAndMonth(Long recruiterId, Date dataMonth);

    /**
     * 根据招募人ID删除招募统计
     * 
     * @param recruiterId 招募人ID
     * @return 结果
     */
    public int deleteCommissionRecruitmentStatsByRecruiter(Long recruiterId);

    /**
     * 根据数据月份删除招募统计
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    public int deleteCommissionRecruitmentStatsByMonth(Date dataMonth);

    /**
     * 检查指定招募人和月份的记录是否存在
     * 
     * @param recruiterId 招募人ID
     * @param dataMonth 数据月份
     * @return 是否存在
     */
    public boolean checkCommissionRecruitmentStatsExists(Long recruiterId, Date dataMonth);

    /**
     * 清理指定日期之前的历史招募统计
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    public int deleteCommissionRecruitmentStatsBeforeDate(Date beforeDate);
} 