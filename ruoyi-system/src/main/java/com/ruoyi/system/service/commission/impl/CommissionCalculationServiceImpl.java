package com.ruoyi.system.service.commission.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Set;
import java.util.stream.Collectors;
import com.ruoyi.system.domain.business.TeamEvents;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.service.commission.ICommissionCalculationService;
import com.ruoyi.system.service.commission.ICommissionMonthlySummaryService;
import com.ruoyi.system.service.commission.ICommissionPayoutsService;
import com.ruoyi.system.service.commission.ICommissionPayoutBreakdownsService;
import com.ruoyi.system.service.commission.ICommissionCalculationLogsService;
import com.ruoyi.system.service.commission.ICommissionConfigSnapshotsService;
import com.ruoyi.system.service.commission.ICommissionDynamicThresholdsService;
import com.ruoyi.system.service.commission.ICommissionDistributorQualificationsService;
import com.ruoyi.system.service.commission.ICommissionRecruitmentStatsService;
import com.ruoyi.system.service.commission.ICommissionSettingsService;
import com.ruoyi.system.service.commission.ICommissionMonthlySettingsService;
import com.ruoyi.system.service.commission.ICommissionLevelRulesService;
import com.ruoyi.system.service.commission.ICommissionMultilevelRulesService;
import com.ruoyi.system.service.commission.ICommissionRecruitmentBonusRulesService;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.system.service.business.ICreatorRelationshipService;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.business.INewRecruitQualificationService;
import com.ruoyi.system.service.business.ITeamEventsService;
import com.ruoyi.system.service.commission.IDistributorLevelAdjustmentService;
import com.ruoyi.system.domain.commission.CommissionMonthlySummary;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import com.ruoyi.system.domain.commission.CommissionPayoutBreakdowns;
import com.ruoyi.system.domain.commission.CommissionDistributorQualifications;
import com.ruoyi.system.domain.commission.CommissionCalculationLogs;
import com.ruoyi.system.domain.commission.CommissionConfigSnapshots;
import com.ruoyi.system.domain.commission.CommissionDynamicThresholds;
import com.ruoyi.system.domain.commission.CommissionRecruitmentStats;
import com.ruoyi.system.domain.commission.CommissionSettings;
import com.ruoyi.system.domain.commission.CommissionMonthlySettings;
import com.ruoyi.system.domain.commission.CommissionLevelRules;
import com.ruoyi.system.domain.commission.CommissionMultilevelRules;
import com.ruoyi.system.domain.commission.CommissionRecruitmentBonusRules;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Objects;

/**
 * 佣金计算核心业务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionCalculationServiceImpl implements ICommissionCalculationService
{
    private static final Logger log = LoggerFactory.getLogger(CommissionCalculationServiceImpl.class);

    @Autowired
    private ICommissionMonthlySummaryService commissionMonthlySummaryService;
    
    @Autowired
    private ICommissionPayoutsService commissionPayoutsService;
    
    @Autowired
    private ICommissionPayoutBreakdownsService commissionPayoutBreakdownsService;
    
    @Autowired
    private ICommissionCalculationLogsService commissionCalculationLogsService;
    
    @Autowired
    private ICommissionConfigSnapshotsService commissionConfigSnapshotsService;
    
    @Autowired
    private ICommissionDynamicThresholdsService commissionDynamicThresholdsService;
    
    @Autowired
    private ICommissionRecruitmentStatsService commissionRecruitmentStatsService;
    
    @Autowired
    private ICommissionSettingsService commissionSettingsService;
    
    @Autowired
    private ICommissionMonthlySettingsService commissionMonthlySettingsService;
    
    @Autowired
    private ICommissionLevelRulesService commissionLevelRulesService;
    
    @Autowired
    private ICommissionMultilevelRulesService commissionMultilevelRulesService;
    
    @Autowired
    private ICommissionRecruitmentBonusRulesService commissionRecruitmentBonusRulesService;
    
    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;
    
    @Autowired
    private ICreatorRelationshipService creatorRelationshipService;
    
    @Autowired
    private ICreatorService creatorService;

    @Autowired
    private INewRecruitQualificationService newRecruitQualificationService;

    @Autowired
    private IDistributorLevelAdjustmentService distributorLevelAdjustmentService;

    @Autowired
    private ITeamEventsService teamEventsService;

    @Autowired
    private ICommissionDistributorQualificationsService commissionDistributorQualificationsService;

    /**
     * 执行月度佣金计算
     * 
     * @param dataMonth 数据月份
     * @return 计算结果信息
     */
    @Override
    @Transactional
    public Map<String, Object> executeMonthlyCalculation(Date dataMonth)
    {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        try {
            log.info("=== 开始执行 {} 月度佣金计算 ===", monthStr);
            
            // 检查前置条件
            Map<String, Object> preconditionResult = checkCalculationPreconditions(dataMonth);
            Boolean canCalculate = (Boolean) preconditionResult.get("canCalculate");
            
            log.info("前置条件检查结果: canCalculate={}, message={}", canCalculate, preconditionResult.get("message"));
            
            if (!canCalculate) {
                result.put("success", false);
                result.put("message", "前置条件检查失败：" + preconditionResult.get("message"));
                log.warn("前置条件检查失败，终止计算: {}", preconditionResult.get("message"));
                return result;
            }
            
            // 检查是否已经计算过
            CommissionMonthlySummary existingSummary = commissionMonthlySummaryService.selectCommissionMonthlySummaryByMonth(dataMonth);
            if (existingSummary != null && "completed".equals(existingSummary.getCalculationStatus())) {
                result.put("success", false);
                result.put("message", "该月份已完成计算，如需重新计算请使用重新计算接口");
                log.warn("该月份已完成计算，状态: {}", existingSummary.getCalculationStatus());
                return result;
            }
            
            log.info("开始创建/更新计算总览记录...");
            // 创建或更新计算总览记录
            CommissionMonthlySummary summary = existingSummary != null ? existingSummary : new CommissionMonthlySummary();
            summary.setDataMonth(dataMonth);
            summary.setCalculationStatus("processing");
            summary.setCalculatedAt(new Date());
            
            if (existingSummary == null) {
                // 为必需字段设置默认值，防止数据库 NOT NULL 约束错误
                summary.setBudgetUsd(BigDecimal.ZERO);
                summary.setPayoutCapUsd(BigDecimal.ZERO);
                summary.setActualPayoutUsd(BigDecimal.ZERO);
                summary.setPayoutToBudgetRatio(BigDecimal.ZERO);
                summary.setExceededAmountUsd(BigDecimal.ZERO);
                summary.setCreateTime(DateUtils.getNowDate());
                commissionMonthlySummaryService.insertCommissionMonthlySummary(summary);
                log.info("创建新的月度总览记录");
            } else {
                summary.setUpdateTime(DateUtils.getNowDate());
                commissionMonthlySummaryService.updateCommissionMonthlySummary(summary);
                log.info("更新现有月度总览记录");
            }
            
            // 执行具体的计算逻辑
            log.info("开始执行具体计算逻辑...");
            performCalculation(dataMonth, summary);
            
            // 更新计算状态为完成
            summary.setCalculationStatus("completed");
            summary.setCalculatedAt(new Date());
            summary.setUpdateTime(DateUtils.getNowDate());
            commissionMonthlySummaryService.updateCommissionMonthlySummary(summary);
            
            log.info("=== 完成 {} 月度佣金计算 ===", monthStr);
            log.info("计算结果总览: 实际支出={}USD, 预算={}USD, 支出上限={}USD", 
                summary.getActualPayoutUsd(), summary.getBudgetUsd(), summary.getPayoutCapUsd());
            
            result.put("success", true);
            result.put("message", "月度佣金计算执行成功");
            result.put("dataMonth", monthStr);
            result.put("summary", summary);
            
        } catch (Exception e) {
            log.error("执行月度佣金计算失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "计算执行失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取计算状态
     * 
     * @param dataMonth 数据月份
     * @return 计算状态信息
     */
    @Override
    public Map<String, Object> getCalculationStatus(Date dataMonth)
    {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        CommissionMonthlySummary summary = commissionMonthlySummaryService.selectCommissionMonthlySummaryByMonth(dataMonth);
        
                 if (summary == null) {
             result.put("status", "not_started");
             result.put("message", "该月份尚未开始计算");
         } else {
             result.put("status", summary.getCalculationStatus());
             result.put("calculatedAt", summary.getCalculatedAt());
             result.put("actualPayoutUsd", summary.getActualPayoutUsd());
             result.put("budgetUsd", summary.getBudgetUsd());
            
            if ("processing".equals(summary.getCalculationStatus())) {
                result.put("message", "计算进行中...");
            } else if ("completed".equals(summary.getCalculationStatus())) {
                result.put("message", "计算已完成");
            } else if ("failed".equals(summary.getCalculationStatus())) {
                result.put("message", "计算失败");
            }
        }
        
        result.put("dataMonth", monthStr);
        return result;
    }

    /**
     * 重新计算指定月份的佣金
     * 
     * @param dataMonth 数据月份
     * @param force 是否强制重新计算
     * @return 计算结果信息
     */
    @Override
    @Transactional
    public Map<String, Object> recalculateMonthlyCommission(Date dataMonth, boolean force)
    {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        try {
            log.info("开始重新计算 {} 月度佣金，强制模式：{}", monthStr, force);
            
            CommissionMonthlySummary existingSummary = commissionMonthlySummaryService.selectCommissionMonthlySummaryByMonth(dataMonth);
            
            if (existingSummary != null && "processing".equals(existingSummary.getCalculationStatus()) && !force) {
                result.put("success", false);
                result.put("message", "该月份正在计算中，如需强制重新计算请设置force=true");
                return result;
            }
            
                         // 重置计算状态和清理历史数据
            if (existingSummary != null) {
                existingSummary.setCalculationStatus("processing");
                existingSummary.setCalculatedAt(new Date());
                existingSummary.setUpdateTime(DateUtils.getNowDate());
                commissionMonthlySummaryService.updateCommissionMonthlySummary(existingSummary);
                
                // 处理历史数据
                handleHistoricalDataOnRecalculation(dataMonth);
            }
            
            // 执行计算
            return executeMonthlyCalculation(dataMonth);
            
        } catch (Exception e) {
            log.error("重新计算月度佣金失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "重新计算失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 预览计算结果（不保存）
     * 
     * @param dataMonth 数据月份
     * @return 预览结果信息
     */
    @Override
    public Map<String, Object> previewCalculation(Date dataMonth)
    {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        try {
            log.info("开始预览 {} 月度佣金计算", monthStr);
            
            // 检查前置条件
            Map<String, Object> preconditionResult = checkCalculationPreconditions(dataMonth);
            Boolean canCalculate = (Boolean) preconditionResult.get("canCalculate");
            
            if (!canCalculate) {
                result.put("success", false);
                result.put("message", "前置条件检查失败：" + preconditionResult.get("message"));
                return result;
            }
            
            // 模拟计算总览数据
            Map<String, Object> previewData = generatePreviewData(dataMonth);
            
            result.put("success", true);
            result.put("message", "预览计算完成");
            result.put("dataMonth", monthStr);
            result.put("previewData", previewData);
            
        } catch (Exception e) {
            log.error("预览计算失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "预览失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 检查计算前置条件
     * 
     * @param dataMonth 数据月份
     * @return 检查结果
     */
    @Override
    public Map<String, Object> checkCalculationPreconditions(Date dataMonth)
    {
        Map<String, Object> result = new HashMap<>();
        StringBuilder message = new StringBuilder();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        boolean canCalculate = true;
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String monthStr = sdf.format(dataMonth);
            
            log.info("开始检查 {} 月度佣金计算前置条件", monthStr);
            
            // 1. 检查数据月份是否有效
            Date currentDate = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(currentDate);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            Date currentMonthStart = cal.getTime();
            
            if (dataMonth.after(currentMonthStart)) {
                canCalculate = false;
                errors.add("不能计算当前月份或未来月份的佣金");
            }
            
            // 2. 检查月度配置是否存在
            CommissionMonthlySettings monthlySettings = commissionMonthlySettingsService.selectCommissionMonthlySettingsByMonth(dataMonth);
            if (monthlySettings == null) {
                canCalculate = false;
                errors.add("该月份未配置月度设置（预算、汇率等）");
            } else {
                // 检查月度配置数据完整性
                if (monthlySettings.getManualIncomeUsd() == null || monthlySettings.getManualIncomeUsd().compareTo(BigDecimal.ZERO) <= 0) {
                    canCalculate = false;
                    errors.add("月度预算收入未配置或配置无效");
                }
                if (monthlySettings.getPayoutCapRate() == null || monthlySettings.getPayoutCapRate().compareTo(BigDecimal.ZERO) <= 0) {
                    canCalculate = false;
                    errors.add("支出上限比例未配置或配置无效");
                }
                if (monthlySettings.getDiamondToUsdRate() == null || monthlySettings.getDiamondToUsdRate().compareTo(BigDecimal.ZERO) <= 0) {
                    canCalculate = false;
                    errors.add("钻石兑美元汇率未配置或配置无效");
                }
            }
            
            // 3. 检查全局配置是否存在
            CommissionSettings baseThresholdSetting = commissionSettingsService.selectCommissionSettingsByKey("base_diamond_threshold");
            if (baseThresholdSetting == null || baseThresholdSetting.getSettingValue() == null || baseThresholdSetting.getSettingValue().trim().isEmpty()) {
                canCalculate = false;
                errors.add("基础钻石门槛未配置");
            } else {
                try {
                    long threshold = Long.parseLong(baseThresholdSetting.getSettingValue());
                    if (threshold <= 0) {
                        canCalculate = false;
                        errors.add("基础钻石门槛配置无效，必须大于0");
                    }
                } catch (NumberFormatException e) {
                    canCalculate = false;
                    errors.add("基础钻石门槛配置格式无效，必须为正整数");
                }
            }
            
            CommissionSettings increaseRateSetting = commissionSettingsService.selectCommissionSettingsByKey("threshold_increase_rate");
            if (increaseRateSetting == null || increaseRateSetting.getSettingValue() == null || increaseRateSetting.getSettingValue().trim().isEmpty()) {
                canCalculate = false;
                errors.add("钻石门槛上浮比例未配置");
            } else {
                try {
                    BigDecimal rate = new BigDecimal(increaseRateSetting.getSettingValue());
                    if (rate.compareTo(BigDecimal.ZERO) < 0 || rate.compareTo(BigDecimal.ONE) > 0) {
                        canCalculate = false;
                        errors.add("钻石门槛上浮比例配置无效，必须在0-1之间");
                    }
                } catch (NumberFormatException e) {
                    canCalculate = false;
                    errors.add("钻石门槛上浮比例配置格式无效，必须为有效数字");
                }
            }
            
            // 4. 检查分销员等级规则配置
            List<CommissionLevelRules> levelRules = commissionLevelRulesService.selectActiveCommissionLevelRules();
            if (levelRules == null || levelRules.isEmpty()) {
                canCalculate = false;
                errors.add("分销员等级规则未配置");
            } else {
                // 检查等级规则的有效性
                for (CommissionLevelRules rule : levelRules) {
                    if (rule.getMinQualifiedRecruits() == null || rule.getMinQualifiedRecruits() < 0) {
                        warnings.add("等级 " + rule.getLevelName() + " 的最低合格拉新人数配置无效");
                    }
                    if (rule.getCommissionRate() == null || rule.getCommissionRate().compareTo(BigDecimal.ZERO) < 0 || rule.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
                        warnings.add("等级 " + rule.getLevelName() + " 的分成比例配置无效");
                    }
                }
            }
            
            // 5. 检查多级提成规则配置
            List<CommissionMultilevelRules> multilevelRules = commissionMultilevelRulesService.selectActiveCommissionMultilevelRules();
            if (multilevelRules == null || multilevelRules.isEmpty()) {
                warnings.add("多级提成规则未配置，将无法计算多级提成");
            } else {
                // 检查是否包含L1、L2、L3的完整配置
                boolean hasL1 = false, hasL2 = false, hasL3 = false;
                for (CommissionMultilevelRules rule : multilevelRules) {
                    if (rule.getDepth() != null) {
                        if (rule.getDepth() == 1) hasL1 = true;
                        else if (rule.getDepth() == 2) hasL2 = true;
                        else if (rule.getDepth() == 3) hasL3 = true;
                    }
                    if (rule.getCommissionRate() == null || rule.getCommissionRate().compareTo(BigDecimal.ZERO) < 0 || rule.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
                        warnings.add("L" + rule.getDepth() + " 层级提成比例配置无效");
                    }
                }
                if (!hasL1 || !hasL2 || !hasL3) {
                    warnings.add("多级提成规则配置不完整，建议配置L1、L2、L3三个层级");
                }
            }
            
            // 6. 检查拉新奖励规则配置
            List<CommissionRecruitmentBonusRules> recruitmentRules = commissionRecruitmentBonusRulesService.selectActiveCommissionRecruitmentBonusRules();
            if (recruitmentRules == null || recruitmentRules.isEmpty()) {
                warnings.add("拉新奖励规则未配置，将无法计算拉新奖励");
            } else {
                for (CommissionRecruitmentBonusRules rule : recruitmentRules) {
                    if (rule.getMinNewRecruits() == null || rule.getMinNewRecruits() <= 0) {
                        warnings.add("拉新奖励规则中最低招募人数配置无效");
                    }
                    if (rule.getBonusUsd() == null || rule.getBonusUsd().compareTo(BigDecimal.ZERO) <= 0) {
                        warnings.add("拉新奖励规则中奖励金额配置无效");
                    }
                }
            }
            
            // 7. 检查是否有主播业绩数据
            MonthlyPerformance queryCondition = new MonthlyPerformance();
            queryCondition.setDataMonth(dataMonth);
            List<MonthlyPerformance> performanceList = monthlyPerformanceService.selectMonthlyPerformanceList(queryCondition);
            
            if (performanceList == null || performanceList.isEmpty()) {
                canCalculate = false;
                errors.add("该月份没有主播业绩数据");
            } else {
                // 统计有效业绩数据
                int validPerformanceCount = 0;
                int totalDiamonds = 0;
                for (MonthlyPerformance performance : performanceList) {
                    if (performance.getDiamonds() != null && performance.getDiamonds() > 0) {
                        validPerformanceCount++;
                        totalDiamonds += performance.getDiamonds();
                    }
                }
                
                if (validPerformanceCount == 0) {
                    canCalculate = false;
                    errors.add("该月份没有有效的钻石收入数据");
                } else {
                    message.append(String.format("找到 %d 个主播的业绩数据，其中 %d 个有钻石收入；", 
                        performanceList.size(), validPerformanceCount));
                }
            }
            
            // 8. 检查主播关系数据
            try {
                // 简单检查是否有主播数据
                Creator queryCreator = new Creator();
                List<Creator> creators = creatorService.selectCreatorList(queryCreator);
                if (creators == null || creators.isEmpty()) {
                    warnings.add("系统中没有主播数据，无法计算团队关系");
                } else {
                    // 统计有上级关系的主播数量
                    int withParentCount = 0;
                    for (Creator creator : creators) {
                        if (creator.getParentId() != null) {
                            withParentCount++;
                        }
                    }
                    message.append(String.format("共有 %d 个主播，其中 %d 个配置了上级关系；", 
                        creators.size(), withParentCount));
                }
            } catch (Exception e) {
                warnings.add("检查主播关系数据时出现异常：" + e.getMessage());
            }
            
            // 9. 检查是否已经计算过
            CommissionMonthlySummary existingSummary = commissionMonthlySummaryService.selectCommissionMonthlySummaryByMonth(dataMonth);
            if (existingSummary != null && "completed".equals(existingSummary.getCalculationStatus())) {
                warnings.add("该月份已完成计算，如需重新计算请使用重新计算接口");
            } else if (existingSummary != null && "processing".equals(existingSummary.getCalculationStatus())) {
                warnings.add("该月份正在计算中");
            }
            
            // 10. 生成最终检查结果
            if (canCalculate && errors.isEmpty()) {
                message.append("所有前置条件检查通过，可以开始计算。");
            } else {
                if (!errors.isEmpty()) {
                    message.append("发现以下错误：").append(String.join("；", errors)).append("。");
                }
                if (!warnings.isEmpty()) {
                    message.append("警告：").append(String.join("；", warnings)).append("。");
                }
            }
            
            result.put("canCalculate", canCalculate && errors.isEmpty());
            result.put("message", message.toString());
            result.put("dataMonth", monthStr);
            result.put("errors", errors);
            result.put("warnings", warnings);
            
            log.info("完成 {} 月度佣金计算前置条件检查，结果：{}", monthStr, canCalculate && errors.isEmpty() ? "通过" : "失败");
            
        } catch (Exception e) {
            log.error("检查计算前置条件失败：{}", e.getMessage(), e);
            result.put("canCalculate", false);
            result.put("message", "前置条件检查异常：" + e.getMessage());
            result.put("errors", java.util.Arrays.asList("系统异常：" + e.getMessage()));
            result.put("warnings", new ArrayList<>());
        }
        
        return result;
    }

    /**
     * 获取月度计算总览
     * 
     * @param dataMonth 数据月份
     * @return 计算总览
     */
    @Override
    public CommissionMonthlySummary getMonthlyCalculationSummary(Date dataMonth)
    {
        return commissionMonthlySummaryService.selectCommissionMonthlySummaryByMonth(dataMonth);
    }

    /**
     * 执行具体的计算逻辑
     * 
     * @param dataMonth 数据月份
     * @param summary 计算总览对象
     */
    private void performCalculation(Date dataMonth, CommissionMonthlySummary summary)
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        try {
            log.info("=== 开始 {} 月度佣金计算核心逻辑 ===", monthStr);
            
            // 记录计算开始日志
            logCalculationEvent(dataMonth, "CALCULATION_START", null, "INFO",
                "开始执行月度佣金计算", null);

            // 前置任务一：执行拉新资格判定
            log.info("前置任务一：执行拉新资格判定...");
            executeNewRecruitQualificationTask(dataMonth);

            // 前置任务二：执行等级调整（防守体系）
            log.info("前置任务二：执行等级调整（防守体系）...");
            executeDistributorLevelAdjustmentTask(dataMonth);

            // 第一步：加载配置数据并创建快照
            log.info("第一步：加载配置数据并创建快照...");
            CalculationConfig config = loadCalculationConfig(dataMonth);
            
            // 创建配置快照
            createConfigSnapshots(dataMonth, config);
            
            log.info("配置数据加载完成 - 预算: {}USD, 汇率: {}, 基础门槛: {}钻石", 
                config.getBudgetUsd(), config.getDiamondToUsdRate(), config.getBaseDiamondThreshold());
            
            // 第二步：获取所有分销员候选人（有业绩的主播）
            log.info("第二步：获取分销员候选人列表...");
            List<Long> distributorCandidates = getDistributorCandidates(dataMonth);
            log.info("找到 {} 个分销员候选人", distributorCandidates.size());
            
            if (distributorCandidates.isEmpty()) {
                log.warn("没有找到任何分销员候选人，计算将直接结束");
                return;
            }
            
            // 第三步：计算每个分销员的收入
            log.info("第三步：开始计算分销员收入...");
            List<CommissionPayouts> allPayouts = new ArrayList<>();
            BigDecimal totalPayoutUsd = BigDecimal.ZERO;
            int processedCount = 0;
            int qualifiedCount = 0;
            
            for (Long creatorId : distributorCandidates) {
                processedCount++;
                log.debug("开始计算分销员 {} ({}/{})...", creatorId, processedCount, distributorCandidates.size());
                
                DistributorCalculationResult result = calculateDistributorIncome(creatorId, dataMonth, config);
                
                // 保存分销员资格记录（无论是否获得收入）
                saveDistributorQualification(result, dataMonth);
                
                if (result.getFinalPayoutUsd().compareTo(BigDecimal.ZERO) > 0) {
                    qualifiedCount++;
                    // 保存分销员收入记录
                    CommissionPayouts payout = saveDistributorPayout(result, dataMonth, config);
                    allPayouts.add(payout);
                    totalPayoutUsd = totalPayoutUsd.add(result.getFinalPayoutUsd());
                    
                    log.info("分销员 {} 计算完成 - 等级: {}, 收入: {}USD, 个人钻石: {}, 团队钻石: {}", 
                        creatorId, result.getAchievedLevel(), result.getFinalPayoutUsd(), 
                        result.getPersonalDiamonds(), result.getTeamDiamonds());
                } else {
                    log.debug("分销员 {} 未获得收入 - 门槛达成: {}, 等级: {}", 
                        creatorId, result.isThresholdMet(), result.getAchievedLevel());
                }
                
                // 每处理100个分销员输出进度
                if (processedCount % 100 == 0) {
                    log.info("计算进度: {}/{}, 当前合格: {}, 当前总支出: {}USD", 
                        processedCount, distributorCandidates.size(), qualifiedCount, totalPayoutUsd);
                }
            }
            
            // 第四步：更新月度总览数据
            log.info("第四步：更新月度总览数据...");
            updateMonthlySummary(summary, config, totalPayoutUsd, allPayouts.size());

            // 任务D：执行月度成就摘要生成
            log.info("任务D：执行月度成就摘要生成...");
            generateTeamAchievementSummary(dataMonth);

            // 任务E：更新用户基础等级
            log.info("任务E：更新用户基础等级...");
            updateUserBaseLevels(dataMonth);

            // 记录计算完成日志
            BigDecimal budgetUtilization = config.getBudgetUsd().compareTo(BigDecimal.ZERO) > 0 ? 
                totalPayoutUsd.divide(config.getBudgetUsd(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP) : 
                BigDecimal.ZERO;
            
            logCalculationEvent(dataMonth, "CALCULATION_COMPLETE", null, "INFO", 
                "月度佣金计算完成", 
                String.format("{\"candidates\":%d,\"qualified\":%d,\"totalPayoutUsd\":\"%s\",\"budgetUtilization\":\"%s%%\"}", 
                    distributorCandidates.size(), qualifiedCount, totalPayoutUsd, budgetUtilization));
            
            log.info("=== 完成 {} 月度佣金计算核心逻辑 ===", monthStr);
            log.info("计算统计: 候选人={}, 合格分销员={}, 总支出={}USD, 预算利用率={}%", 
                distributorCandidates.size(), 
                qualifiedCount, 
                totalPayoutUsd,
                budgetUtilization);
                
        } catch (Exception e) {
            // 记录计算失败日志
            logCalculationEvent(dataMonth, "CALCULATION_ERROR", null, "ERROR", 
                "月度佣金计算失败: " + e.getMessage(), 
                String.format("{\"errorType\":\"%s\",\"stackTrace\":\"%s\"}", 
                    e.getClass().getSimpleName(), 
                    e.getStackTrace()[0].toString()));
            
            log.error("执行佣金计算逻辑失败", e);
            throw new RuntimeException("佣金计算逻辑执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 加载计算所需的配置数据
     * 
     * @param dataMonth 数据月份
     * @return 计算配置对象
     */
    private CalculationConfig loadCalculationConfig(Date dataMonth) {
        CalculationConfig config = new CalculationConfig();
        
        // 加载月度配置
        CommissionMonthlySettings monthlySettings = commissionMonthlySettingsService.selectCommissionMonthlySettingsByMonth(dataMonth);
        if (monthlySettings == null) {
            throw new RuntimeException("未找到月度配置数据，请先配置当月预算和汇率");
        }
        
        config.setBudgetUsd(monthlySettings.getManualIncomeUsd());
        config.setPayoutCapRate(monthlySettings.getPayoutCapRate());
        config.setDiamondToUsdRate(monthlySettings.getDiamondToUsdRate());
        config.setPayoutCapUsd(monthlySettings.getManualIncomeUsd().multiply(monthlySettings.getPayoutCapRate()));
        
        // 加载全局配置
        CommissionSettings baseThresholdSetting = commissionSettingsService.selectCommissionSettingsByKey("base_diamond_threshold");
        CommissionSettings increaseRateSetting = commissionSettingsService.selectCommissionSettingsByKey("threshold_increase_rate");
        
        if (baseThresholdSetting == null || increaseRateSetting == null) {
            throw new RuntimeException("未找到基础门槛配置，请先配置系统参数");
        }
        
        config.setBaseDiamondThreshold(Long.parseLong(baseThresholdSetting.getSettingValue()));
        config.setThresholdIncreaseRate(new BigDecimal(increaseRateSetting.getSettingValue()));
        
        // 加载分销员等级规则
        config.setLevelRules(commissionLevelRulesService.selectActiveCommissionLevelRules());
        
        // 加载多级提成规则
        config.setMultilevelRules(commissionMultilevelRulesService.selectActiveCommissionMultilevelRules());
        
        // 加载拉新奖励规则
        config.setRecruitmentBonusRules(commissionRecruitmentBonusRulesService.selectActiveCommissionRecruitmentBonusRules());
        
        log.info("配置加载完成 - 汇率: {}, 基础门槛: {}, 上浮比例: {}%", 
            config.getDiamondToUsdRate(), 
            config.getBaseDiamondThreshold(), 
            config.getThresholdIncreaseRate().multiply(new BigDecimal("100")));
            
        return config;
    }
    
    /**
     * 获取所有分销员候选人（包括个人有业绩的主播和团队有业绩的分销员）
     * 
     * @param dataMonth 数据月份
     * @return 分销员候选人ID列表
     */
    private List<Long> getDistributorCandidates(Date dataMonth) {
        Set<Long> candidates = new HashSet<>();
        
        // 第一类：当月有个人业绩的主播
        MonthlyPerformance queryCondition = new MonthlyPerformance();
        queryCondition.setDataMonth(dataMonth);
        
        List<MonthlyPerformance> monthlyPerformances = monthlyPerformanceService.selectMonthlyPerformanceList(queryCondition);
        
        for (MonthlyPerformance performance : monthlyPerformances) {
            if (performance.getDiamonds() != null && performance.getDiamonds() > 0) {
                candidates.add(performance.getCreatorId());
            }
        }
        
        log.debug("找到 {} 个有个人业绩的主播", candidates.size());
        
        // 第二类：虽然个人没业绩，但有下级且下级有业绩的分销员
        Set<Long> potentialDistributors = getPotentialDistributorsWithTeamPerformance(dataMonth, monthlyPerformances);
        candidates.addAll(potentialDistributors);
        
        log.debug("总共找到 {} 个分销员候选人（包括团队业绩分销员）", candidates.size());
        
        return new ArrayList<>(candidates);
    }
    
    /**
     * 获取虽然个人没业绩但团队有业绩的潜在分销员
     * 
     * @param dataMonth 数据月份
     * @param monthlyPerformances 当月所有业绩记录
     * @return 潜在分销员ID集合
     */
    private Set<Long> getPotentialDistributorsWithTeamPerformance(Date dataMonth, List<MonthlyPerformance> monthlyPerformances) {
        Set<Long> potentialDistributors = new HashSet<>();
        
        try {
            // 获取所有有业绩的主播ID
            Set<Long> creatorsWithPerformance = new HashSet<>();
            for (MonthlyPerformance performance : monthlyPerformances) {
                if (performance.getDiamonds() != null && performance.getDiamonds() > 0) {
                    creatorsWithPerformance.add(performance.getCreatorId());
                }
            }
            
            // 遍历所有有业绩的主播，找到他们的上级（潜在分销员）
            for (Long creatorId : creatorsWithPerformance) {
                try {
                    Creator creator = creatorService.selectCreatorById(creatorId);
                    if (creator != null && creator.getParentId() != null) {
                        // 将上级添加为潜在分销员候选人
                        potentialDistributors.add(creator.getParentId());
                        
                        // 递归查找更上级的分销员（L2、L3层级的上级）
                        addUpperLevelDistributors(creator.getParentId(), potentialDistributors, 2); // 最多查找2级上级
                    }
                } catch (Exception e) {
                    log.debug("查询主播 {} 的上级关系失败: {}", creatorId, e.getMessage());
                }
            }
            
            log.debug("通过团队业绩发现 {} 个潜在分销员", potentialDistributors.size());
            
        } catch (Exception e) {
            log.error("获取团队业绩分销员失败: {}", e.getMessage());
        }
        
        return potentialDistributors;
    }
    
    /**
     * 递归添加更上级的分销员
     * 
     * @param creatorId 当前主播ID
     * @param potentialDistributors 潜在分销员集合
     * @param maxDepth 最大递归深度
     */
    private void addUpperLevelDistributors(Long creatorId, Set<Long> potentialDistributors, int maxDepth) {
        if (maxDepth <= 0 || creatorId == null) {
            return;
        }
        
        try {
            Creator creator = creatorService.selectCreatorById(creatorId);
            if (creator != null && creator.getParentId() != null) {
                potentialDistributors.add(creator.getParentId());
                // 递归查找上级的上级
                addUpperLevelDistributors(creator.getParentId(), potentialDistributors, maxDepth - 1);
            }
        } catch (Exception e) {
            log.debug("递归查找主播 {} 的上级失败: {}", creatorId, e.getMessage());
        }
    }
    
    /**
     * 计算单个分销员的收入
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @param config 计算配置
     * @return 计算结果
     */
    private DistributorCalculationResult calculateDistributorIncome(Long creatorId, Date dataMonth, CalculationConfig config) {
        DistributorCalculationResult result = new DistributorCalculationResult();
        result.setCreatorId(creatorId);
        
        log.debug("开始计算分销员 {} 的收入详情...", creatorId);
        
        // 添加详细的计算步骤日志
        logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION_START", creatorId, "INFO", 
            "开始计算分销员收入", 
            String.format("{\"creatorId\":%d,\"step\":\"START\"}", creatorId));
        
        // 第一步：获取个人业绩数据
        MonthlyPerformance performance = getCreatorPerformance(creatorId, dataMonth);
        long personalDiamonds = 0;
        
        if (performance == null || performance.getDiamonds() == null) {
            logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO", 
                "分销员无个人业绩，个人钻石设为0", 
                String.format("{\"creatorId\":%d,\"step\":\"PERFORMANCE_CHECK\",\"result\":\"NO_PERSONAL_DATA\"}", creatorId));
            log.debug("分销员 {} 无个人业绩数据，个人钻石设为0", creatorId);
            personalDiamonds = 0;
        } else {
            personalDiamonds = performance.getDiamonds();
            logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO", 
                "获取个人业绩数据", 
                String.format("{\"creatorId\":%d,\"step\":\"PERSONAL_PERFORMANCE\",\"personalDiamonds\":%d}", 
                    creatorId, personalDiamonds));
            log.debug("分销员 {} 个人钻石收入: {}", creatorId, personalDiamonds);
        }
        
        result.setPersonalDiamonds(personalDiamonds);
        
        // 第二步：计算团队数据（先计算团队，再判断总门槛）
        List<Long> directDownlines = getDirectDownlines(creatorId);
        result.setDirectDownlinesCount(directDownlines.size());
        
        long teamDiamonds = calculateTeamDiamonds(directDownlines, dataMonth);
        result.setTeamDiamonds(teamDiamonds);
        
        // 记录团队数据
        logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO", 
            "计算团队数据", 
            String.format("{\"creatorId\":%d,\"step\":\"TEAM_CALCULATION\",\"directDownlinesCount\":%d,\"teamDiamonds\":%d,\"downlineIds\":%s}", 
                creatorId, directDownlines.size(), teamDiamonds, directDownlines.toString()));
        log.debug("分销员 {} 团队数据: 下级数量={}, 团队钻石={}", creatorId, directDownlines.size(), teamDiamonds);
        
        // 第三步：计算动态门槛
        long dynamicThreshold = calculateDynamicThreshold(creatorId, dataMonth, config);
        result.setDynamicThreshold(dynamicThreshold);
        
        // 重要修改：基于个人+团队总钻石数检查门槛
        long totalDiamonds = personalDiamonds + teamDiamonds;
        boolean thresholdMet = totalDiamonds >= dynamicThreshold;
        result.setThresholdMet(thresholdMet);
        
        // 记录门槛检查
        logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO", 
            "动态门槛检查（基于个人+团队总钻石）", 
            String.format("{\"creatorId\":%d,\"step\":\"THRESHOLD_CHECK\",\"personalDiamonds\":%d,\"teamDiamonds\":%d,\"totalDiamonds\":%d,\"dynamicThreshold\":%d,\"thresholdMet\":%b}", 
                creatorId, personalDiamonds, teamDiamonds, totalDiamonds, dynamicThreshold, thresholdMet));
        
        if (!thresholdMet) {
            logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO", 
                "未达到门槛，计算结束", 
                String.format("{\"creatorId\":%d,\"step\":\"THRESHOLD_NOT_MET\",\"totalDiamonds\":%d,\"dynamicThreshold\":%d,\"finalPayoutUsd\":0}", 
                    creatorId, totalDiamonds, dynamicThreshold));
            log.debug("分销员 {} 未达到动态门槛 {} (个人+团队收入: {} = {} + {})", 
                creatorId, dynamicThreshold, totalDiamonds, personalDiamonds, teamDiamonds);
            return result; // 未达到门槛，无法获得等级分成和多级提成
        }
        
        log.debug("分销员 {} 达到动态门槛，继续计算... (个人+团队收入: {} = {} + {})", 
            creatorId, totalDiamonds, personalDiamonds, teamDiamonds);
        
        // 第四步：评定分销员等级（基于合格拉新人数）
        int qualifiedRecruitsCount = countQualifiedNewRecruits(creatorId, dataMonth);
        String achievedLevel = evaluateDistributorLevel(qualifiedRecruitsCount, config.getLevelRules());
        result.setAchievedLevel(achievedLevel);
        
        // 记录等级评定
        logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO",
            "评定分销员等级",
            String.format("{\"creatorId\":%d,\"step\":\"LEVEL_EVALUATION\",\"qualifiedRecruitsCount\":%d,\"achievedLevel\":\"%s\"}",
                creatorId, qualifiedRecruitsCount, achievedLevel != null ? achievedLevel : "NONE"));
        log.debug("分销员 {} 评定等级: {} (基于{}个合格拉新)", creatorId, achievedLevel != null ? achievedLevel : "无等级", qualifiedRecruitsCount);
        
        if (achievedLevel != null) {
            // 第五步：计算等级分成
            BigDecimal levelCommission = calculateLevelCommission(personalDiamonds, teamDiamonds, achievedLevel, config);
            result.setLevelCommissionDiamonds(levelCommission);
            
            // 记录完整的等级分成算法过程
            BigDecimal levelCommissionRate = getLevelCommissionRate(achievedLevel, config.getLevelRules());
            logLevelCommissionAlgorithm(dataMonth, creatorId, personalDiamonds, teamDiamonds, 
                achievedLevel, config, levelCommissionRate, levelCommission);
            log.debug("分销员 {} 等级分成: {}钻石", creatorId, levelCommission);
            
            // 第六步：计算多级提成（基于等级的收益深度限制）
            BigDecimal multilevelCommission = calculateMultilevelCommission(creatorId, dataMonth, config, achievedLevel);
            result.setMultilevelCommissionDiamonds(multilevelCommission);
            
            // 记录多级提成（需要详细记录每层级的计算）
            logMultilevelCommissionDetails(creatorId, dataMonth, config, multilevelCommission, achievedLevel);
            log.debug("分销员 {} 多级提成: {}钻石", creatorId, multilevelCommission);
        }
        
        // 第七步：计算拉新奖励（同时保存拉新人数到结果中）
        int newRecruitsCount = countNewRecruits(creatorId, dataMonth);
        BigDecimal recruitmentBonus = calculateRecruitmentBonusWithCount(creatorId, dataMonth, config, newRecruitsCount);
        result.setRecruitmentBonusUsd(recruitmentBonus);
        result.setNewRecruitsCount(newRecruitsCount); // 保存拉新人数到结果中，避免重复计算
        log.debug("分销员 {} 拉新人数: {}, 拉新奖励: {}USD", creatorId, newRecruitsCount, recruitmentBonus);
        
        // 第八步：计算最终收入
        BigDecimal totalCommissionDiamonds = result.getLevelCommissionDiamonds().add(result.getMultilevelCommissionDiamonds());
        BigDecimal diamondsToUsd = totalCommissionDiamonds.multiply(config.getDiamondToUsdRate()).setScale(4, RoundingMode.HALF_UP);
        BigDecimal finalPayoutUsd = diamondsToUsd.add(result.getRecruitmentBonusUsd()).setScale(2, RoundingMode.HALF_UP);
        result.setFinalPayoutUsd(finalPayoutUsd);
        
        // 记录最终计算结果
        logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO", 
            "最终收入计算完成", 
            String.format("{\"creatorId\":%d,\"step\":\"FINAL_CALCULATION\",\"levelCommissionDiamonds\":\"%s\",\"multilevelCommissionDiamonds\":\"%s\",\"recruitmentBonusUsd\":\"%s\",\"diamondToUsdRate\":\"%s\",\"finalPayoutUsd\":\"%s\"}", 
                creatorId, result.getLevelCommissionDiamonds(), result.getMultilevelCommissionDiamonds(), 
                result.getRecruitmentBonusUsd(), config.getDiamondToUsdRate(), finalPayoutUsd));
        
        log.debug("分销员 {} 收入计算完成: 总分成钻石={}, 钻石转USD={}, 最终收入={}USD", 
            creatorId, totalCommissionDiamonds, diamondsToUsd, finalPayoutUsd);
        
        return result;
    }
    
    /**
     * 计算动态钻石门槛
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 当前月份
     * @param config 计算配置
     * @return 动态门槛值
     */
    private long calculateDynamicThreshold(Long creatorId, Date dataMonth, CalculationConfig config) {
        // 获取上月钻石收入
        Date lastMonth = getLastMonth(dataMonth);
        MonthlyPerformance lastMonthPerformance = getCreatorPerformance(creatorId, lastMonth);
        
        long lastMonthDiamonds = 0;
        if (lastMonthPerformance != null && lastMonthPerformance.getDiamonds() != null) {
            lastMonthDiamonds = lastMonthPerformance.getDiamonds();
        }
        
        // 计算动态门槛：max(上月收入 * (1 + 上浮比例), 基础门槛)
        BigDecimal calculatedThreshold = BigDecimal.valueOf(lastMonthDiamonds)
            .multiply(BigDecimal.ONE.add(config.getThresholdIncreaseRate()));
        
        long dynamicThreshold = Math.max(calculatedThreshold.longValue(), config.getBaseDiamondThreshold());
        
        // 记录完整的算法执行过程
        logThresholdCalculationAlgorithm(dataMonth, creatorId, lastMonthDiamonds, config, 
            calculatedThreshold.longValue(), dynamicThreshold);
        
        // 保存动态门槛记录
        saveDynamicThreshold(creatorId, dataMonth, lastMonthDiamonds, dynamicThreshold, config);
        
        log.debug("分销员 {} 动态门槛计算: 上月收入={}, 基础门槛={}, 计算门槛={}", 
            creatorId, lastMonthDiamonds, config.getBaseDiamondThreshold(), dynamicThreshold);
            
        return dynamicThreshold;
    }
    
    /**
     * 计算等级分成
     * 
     * @param personalDiamonds 个人钻石收入
     * @param teamDiamonds 团队钻石收入
     * @param achievedLevel 达成等级
     * @param config 计算配置
     * @return 等级分成钻石数
     */
    private BigDecimal calculateLevelCommission(long personalDiamonds, long teamDiamonds, 
                                               String achievedLevel, CalculationConfig config) {
        if (achievedLevel == null) {
            return BigDecimal.ZERO;
        }
        
        // 查找对应等级的分成比例
        BigDecimal commissionRate = BigDecimal.ZERO;
        for (CommissionLevelRules rule : config.getLevelRules()) {
            if (achievedLevel.equals(rule.getLevelName())) {
                commissionRate = rule.getCommissionRate();
                break;
            }
        }
        
        if (commissionRate.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        // 等级分成 = (个人钻石 + 团队钻石) * 分成比例
        long totalTeamDiamonds = personalDiamonds + teamDiamonds;
        BigDecimal levelCommission = BigDecimal.valueOf(totalTeamDiamonds)
            .multiply(commissionRate)
            .setScale(4, RoundingMode.HALF_UP);
        
        // 需要creatorId参数，这里暂时传null，后续需要从调用方法传递
        // logLevelCommissionAlgorithm(dataMonth, creatorId, personalDiamonds, teamDiamonds, 
        //     achievedLevel, config, commissionRate, levelCommission);
        
        log.debug("等级分成计算: {}级 {}钻石 * {}% = {}钻石", 
            achievedLevel, totalTeamDiamonds, commissionRate.multiply(new BigDecimal("100")), levelCommission);
            
        return levelCommission;
    }
    
    /**
     * 计算多级提成（基于等级的收益深度限制）
     *
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @param config 计算配置
     * @param achievedLevel 达成的等级名称
     * @return 多级提成钻石数
     */
    private BigDecimal calculateMultilevelCommission(Long creatorId, Date dataMonth, CalculationConfig config, String achievedLevel) {
        BigDecimal totalMultilevelCommission = BigDecimal.ZERO;
        Map<Integer, Long> depthIncomes = new HashMap<>();
        Map<Integer, BigDecimal> depthCommissions = new HashMap<>();

        // 获取用户等级对应的收益深度限制
        Integer payoutDepth = getPayoutDepthByLevel(achievedLevel, config.getLevelRules());
        if (payoutDepth == null) {
            log.debug("分销员 {} 未达到任何等级，无多级提成收益", creatorId);
            return BigDecimal.ZERO;
        }

        for (CommissionMultilevelRules rule : config.getMultilevelRules()) {
            // 检查是否超出收益深度限制
            if (rule.getDepth() > payoutDepth) {
                log.debug("L{}层级超出收益深度限制({}级)，跳过计算", rule.getDepth(), payoutDepth);
                depthIncomes.put(rule.getDepth(), 0L);
                depthCommissions.put(rule.getDepth(), BigDecimal.ZERO);
                continue;
            }

            // 获取指定层级下级的钻石收入总和
            long depthIncome = getDownlineIncomeByDepth(creatorId, dataMonth, rule.getDepth());
            depthIncomes.put(rule.getDepth(), depthIncome);

            if (depthIncome > 0) {
                BigDecimal depthCommission = BigDecimal.valueOf(depthIncome)
                    .multiply(rule.getCommissionRate())
                    .setScale(4, RoundingMode.HALF_UP);

                depthCommissions.put(rule.getDepth(), depthCommission);
                totalMultilevelCommission = totalMultilevelCommission.add(depthCommission);

                log.debug("L{}提成计算: {}钻石 * {}% = {}钻石 (收益深度限制:{}级)",
                    rule.getDepth(), depthIncome,
                    rule.getCommissionRate().multiply(new BigDecimal("100")),
                    depthCommission, payoutDepth);
            } else {
                depthCommissions.put(rule.getDepth(), BigDecimal.ZERO);
            }
        }
        
        // 记录完整的多级提成算法过程
        logMultilevelCommissionAlgorithm(dataMonth, creatorId, config, 
            depthIncomes, depthCommissions, totalMultilevelCommission);
        
        return totalMultilevelCommission;
    }

    /**
     * 根据等级名称获取对应的收益深度
     * 使用Map缓存提升查找效率
     *
     * @param levelName 等级名称
     * @param levelRules 等级规则列表
     * @return 收益深度，如果等级不存在则返回null
     */
    private Integer getPayoutDepthByLevel(String levelName, List<CommissionLevelRules> levelRules) {
        if (levelName == null || levelRules == null) {
            return null;
        }

        // 使用Map缓存提升查找效率
        Map<String, Integer> levelPayoutDepthMap = buildLevelPayoutDepthMap(levelRules);
        return levelPayoutDepthMap.get(levelName);
    }

    /**
     * 构建等级名称到收益深度的映射Map
     *
     * @param levelRules 等级规则列表
     * @return 等级名称到收益深度的映射
     */
    private Map<String, Integer> buildLevelPayoutDepthMap(List<CommissionLevelRules> levelRules) {
        Map<String, Integer> map = new HashMap<>();
        if (levelRules != null) {
            for (CommissionLevelRules rule : levelRules) {
                if (rule.getLevelName() != null && rule.getPayoutDepth() != null) {
                    map.put(rule.getLevelName(), rule.getPayoutDepth());
                }
            }
        }
        return map;
    }

    /**
     * 计算新人奖励
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @param config 计算配置
     * @return 新人奖励USD金额
     */
    private BigDecimal calculateRecruitmentBonus(Long creatorId, Date dataMonth, CalculationConfig config) {
        // 统计当月新人下级数量（基于 is_rookie=1）
        int newRecruitsCount = countNewRecruits(creatorId, dataMonth);
        return calculateRecruitmentBonusWithCount(creatorId, dataMonth, config, newRecruitsCount);
    }
    
    /**
     * 基于已知拉新人数计算新人奖励
     *
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @param config 计算配置
     * @param newRecruitsCount 新招募人数
     * @return 新人奖励USD金额
     */
    private BigDecimal calculateRecruitmentBonusWithCount(Long creatorId, Date dataMonth, CalculationConfig config, int newRecruitsCount) {
        if (newRecruitsCount == 0) {
            // 记录无新人下级的情况
            logRecruitmentBonusAlgorithm(dataMonth, creatorId, newRecruitsCount, config, BigDecimal.ZERO);
            return BigDecimal.ZERO;
        }

        // 🔴 关键修复：不依赖外部排序，内部确保按min_new_recruits降序排序
        // 这样可以确保用户获得最高的符合条件的档位奖励，避免业务逻辑错误
        List<CommissionRecruitmentBonusRules> sortedRules = new ArrayList<>(config.getRecruitmentBonusRules());
        sortedRules.sort((r1, r2) -> {
            // 按min_new_recruits降序排序，确保高档位优先匹配
            int compare = Integer.compare(r2.getMinNewRecruits(), r1.getMinNewRecruits());
            if (compare == 0) {
                // 如果拉新数相同，按ID升序排序保证稳定性
                return Integer.compare(r1.getId(), r2.getId());
            }
            return compare;
        });

        // 按新人下级数量匹配最高档奖励单价
        BigDecimal bonusUsdPerPerson = BigDecimal.ZERO;
        for (CommissionRecruitmentBonusRules rule : sortedRules) {
            if (newRecruitsCount >= rule.getMinNewRecruits()) {
                bonusUsdPerPerson = rule.getBonusUsd();
                break; // 已按min_new_recruits降序排列，匹配第一个即为最高档
            }
        }

        // 计算总奖励金额：拉新奖励金额 = bonus_usd * 拉新人数
        BigDecimal totalBonusUsd = bonusUsdPerPerson.multiply(new BigDecimal(newRecruitsCount))
            .setScale(4, RoundingMode.HALF_UP);

        // 记录完整的新人奖励算法过程
        logRecruitmentBonusAlgorithm(dataMonth, creatorId, newRecruitsCount, config, totalBonusUsd, bonusUsdPerPerson);

        // 保存新人统计记录
        saveRecruitmentStats(creatorId, dataMonth, newRecruitsCount, totalBonusUsd);

        log.debug("新人奖励计算: 当月新人下级{}人，单价{}USD，总奖励{}USD", newRecruitsCount, bonusUsdPerPerson, totalBonusUsd);
        return totalBonusUsd;
    }
    
    /**
     * 获取主播业绩数据
     * 
     * @param creatorId 主播ID
     * @param dataMonth 数据月份
     * @return 月度业绩数据
     */
    private MonthlyPerformance getCreatorPerformance(Long creatorId, Date dataMonth) {
        MonthlyPerformance queryCondition = new MonthlyPerformance();
        queryCondition.setCreatorId(creatorId);
        queryCondition.setDataMonth(dataMonth);
        
        List<MonthlyPerformance> performances = monthlyPerformanceService.selectMonthlyPerformanceList(queryCondition);
        
        // 返回匹配的第一条记录，如果没有则返回null
        return performances.isEmpty() ? null : performances.get(0);
    }
    
    private Date getLastMonth(Date dataMonth) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dataMonth);
        cal.add(Calendar.MONTH, -1);
        return cal.getTime();
    }
    
    /**
     * 获取直属下级列表（L1层级）
     * 
     * @param creatorId 分销员ID
     * @return 直属下级ID列表
     */
    private List<Long> getDirectDownlines(Long creatorId) {
        try {
            // 查询直接下级（parent_id = creatorId）
            Creator queryCondition = new Creator();
            queryCondition.setParentId(creatorId);
            
            List<Creator> directDownlines = creatorService.selectCreatorList(queryCondition);
            
            List<Long> downlineIds = new ArrayList<>();
            for (Creator creator : directDownlines) {
                downlineIds.add(creator.getId());
            }
            
            return downlineIds;
        } catch (Exception e) {
            log.warn("获取分销员 {} 的直属下级失败: {}", creatorId, e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 计算团队钻石收入（直属下级的钻石收入总和）
     * 🔴 关键修复：使用批量查询避免N+1查询问题
     *
     * @param directDownlines 直属下级ID列表
     * @param dataMonth 数据月份
     * @return 团队钻石收入总和
     */
    private long calculateTeamDiamonds(List<Long> directDownlines, Date dataMonth) {
        if (directDownlines.isEmpty()) {
            return 0;
        }

        // 🔴 修复：使用批量查询替代循环查询，避免N+1查询问题
        long totalTeamDiamonds = getBatchCreatorPerformanceSum(directDownlines, dataMonth);

        log.debug("团队钻石收入计算: {} 个下级，总收入 {} 钻石 (使用批量查询)",
            directDownlines.size(), totalTeamDiamonds);
        return totalTeamDiamonds;
    }
    
    /**
     * 评定分销员等级（基于合格拉新人数）
     *
     * @param qualifiedRecruitsCount 合格拉新人数
     * @param levelRules 等级规则列表
     * @return 达成的等级名称，如果未达到任何等级则返回null
     */
    private String evaluateDistributorLevel(int qualifiedRecruitsCount, List<CommissionLevelRules> levelRules) {
        if (levelRules == null || levelRules.isEmpty()) {
            return null;
        }

        // 🔴 关键修复：不依赖外部排序，内部确保按min_qualified_recruits降序排序
        // 这样可以确保用户获得最高的符合条件的等级，避免业务逻辑错误
        List<CommissionLevelRules> sortedRules = new ArrayList<>(levelRules);
        sortedRules.sort((r1, r2) -> {
            // 按min_qualified_recruits降序排序，确保高等级优先匹配
            int compare = Integer.compare(r2.getMinQualifiedRecruits(), r1.getMinQualifiedRecruits());
            if (compare == 0) {
                // 如果拉新数相同，按ID升序排序保证稳定性
                return Integer.compare(r1.getId(), r2.getId());
            }
            return compare;
        });

        // 按等级要求从高到低检查
        for (CommissionLevelRules rule : sortedRules) {
            if (qualifiedRecruitsCount >= rule.getMinQualifiedRecruits()) {
                log.debug("分销员等级评定: {} 个合格拉新，达成 {} 级 (需要{}个)",
                    qualifiedRecruitsCount, rule.getLevelName(), rule.getMinQualifiedRecruits());
                return rule.getLevelName();
            }
        }

        log.debug("分销员等级评定: {} 个合格拉新，未达到任何等级", qualifiedRecruitsCount);
        return null;
    }
    
    /**
     * 按层级获取下级收入总和
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @param depth 层级深度（1=L1, 2=L2, 3=L3）
     * @return 指定层级下级的钻石收入总和
     */
    private long getDownlineIncomeByDepth(Long creatorId, Date dataMonth, Integer depth) {
        try {
            // 根据层级深度获取下级ID列表
            List<Long> downlineIds = getDownlineIdsByDepth(creatorId, depth);

            if (downlineIds.isEmpty()) {
                return 0;
            }

            // 使用批量查询避免N+1问题
            long totalIncome = getBatchCreatorPerformanceSum(downlineIds, dataMonth);

            log.debug("L{} 层级收入计算: {} 个下级，总收入 {} 钻石", depth, downlineIds.size(), totalIncome);
            return totalIncome;

        } catch (Exception e) {
            log.warn("获取分销员 {} 的L{} 层级收入失败: {}", creatorId, depth, e.getMessage());
            return 0;
        }
    }

    /**
     * 批量查询创作者业绩并计算总和
     * 避免N+1查询问题
     *
     * @param creatorIds 创作者ID列表
     * @param dataMonth 数据月份
     * @return 钻石收入总和
     */
    private long getBatchCreatorPerformanceSum(List<Long> creatorIds, Date dataMonth) {
        if (creatorIds == null || creatorIds.isEmpty()) {
            return 0;
        }

        try {
            // 使用新的批量查询方法，只查询指定创作者的业绩
            List<MonthlyPerformance> performances = monthlyPerformanceService
                .selectMonthlyPerformanceByCreatorIdsAndMonth(creatorIds, dataMonth);

            // 计算总和
            long totalIncome = 0;
            for (MonthlyPerformance performance : performances) {
                if (performance.getDiamonds() != null) {
                    totalIncome += performance.getDiamonds();
                }
            }

            log.debug("批量查询业绩: {} 个创作者，{} 条记录，总收入 {} 钻石",
                creatorIds.size(), performances.size(), totalIncome);

            return totalIncome;

        } catch (Exception e) {
            log.warn("批量查询创作者业绩失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 根据层级深度获取下级ID列表
     * 
     * @param creatorId 分销员ID
     * @param depth 层级深度
     * @return 下级ID列表
     */
    private List<Long> getDownlineIdsByDepth(Long creatorId, Integer depth) {
        if (depth == 1) {
            // L1：直属下级
            return getDirectDownlines(creatorId);
        } else if (depth == 2) {
            // L2：直属下级的下级
            List<Long> l2Downlines = new ArrayList<>();
            List<Long> l1Downlines = getDirectDownlines(creatorId);
            for (Long l1Id : l1Downlines) {
                l2Downlines.addAll(getDirectDownlines(l1Id));
            }
            return l2Downlines;
        } else if (depth == 3) {
            // L3：L2下级的下级
            List<Long> l3Downlines = new ArrayList<>();
            List<Long> l2Downlines = getDownlineIdsByDepth(creatorId, 2);
            for (Long l2Id : l2Downlines) {
                l3Downlines.addAll(getDirectDownlines(l2Id));
            }
            return l3Downlines;
        } else {
            log.warn("不支持的层级深度: {}", depth);
            return new ArrayList<>();
        }
    }
    
    /**
     * 统计当月新招募人数
     * 基于新的拉新资格判定机制，统计qualified_by_recruiter_id=creatorId且first_qualified_month为当月的用户数量
     *
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 当月新招募人数
     */
    private int countNewRecruits(Long creatorId, Date dataMonth) {
        try {
            // 使用新的拉新资格判定机制
            int count = countQualifiedNewRecruits(creatorId, dataMonth);

            // 添加调试日志
            log.info("统计分销员 {} 在 {} 月的合格拉新人数: {}",
                creatorId, new SimpleDateFormat("yyyy-MM").format(dataMonth), count);

            return count;

        } catch (Exception e) {
            log.warn("统计分销员 {} 的合格拉新人数失败: {}", creatorId, e.getMessage());
            return 0;
        }
    }
    
    /**
     * 基于 monthly_performance 表中的 is_rookie 字段统计新人数量
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 新人数量
     */
    private int countNewRecruitsByRookieStatus(Long creatorId, Date dataMonth) {
        // 获取所有直属下级
        List<Long> directDownlines = getDirectDownlines(creatorId);
        
        log.debug("开始统计分销员 {} 的新人下级: 总下级数量={}", creatorId, directDownlines.size());
        
        if (directDownlines.isEmpty()) {
            log.debug("分销员 {} 没有直属下级", creatorId);
            return 0;
        }
        
        int rookieCount = 0;
        
        // 遍历所有直属下级，检查其在当月的 is_rookie 状态
        for (Long downlineId : directDownlines) {
            try {
                MonthlyPerformance performance = getCreatorPerformance(downlineId, dataMonth);
                log.debug("检查下级主播 {} 的业绩数据: performance={}", downlineId, 
                    performance != null ? "存在" : "不存在");
                
                if (performance != null) {
                    log.debug("下级主播 {} 的 is_rookie 状态: {}", downlineId, performance.getIsRookie());
                    
                    if (performance.getIsRookie() != null && performance.getIsRookie() == 1) {
                        rookieCount++;
                        log.info("下级主播 {} 在当月标记为新人 (is_rookie=1), 当前新人计数={}", downlineId, rookieCount);
                    }
                } else {
                    log.debug("下级主播 {} 在当月没有业绩数据", downlineId);
                }
            } catch (Exception e) {
                log.warn("查询下级主播 {} 的当月业绩数据失败: {}", downlineId, e.getMessage());
            }
        }
        
        log.info("新人统计完成: 分销员 {} 在 {} 月有 {} 个新人下级 (基于 is_rookie 字段)", 
            creatorId, new SimpleDateFormat("yyyy-MM").format(dataMonth), rookieCount);
        
        return rookieCount;
    }

    /**
     * 基于新的拉新资格判定机制统计合格拉新人数
     * 统计qualified_by_recruiter_id=creatorId且first_qualified_month为当月的用户数量
     *
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 合格拉新人数
     */
    private int countQualifiedNewRecruits(Long creatorId, Date dataMonth) {
        try {
            // 计算当月的第一天
            Calendar cal = Calendar.getInstance();
            cal.setTime(dataMonth);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            Date monthStart = cal.getTime();

            // 查询所有用户
            Creator queryCondition = new Creator();
            List<Creator> allCreators = creatorService.selectCreatorList(queryCondition);

            int qualifiedCount = 0;
            for (Creator creator : allCreators) {
                // 检查是否为指定招募人的合格拉新用户
                if (creator.getQualifiedByRecruiterId() != null &&
                    creator.getQualifiedByRecruiterId().equals(creatorId) &&
                    creator.getFirstQualifiedMonth() != null &&
                    isSameMonth(creator.getFirstQualifiedMonth(), monthStart)) {

                    qualifiedCount++;
                    log.debug("找到分销员 {} 的合格拉新用户: {} (合格月份: {})",
                        creatorId, creator.getId(),
                        new SimpleDateFormat("yyyy-MM").format(creator.getFirstQualifiedMonth()));
                }
            }

            log.debug("分销员 {} 在 {} 月的合格拉新人数: {}",
                creatorId, new SimpleDateFormat("yyyy-MM").format(dataMonth), qualifiedCount);

            return qualifiedCount;

        } catch (Exception e) {
            log.error("统计分销员 {} 的合格拉新人数失败: {}", creatorId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 判断两个日期是否为同一个月
     */
    private boolean isSameMonth(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
    }

    /**
     * 基于创建时间统计新招募人数（保留原有方法作为备用）
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 新招募人数
     */
    private int countNewRecruitsByCreateTime(Long creatorId, Date dataMonth) {
        // 查询parent_id=creatorId且created_at在指定月份的主播
        Calendar cal = Calendar.getInstance();
        cal.setTime(dataMonth);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date monthStart = cal.getTime();
        
        cal.add(Calendar.MONTH, 1);
        Date monthEnd = cal.getTime();
        
        // 获取所有直属下级，然后过滤出当月创建的
        List<Long> directDownlines = getDirectDownlines(creatorId);
        
        int newCount = 0;
        for (Long downlineId : directDownlines) {
            try {
                Creator creator = creatorService.selectCreatorById(downlineId);
                if (creator != null && creator.getCreateTime() != null) {
                    Date createdAt = creator.getCreateTime();
                    if (createdAt.compareTo(monthStart) >= 0 && createdAt.compareTo(monthEnd) < 0) {
                        newCount++;
                    }
                }
            } catch (Exception e) {
                log.debug("查询主播 {} 创建时间失败: {}", downlineId, e.getMessage());
            }
        }
        
        log.debug("拉新统计: 分销员 {} 在 {} 月新招募 {} 人", 
            creatorId, new SimpleDateFormat("yyyy-MM").format(dataMonth), newCount);
        
        return newCount;
    }
    
    /**
     * 保存分销员收入记录
     * 
     * @param result 计算结果
     * @param dataMonth 数据月份
     * @return 保存的收入记录
     */
    private CommissionPayouts saveDistributorPayout(DistributorCalculationResult result, Date dataMonth, CalculationConfig config) {
        try {
            // 创建分销员收入记录
            CommissionPayouts payout = new CommissionPayouts();
            payout.setCreatorId(result.getCreatorId());
            payout.setDataMonth(dataMonth);
            payout.setDistributorLevel(result.getAchievedLevel());
            payout.setFinalPayoutUsd(result.getFinalPayoutUsd());
            
            // 保存主记录（使用插入或更新方法处理重复记录）
            int rows = commissionPayoutsService.insertOrUpdateCommissionPayouts(payout);
            if (rows <= 0) {
                throw new RuntimeException("保存分销员收入记录失败");
            }
            
            // 保存收入构成明细
            savePayoutBreakdowns(payout, result, dataMonth, config);
            
            log.debug("已保存分销员 {} 的收入记录，金额: {} USD", result.getCreatorId(), result.getFinalPayoutUsd());
            
            return payout;
            
        } catch (Exception e) {
            log.error("保存分销员 {} 收入记录失败: {}", result.getCreatorId(), e.getMessage(), e);
            throw new RuntimeException("保存收入记录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 保存收入构成明细
     * 
     * @param payout 收入记录
     * @param result 计算结果
     * @param dataMonth 数据月份
     */
    private void savePayoutBreakdowns(CommissionPayouts payout, DistributorCalculationResult result, Date dataMonth, CalculationConfig config) {
        List<CommissionPayoutBreakdowns> breakdowns = new ArrayList<>();
        
        // 等级分成明细
        if (result.getLevelCommissionDiamonds().compareTo(BigDecimal.ZERO) > 0) {
            CommissionPayoutBreakdowns levelBreakdown = new CommissionPayoutBreakdowns();
            levelBreakdown.setPayoutId(payout.getId());
            levelBreakdown.setCreatorId(result.getCreatorId());
            levelBreakdown.setDataMonth(dataMonth);
            levelBreakdown.setSourceType("LEVEL_COMMISSION");
            levelBreakdown.setBaseAmountDiamonds(result.getPersonalDiamonds() + result.getTeamDiamonds());
            levelBreakdown.setCalculatedAmountDiamonds(result.getLevelCommissionDiamonds().longValue());
            
            // 计算钻石对应的USD金额
            BigDecimal levelCommissionUsd = result.getLevelCommissionDiamonds().multiply(config.getDiamondToUsdRate()).setScale(4, RoundingMode.HALF_UP);
            levelBreakdown.setCalculatedAmountUsd(levelCommissionUsd);
            levelBreakdown.setCreateTime(DateUtils.getNowDate());
            breakdowns.add(levelBreakdown);
        }
        
        // 多级提成明细
        if (result.getMultilevelCommissionDiamonds().compareTo(BigDecimal.ZERO) > 0) {
            CommissionPayoutBreakdowns multilevelBreakdown = new CommissionPayoutBreakdowns();
            multilevelBreakdown.setPayoutId(payout.getId());
            multilevelBreakdown.setCreatorId(result.getCreatorId());
            multilevelBreakdown.setDataMonth(dataMonth);
            multilevelBreakdown.setSourceType("MULTI_LEVEL_COMMISSION");
            multilevelBreakdown.setBaseAmountDiamonds(null); // 多级提成基数为各层级下级收入，这里不单独设置
            multilevelBreakdown.setCalculatedAmountDiamonds(result.getMultilevelCommissionDiamonds().longValue());
            
            // 计算钻石对应的USD金额
            BigDecimal multilevelCommissionUsd = result.getMultilevelCommissionDiamonds().multiply(config.getDiamondToUsdRate()).setScale(4, RoundingMode.HALF_UP);
            multilevelBreakdown.setCalculatedAmountUsd(multilevelCommissionUsd);
            multilevelBreakdown.setCreateTime(DateUtils.getNowDate());
            breakdowns.add(multilevelBreakdown);
        }
        
        // 拉新奖励明细
        if (result.getRecruitmentBonusUsd().compareTo(BigDecimal.ZERO) > 0) {
            // 使用已计算的拉新人数，避免重复统计
            int newRecruitsCount = result.getNewRecruitsCount();
            
            // 添加调试日志
            log.info("保存拉新奖励明细: 分销员={}, 拉新人数={}, 奖励金额={}USD", 
                result.getCreatorId(), newRecruitsCount, result.getRecruitmentBonusUsd());
            
            CommissionPayoutBreakdowns recruitmentBreakdown = new CommissionPayoutBreakdowns();
            recruitmentBreakdown.setPayoutId(payout.getId());
            recruitmentBreakdown.setCreatorId(result.getCreatorId());
            recruitmentBreakdown.setDataMonth(dataMonth);
            recruitmentBreakdown.setSourceType("RECRUITMENT_BONUS");
            recruitmentBreakdown.setBaseAmountDiamonds((long) newRecruitsCount); // 存储拉新人数到base_amount_diamonds字段
            recruitmentBreakdown.setCalculatedAmountDiamonds(0L); // 拉新奖励不涉及钻石
            recruitmentBreakdown.setCalculatedAmountUsd(result.getRecruitmentBonusUsd());
            recruitmentBreakdown.setCreateTime(DateUtils.getNowDate());
            breakdowns.add(recruitmentBreakdown);
            
            // 再次确认保存的值
            log.info("准备保存的拉新奖励明细: baseAmountDiamonds={}, calculatedAmountUsd={}", 
                recruitmentBreakdown.getBaseAmountDiamonds(), recruitmentBreakdown.getCalculatedAmountUsd());
        }
        
        // 批量保存明细记录
        if (!breakdowns.isEmpty()) {
            try {
                int rows = commissionPayoutBreakdownsService.batchInsertCommissionPayoutBreakdowns(breakdowns);
                log.debug("成功保存 {} 条收入构成明细记录", rows);
                
                for (CommissionPayoutBreakdowns breakdown : breakdowns) {
                    log.debug("明细记录保存成功: 分销员={}, 类型={}, 钻石={}, USD={}", 
                        breakdown.getCreatorId(), breakdown.getSourceType(), 
                        breakdown.getCalculatedAmountDiamonds(), breakdown.getCalculatedAmountUsd());
                }
            } catch (Exception e) {
                log.error("保存收入构成明细失败: {}", e.getMessage(), e);
                throw new RuntimeException("保存收入构成明细失败: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 更新月度总览统计数据
     * 
     * @param summary 月度总览对象
     * @param config 计算配置
     * @param totalPayoutUsd 实际总支出
     * @param distributorCount 分销员数量
     */
    private void updateMonthlySummary(CommissionMonthlySummary summary, CalculationConfig config, 
                                    BigDecimal totalPayoutUsd, int distributorCount) {
        try {
            // 更新基础数据
            summary.setBudgetUsd(config.getBudgetUsd());
            summary.setPayoutCapUsd(config.getPayoutCapUsd());
            summary.setActualPayoutUsd(totalPayoutUsd);
            
            // 计算支出/收入比例
            BigDecimal payoutToBudgetRatio = BigDecimal.ZERO;
            if (config.getBudgetUsd().compareTo(BigDecimal.ZERO) > 0) {
                payoutToBudgetRatio = totalPayoutUsd
                    .divide(config.getBudgetUsd(), 4, RoundingMode.HALF_UP);
            }
            summary.setPayoutToBudgetRatio(payoutToBudgetRatio);
            
            // 计算超出预算金额
            BigDecimal exceededAmountUsd = totalPayoutUsd.subtract(config.getPayoutCapUsd());
            if (exceededAmountUsd.compareTo(BigDecimal.ZERO) < 0) {
                exceededAmountUsd = BigDecimal.ZERO;
            }
            summary.setExceededAmountUsd(exceededAmountUsd);
            
            // 计算当月主播奖金估计总额
            BigDecimal totalBonusEstimated = calculateTotalBonusEstimated(summary.getDataMonth());
            summary.setTotalBonusEstimated(totalBonusEstimated);
            
            // 设置计算完成状态
            summary.setCalculationStatus("completed");
            summary.setCalculatedAt(new Date());
            summary.setUpdateTime(DateUtils.getNowDate());
            
            // 保存更新
            commissionMonthlySummaryService.updateCommissionMonthlySummary(summary);
            
            log.info("月度总览更新完成 - 预算: {} USD, 支出上限: {} USD, 实际支出: {} USD, 主播奖金估计总额: {} USD, 支出比例: {}%, 分销员数量: {}", 
                config.getBudgetUsd(), 
                config.getPayoutCapUsd(), 
                totalPayoutUsd, 
                totalBonusEstimated,
                payoutToBudgetRatio.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP), 
                distributorCount);
                
        } catch (Exception e) {
            log.error("更新月度总览失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新月度总览失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 计算当月主播奖金估计总额
     * 
     * @param dataMonth 数据月份
     * @return 主播奖金估计总额
     */
    private BigDecimal calculateTotalBonusEstimated(Date dataMonth) {
        try {
            // 查询当月所有主播的 bonus_estimated 累计值
            BigDecimal totalBonusEstimated = commissionMonthlySummaryService.selectTotalBonusEstimatedByMonth(dataMonth);
            return totalBonusEstimated != null ? totalBonusEstimated : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("计算当月主播奖金估计总额失败: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 计算配置数据类
     */
    private static class CalculationConfig {
        private BigDecimal budgetUsd;
        private BigDecimal payoutCapRate;
        private BigDecimal payoutCapUsd;
        private BigDecimal diamondToUsdRate;
        private Long baseDiamondThreshold;
        private BigDecimal thresholdIncreaseRate;
        private List<CommissionLevelRules> levelRules;
        private List<CommissionMultilevelRules> multilevelRules;
        private List<CommissionRecruitmentBonusRules> recruitmentBonusRules;
        
        // Getters and Setters
        public BigDecimal getBudgetUsd() { return budgetUsd; }
        public void setBudgetUsd(BigDecimal budgetUsd) { this.budgetUsd = budgetUsd; }
        
        public BigDecimal getPayoutCapRate() { return payoutCapRate; }
        public void setPayoutCapRate(BigDecimal payoutCapRate) { this.payoutCapRate = payoutCapRate; }
        
        public BigDecimal getPayoutCapUsd() { return payoutCapUsd; }
        public void setPayoutCapUsd(BigDecimal payoutCapUsd) { this.payoutCapUsd = payoutCapUsd; }
        
        public BigDecimal getDiamondToUsdRate() { return diamondToUsdRate; }
        public void setDiamondToUsdRate(BigDecimal diamondToUsdRate) { this.diamondToUsdRate = diamondToUsdRate; }
        
        public Long getBaseDiamondThreshold() { return baseDiamondThreshold; }
        public void setBaseDiamondThreshold(Long baseDiamondThreshold) { this.baseDiamondThreshold = baseDiamondThreshold; }
        
        public BigDecimal getThresholdIncreaseRate() { return thresholdIncreaseRate; }
        public void setThresholdIncreaseRate(BigDecimal thresholdIncreaseRate) { this.thresholdIncreaseRate = thresholdIncreaseRate; }
        
        public List<CommissionLevelRules> getLevelRules() { return levelRules; }
        public void setLevelRules(List<CommissionLevelRules> levelRules) { this.levelRules = levelRules; }
        
        public List<CommissionMultilevelRules> getMultilevelRules() { return multilevelRules; }
        public void setMultilevelRules(List<CommissionMultilevelRules> multilevelRules) { this.multilevelRules = multilevelRules; }
        
        public List<CommissionRecruitmentBonusRules> getRecruitmentBonusRules() { return recruitmentBonusRules; }
        public void setRecruitmentBonusRules(List<CommissionRecruitmentBonusRules> recruitmentBonusRules) { this.recruitmentBonusRules = recruitmentBonusRules; }
    }
    
    /**
     * 分销员计算结果数据类
     */
    private static class DistributorCalculationResult {
        private Long creatorId;
        private long personalDiamonds;
        private long dynamicThreshold;
        private boolean thresholdMet;
        private int directDownlinesCount;
        private long teamDiamonds;
        private String achievedLevel;
        private BigDecimal levelCommissionDiamonds = BigDecimal.ZERO;
        private BigDecimal multilevelCommissionDiamonds = BigDecimal.ZERO;
        private BigDecimal recruitmentBonusUsd = BigDecimal.ZERO;
        private BigDecimal finalPayoutUsd = BigDecimal.ZERO;
        private int newRecruitsCount = 0; // 新增拉新人数字段
        
        // Getters and Setters
        public Long getCreatorId() { return creatorId; }
        public void setCreatorId(Long creatorId) { this.creatorId = creatorId; }
        
        public long getPersonalDiamonds() { return personalDiamonds; }
        public void setPersonalDiamonds(long personalDiamonds) { this.personalDiamonds = personalDiamonds; }
        
        public long getDynamicThreshold() { return dynamicThreshold; }
        public void setDynamicThreshold(long dynamicThreshold) { this.dynamicThreshold = dynamicThreshold; }
        
        public boolean isThresholdMet() { return thresholdMet; }
        public void setThresholdMet(boolean thresholdMet) { this.thresholdMet = thresholdMet; }
        
        public int getDirectDownlinesCount() { return directDownlinesCount; }
        public void setDirectDownlinesCount(int directDownlinesCount) { this.directDownlinesCount = directDownlinesCount; }
        
        public long getTeamDiamonds() { return teamDiamonds; }
        public void setTeamDiamonds(long teamDiamonds) { this.teamDiamonds = teamDiamonds; }
        
        public String getAchievedLevel() { return achievedLevel; }
        public void setAchievedLevel(String achievedLevel) { this.achievedLevel = achievedLevel; }
        
        public BigDecimal getLevelCommissionDiamonds() { return levelCommissionDiamonds; }
        public void setLevelCommissionDiamonds(BigDecimal levelCommissionDiamonds) { this.levelCommissionDiamonds = levelCommissionDiamonds; }
        
        public BigDecimal getMultilevelCommissionDiamonds() { return multilevelCommissionDiamonds; }
        public void setMultilevelCommissionDiamonds(BigDecimal multilevelCommissionDiamonds) { this.multilevelCommissionDiamonds = multilevelCommissionDiamonds; }
        
        public BigDecimal getRecruitmentBonusUsd() { return recruitmentBonusUsd; }
        public void setRecruitmentBonusUsd(BigDecimal recruitmentBonusUsd) { this.recruitmentBonusUsd = recruitmentBonusUsd; }
        
        public BigDecimal getFinalPayoutUsd() { return finalPayoutUsd; }
        public void setFinalPayoutUsd(BigDecimal finalPayoutUsd) { this.finalPayoutUsd = finalPayoutUsd; }
        
        public int getNewRecruitsCount() { return newRecruitsCount; }
        public void setNewRecruitsCount(int newRecruitsCount) { this.newRecruitsCount = newRecruitsCount; }
    }

    /**
     * 生成预览计算数据
     * 
     * @param dataMonth 数据月份
     * @return 预览数据
     */
    private Map<String, Object> generatePreviewData(Date dataMonth)
    {
        Map<String, Object> previewData = new HashMap<>();
        
        // 模拟预览数据
        previewData.put("totalDistributors", 100);
        previewData.put("totalCommissionAmount", new BigDecimal("50000.00"));
        previewData.put("totalLevelCommission", new BigDecimal("30000.00"));
        previewData.put("totalMultilevelCommission", new BigDecimal("15000.00"));
        previewData.put("totalRecruitmentBonus", new BigDecimal("5000.00"));
        
        return previewData;
    }

    /**
     * 保存分销员资格记录
     * 
     * @param result 计算结果
     * @param dataMonth 数据月份
     */
    private void saveDistributorQualification(DistributorCalculationResult result, Date dataMonth) {
        try {
            CommissionDistributorQualifications qualification = new CommissionDistributorQualifications();
            qualification.setCreatorId(result.getCreatorId());
            qualification.setDataMonth(dataMonth);
            qualification.setIsQualified(result.getFinalPayoutUsd().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0);
            qualification.setAchievedLevel(result.getAchievedLevel());
            qualification.setPersonalDiamonds(result.getPersonalDiamonds());
            qualification.setTeamDiamonds(result.getTeamDiamonds());
            qualification.setDirectDownlinesCount(result.getDirectDownlinesCount());
            qualification.setDynamicThreshold(result.getDynamicThreshold());
            qualification.setThresholdMet(result.isThresholdMet() ? 1 : 0);
            qualification.setCreateTime(DateUtils.getNowDate());
            
            commissionDistributorQualificationsService.insertOrUpdateCommissionDistributorQualifications(qualification);
            
            log.debug("分销员 {} 资格记录: 合格={}, 等级={}, 个人钻石={}, 团队钻石={}, 门槛达成={}", 
                result.getCreatorId(), qualification.getIsQualified(), qualification.getAchievedLevel(),
                qualification.getPersonalDiamonds(), qualification.getTeamDiamonds(), qualification.getThresholdMet());
                
        } catch (Exception e) {
            log.error("保存分销员 {} 资格记录失败: {}", result.getCreatorId(), e.getMessage(), e);
            // 不抛出异常，避免影响主要计算流程
        }
    }

    /**
     * 记录计算事件日志
     * 
     * @param dataMonth 数据月份
     * @param calculationType 计算类型
     * @param creatorId 分销员ID（可为空）
     * @param logLevel 日志级别
     * @param message 日志消息
     * @param details 详细信息（可为空）
     */
    private void logCalculationEvent(Date dataMonth, String calculationType, Long creatorId, 
                                   String logLevel, String message, String details) {
        try {
            CommissionCalculationLogs logEntry = new CommissionCalculationLogs();
            logEntry.setDataMonth(dataMonth);
            logEntry.setCalculationType(calculationType);
            logEntry.setCreatorId(creatorId);
            logEntry.setLogLevel(logLevel);
            logEntry.setMessage(message);
            logEntry.setDetails(details);
            logEntry.setCreatedAt(DateUtils.getNowDate());
            
            // 保存计算日志到数据库
            commissionCalculationLogsService.insertCommissionCalculationLogs(logEntry);
            
            log.debug("计算日志记录: {} - {} - {}", calculationType, logLevel, message);
        } catch (Exception e) {
            log.error("记录计算日志失败: {}", e.getMessage());
            // 不抛出异常，避免影响主要计算流程
        }
    }

    /**
     * 创建配置快照
     * 
     * @param dataMonth 数据月份
     * @param config 计算配置
     */
    private void createConfigSnapshots(Date dataMonth, CalculationConfig config) {
        try {
            // 创建月度配置快照
            CommissionConfigSnapshots monthlySnapshot = new CommissionConfigSnapshots();
            monthlySnapshot.setDataMonth(dataMonth);
            monthlySnapshot.setConfigType("MONTHLY_SETTINGS");
            monthlySnapshot.setConfigData(String.format(
                "{\"budgetUsd\":\"%s\",\"payoutCapRate\":\"%s\",\"diamondToUsdRate\":\"%s\"}",
                config.getBudgetUsd(), config.getPayoutCapRate(), config.getDiamondToUsdRate()));
            monthlySnapshot.setCreatedAt(DateUtils.getNowDate());
            
            // 创建全局配置快照
            CommissionConfigSnapshots globalSnapshot = new CommissionConfigSnapshots();
            globalSnapshot.setDataMonth(dataMonth);
            globalSnapshot.setConfigType("GLOBAL_SETTINGS");
            globalSnapshot.setConfigData(String.format(
                "{\"baseDiamondThreshold\":\"%s\",\"thresholdIncreaseRate\":\"%s\"}",
                config.getBaseDiamondThreshold(), config.getThresholdIncreaseRate()));
            globalSnapshot.setCreatedAt(DateUtils.getNowDate());
            
            // 保存配置快照到数据库
            commissionConfigSnapshotsService.insertCommissionConfigSnapshots(monthlySnapshot);
            commissionConfigSnapshotsService.insertCommissionConfigSnapshots(globalSnapshot);
            
            log.debug("配置快照创建完成: 月度配置和全局配置");
        } catch (Exception e) {
            log.error("创建配置快照失败: {}", e.getMessage());
            // 不抛出异常，避免影响主要计算流程
        }
    }

    /**
     * 保存动态门槛记录
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @param lastMonthDiamonds 上月钻石收入
     * @param calculatedThreshold 计算出的动态门槛
     * @param config 计算配置
     */
    private void saveDynamicThreshold(Long creatorId, Date dataMonth, long lastMonthDiamonds, 
                                    long calculatedThreshold, CalculationConfig config) {
        try {
            CommissionDynamicThresholds threshold = new CommissionDynamicThresholds();
            threshold.setCreatorId(creatorId);
            threshold.setDataMonth(dataMonth);
            threshold.setLastMonthDiamonds(lastMonthDiamonds);
            threshold.setCalculatedThreshold(calculatedThreshold);
            threshold.setBaseThreshold(config.getBaseDiamondThreshold());
            threshold.setThresholdIncreaseRate(config.getThresholdIncreaseRate());
            threshold.setCreateTime(DateUtils.getNowDate());
            
            // 保存动态门槛记录到数据库
            commissionDynamicThresholdsService.insertOrUpdateCommissionDynamicThresholds(threshold);
            
            log.debug("动态门槛记录保存: 分销员={}, 上月钻石={}, 计算门槛={}", 
                creatorId, lastMonthDiamonds, calculatedThreshold);
        } catch (Exception e) {
            log.error("保存动态门槛记录失败: 分销员={}, 错误={}", creatorId, e.getMessage());
            // 不抛出异常，避免影响主要计算流程
        }
    }

    /**
     * 保存招募统计
     * 
     * @param recruiterId 招募人ID
     * @param dataMonth 数据月份
     * @param newRecruitsCount 新招募人数
     * @param qualifiedBonusUsd 符合条件的奖励金额
     */
    private void saveRecruitmentStats(Long recruiterId, Date dataMonth, int newRecruitsCount, 
                                    BigDecimal qualifiedBonusUsd) {
        try {
            CommissionRecruitmentStats stats = new CommissionRecruitmentStats();
            stats.setRecruiterId(recruiterId);
            stats.setDataMonth(dataMonth);
            stats.setNewRecruitsCount(newRecruitsCount);
            stats.setQualifiedBonusUsd(qualifiedBonusUsd);
            stats.setCreateTime(DateUtils.getNowDate());
            
            // 保存招募统计到数据库
            commissionRecruitmentStatsService.insertOrUpdateCommissionRecruitmentStats(stats);
            
            log.debug("招募统计保存: 招募人={}, 新招募={}, 奖励={}USD", 
                recruiterId, newRecruitsCount, qualifiedBonusUsd);
        } catch (Exception e) {
            log.error("保存招募统计失败: 招募人={}, 错误={}", recruiterId, e.getMessage());
            // 不抛出异常，避免影响主要计算流程
        }
    }

    /**
     * 辅助方法：获取等级分成比例
     * 
     * @param achievedLevel 达成等级
     * @param levelRules 等级规则列表
     * @return 分成比例
     */
    private BigDecimal getLevelCommissionRate(String achievedLevel, List<CommissionLevelRules> levelRules) {
        for (CommissionLevelRules rule : levelRules) {
            if (achievedLevel.equals(rule.getLevelName())) {
                return rule.getCommissionRate();
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 辅助方法：记录多级提成详情
     *
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @param config 计算配置
     * @param totalMultilevelCommission 总多级提成
     * @param achievedLevel 达成的等级名称
     */
    private void logMultilevelCommissionDetails(Long creatorId, Date dataMonth, CalculationConfig config, BigDecimal totalMultilevelCommission, String achievedLevel) {
        // 获取用户等级对应的收益深度限制
        Integer payoutDepth = getPayoutDepthByLevel(achievedLevel, config.getLevelRules());

        StringBuilder details = new StringBuilder();
        details.append(String.format("{\"creatorId\":%d,\"step\":\"MULTILEVEL_COMMISSION\",\"achievedLevel\":\"%s\",\"payoutDepth\":%d,\"details\":[",
            creatorId, achievedLevel != null ? achievedLevel : "NONE", payoutDepth != null ? payoutDepth : 0));

        boolean first = true;
        for (CommissionMultilevelRules rule : config.getMultilevelRules()) {
            long depthIncome = 0;
            BigDecimal depthCommission = BigDecimal.ZERO;
            boolean withinDepthLimit = (payoutDepth != null && rule.getDepth() <= payoutDepth);

            if (withinDepthLimit) {
                depthIncome = getDownlineIncomeByDepth(creatorId, dataMonth, rule.getDepth());
                depthCommission = BigDecimal.valueOf(depthIncome).multiply(rule.getCommissionRate()).setScale(4, RoundingMode.HALF_UP);
            }

            if (!first) details.append(",");
            details.append(String.format("{\"depth\":%d,\"depthIncome\":%d,\"commissionRate\":\"%s\",\"depthCommission\":\"%s\",\"withinDepthLimit\":%s}",
                rule.getDepth(), depthIncome, rule.getCommissionRate(), depthCommission, withinDepthLimit));
            first = false;
        }

        details.append(String.format("],\"totalMultilevelCommission\":\"%s\"}", totalMultilevelCommission));
        
        logCalculationEvent(dataMonth, "DISTRIBUTOR_CALCULATION", creatorId, "INFO", 
            "多级提成计算详情", details.toString());
    }

    /**
     * 验证方法实现
     */

    @Override
    public Map<String, Object> validateSingleDistributorCalculation(Long creatorId, Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        try {
            log.info("开始验证分销员 {} 在 {} 的计算结果", creatorId, monthStr);
            
            // 加载计算配置
            CalculationConfig config = loadCalculationConfig(dataMonth);
            
            // 执行单个分销员计算（详细模式）
            DistributorCalculationResult calculationResult = calculateDistributorIncome(creatorId, dataMonth, config);
            
            // 获取数据库中的实际结果
            CommissionPayouts actualPayout = getActualPayout(creatorId, dataMonth);
            List<CommissionPayoutBreakdowns> actualBreakdowns = getActualBreakdowns(creatorId, dataMonth);
            
            // 对比计算结果与实际结果
            Map<String, Object> validation = compareResults(calculationResult, actualPayout, actualBreakdowns, config);
            
            // 获取计算日志
            List<CommissionCalculationLogs> logs = commissionCalculationLogsService.selectCommissionCalculationLogsByCreatorAndMonth(creatorId, dataMonth);
            
            result.put("success", true);
            result.put("creatorId", creatorId);
            result.put("dataMonth", monthStr);
            result.put("calculatedResult", buildCalculationDetails(calculationResult, config));
            result.put("actualResult", buildActualDetails(actualPayout, actualBreakdowns));
            result.put("validation", validation);
            result.put("logs", logs);
            
        } catch (Exception e) {
            log.error("验证分销员 {} 计算失败: {}", creatorId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "验证失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getCalculationValidationReport(Date dataMonth) {
        Map<String, Object> report = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        try {
            // 获取月度总览
            CommissionMonthlySummary summary = getMonthlyCalculationSummary(dataMonth);
            
            // 获取所有分销员收入记录
            List<CommissionPayouts> allPayouts = getAllPayouts(dataMonth);
            
            // 验证汇总数据
            Map<String, Object> summaryValidation = validateSummaryData(summary, allPayouts);
            
            // 获取计算统计
            Map<String, Object> statistics = getCalculationStatistics(dataMonth);
            
            // 获取计算日志统计
            Map<String, Object> logStatistics = getLogStatistics(dataMonth);
            
            report.put("success", true);
            report.put("dataMonth", monthStr);
            report.put("summary", summary);
            report.put("summaryValidation", summaryValidation);
            report.put("statistics", statistics);
            report.put("logStatistics", logStatistics);
            report.put("totalDistributors", allPayouts.size());
            
        } catch (Exception e) {
            log.error("生成计算验证报告失败: {}", e.getMessage(), e);
            report.put("success", false);
            report.put("message", "生成报告失败: " + e.getMessage());
        }
        
        return report;
    }

    @Override
    public List<CommissionCalculationLogs> getDistributorCalculationLogs(Long creatorId, Date dataMonth) {
        return commissionCalculationLogsService.selectCommissionCalculationLogsByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 辅助验证方法
     */

    private CommissionPayouts getActualPayout(Long creatorId, Date dataMonth) {
        try {
            CommissionPayouts queryCondition = new CommissionPayouts();
            queryCondition.setCreatorId(creatorId);
            queryCondition.setDataMonth(dataMonth);
            
            List<CommissionPayouts> payouts = commissionPayoutsService.selectCommissionPayoutsList(queryCondition);
            return payouts.isEmpty() ? null : payouts.get(0);
        } catch (Exception e) {
            log.error("获取分销员 {} 实际收入记录失败: {}", creatorId, e.getMessage());
            return null;
        }
    }

    private List<CommissionPayoutBreakdowns> getActualBreakdowns(Long creatorId, Date dataMonth) {
        try {
            CommissionPayoutBreakdowns queryCondition = new CommissionPayoutBreakdowns();
            queryCondition.setCreatorId(creatorId);
            queryCondition.setDataMonth(dataMonth);
            
            return commissionPayoutBreakdownsService.selectCommissionPayoutBreakdownsList(queryCondition);
        } catch (Exception e) {
            log.error("获取分销员 {} 收入构成明细失败: {}", creatorId, e.getMessage());
            return new ArrayList<>();
        }
    }

    private Map<String, Object> compareResults(DistributorCalculationResult calculated, 
                                             CommissionPayouts actual, 
                                             List<CommissionPayoutBreakdowns> breakdowns,
                                             CalculationConfig config) {
        Map<String, Object> validation = new HashMap<>();
        List<String> differences = new ArrayList<>();
        boolean isValid = true;
        
        if (actual == null) {
            if (calculated.getFinalPayoutUsd().compareTo(BigDecimal.ZERO) > 0) {
                differences.add("计算结果显示应有收入，但数据库中无记录");
                isValid = false;
            }
        } else {
            // 比较最终收入
            if (calculated.getFinalPayoutUsd().compareTo(actual.getFinalPayoutUsd()) != 0) {
                differences.add(String.format("最终收入不匹配: 计算=%s, 实际=%s", 
                    calculated.getFinalPayoutUsd(), actual.getFinalPayoutUsd()));
                isValid = false;
            }
            
            // 比较等级
            if (!Objects.equals(calculated.getAchievedLevel(), actual.getDistributorLevel())) {
                differences.add(String.format("分销员等级不匹配: 计算=%s, 实际=%s", 
                    calculated.getAchievedLevel(), actual.getDistributorLevel()));
                isValid = false;
            }
            
            // 比较收入构成明细
            Map<String, Object> breakdownValidation = validateBreakdowns(calculated, breakdowns, config);
            if (!(Boolean) breakdownValidation.get("isValid")) {
                differences.addAll((List<String>) breakdownValidation.get("differences"));
                isValid = false;
            }
        }
        
        validation.put("isValid", isValid);
        validation.put("differences", differences);
        validation.put("comparedAt", new Date());
        
        return validation;
    }

    private Map<String, Object> buildCalculationDetails(DistributorCalculationResult result, CalculationConfig config) {
        Map<String, Object> details = new HashMap<>();
        details.put("personalDiamonds", result.getPersonalDiamonds());
        details.put("dynamicThreshold", result.getDynamicThreshold());
        details.put("thresholdMet", result.isThresholdMet());
        details.put("teamDiamonds", result.getTeamDiamonds());
        details.put("directDownlinesCount", result.getDirectDownlinesCount());
        details.put("achievedLevel", result.getAchievedLevel());
        details.put("levelCommissionDiamonds", result.getLevelCommissionDiamonds());
        details.put("multilevelCommissionDiamonds", result.getMultilevelCommissionDiamonds());
        details.put("recruitmentBonusUsd", result.getRecruitmentBonusUsd());
        details.put("finalPayoutUsd", result.getFinalPayoutUsd());
        details.put("diamondToUsdRate", config.getDiamondToUsdRate());
        return details;
    }

    private Map<String, Object> buildActualDetails(CommissionPayouts payout, List<CommissionPayoutBreakdowns> breakdowns) {
        Map<String, Object> details = new HashMap<>();
        if (payout != null) {
            details.put("finalPayoutUsd", payout.getFinalPayoutUsd());
            details.put("distributorLevel", payout.getDistributorLevel());
            details.put("breakdowns", breakdowns);
        }
        return details;
    }

    private Map<String, Object> validateBreakdowns(DistributorCalculationResult calculated, 
                                                  List<CommissionPayoutBreakdowns> actualBreakdowns, 
                                                  CalculationConfig config) {
        Map<String, Object> validation = new HashMap<>();
        List<String> differences = new ArrayList<>();
        boolean isValid = true;
        
        // 这里可以添加更详细的明细对比逻辑
        // 比如检查等级分成、多级提成、拉新奖励的明细是否匹配
        
        validation.put("isValid", isValid);
        validation.put("differences", differences);
        return validation;
    }

    private List<CommissionPayouts> getAllPayouts(Date dataMonth) {
        try {
            CommissionPayouts queryCondition = new CommissionPayouts();
            queryCondition.setDataMonth(dataMonth);
            return commissionPayoutsService.selectCommissionPayoutsList(queryCondition);
        } catch (Exception e) {
            log.error("获取月度收入记录失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private Map<String, Object> validateSummaryData(CommissionMonthlySummary summary, List<CommissionPayouts> allPayouts) {
        Map<String, Object> validation = new HashMap<>();
        List<String> differences = new ArrayList<>();
        boolean isValid = true;
        
        if (summary != null && !allPayouts.isEmpty()) {
            // 计算实际总支出
            BigDecimal calculatedTotal = allPayouts.stream()
                .map(CommissionPayouts::getFinalPayoutUsd)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 比较总支出
            if (calculatedTotal.compareTo(summary.getActualPayoutUsd()) != 0) {
                differences.add(String.format("总支出不匹配: 计算=%s, 汇总=%s", 
                    calculatedTotal, summary.getActualPayoutUsd()));
                isValid = false;
            }
        }
        
        validation.put("isValid", isValid);
        validation.put("differences", differences);
        return validation;
    }

    private Map<String, Object> getCalculationStatistics(Date dataMonth) {
        Map<String, Object> stats = new HashMap<>();
        // 这里可以添加更多统计信息
        stats.put("dataMonth", dataMonth);
        return stats;
    }

    private Map<String, Object> getLogStatistics(Date dataMonth) {
        Map<String, Object> stats = new HashMap<>();
        try {
            List<CommissionCalculationLogs> logs = commissionCalculationLogsService.selectCommissionCalculationLogsByMonth(dataMonth);
            stats.put("totalLogs", logs.size());
            stats.put("errorLogs", logs.stream().filter(log -> "ERROR".equals(log.getLogLevel())).count());
            stats.put("warnLogs", logs.stream().filter(log -> "WARN".equals(log.getLogLevel())).count());
        } catch (Exception e) {
            log.error("获取日志统计失败: {}", e.getMessage());
        }
        return stats;
    }

    /**
     * ==============================================
     * 增强的算法上下文记录系统
     * ==============================================
     */

    /**
     * 记录完整的计算上下文和算法条件
     * 
     * @param dataMonth 数据月份
     * @param creatorId 分销员ID
     * @param stepName 计算步骤名称
     * @param inputData 输入数据
     * @param algorithm 算法描述
     * @param conditions 计算条件
     * @param result 计算结果
     */
    private void logAlgorithmExecution(Date dataMonth, Long creatorId, String stepName, 
                                     Object inputData, String algorithm, Object conditions, Object result) {
        try {
            Map<String, Object> context = new HashMap<>();
            context.put("step", stepName);
            context.put("algorithm", algorithm);
            context.put("inputData", inputData);
            context.put("conditions", conditions);
            context.put("result", result);
            context.put("timestamp", System.currentTimeMillis());
            
            // 使用ObjectMapper或手动构建JSON字符串
            String detailsJson = buildJsonString(context);
            
            logCalculationEvent(dataMonth, "ALGORITHM_EXECUTION", creatorId, "INFO", 
                String.format("算法执行: %s", stepName), detailsJson);
                
        } catch (Exception e) {
            log.error("记录算法执行日志失败: {}", e.getMessage());
        }
    }

    /**
     * 记录动态门槛计算的完整算法过程
     */
    private void logThresholdCalculationAlgorithm(Date dataMonth, Long creatorId, 
                                                 long lastMonthDiamonds, CalculationConfig config, 
                                                 long calculatedThreshold, long finalThreshold) {
        Map<String, Object> algorithm = new HashMap<>();
        algorithm.put("formula", "max(上月收入 * (1 + 上浮比例), 基础门槛)");
        algorithm.put("step1", String.format("上月收入 * (1 + 上浮比例) = %d * (1 + %s) = %d", 
            lastMonthDiamonds, config.getThresholdIncreaseRate(), calculatedThreshold));
        algorithm.put("step2", String.format("max(%d, %d) = %d", 
            calculatedThreshold, config.getBaseDiamondThreshold(), finalThreshold));
        
        Map<String, Object> inputData = new HashMap<>();
        inputData.put("lastMonthDiamonds", lastMonthDiamonds);
        inputData.put("thresholdIncreaseRate", config.getThresholdIncreaseRate());
        inputData.put("baseDiamondThreshold", config.getBaseDiamondThreshold());
        
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("rule", "动态门槛不能低于基础门槛");
        conditions.put("baseThreshold", config.getBaseDiamondThreshold());
        
        Map<String, Object> result = new HashMap<>();
        result.put("calculatedByFormula", calculatedThreshold);
        result.put("finalThreshold", finalThreshold);
        result.put("appliedRule", finalThreshold == config.getBaseDiamondThreshold() ? "使用基础门槛" : "使用计算门槛");
        
        logAlgorithmExecution(dataMonth, creatorId, "动态门槛计算", inputData, 
            algorithm.toString(), conditions, result);
    }

    /**
     * 记录等级分成计算的完整算法过程
     */
    private void logLevelCommissionAlgorithm(Date dataMonth, Long creatorId, 
                                           long personalDiamonds, long teamDiamonds, 
                                           String achievedLevel, CalculationConfig config, 
                                           BigDecimal commissionRate, BigDecimal levelCommission) {
        Map<String, Object> algorithm = new HashMap<>();
        algorithm.put("formula", "等级分成 = (个人钻石 + 团队钻石) * 等级分成比例");
        algorithm.put("calculation", String.format("(%d + %d) * %s = %s", 
            personalDiamonds, teamDiamonds, commissionRate, levelCommission));
        
        Map<String, Object> inputData = new HashMap<>();
        inputData.put("personalDiamonds", personalDiamonds);
        inputData.put("teamDiamonds", teamDiamonds);
        inputData.put("totalDiamonds", personalDiamonds + teamDiamonds);
        inputData.put("achievedLevel", achievedLevel);
        
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("levelRules", getLevelRulesForLogging(config.getLevelRules()));
        conditions.put("appliedRule", String.format("等级%s的分成比例为%s", achievedLevel, commissionRate));
        
        Map<String, Object> result = new HashMap<>();
        result.put("commissionRate", commissionRate);
        result.put("levelCommissionDiamonds", levelCommission);
        
        logAlgorithmExecution(dataMonth, creatorId, "等级分成计算", inputData, 
            algorithm.toString(), conditions, result);
    }

    /**
     * 记录多级提成计算的完整算法过程
     */
    private void logMultilevelCommissionAlgorithm(Date dataMonth, Long creatorId, 
                                                 CalculationConfig config, 
                                                 Map<Integer, Long> depthIncomes,
                                                 Map<Integer, BigDecimal> depthCommissions,
                                                 BigDecimal totalCommission) {
        Map<String, Object> algorithm = new HashMap<>();
        algorithm.put("formula", "多级提成 = ∑(各层级下级收入 * 对应层级提成比例)");
        
        StringBuilder calculation = new StringBuilder();
        for (CommissionMultilevelRules rule : config.getMultilevelRules()) {
            int depth = rule.getDepth();
            long depthIncome = depthIncomes.getOrDefault(depth, 0L);
            BigDecimal depthCommission = depthCommissions.getOrDefault(depth, BigDecimal.ZERO);
            
            if (calculation.length() > 0) calculation.append(" + ");
            calculation.append(String.format("L%d(%d * %s = %s)", 
                depth, depthIncome, rule.getCommissionRate(), depthCommission));
        }
        calculation.append(String.format(" = %s", totalCommission));
        algorithm.put("calculation", calculation.toString());
        
        Map<String, Object> inputData = new HashMap<>();
        inputData.put("depthIncomes", depthIncomes);
        
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("multilevelRules", getMultilevelRulesForLogging(config.getMultilevelRules()));
        
        Map<String, Object> result = new HashMap<>();
        result.put("depthCommissions", depthCommissions);
        result.put("totalMultilevelCommission", totalCommission);
        
        logAlgorithmExecution(dataMonth, creatorId, "多级提成计算", inputData, 
            algorithm.toString(), conditions, result);
    }

    /**
     * 记录拉新奖励计算的完整算法过程
     */
    private void logRecruitmentBonusAlgorithm(Date dataMonth, Long creatorId,
                                            int newRecruitsCount, CalculationConfig config,
                                            BigDecimal totalBonusUsd, BigDecimal bonusUsdPerPerson) {
        Map<String, Object> algorithm = new HashMap<>();
        algorithm.put("formula", "拉新奖励金额 = bonus_usd * 拉新人数");
        algorithm.put("calculation_method", "基于 monthly_performance 表中的 is_rookie=1 统计新人数量，按最高档位单价乘以拉新人数");

        Map<String, Object> inputData = new HashMap<>();
        inputData.put("rookieCount", newRecruitsCount);
        inputData.put("calculationPeriod", new SimpleDateFormat("yyyy-MM").format(dataMonth));

        Map<String, Object> conditions = new HashMap<>();
        conditions.put("recruitmentRules", getRecruitmentRulesForLogging(config.getRecruitmentBonusRules()));

        // 找到匹配的规则（使用排序后的规则确保一致性）
        List<CommissionRecruitmentBonusRules> sortedRulesForLog = new ArrayList<>(config.getRecruitmentBonusRules());
        sortedRulesForLog.sort((r1, r2) -> {
            int compare = Integer.compare(r2.getMinNewRecruits(), r1.getMinNewRecruits());
            if (compare == 0) {
                return Integer.compare(r1.getId(), r2.getId());
            }
            return compare;
        });

        String appliedRule = "无匹配规则";
        for (CommissionRecruitmentBonusRules rule : sortedRulesForLog) {
            if (newRecruitsCount >= rule.getMinNewRecruits() && bonusUsdPerPerson.compareTo(rule.getBonusUsd()) == 0) {
                appliedRule = String.format("当月新人下级%d人 >= %d人，单价%sUSD × %d人 = %sUSD",
                    newRecruitsCount, rule.getMinNewRecruits(), rule.getBonusUsd(), newRecruitsCount, totalBonusUsd);
                break;
            }
        }
        conditions.put("appliedRule", appliedRule);

        Map<String, Object> result = new HashMap<>();
        result.put("bonusUsdPerPerson", bonusUsdPerPerson);
        result.put("totalBonusUsd", totalBonusUsd);
        result.put("qualified", totalBonusUsd.compareTo(BigDecimal.ZERO) > 0);

        logAlgorithmExecution(dataMonth, creatorId, "拉新奖励计算", inputData,
            algorithm.toString(), conditions, result);
    }

    /**
     * 记录拉新奖励计算的完整算法过程（兼容旧版本调用）
     */
    private void logRecruitmentBonusAlgorithm(Date dataMonth, Long creatorId,
                                            int newRecruitsCount, CalculationConfig config,
                                            BigDecimal bonusUsd) {
        // 兼容旧版本调用，假设bonusUsd是总金额，单价为0（表示使用旧逻辑）
        logRecruitmentBonusAlgorithm(dataMonth, creatorId, newRecruitsCount, config, bonusUsd, BigDecimal.ZERO);
    }

    /**
     * 辅助方法：构建JSON字符串
     */
    private String buildJsonString(Map<String, Object> data) {
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (!first) json.append(",");
            json.append("\"").append(entry.getKey()).append("\":");
            
            Object value = entry.getValue();
            if (value instanceof String) {
                json.append("\"").append(value.toString().replace("\"", "\\\"")).append("\"");
            } else if (value instanceof Number || value instanceof Boolean) {
                json.append(value.toString());
            } else {
                json.append("\"").append(value != null ? value.toString().replace("\"", "\\\"") : "null").append("\"");
            }
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    /**
     * 辅助方法：格式化等级规则用于日志记录
     */
    private Object getLevelRulesForLogging(List<CommissionLevelRules> levelRules) {
        Map<String, Object> rules = new HashMap<>();
        for (CommissionLevelRules rule : levelRules) {
            Map<String, Object> ruleInfo = new HashMap<>();
            ruleInfo.put("minQualifiedRecruits", rule.getMinQualifiedRecruits());
            ruleInfo.put("commissionRate", rule.getCommissionRate());
            ruleInfo.put("payoutDepth", rule.getPayoutDepth());
            rules.put(rule.getLevelName(), ruleInfo);
        }
        return rules;
    }

    /**
     * 辅助方法：格式化多级规则用于日志记录
     */
    private Object getMultilevelRulesForLogging(List<CommissionMultilevelRules> multilevelRules) {
        Map<String, Object> rules = new HashMap<>();
        for (CommissionMultilevelRules rule : multilevelRules) {
            rules.put("L" + rule.getDepth(), rule.getCommissionRate());
        }
        return rules;
    }

    /**
     * 辅助方法：格式化拉新规则用于日志记录
     */
    private Object getRecruitmentRulesForLogging(List<CommissionRecruitmentBonusRules> recruitmentRules) {
        List<Map<String, Object>> rules = new ArrayList<>();
        for (CommissionRecruitmentBonusRules rule : recruitmentRules) {
            Map<String, Object> ruleInfo = new HashMap<>();
            ruleInfo.put("minNewRecruits", rule.getMinNewRecruits());
            ruleInfo.put("bonusUsd", rule.getBonusUsd());
            rules.add(ruleInfo);
        }
        return rules;
    }

    /**
     * ==============================================
     * 重新计算时的历史数据处理
     * ==============================================
     */

    /**
     * 处理重新计算时的历史数据
     * 
     * @param dataMonth 数据月份
     */
    private void handleHistoricalDataOnRecalculation(Date dataMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);
        
        try {
            log.info("开始处理 {} 月度重新计算的历史数据", monthStr);
            
            // 1. 标记历史计算日志为已废弃
            markHistoricalCalculationLogs(dataMonth);
            
            // 2. 备份当前计算结果数据
            backupCurrentCalculationData(dataMonth);
            
            // 3. 清理当前计算数据（为重新计算准备）
            // 可以选择使用批量清理方法（更高效）或逐条清理方法（更安全）
            boolean useBatchCleanup = true; // 配置项：是否使用批量清理
            if (useBatchCleanup) {
                cleanCurrentCalculationDataBatch(dataMonth);
            } else {
                cleanCurrentCalculationData(dataMonth);
            }
            
            log.info("完成 {} 月度重新计算的历史数据处理", monthStr);
            
        } catch (Exception e) {
            log.error("处理重新计算历史数据失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响重新计算流程
        }
    }

    /**
     * 标记历史计算日志为已废弃
     * 
     * @param dataMonth 数据月份
     */
    private void markHistoricalCalculationLogs(Date dataMonth) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String monthStr = sdf.format(dataMonth);
            
            // 1. 查询该月份的所有计算日志
            List<CommissionCalculationLogs> existingLogs = commissionCalculationLogsService.selectCommissionCalculationLogsByMonth(dataMonth);
            
            if (!existingLogs.isEmpty()) {
                // 2. 记录重新计算开始事件（带有批次ID）
                String recalculationBatchId = "RECALC_" + System.currentTimeMillis();
                logCalculationEvent(dataMonth, "RECALCULATION_START", null, "INFO", 
                    String.format("开始第%d次重新计算，标记%d条历史日志为废弃状态", 
                        countRecalculationHistory(dataMonth) + 1, existingLogs.size()), 
                    String.format("{\"action\":\"MARK_HISTORICAL_LOGS\",\"batchId\":\"%s\",\"historicalLogsCount\":%d,\"timestamp\":%d}", 
                        recalculationBatchId, existingLogs.size(), System.currentTimeMillis()));
                
                // 3. 为每个历史日志创建"标记为历史"的记录
                for (CommissionCalculationLogs historicalLog : existingLogs) {
                    // 为该日志创建一个"历史标记"记录
                    logCalculationEvent(dataMonth, "HISTORICAL_LOG_MARK", historicalLog.getCreatorId(), "INFO", 
                        String.format("标记历史日志: %s", historicalLog.getCalculationType()), 
                        String.format("{\"action\":\"MARK_AS_HISTORICAL\",\"originalLogId\":%d,\"originalType\":\"%s\",\"originalMessage\":\"%s\",\"batchId\":\"%s\",\"timestamp\":%d}", 
                            historicalLog.getId(), 
                            historicalLog.getCalculationType(), 
                            historicalLog.getMessage().replace("\"", "\\\""),
                            recalculationBatchId,
                            System.currentTimeMillis()));
                }
                
                log.info("已标记 {} 月度的 {} 条历史计算日志，批次ID: {}", monthStr, existingLogs.size(), recalculationBatchId);
            } else {
                // 4. 如果没有历史日志，也记录这个情况
                logCalculationEvent(dataMonth, "RECALCULATION_START", null, "INFO", 
                    "开始首次计算，无历史日志需要标记", 
                    String.format("{\"action\":\"FIRST_CALCULATION\",\"timestamp\":%d}", 
                        System.currentTimeMillis()));
                
                log.info("首次计算 {} 月度佣金，无历史日志需要标记", monthStr);
            }
            
        } catch (Exception e) {
            log.error("标记历史计算日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 统计该月份的重新计算历史次数
     * 
     * @param dataMonth 数据月份
     * @return 重新计算次数
     */
    private int countRecalculationHistory(Date dataMonth) {
        try {
            List<CommissionCalculationLogs> recalcLogs = commissionCalculationLogsService.selectCommissionCalculationLogsByMonth(dataMonth);
            return (int) recalcLogs.stream()
                .filter(log -> "RECALCULATION_START".equals(log.getCalculationType()))
                .count();
        } catch (Exception e) {
            log.error("统计重新计算历史失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 备份当前计算结果数据
     * 
     * @param dataMonth 数据月份
     */
    private void backupCurrentCalculationData(Date dataMonth) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String monthStr = sdf.format(dataMonth);
            
            // 统计需要备份的数据
            CommissionPayouts queryCondition = new CommissionPayouts();
            queryCondition.setDataMonth(dataMonth);
            List<CommissionPayouts> existingPayouts = commissionPayoutsService.selectCommissionPayoutsList(queryCondition);
            
            if (!existingPayouts.isEmpty()) {
                // 记录备份信息
                logCalculationEvent(dataMonth, "DATA_BACKUP", null, "INFO", 
                    "备份现有计算数据", 
                    String.format("{\"action\":\"BACKUP_DATA\",\"payoutCount\":%d,\"timestamp\":%d}", 
                        existingPayouts.size(), System.currentTimeMillis()));
                
                log.info("已备份 {} 月度的 {} 条收入记录", monthStr, existingPayouts.size());
            }
            
        } catch (Exception e) {
            log.error("备份当前计算数据失败: {}", e.getMessage());
        }
    }

    /**
     * 清理当前计算数据
     *
     * @param dataMonth 数据月份
     */
    private void cleanCurrentCalculationData(Date dataMonth) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String monthStr = sdf.format(dataMonth);
            
            int deletedCount = 0;
            
            // 1. 清理分销员收入记录
            CommissionPayouts payoutQuery = new CommissionPayouts();
            payoutQuery.setDataMonth(dataMonth);
            List<CommissionPayouts> existingPayouts = commissionPayoutsService.selectCommissionPayoutsList(payoutQuery);
            
            if (!existingPayouts.isEmpty()) {
                // 方式1: 利用外键级联删除 (推荐，更高效)
                // 由于表有 ON DELETE CASCADE 外键约束，删除 commission_payouts 时会自动删除相关的 commission_payout_breakdowns
                for (CommissionPayouts payout : existingPayouts) {
                    commissionPayoutsService.deleteCommissionPayoutsById(payout.getId());
                    deletedCount++;
                }

                // 方式2: 也可以直接按月份批量删除 (备选方案)
                // commissionPayoutBreakdownsService.deleteCommissionPayoutBreakdownsByMonth(dataMonth);
                // commissionPayoutsService.deleteCommissionPayoutsByMonth(dataMonth);
            }
            
            // 2. 清理分销员资格记录（如果有对应的service和方法）
            // TODO: 需要根据实际的Service接口实现
            
            // 3. 清理动态门槛记录
            try {
                // 假设有删除方法，需要根据实际API调整
                // commissionDynamicThresholdsService.deleteByDataMonth(dataMonth);
            } catch (Exception e) {
                log.debug("清理动态门槛记录时出现异常: {}", e.getMessage());
            }
            
            // 4. 清理招募统计记录
            try {
                // 假设有删除方法，需要根据实际API调整
                // commissionRecruitmentStatsService.deleteByDataMonth(dataMonth);
            } catch (Exception e) {
                log.debug("清理招募统计记录时出现异常: {}", e.getMessage());
            }
            
            // 记录清理完成事件
            logCalculationEvent(dataMonth, "DATA_CLEANUP", null, "INFO", 
                "清理现有计算数据完成", 
                String.format("{\"action\":\"CLEANUP_DATA\",\"deletedPayouts\":%d,\"timestamp\":%d}", 
                    deletedCount, System.currentTimeMillis()));
            
            log.info("已清理 {} 月度的 {} 条计算数据，准备重新计算", monthStr, deletedCount);

            // 验证清理结果
            Map<String, Object> verifyResult = verifyDataCleanup(dataMonth);
            if (!(Boolean) verifyResult.get("isCleanupComplete")) {
                log.warn("数据清理验证失败，可能存在残留数据: {}", verifyResult);
            }

        } catch (Exception e) {
            log.error("清理当前计算数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("清理计算数据失败，无法继续重新计算: " + e.getMessage());
        }
    }

    /**
     * 批量清理当前计算数据 (高效版本)
     *
     * @param dataMonth 数据月份
     */
    private void cleanCurrentCalculationDataBatch(Date dataMonth) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String monthStr = sdf.format(dataMonth);

            log.info("开始批量清理 {} 月度的计算数据", monthStr);

            // 1. 直接按月份批量删除收入构成明细
            int deletedBreakdowns = commissionPayoutBreakdownsService.deleteCommissionPayoutBreakdownsByMonth(dataMonth);

            // 2. 直接按月份批量删除收入记录
            int deletedPayouts = commissionPayoutsService.deleteCommissionPayoutsByMonth(dataMonth);

            // 3. 清理动态门槛记录
            try {
                int deletedThresholds = commissionDynamicThresholdsService.deleteCommissionDynamicThresholdsByMonth(dataMonth);
                log.info("清理动态门槛记录: {} 条", deletedThresholds);
            } catch (Exception e) {
                log.debug("清理动态门槛记录时出现异常: {}", e.getMessage());
            }

            // 记录清理完成事件
            logCalculationEvent(dataMonth, "DATA_CLEANUP_BATCH", null, "INFO",
                "批量清理现有计算数据完成",
                String.format("{\"action\":\"CLEANUP_DATA_BATCH\",\"deletedPayouts\":%d,\"deletedBreakdowns\":%d,\"timestamp\":%d}",
                    deletedPayouts, deletedBreakdowns, System.currentTimeMillis()));

            log.info("批量清理 {} 月度数据完成: 收入记录 {} 条, 明细记录 {} 条", monthStr, deletedPayouts, deletedBreakdowns);

        } catch (Exception e) {
            log.error("批量清理当前计算数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量清理计算数据失败，无法继续重新计算: " + e.getMessage());
        }
    }

    /**
     * 验证数据清理结果
     *
     * @param dataMonth 数据月份
     * @return 验证结果
     */
    private Map<String, Object> verifyDataCleanup(Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);

        try {
            // 检查收入记录是否清理完成
            CommissionPayouts payoutQuery = new CommissionPayouts();
            payoutQuery.setDataMonth(dataMonth);
            List<CommissionPayouts> remainingPayouts = commissionPayoutsService.selectCommissionPayoutsList(payoutQuery);

            // 检查收入构成明细是否清理完成
            CommissionPayoutBreakdowns breakdownQuery = new CommissionPayoutBreakdowns();
            breakdownQuery.setDataMonth(dataMonth);
            List<CommissionPayoutBreakdowns> remainingBreakdowns = commissionPayoutBreakdownsService.selectCommissionPayoutBreakdownsList(breakdownQuery);

            result.put("dataMonth", monthStr);
            result.put("remainingPayouts", remainingPayouts.size());
            result.put("remainingBreakdowns", remainingBreakdowns.size());
            result.put("isCleanupComplete", remainingPayouts.isEmpty() && remainingBreakdowns.isEmpty());

            if (!remainingPayouts.isEmpty() || !remainingBreakdowns.isEmpty()) {
                log.warn("数据清理不完整 - 月份: {}, 剩余收入记录: {}, 剩余明细记录: {}",
                    monthStr, remainingPayouts.size(), remainingBreakdowns.size());
            } else {
                log.info("数据清理验证通过 - 月份: {}", monthStr);
            }

        } catch (Exception e) {
            log.error("验证数据清理结果失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("isCleanupComplete", false);
        }

        return result;
    }

    /**
     * 获取重新计算的统计信息
     * 
     * @param dataMonth 数据月份
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getRecalculationStatistics(Date dataMonth) {
        Map<String, Object> stats = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        
        try {
            // 统计历史日志数量
            List<CommissionCalculationLogs> allLogs = commissionCalculationLogsService.selectCommissionCalculationLogsByMonth(dataMonth);
            long recalculationLogs = allLogs.stream()
                .filter(log -> "RECALCULATION_START".equals(log.getCalculationType()) 
                    || "DATA_BACKUP".equals(log.getCalculationType())
                    || "DATA_CLEANUP".equals(log.getCalculationType()))
                .count();
            
            long historicalMarkLogs = allLogs.stream()
                .filter(log -> "HISTORICAL_LOG_MARK".equals(log.getCalculationType()))
                .count();
            
            int recalculationCount = countRecalculationHistory(dataMonth);
            
            stats.put("dataMonth", sdf.format(dataMonth));
            stats.put("totalLogs", allLogs.size());
            stats.put("recalculationEvents", recalculationLogs);
            stats.put("historicalMarkLogs", historicalMarkLogs);
            stats.put("hasHistoricalData", recalculationLogs > 0);
            
        } catch (Exception e) {
            log.error("获取重新计算统计信息失败: {}", e.getMessage());
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    /**
     * 查询历史计算日志（被标记为历史的日志）
     * 
     * @param dataMonth 数据月份
     * @param batchId 批次ID（可选）
     * @return 历史日志列表
     */
    @Override
    public Map<String, Object> getHistoricalCalculationLogs(Date dataMonth, String batchId) {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        
        try {
            List<CommissionCalculationLogs> allLogs = commissionCalculationLogsService.selectCommissionCalculationLogsByMonth(dataMonth);
            
            // 过滤出标记为历史的日志
            List<CommissionCalculationLogs> historicalMarkLogs = allLogs.stream()
                .filter(log -> "HISTORICAL_LOG_MARK".equals(log.getCalculationType()))
                .filter(log -> batchId == null || (log.getDetails() != null && log.getDetails().contains(batchId)))
                .collect(java.util.stream.Collectors.toList());
            
            // 按批次分组
            Map<String, List<CommissionCalculationLogs>> logsByBatch = new HashMap<>();
            for (CommissionCalculationLogs log : historicalMarkLogs) {
                String logBatchId = extractBatchIdFromDetails(log.getDetails());
                logsByBatch.computeIfAbsent(logBatchId, k -> new ArrayList<>()).add(log);
            }
            
            result.put("success", true);
            result.put("dataMonth", sdf.format(dataMonth));
            result.put("historicalLogs", historicalMarkLogs);
            result.put("logsByBatch", logsByBatch);
            result.put("batchCount", logsByBatch.size());
            
        } catch (Exception e) {
            log.error("查询历史计算日志失败: {}", e.getMessage());
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 查询重新计算历史记录
     * 
     * @param dataMonth 数据月份
     * @return 重新计算历史
     */
    @Override
    public Map<String, Object> getRecalculationHistory(Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        
        try {
            List<CommissionCalculationLogs> allLogs = commissionCalculationLogsService.selectCommissionCalculationLogsByMonth(dataMonth);
            
            // 找出所有重新计算开始记录
            List<CommissionCalculationLogs> recalculationStarts = allLogs.stream()
                .filter(log -> "RECALCULATION_START".equals(log.getCalculationType()))
                .sorted((a, b) -> a.getCreatedAt().compareTo(b.getCreatedAt()))
                .collect(java.util.stream.Collectors.toList());
            
            List<Map<String, Object>> history = new ArrayList<>();
            for (int i = 0; i < recalculationStarts.size(); i++) {
                CommissionCalculationLogs startLog = recalculationStarts.get(i);
                Map<String, Object> recalcInfo = new HashMap<>();
                recalcInfo.put("recalculationNumber", i + 1);
                recalcInfo.put("startTime", startLog.getCreatedAt());
                recalcInfo.put("message", startLog.getMessage());
                recalcInfo.put("batchId", extractBatchIdFromDetails(startLog.getDetails()));
                recalcInfo.put("details", startLog.getDetails());
                history.add(recalcInfo);
            }
            
            result.put("success", true);
            result.put("dataMonth", sdf.format(dataMonth));
            result.put("recalculationHistory", history);
            result.put("totalRecalculations", recalculationStarts.size());
            
        } catch (Exception e) {
            log.error("查询重新计算历史失败: {}", e.getMessage());
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取最新的有效计算日志（排除历史日志）
     * 
     * @param dataMonth 数据月份
     * @return 最新计算日志列表
     */
    @Override
    public List<CommissionCalculationLogs> getLatestValidCalculationLogs(Date dataMonth) {
        try {
            List<CommissionCalculationLogs> allLogs = commissionCalculationLogsService.selectCommissionCalculationLogsByMonth(dataMonth);
            
            // 找到最后一次重新计算的开始时间
            Date lastRecalculationTime = allLogs.stream()
                .filter(log -> "RECALCULATION_START".equals(log.getCalculationType()))
                .map(CommissionCalculationLogs::getCreatedAt)
                .max(Date::compareTo)
                .orElse(new Date(0)); // 如果没有重新计算记录，使用最早时间
            
            // 返回最后一次重新计算之后的所有日志（排除历史标记日志）
            return allLogs.stream()
                .filter(log -> log.getCreatedAt().compareTo(lastRecalculationTime) >= 0)
                .filter(log -> !"HISTORICAL_LOG_MARK".equals(log.getCalculationType()))
                .sorted((a, b) -> a.getCreatedAt().compareTo(b.getCreatedAt()))
                .collect(java.util.stream.Collectors.toList());
                
        } catch (Exception e) {
            log.error("获取最新有效计算日志失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 从日志详情JSON中提取批次ID
     * 
     * @param details JSON格式的详情字符串
     * @return 批次ID，如果未找到则返回"UNKNOWN"
     */
    private String extractBatchIdFromDetails(String details) {
        if (details == null || details.trim().isEmpty()) {
            return "UNKNOWN";
        }
        
        // 简单的JSON解析，提取batchId
        try {
            if (details.contains("\"batchId\":\"")) {
                int start = details.indexOf("\"batchId\":\"") + 11;
                int end = details.indexOf("\"", start);
                if (end > start) {
                    return details.substring(start, end);
                }
            }
        } catch (Exception e) {
            log.debug("解析批次ID失败: {}", e.getMessage());
        }
        
        return "UNKNOWN";
    }

    /**
     * 执行拉新资格判定前置任务
     *
     * @param dataMonth 数据月份
     */
    private void executeNewRecruitQualificationTask(Date dataMonth) {
        try {
            log.info("开始执行拉新资格判定前置任务...");

            // 记录前置任务开始日志
            logCalculationEvent(dataMonth, "NEW_RECRUIT_QUALIFICATION_START", null, "INFO",
                "开始执行拉新资格判定前置任务", null);

            // 执行拉新资格判定
            Map<String, Object> qualificationResult = newRecruitQualificationService.executeNewRecruitQualification(dataMonth);

            if ((Boolean) qualificationResult.get("success")) {
                int qualifiedCount = (Integer) qualificationResult.get("qualifiedCount");
                int totalCandidates = (Integer) qualificationResult.get("totalCandidates");

                log.info("拉新资格判定完成 - 候选用户: {}, 合格用户: {}", totalCandidates, qualifiedCount);

                // 记录成功日志
                logCalculationEvent(dataMonth, "NEW_RECRUIT_QUALIFICATION_SUCCESS", null, "INFO",
                    String.format("拉新资格判定成功，候选用户: %d, 合格用户: %d", totalCandidates, qualifiedCount),
                    qualificationResult.toString());

            } else {
                String errorMessage = (String) qualificationResult.get("message");
                log.error("拉新资格判定失败: {}", errorMessage);

                // 记录失败日志
                logCalculationEvent(dataMonth, "NEW_RECRUIT_QUALIFICATION_ERROR", null, "ERROR",
                    "拉新资格判定失败: " + errorMessage, qualificationResult.toString());

                // 抛出异常，中断佣金计算流程
                throw new RuntimeException("拉新资格判定失败，无法继续执行佣金计算: " + errorMessage);
            }

        } catch (Exception e) {
            log.error("执行拉新资格判定前置任务失败: {}", e.getMessage(), e);

            // 记录异常日志
            logCalculationEvent(dataMonth, "NEW_RECRUIT_QUALIFICATION_EXCEPTION", null, "ERROR",
                "拉新资格判定前置任务异常: " + e.getMessage(), null);

            throw new RuntimeException("拉新资格判定前置任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行等级调整前置任务（防守体系）
     *
     * @param dataMonth 数据月份
     */
    private void executeDistributorLevelAdjustmentTask(Date dataMonth) {
        try {
            log.info("开始执行等级调整前置任务（防守体系）...");

            // 记录前置任务开始日志
            logCalculationEvent(dataMonth, "LEVEL_ADJUSTMENT_START", null, "INFO",
                "开始执行等级调整前置任务（防守体系）", null);

            // 执行等级调整
            Map<String, Object> adjustmentResult = distributorLevelAdjustmentService.executeMonthlyLevelAdjustment(dataMonth);

            if ((Boolean) adjustmentResult.get("success")) {
                int totalProcessed = (Integer) adjustmentResult.get("totalProcessed");
                int protectedCount = (Integer) adjustmentResult.get("protectedCount");
                int downgradedCount = (Integer) adjustmentResult.get("downgradedCount");
                int maintainedCount = (Integer) adjustmentResult.get("maintainedCount");

                log.info("等级调整完成 - 处理总数: {}, 保护期: {}, 降级: {}, 维持: {}",
                    totalProcessed, protectedCount, downgradedCount, maintainedCount);

                // 记录成功日志
                logCalculationEvent(dataMonth, "LEVEL_ADJUSTMENT_SUCCESS", null, "INFO",
                    String.format("等级调整成功，处理总数: %d, 保护期: %d, 降级: %d, 维持: %d",
                        totalProcessed, protectedCount, downgradedCount, maintainedCount),
                    adjustmentResult.toString());

            } else {
                String errorMessage = (String) adjustmentResult.get("message");
                log.error("等级调整失败: {}", errorMessage);

                // 记录失败日志
                logCalculationEvent(dataMonth, "LEVEL_ADJUSTMENT_ERROR", null, "ERROR",
                    "等级调整失败: " + errorMessage, adjustmentResult.toString());

                // 抛出异常，中断佣金计算流程
                throw new RuntimeException("等级调整失败，无法继续执行佣金计算: " + errorMessage);
            }

        } catch (Exception e) {
            log.error("执行等级调整前置任务失败: {}", e.getMessage(), e);

            // 记录异常日志
            logCalculationEvent(dataMonth, "LEVEL_ADJUSTMENT_EXCEPTION", null, "ERROR",
                "等级调整前置任务异常: " + e.getMessage(), null);

            throw new RuntimeException("等级调整前置任务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 任务D：执行月度成就摘要生成
     *
     * @param dataMonth 数据月份
     */
    private void generateTeamAchievementSummary(Date dataMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);

        try {
            log.info("=== 开始 {} 月度成就摘要生成 ===", monthStr);

            // 记录任务开始日志
            logCalculationEvent(dataMonth, "TEAM_ACHIEVEMENT_START", null, "INFO",
                "开始执行月度成就摘要生成", null);

            // 清理当月已存在的团队事件，防止重复生成
            log.info("清理当月已存在的团队事件...");
            cleanupExistingTeamEvents(dataMonth);

            // 1. 生成等级提升事件
            log.info("生成等级提升事件...");
            generateLevelUpEvents(dataMonth);

            // 2. 生成新人加入事件
            log.info("生成新人加入事件...");
            generateNewRecruitEvents(dataMonth);

            // 3. 生成业绩里程碑事件
            log.info("生成业绩里程碑事件...");
            generatePerformanceMilestoneEvents(dataMonth);

            // 4. 生成即将达标事件
            log.info("生成即将达标事件...");
            generateApproachingMilestoneEvents(dataMonth);

            // 记录任务完成日志
            logCalculationEvent(dataMonth, "TEAM_ACHIEVEMENT_COMPLETE", null, "INFO",
                "月度成就摘要生成完成", null);

            log.info("=== 完成 {} 月度成就摘要生成 ===", monthStr);

        } catch (Exception e) {
            log.error("执行月度成就摘要生成失败: {}", e.getMessage(), e);

            // 记录异常日志
            logCalculationEvent(dataMonth, "TEAM_ACHIEVEMENT_ERROR", null, "ERROR",
                "月度成就摘要生成失败: " + e.getMessage(),
                String.format("{\"errorType\":\"%s\",\"stackTrace\":\"%s\"}",
                    e.getClass().getSimpleName(),
                    e.getStackTrace()[0].toString()));

            throw new RuntimeException("月度成就摘要生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成等级提升事件
     * 对比本月和上月的最终等级，找出所有等级提升的用户，并为他们及其上级插入LEVEL_UP事件
     *
     * @param dataMonth 数据月份
     */
    private void generateLevelUpEvents(Date dataMonth) {
        try {
            // 获取本月所有分销员资格记录
            List<CommissionDistributorQualifications> currentMonthQualifications =
                getDistributorQualificationsByMonth(dataMonth);

            if (currentMonthQualifications.isEmpty()) {
                log.info("本月无分销员资格记录，跳过等级提升事件生成");
                return;
            }

            // 获取上月分销员资格记录
            Date lastMonth = getLastMonth(dataMonth);
            List<CommissionDistributorQualifications> lastMonthQualifications =
                getDistributorQualificationsByMonth(lastMonth);

            // 创建上月等级映射
            Map<Long, String> lastMonthLevels = new HashMap<>();
            for (CommissionDistributorQualifications qualification : lastMonthQualifications) {
                lastMonthLevels.put(qualification.getCreatorId(), qualification.getAchievedLevel());
            }

            // 批量获取所有相关用户的Creator信息，避免N+1查询
            Set<Long> allCreatorIds = currentMonthQualifications.stream()
                .map(CommissionDistributorQualifications::getCreatorId)
                .collect(Collectors.toSet());

            Map<Long, Creator> creatorMap = batchGetCreators(allCreatorIds);

            // 收集所有需要创建的事件
            List<TeamEvents> levelUpEvents = new ArrayList<>();
            Date eventTimestamp = getBusinessMonthTimestamp(dataMonth);

            // 对比等级变化
            for (CommissionDistributorQualifications currentQualification : currentMonthQualifications) {
                Long creatorId = currentQualification.getCreatorId();
                String currentLevel = currentQualification.getAchievedLevel();
                String lastLevel = lastMonthLevels.get(creatorId);

                // 检查是否有等级提升
                if (isLevelUpgrade(lastLevel, currentLevel)) {
                    // 从缓存中获取该用户的上级
                    Creator creator = creatorMap.get(creatorId);
                    Long parentId = (creator != null) ? creator.getParentId() : null;

                    if (parentId != null) {
                        // 创建等级提升事件对象
                        TeamEvents levelUpEvent = createLevelUpEventObject(parentId, creatorId,
                            lastLevel != null ? lastLevel : "无", currentLevel, eventTimestamp);
                        levelUpEvents.add(levelUpEvent);

                        log.debug("准备创建等级提升事件: 上级={}, 晋升者={}, {}级 -> {}级",
                            parentId, creatorId, lastLevel, currentLevel);
                    }
                }
            }

            // 批量插入事件
            if (!levelUpEvents.isEmpty()) {
                teamEventsService.insertTeamEventsBatch(levelUpEvents);
                log.info("等级提升事件生成完成，共生成 {} 个事件", levelUpEvents.size());
            } else {
                log.info("本月无等级提升，未生成等级提升事件");
            }

        } catch (Exception e) {
            log.error("生成等级提升事件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成等级提升事件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成新人加入事件
     * 查询任务A中所有被"盖章"的新人，为他们的上级插入NEW_RECRUIT事件
     *
     * @param dataMonth 数据月份
     */
    private void generateNewRecruitEvents(Date dataMonth) {
        try {
            // 查询本月首次合格的新人（first_qualified_month = dataMonth）
            List<Creator> newQualifiedCreators = getNewQualifiedCreators(dataMonth);

            if (newQualifiedCreators.isEmpty()) {
                log.info("本月无新合格用户，跳过新人加入事件生成");
                return;
            }

            // 收集所有需要创建的事件
            List<TeamEvents> newRecruitEvents = new ArrayList<>();
            Date eventTimestamp = getBusinessMonthTimestamp(dataMonth);

            for (Creator creator : newQualifiedCreators) {
                Long recruiterId = creator.getQualifiedByRecruiterId();

                if (recruiterId != null) {
                    // 创建新人加入事件对象
                    TeamEvents newRecruitEvent = createNewRecruitEventObject(recruiterId, creator.getId(), eventTimestamp);
                    newRecruitEvents.add(newRecruitEvent);

                    log.debug("准备创建新人加入事件: 招募人={}, 新人={}", recruiterId, creator.getId());
                }
            }

            // 批量插入事件
            if (!newRecruitEvents.isEmpty()) {
                teamEventsService.insertTeamEventsBatch(newRecruitEvents);
                log.info("新人加入事件生成完成，共生成 {} 个事件", newRecruitEvents.size());
            } else {
                log.info("本月新合格用户无有效招募人，未生成新人加入事件");
            }

        } catch (Exception e) {
            log.error("生成新人加入事件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成新人加入事件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成业绩里程碑事件
     * 遍历所有用户，对比其本月和上月的团队总业绩，找出所有在本月"跨越"了某个预设里程碑金额的用户
     *
     * @param dataMonth 数据月份
     */
    private void generatePerformanceMilestoneEvents(Date dataMonth) {
        try {
            // 从配置中获取业绩里程碑，如果没有配置则使用默认值
            Long[] milestones = getPerformanceMilestones();

            // 获取本月所有有业绩的用户
            List<Long> activeCreators = getActiveCreators(dataMonth);

            if (activeCreators.isEmpty()) {
                log.info("本月无活跃用户，跳过业绩里程碑事件生成");
                return;
            }

            Date lastMonth = getLastMonth(dataMonth);

            // 批量获取本月和上月的团队业绩，避免N+1查询
            Map<Long, Long> currentMonthTeamDiamonds = batchGetTeamTotalDiamonds(activeCreators, dataMonth);
            Map<Long, Long> lastMonthTeamDiamonds = batchGetTeamTotalDiamonds(activeCreators, lastMonth);

            // 收集所有需要创建的事件
            List<TeamEvents> milestoneEvents = new ArrayList<>();
            Date eventTimestamp = getBusinessMonthTimestamp(dataMonth);

            for (Long creatorId : activeCreators) {
                Long currentTeamDiamonds = currentMonthTeamDiamonds.getOrDefault(creatorId, 0L);
                Long lastTeamDiamonds = lastMonthTeamDiamonds.getOrDefault(creatorId, 0L);

                // 检查是否跨越了里程碑
                for (Long milestone : milestones) {
                    if (lastTeamDiamonds < milestone && currentTeamDiamonds >= milestone) {
                        // 跨越了里程碑，创建事件对象
                        TeamEvents milestoneEvent = createPerformanceMilestoneEventObject(creatorId,
                            milestone, currentTeamDiamonds, eventTimestamp);
                        milestoneEvents.add(milestoneEvent);

                        log.debug("准备创建业绩里程碑事件: 用户={}, 里程碑={}, 当前业绩={}",
                            creatorId, milestone, currentTeamDiamonds);
                    }
                }
            }

            // 批量插入事件
            if (!milestoneEvents.isEmpty()) {
                teamEventsService.insertTeamEventsBatch(milestoneEvents);
                log.info("业绩里程碑事件生成完成，共生成 {} 个事件", milestoneEvents.size());
            } else {
                log.info("本月无业绩里程碑跨越，未生成业绩里程碑事件");
            }

        } catch (Exception e) {
            log.error("生成业绩里程碑事件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成业绩里程碑事件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成即将达标事件
     * 筛选未达标新人和未跨越业绩里程碑的用户，生成即将达标事件
     *
     * @param dataMonth 数据月份
     */
    private void generateApproachingMilestoneEvents(Date dataMonth) {
        try {
            // 从配置中获取参数
            CommissionSettings approachingRecruitMinDaysSetting =
                commissionSettingsService.selectCommissionSettingsByKey("approaching_recruit_min_days");
            CommissionSettings approachingPerformanceMinRatioSetting =
                commissionSettingsService.selectCommissionSettingsByKey("approaching_performance_min_ratio");
            CommissionSettings newRecruitMinValidDaysSetting =
                commissionSettingsService.selectCommissionSettingsByKey("new_recruit_min_valid_days");

            int approachingRecruitMinDays = approachingRecruitMinDaysSetting != null ?
                Integer.parseInt(approachingRecruitMinDaysSetting.getSettingValue()) : 20;
            double approachingPerformanceMinRatio = approachingPerformanceMinRatioSetting != null ?
                Double.parseDouble(approachingPerformanceMinRatioSetting.getSettingValue()) : 0.80;
            int newRecruitMinValidDays = newRecruitMinValidDaysSetting != null ?
                Integer.parseInt(newRecruitMinValidDaysSetting.getSettingValue()) : 24;

            int approachingCount = 0;
            Date eventTimestamp = getBusinessMonthTimestamp(dataMonth);

            // 1. 生成即将达标(拉新)事件
            List<MonthlyPerformance> potentialNewRecruits = getPotentialNewRecruits(dataMonth);
            for (MonthlyPerformance performance : potentialNewRecruits) {
                Integer validDays = performance.getValidDays();
                if (validDays != null && validDays >= approachingRecruitMinDays && validDays < newRecruitMinValidDays) {
                    teamEventsService.createApproachingMilestoneEvent(performance.getCreatorId(),
                        "拉新", (long)validDays, (long)newRecruitMinValidDays, eventTimestamp);
                    approachingCount++;

                    log.debug("创建即将达标(拉新)事件: 用户={}, 当前有效天数={}, 目标天数={}",
                        performance.getCreatorId(), validDays, newRecruitMinValidDays);
                }
            }

            // 2. 生成即将达标(业绩)事件
            Long[] milestones = {100000L, 500000L, 1000000L, 2000000L, 5000000L, 10000000L};
            List<Long> activeCreators = getActiveCreators(dataMonth);

            for (Long creatorId : activeCreators) {
                Long currentTeamDiamonds = getTeamTotalDiamonds(creatorId, dataMonth);
                if (currentTeamDiamonds == null) currentTeamDiamonds = 0L;

                // 找到下一个里程碑
                for (Long milestone : milestones) {
                    if (currentTeamDiamonds < milestone) {
                        double completionRatio = (double) currentTeamDiamonds / milestone;
                        if (completionRatio >= approachingPerformanceMinRatio && completionRatio < 1.0) {
                            teamEventsService.createApproachingMilestoneEvent(creatorId,
                                "业绩", currentTeamDiamonds, milestone, eventTimestamp);
                            approachingCount++;

                            log.debug("创建即将达标(业绩)事件: 用户={}, 当前业绩={}, 目标里程碑={}, 完成度={}%",
                                creatorId, currentTeamDiamonds, milestone, completionRatio * 100);
                        }
                        break; // 只检查下一个里程碑
                    }
                }
            }

            log.info("即将达标事件生成完成，共生成 {} 个事件", approachingCount);

        } catch (Exception e) {
            log.error("生成即将达标事件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成即将达标事件失败: " + e.getMessage(), e);
        }
    }

    // ==================== 辅助方法 ====================



    /**
     * 获取指定月份的分销员资格记录
     */
    private List<CommissionDistributorQualifications> getDistributorQualificationsByMonth(Date dataMonth) {
        return commissionDistributorQualificationsService.selectCommissionDistributorQualificationsByMonth(dataMonth);
    }

    /**
     * 判断是否为等级提升
     */
    private boolean isLevelUpgrade(String oldLevel, String newLevel) {
        if (oldLevel == null || newLevel == null) {
            return newLevel != null;
        }

        // 从数据库获取等级优先级映射
        Map<String, Integer> levelPriority = getLevelPriorityMap();

        Integer oldPriority = levelPriority.get(oldLevel);
        Integer newPriority = levelPriority.get(newLevel);

        if (oldPriority == null) oldPriority = 0;
        if (newPriority == null) newPriority = 0;

        return newPriority > oldPriority;
    }

    /**
     * 获取等级优先级映射
     */
    private Map<String, Integer> getLevelPriorityMap() {
        try {
            // 从commission_level_rules表获取等级信息
            List<CommissionLevelRules> levelRules = commissionLevelRulesService.selectCommissionLevelRulesList(new CommissionLevelRules());

            Map<String, Integer> levelPriority = new HashMap<>();

            // 根据display_order作为优先级（display_order越大等级越高）
            for (CommissionLevelRules rule : levelRules) {
                if (rule.getLevelName() != null && rule.getDisplayOrder() != null) {
                    levelPriority.put(rule.getLevelName(), rule.getDisplayOrder());
                }
            }

            if (!levelPriority.isEmpty()) {
                return levelPriority;
            }
        } catch (Exception e) {
            log.warn("从数据库读取等级优先级失败，使用默认值: {}", e.getMessage());
        }

        // 如果数据库读取失败，使用默认映射
        Map<String, Integer> defaultPriority = new HashMap<>();
        defaultPriority.put("铜牌", 1);
        defaultPriority.put("银牌", 2);
        defaultPriority.put("金牌", 3);
        defaultPriority.put("钻石", 4);

        return defaultPriority;
    }

    /**
     * 获取用户的上级ID
     */
    private Long getParentCreatorId(Long creatorId) {
        Creator creator = creatorService.selectCreatorById(creatorId);
        return creator != null ? creator.getParentId() : null;
    }

    /**
     * 获取本月首次合格的新人列表
     */
    private List<Creator> getNewQualifiedCreators(Date dataMonth) {
        Creator queryCreator = new Creator();
        queryCreator.setFirstQualifiedMonth(dataMonth);
        return creatorService.selectCreatorList(queryCreator);
    }

    /**
     * 获取指定月份的活跃用户列表（有业绩的用户）
     */
    private List<Long> getActiveCreators(Date dataMonth) {
        MonthlyPerformance queryPerformance = new MonthlyPerformance();
        queryPerformance.setDataMonth(dataMonth);
        List<MonthlyPerformance> performances = monthlyPerformanceService.selectMonthlyPerformanceList(queryPerformance);

        return performances.stream()
            .filter(p -> p.getDiamonds() != null && p.getDiamonds() > 0)
            .map(MonthlyPerformance::getCreatorId)
            .collect(Collectors.toList());
    }

    /**
     * 获取用户的团队总业绩（个人+下级）
     */
    private Long getTeamTotalDiamonds(Long creatorId, Date dataMonth) {
        try {
            // 获取个人业绩
            MonthlyPerformance personalPerformance = monthlyPerformanceService.selectMonthlyPerformanceByCreatorAndMonth(creatorId, dataMonth);
            Long personalDiamonds = (personalPerformance != null && personalPerformance.getDiamonds() != null) ?
                personalPerformance.getDiamonds() : 0L;

            // 获取团队业绩（下级业绩总和）
            List<Long> downlines = getDirectDownlines(creatorId);
            Long teamDiamonds = calculateTeamDiamonds(downlines, dataMonth);

            return personalDiamonds + teamDiamonds;
        } catch (Exception e) {
            log.warn("获取用户 {} 在 {} 的团队总业绩失败: {}", creatorId, dataMonth, e.getMessage());
            return 0L;
        }
    }

    /**
     * 获取潜在新人列表（可能达标的新人）
     */
    private List<MonthlyPerformance> getPotentialNewRecruits(Date dataMonth) {
        MonthlyPerformance queryPerformance = new MonthlyPerformance();
        queryPerformance.setDataMonth(dataMonth);
        queryPerformance.setIsRookie(1); // 新人标记

        List<MonthlyPerformance> rookiePerformances = monthlyPerformanceService.selectMonthlyPerformanceList(queryPerformance);

        if (rookiePerformances.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量获取所有相关的Creator信息，避免N+1查询
        Set<Long> creatorIds = rookiePerformances.stream()
            .map(MonthlyPerformance::getCreatorId)
            .collect(Collectors.toSet());

        Map<Long, Creator> creatorMap = batchGetCreators(creatorIds);

        // 过滤出还未达标的新人（first_qualified_month为空）
        return rookiePerformances.stream()
            .filter(p -> {
                Creator creator = creatorMap.get(p.getCreatorId());
                return creator != null && creator.getFirstQualifiedMonth() == null;
            })
            .collect(Collectors.toList());
    }

    /**
     * 批量获取Creator信息，避免N+1查询
     */
    private Map<Long, Creator> batchGetCreators(Set<Long> creatorIds) {
        if (creatorIds.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询所有Creator
        Creator queryCreator = new Creator();
        List<Creator> creators = creatorService.selectCreatorList(queryCreator);

        // 过滤出需要的Creator并构建Map
        return creators.stream()
            .filter(creator -> creatorIds.contains(creator.getId()))
            .collect(Collectors.toMap(Creator::getId, creator -> creator));
    }

    /**
     * 创建等级提升事件对象
     */
    private TeamEvents createLevelUpEventObject(Long actorCreatorId, Long targetCreatorId,
                                               String oldLevel, String newLevel, Date eventTimestamp) {
        TeamEvents teamEvents = new TeamEvents();
        teamEvents.setActorCreatorId(actorCreatorId);
        teamEvents.setTargetCreatorId(targetCreatorId);
        teamEvents.setEventType("LEVEL_UP");
        teamEvents.setEventTimestamp(eventTimestamp);

        // 构建详情JSON
        String details = String.format("{\"old_level\":\"%s\",\"new_level\":\"%s\"}", oldLevel, newLevel);
        teamEvents.setDetails(details);

        return teamEvents;
    }

    /**
     * 获取业绩里程碑配置
     */
    private Long[] getPerformanceMilestones() {
        try {
            // 从配置表中读取业绩里程碑
            CommissionSettings milestoneSetting = commissionSettingsService.selectCommissionSettingsByKey("performance_milestones");

            if (milestoneSetting != null && milestoneSetting.getSettingValue() != null) {
                String[] milestoneStrings = milestoneSetting.getSettingValue().split(",");
                Long[] milestones = new Long[milestoneStrings.length];

                for (int i = 0; i < milestoneStrings.length; i++) {
                    milestones[i] = Long.parseLong(milestoneStrings[i].trim());
                }

                return milestones;
            }
        } catch (Exception e) {
            log.warn("从配置表读取业绩里程碑失败，使用默认值: {}", e.getMessage());
        }

        // 如果配置读取失败，使用默认值
        return new Long[]{100000L, 500000L, 1000000L, 2000000L, 5000000L, 10000000L};
    }

    /**
     * 批量获取团队总业绩，避免N+1查询
     */
    private Map<Long, Long> batchGetTeamTotalDiamonds(List<Long> creatorIds, Date dataMonth) {
        Map<Long, Long> result = new HashMap<>();

        if (creatorIds.isEmpty()) {
            return result;
        }

        try {
            // 批量获取个人业绩
            MonthlyPerformance queryPerformance = new MonthlyPerformance();
            queryPerformance.setDataMonth(dataMonth);
            List<MonthlyPerformance> allPerformances = monthlyPerformanceService.selectMonthlyPerformanceList(queryPerformance);

            // 构建个人业绩映射
            Map<Long, Long> personalDiamondsMap = allPerformances.stream()
                .filter(p -> p.getDiamonds() != null)
                .collect(Collectors.toMap(
                    MonthlyPerformance::getCreatorId,
                    MonthlyPerformance::getDiamonds,
                    (existing, replacement) -> existing // 如果有重复，保留现有值
                ));

            // 批量获取所有Creator关系
            Creator queryCreator = new Creator();
            List<Creator> allCreators = creatorService.selectCreatorList(queryCreator);
            Map<Long, List<Long>> parentToChildrenMap = new HashMap<>();

            // 构建父子关系映射
            for (Creator creator : allCreators) {
                if (creator.getParentId() != null) {
                    parentToChildrenMap.computeIfAbsent(creator.getParentId(), k -> new ArrayList<>())
                        .add(creator.getId());
                }
            }

            // 计算每个用户的团队总业绩
            for (Long creatorId : creatorIds) {
                Long personalDiamonds = personalDiamondsMap.getOrDefault(creatorId, 0L);
                Long teamDiamonds = calculateTeamDiamondsRecursive(creatorId, parentToChildrenMap, personalDiamondsMap);
                result.put(creatorId, personalDiamonds + teamDiamonds);
            }

        } catch (Exception e) {
            log.warn("批量获取团队总业绩失败: {}", e.getMessage());
            // 如果批量获取失败，返回空结果，避免影响主流程
        }

        return result;
    }

    /**
     * 递归计算团队业绩
     */
    private Long calculateTeamDiamondsRecursive(Long creatorId, Map<Long, List<Long>> parentToChildrenMap,
                                               Map<Long, Long> personalDiamondsMap) {
        Long totalTeamDiamonds = 0L;
        List<Long> children = parentToChildrenMap.get(creatorId);

        if (children != null) {
            for (Long childId : children) {
                Long childPersonalDiamonds = personalDiamondsMap.getOrDefault(childId, 0L);
                Long childTeamDiamonds = calculateTeamDiamondsRecursive(childId, parentToChildrenMap, personalDiamondsMap);
                totalTeamDiamonds += childPersonalDiamonds + childTeamDiamonds;
            }
        }

        return totalTeamDiamonds;
    }

    /**
     * 创建业绩里程碑事件对象
     */
    private TeamEvents createPerformanceMilestoneEventObject(Long actorCreatorId, Long milestoneValue,
                                                           Long currentValue, Date eventTimestamp) {
        TeamEvents teamEvents = new TeamEvents();
        teamEvents.setActorCreatorId(actorCreatorId);
        teamEvents.setEventType("PERFORMANCE_MILESTONE");
        teamEvents.setEventTimestamp(eventTimestamp);

        // 构建详情JSON
        String details = String.format("{\"milestone_value\":%d,\"current_value\":%d}", milestoneValue, currentValue);
        teamEvents.setDetails(details);

        return teamEvents;
    }

    /**
     * 创建新人加入事件对象
     */
    private TeamEvents createNewRecruitEventObject(Long actorCreatorId, Long targetCreatorId, Date eventTimestamp) {
        TeamEvents teamEvents = new TeamEvents();
        teamEvents.setActorCreatorId(actorCreatorId);
        teamEvents.setTargetCreatorId(targetCreatorId);
        teamEvents.setEventType("NEW_RECRUIT");
        teamEvents.setEventTimestamp(eventTimestamp);

        // 构建详情JSON
        String details = String.format("{\"recruit_id\":%d}", targetCreatorId);
        teamEvents.setDetails(details);

        return teamEvents;
    }

    /**
     * 清理当月已存在的团队事件，防止重复生成
     * 使用高性能的时间范围删除，避免先查询再删除的性能问题
     */
    private void cleanupExistingTeamEvents(Date dataMonth) {
        try {
            // 计算当月的开始和结束时间
            Calendar cal = Calendar.getInstance();
            cal.setTime(dataMonth);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            Date monthStart = cal.getTime();

            cal.add(Calendar.MONTH, 1);
            cal.add(Calendar.MILLISECOND, -1);
            Date monthEnd = cal.getTime();

            // 直接按时间范围删除，避免先查询再删除的性能问题
            int deletedCount = teamEventsService.deleteTeamEventsByTimeRange(monthStart, monthEnd);

            if (deletedCount > 0) {
                log.info("清理了 {} 个当月已存在的团队事件", deletedCount);
            } else {
                log.info("当月无已存在的团队事件需要清理");
            }

        } catch (Exception e) {
            log.warn("清理当月团队事件失败: {}", e.getMessage());
            // 清理失败不影响主流程，只记录警告日志
        }
    }

    /**
     * 获取业务月份的标准时间戳（月初时间）
     * 统一使用月初时间便于按月数据分析和归档
     *
     * @param dataMonth 数据月份
     * @return 月初时间戳（如2025-03-01 00:00:00）
     */
    private Date getBusinessMonthTimestamp(Date dataMonth) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dataMonth);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 任务E：更新用户基础等级
     * 将本月的最终等级结果固化为下一个月的考核基准
     *
     * @param dataMonth 数据月份
     */
    private void updateUserBaseLevels(Date dataMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String monthStr = sdf.format(dataMonth);

        try {
            log.info("=== 开始 {} 用户基础等级更新 ===", monthStr);

            // 记录任务开始日志
            logCalculationEvent(dataMonth, "USER_BASE_LEVEL_UPDATE_START", null, "INFO",
                "开始执行用户基础等级更新", null);

            // 获取本月所有分销员的最终等级
            List<CommissionDistributorQualifications> currentMonthQualifications =
                getDistributorQualificationsByMonth(dataMonth);

            if (currentMonthQualifications.isEmpty()) {
                log.info("本月无分销员资格记录，跳过基础等级更新");
                return;
            }

            // 批量更新用户基础等级
            int updatedCount = batchUpdateUserBaseLevels(currentMonthQualifications);

            // 记录任务完成日志
            logCalculationEvent(dataMonth, "USER_BASE_LEVEL_UPDATE_COMPLETE", null, "INFO",
                String.format("用户基础等级更新完成，共更新 %d 个用户", updatedCount),
                String.format("{\"updated_count\":%d}", updatedCount));

            log.info("=== 完成 {} 用户基础等级更新，共更新 {} 个用户 ===", monthStr, updatedCount);

        } catch (Exception e) {
            log.error("执行用户基础等级更新失败: {}", e.getMessage(), e);

            // 记录异常日志
            logCalculationEvent(dataMonth, "USER_BASE_LEVEL_UPDATE_ERROR", null, "ERROR",
                "用户基础等级更新失败: " + e.getMessage(),
                String.format("{\"errorType\":\"%s\",\"stackTrace\":\"%s\"}",
                    e.getClass().getSimpleName(),
                    e.getStackTrace()[0].toString()));

            throw new RuntimeException("用户基础等级更新失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量更新用户基础等级
     *
     * @param qualifications 分销员资格记录列表
     * @return 更新的用户数量
     */
    private int batchUpdateUserBaseLevels(List<CommissionDistributorQualifications> qualifications) {
        int updatedCount = 0;
        Date updateTime = new Date();

        try {
            // 获取等级名称到等级ID的映射
            Map<String, Long> levelNameToIdMap = getLevelNameToIdMap();

            for (CommissionDistributorQualifications qualification : qualifications) {
                Long creatorId = qualification.getCreatorId();
                String achievedLevel = qualification.getAchievedLevel();

                if (creatorId != null && achievedLevel != null) {
                    // 获取等级ID
                    Long levelId = levelNameToIdMap.get(achievedLevel);

                    if (levelId != null) {
                        // 更新用户的基础等级
                        boolean updated = updateCreatorBaseLevel(creatorId, levelId.intValue(), updateTime);
                        if (updated) {
                            updatedCount++;
                            log.debug("更新用户 {} 的基础等级为: {} (ID: {})", creatorId, achievedLevel, levelId);
                        }
                    } else {
                        log.warn("未找到等级 '{}' 对应的等级ID，跳过用户 {}", achievedLevel, creatorId);
                    }
                }
            }

        } catch (Exception e) {
            log.error("批量更新用户基础等级失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量更新用户基础等级失败: " + e.getMessage(), e);
        }

        return updatedCount;
    }

    /**
     * 获取等级名称到等级ID的映射
     */
    private Map<String, Long> getLevelNameToIdMap() {
        try {
            List<CommissionLevelRules> levelRules = commissionLevelRulesService.selectCommissionLevelRulesList(new CommissionLevelRules());

            Map<String, Long> levelMap = new HashMap<>();
            for (CommissionLevelRules rule : levelRules) {
                if (rule.getLevelName() != null && rule.getId() != null) {
                    levelMap.put(rule.getLevelName(), rule.getId().longValue());
                }
            }

            return levelMap;
        } catch (Exception e) {
            log.error("获取等级映射失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 更新单个用户的基础等级
     *
     * @param creatorId 用户ID
     * @param levelId 等级ID
     * @param updateTime 更新时间
     * @return 是否更新成功
     */
    private boolean updateCreatorBaseLevel(Long creatorId, Integer levelId, Date updateTime) {
        try {
            Creator creator = creatorService.selectCreatorById(creatorId);
            if (creator != null) {
                creator.setCurrentDistributorLevelId(levelId);
                creator.setLevelUpdatedAt(updateTime);

                int result = creatorService.updateCreator(creator);
                return result > 0;
            }
            return false;
        } catch (Exception e) {
            log.error("更新用户 {} 基础等级失败: {}", creatorId, e.getMessage());
            return false;
        }
    }
}