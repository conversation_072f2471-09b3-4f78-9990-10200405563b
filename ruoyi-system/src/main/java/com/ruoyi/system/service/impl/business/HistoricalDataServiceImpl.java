package com.ruoyi.system.service.impl.business;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.service.business.IHistoricalDataService;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.business.IRawImportService;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.system.service.business.IGroupCreatorRelationService;
import com.ruoyi.system.service.business.ICreatorRelationshipService;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.RawImport;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.business.GroupCreatorRelation;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;

// 添加ruoyi系统同步相关的导入
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// 添加PostConstruct支持
import javax.annotation.PostConstruct;

/**
 * 历史数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class HistoricalDataServiceImpl implements IHistoricalDataService
{
    private static final Logger log = LoggerFactory.getLogger(HistoricalDataServiceImpl.class);

    @Autowired
    private ICreatorService creatorService;

    @Autowired
    private IRawImportService rawImportService;

    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;

    @Autowired
    private IGroupCreatorRelationService groupCreatorRelationService;

    @Autowired
    private ICreatorRelationshipService creatorRelationshipService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 初始化检查：验证UserSyncService Bean是否正确注册
     */
    @PostConstruct
    public void checkUserSyncServiceBean() {
        log.info("=== 检查HistoricalUserSyncService Bean注册状态 ===");
        
        try {
            // 方法1：通过Bean名称获取
            HistoricalUserSyncService bean1 = SpringUtils.getBean("historicalUserSyncService");
            log.info("✅ 通过Bean名称'historicalUserSyncService'获取成功: {}", bean1.getClass().getName());
        } catch (Exception e) {
            log.warn("❌ 通过Bean名称'historicalUserSyncService'获取失败: {}", e.getMessage());
        }
        
        try {
            // 方法2：通过类型获取
            HistoricalUserSyncService bean2 = SpringUtils.getBean(HistoricalUserSyncService.class);
            log.info("✅ 通过类型HistoricalUserSyncService.class获取成功: {}", bean2.getClass().getName());
        } catch (Exception e) {
            log.warn("❌ 通过类型HistoricalUserSyncService.class获取失败: {}", e.getMessage());
        }
        
        log.info("=== HistoricalUserSyncService Bean检查完成 ===");
    }

    /**
     * 导入Excel历史数据
     */
    @Override
    public Map<String, Object> importHistoricalData(MultipartFile file, String updateBy) throws Exception
    {
        return importHistoricalData(file, updateBy, false);
    }

    /**
     * 导入Excel历史数据（支持创建时间策略选择）
     *
     * @param file Excel文件
     * @param updateBy 操作人
     * @param useHistoricalCreateTime 是否使用历史创建时间
     *        - true: 新creator的创建时间设为数据月份，可能影响新人资格判定
     *        - false: 新creator的创建时间设为当前时间，不影响新人资格判定
     * @return 导入结果
     */
    public Map<String, Object> importHistoricalData(MultipartFile file, String updateBy, boolean useHistoricalCreateTime) throws Exception
    {
        // 第一步：在事务中导入数据
        Map<String, Object> result = importHistoricalDataInTransaction(file, updateBy, useHistoricalCreateTime);

        // 第二步：在事务外同步到ruoyi系统
        @SuppressWarnings("unchecked")
        List<Creator> syncedCreators = (List<Creator>) result.get("syncedCreators");
        syncCreatorsToRuoyiSystem(syncedCreators, updateBy);

        // 移除内部使用的字段
        result.remove("syncedCreators");

        return result;
    }
    
    /**
     * 在事务中导入历史数据（兼容旧版本）
     */
    @Transactional
    private Map<String, Object> importHistoricalDataInTransaction(MultipartFile file, String updateBy) throws Exception
    {
        return importHistoricalDataInTransaction(file, updateBy, false);
    }

    /**
     * 在事务中导入历史数据
     */
    @Transactional
    private Map<String, Object> importHistoricalDataInTransaction(MultipartFile file, String updateBy, boolean useHistoricalCreateTime) throws Exception
    {
        Map<String, Object> result = new HashMap<>();
        int rawImportCount = 0;
        int creatorCount = 0;
        int monthlyPerformanceCount = 0;
        int errorCount = 0;
        List<String> errorMessages = new ArrayList<>();
        List<Creator> syncedCreators = new ArrayList<>(); // 收集需要同步到ruoyi系统的creators

        try (InputStream is = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(is))
        {
            Sheet sheet = workbook.getSheetAt(0);

            // 跳过标题行
            for (int i = 1; i <= sheet.getLastRowNum(); i++)
            {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                try
                {
                    // 解析行数据
                    Map<String, Object> rowData = parseRowData(row, file.getOriginalFilename());

                    // 导入到raw_imports表
                    if (insertRawImport(rowData, updateBy))
                    {
                        rawImportCount++;
                    }

                    // 导入到creators表，并收集成功同步的creator
                    Creator syncedCreator = insertOrUpdateCreator(rowData, updateBy, useHistoricalCreateTime);
                    if (syncedCreator != null)
                    {
                        creatorCount++;
                        syncedCreators.add(syncedCreator);
                    }

                    // 导入到monthly_performance表
                    if (insertOrUpdateMonthlyPerformance(rowData, updateBy))
                    {
                        monthlyPerformanceCount++;
                    }
                }
                catch (Exception e)
                {
                    errorCount++;
                    errorMessages.add("第" + (i + 1) + "行数据处理失败: " + e.getMessage());
                }
            }
        }

        result.put("rawImportCount", rawImportCount);
        result.put("creatorCount", creatorCount);
        result.put("monthlyPerformanceCount", monthlyPerformanceCount);
        result.put("errorCount", errorCount);
        result.put("errorMessages", errorMessages);
        result.put("syncedCreators", syncedCreators); // 传递给外层方法
        result.put("useHistoricalCreateTime", useHistoricalCreateTime); // 记录使用的策略

        return result;
    }
    
    /**
     * 在事务外同步creators到ruoyi系统
     */
    private void syncCreatorsToRuoyiSystem(List<Creator> syncedCreators, String updateBy)
    {
        if (syncedCreators == null || syncedCreators.isEmpty()) {
            log.info("没有需要同步的creators到ruoyi系统");
            return;
        }
        
        log.info("开始同步 {} 个creators到ruoyi系统", syncedCreators.size());
        try
        {
            // 使用独立的服务Bean，确保@DataSource注解生效
            HistoricalUserSyncService userSyncService = null;
            
            // 方法1：尝试通过Bean名称获取
            try {
                userSyncService = SpringUtils.getBean("historicalUserSyncService");
                log.info("通过Bean名称获取HistoricalUserSyncService成功");
            } catch (Exception e1) {
                log.warn("通过Bean名称获取HistoricalUserSyncService失败：{}", e1.getMessage());
                
                // 方法2：尝试通过类型获取
                try {
                    userSyncService = SpringUtils.getBean(HistoricalUserSyncService.class);
                    log.info("通过类型获取HistoricalUserSyncService成功");
                } catch (Exception e2) {
                    log.error("通过类型获取HistoricalUserSyncService也失败：{}", e2.getMessage());
                    log.error("无法获取HistoricalUserSyncService Bean，跳过用户同步");
                    return;
                }
            }
            
            if (userSyncService != null) {
                userSyncService.synchronizeToRuoyiUserSystemWithMasterDB(syncedCreators, updateBy);
                log.info("用户同步到ruoyi系统完成");
            } else {
                log.error("无法获取或创建HistoricalUserSyncService实例");
            }
        }
        catch (Exception e)
        {
            log.error("同步用户到ruoyi系统失败，但不影响主要导入流程: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建虚拟上级并分配给组
     */
    @Override
    public Long createVirtualParentForGroup(String groupName, String virtualNickname, String remark, String createBy)
    {
        // 第一步：在事务中创建虚拟上级和建立关系
        Long virtualId = createVirtualParentInTransaction(groupName, virtualNickname, remark, createBy);
        
        // 第二步：在事务外同步到ruoyi系统，确保数据源切换生效
        syncVirtualParentToRuoyiSystem(virtualId, createBy);
        
        return virtualId;
    }
    
    /**
     * 在事务中创建虚拟上级和建立关系
     */
    @Transactional
    private Long createVirtualParentInTransaction(String groupName, String virtualNickname, String remark, String createBy)
    {
        log.info("开始为组 {} 创建虚拟上级，昵称：{}，备注：{}", groupName, virtualNickname, remark);
        
        // 生成虚拟ID
        Long virtualId = generateVirtualCreatorId();
        log.info("生成虚拟上级ID：{}", virtualId);
        
        // 创建虚拟creator
        Creator virtualCreator = new Creator();
        virtualCreator.setId(virtualId);
        virtualCreator.setNickname(virtualNickname);
        virtualCreator.setHandle("virtual_" + virtualId);
        virtualCreator.setRemark(remark);  // 直接使用传入的备注
        virtualCreator.setCreateBy(createBy);
        virtualCreator.setUpdateBy(createBy);
        
        log.info("准备插入虚拟creator到数据库：ID={}, 昵称={}, Handle={}", 
            virtualId, virtualNickname, virtualCreator.getHandle());
        creatorService.insertCreator(virtualCreator);
        log.info("✅ 虚拟creator插入数据库成功");
        
        // 建立组关系
        log.info("准备建立组关系：组名={}, CreatorID={}", groupName, virtualId);
        groupCreatorRelationService.assignGroupParent(groupName, virtualId, createBy);
        log.info("✅ 组关系建立成功");
        
        return virtualId;
    }
    
    /**
     * 在事务外同步虚拟上级到ruoyi系统
     */
    private void syncVirtualParentToRuoyiSystem(Long virtualId, String createBy)
    {
        log.info("开始同步虚拟上级到ruoyi用户系统，virtualId: {}", virtualId);
        try
        {
            // 查询虚拟creator
            Creator virtualCreator = creatorService.selectCreatorById(virtualId);
            if (virtualCreator == null) {
                log.error("未找到虚拟creator，ID: {}", virtualId);
                return;
            }
            
            // 检查HistoricalUserSyncService是否可用
            HistoricalUserSyncService userSyncService = null;
            
            // 方法1：尝试通过Bean名称获取
            try {
                userSyncService = SpringUtils.getBean("historicalUserSyncService");
                log.info("通过Bean名称获取HistoricalUserSyncService成功");
            } catch (Exception e1) {
                log.warn("通过Bean名称获取HistoricalUserSyncService失败：{}", e1.getMessage());
                
                // 方法2：尝试通过类型获取
                try {
                    userSyncService = SpringUtils.getBean(HistoricalUserSyncService.class);
                    log.info("通过类型获取HistoricalUserSyncService成功");
                } catch (Exception e2) {
                    log.error("通过类型获取HistoricalUserSyncService也失败：{}", e2.getMessage());
                    log.error("无法获取HistoricalUserSyncService Bean，跳过虚拟上级同步");
                    return;
                }
            }
            
            if (userSyncService != null) 
            {
                List<Creator> virtualCreatorList = Arrays.asList(virtualCreator);
                log.info("调用HistoricalUserSyncService同步虚拟上级，creator数量：{}", virtualCreatorList.size());
                
                // 直接调用同步方法
                userSyncService.synchronizeToRuoyiUserSystemWithMasterDB(virtualCreatorList, createBy);
                log.info("✅ 虚拟上级同步到ruoyi系统成功");
            } 
            else 
            {
                log.error("❌ HistoricalUserSyncService为null，无法同步虚拟上级到ruoyi系统");
            }
        }
        catch (Exception e)
        {
            log.error("同步虚拟上级到ruoyi系统失败: {}", e.getMessage(), e);
            // 不抛出异常，同步失败不应影响主要业务流程
        }
    }

    /**
     * 重建创建者关系
     */
    @Override
    @Transactional
    public Map<String, Object> rebuildCreatorRelationships(String updateBy)
    {
        Map<String, Object> result = new HashMap<>();
        int updatedCreators = 0;
        int rebuiltRelationships = 0;

        try
        {
            // 1. 清理现有creator_relationships数据
            creatorRelationshipService.deleteAllCreatorRelationships();

            // 2. 根据group_creator_relation更新creators.parent_id
            List<GroupCreatorRelation> groupRelations = groupCreatorRelationService.selectGroupCreatorRelationList(new GroupCreatorRelation());
            
            for (GroupCreatorRelation groupRelation : groupRelations)
            {
                // 获取该组下所有的creators
                MonthlyPerformance queryParam = new MonthlyPerformance();
                queryParam.setGroupName(groupRelation.getGroupName());
                List<MonthlyPerformance> performances = monthlyPerformanceService.selectMonthlyPerformanceList(queryParam);
                
                // 更新这些creators的parent_id
                for (MonthlyPerformance performance : performances)
                {
                    Creator creator = creatorService.selectCreatorById(performance.getCreatorId());
                    if (creator != null && !creator.getId().equals(groupRelation.getCreatorId()))
                    {
                        creator.setParentId(groupRelation.getCreatorId());
                        creator.setUpdateBy(updateBy);
                        creatorService.updateCreator(creator);
                        updatedCreators++;
                    }
                }
            }

            // 3. 重新生成creator_relationships闭包表
            rebuiltRelationships = creatorRelationshipService.rebuildAllRelationships();

            result.put("success", true);
            result.put("updatedCreators", updatedCreators);
            result.put("rebuiltRelationships", rebuiltRelationships);
        }
        catch (Exception e)
        {
            result.put("success", false);
            result.put("error", e.getMessage());
            throw new ServiceException("重建关系失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 生成唯一的虚拟ID
     * 格式：888 + 10位时间戳 + 4位随机数 = 17位（在long范围内）
     */
    @Override
    public Long generateVirtualCreatorId()
    {
        // 888 + 时间戳后10位 + 随机数(4位) = 17位数字，确保在long范围内
        long timestamp = System.currentTimeMillis();
        int random = ThreadLocalRandom.current().nextInt(1000, 9999);
        
        // 只取时间戳的后10位，避免ID过长
        String timestampStr = String.valueOf(timestamp);
        String shortTimestamp = timestampStr.substring(Math.max(0, timestampStr.length() - 10));
        
        String idStr = "888" + shortTimestamp + random;
        return Long.parseLong(idStr);
    }

    /**
     * 验证Excel数据格式
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public Map<String, Object> validateExcelData(MultipartFile file) throws Exception
    {
        Map<String, Object> result = new HashMap<>();
        // TODO: 实现Excel验证逻辑
        result.put("message", "验证功能待实现");
        return result;
    }

    /**
     * 自动为孤儿组创建虚拟上级并建立层级关系
     */
    @Override
    @Transactional
    public Map<String, Object> autoGenerateRelationships(String updateBy)
    {
        log.info("=== 开始自动为孤儿组创建虚拟上级并建立层级关系 ===");
        log.info("操作人：{}", updateBy);
        
        Map<String, Object> result = new HashMap<>();
        List<String> orphanGroups = new ArrayList<>();
        List<Long> createdVirtualCreators = new ArrayList<>();
        int skippedGroups = 0;
        
        try
        {
            // 第一步：识别孤儿组
            log.info("第一步：开始识别孤儿组...");
            orphanGroups = findOrphanGroups();
            log.info("识别到孤儿组数量：{}，组名列表：{}", orphanGroups.size(), orphanGroups);
            
            if (orphanGroups.isEmpty())
            {
                log.info("没有发现孤儿组，无需处理");
                result.put("success", true);
                result.put("orphanGroups", 0);
                result.put("createdVirtualCreators", 0);
                result.put("skippedGroups", 0);
                result.put("message", "没有发现孤儿组，无需处理");
                return result;
            }
            
            // 第二步：为孤儿组创建虚拟上级
            log.info("第二步：开始为孤儿组创建虚拟上级...");
            for (String groupName : orphanGroups)
            {
                log.info("正在处理孤儿组：{}", groupName);
                
                // 检查是否已存在关系
                GroupCreatorRelation existingRelation = groupCreatorRelationService.selectGroupCreatorRelationByGroupName(groupName);
                if (existingRelation != null && existingRelation.getCreatorId() != null)
                {
                    log.info("组 {} 已存在关系，CreatorID：{}，跳过处理", groupName, existingRelation.getCreatorId());
                    skippedGroups++;
                    continue;
                }
                
                // 为该组创建虚拟上级
                String virtualNickname = groupName + "-虚拟";
                String remark = "虚拟上级";
                log.info("准备为组 {} 创建虚拟上级，昵称：{}", groupName, virtualNickname);
                
                // 检查是否已存在同名的虚拟上级
                Creator queryParam = new Creator();
                queryParam.setNickname(virtualNickname);
                List<Creator> existingVirtualCreators = creatorService.selectCreatorList(queryParam);
                
                // 过滤出虚拟creator（ID以888开头）
                List<Creator> virtualCreatorsWithSameName = existingVirtualCreators.stream()
                    .filter(creator -> creator.getId() != null && creator.getId().toString().startsWith("888"))
                    .collect(Collectors.toList());
                
                if (!virtualCreatorsWithSameName.isEmpty())
                {
                    // 如果已存在同名虚拟上级，跳过创建并使用已存在的
                    Creator existingVirtual = virtualCreatorsWithSameName.get(0);
                    log.info("发现已存在同名虚拟上级：{}，ID：{}，跳过创建，复用现有虚拟上级", virtualNickname, existingVirtual.getId());
                    
                    // 建立组关系（如果还没有的话）
                    if (existingRelation == null)
                    {
                        log.info("为组 {} 建立与现有虚拟上级 {} 的关系", groupName, existingVirtual.getId());
                        groupCreatorRelationService.assignGroupParent(groupName, existingVirtual.getId(), updateBy);
                    }
                    skippedGroups++;
                    continue;
                }
                
                log.info("开始为组 {} 创建新的虚拟上级...", groupName);
                Long virtualId = createVirtualParentForGroup(groupName, virtualNickname, remark, updateBy);
                createdVirtualCreators.add(virtualId);
                log.info("✅ 成功为组 {} 创建虚拟上级，ID：{}，昵称：{}", groupName, virtualId, virtualNickname);
            }
            
            log.info("第二步完成，创建的虚拟上级数量：{}，跳过的组数量：{}", createdVirtualCreators.size(), skippedGroups);
            log.info("新创建的虚拟上级ID列表：{}", createdVirtualCreators);
            
            // 第三步：建立虚拟上级层级关系
            if (!createdVirtualCreators.isEmpty())
            {
                log.info("第三步：开始建立虚拟上级层级关系...");
                establishVirtualCreatorHierarchy(createdVirtualCreators, updateBy);
                log.info("第三步完成：虚拟上级层级关系建立完成");
            }
            else
            {
                log.info("第三步跳过：没有新创建的虚拟上级，无需建立层级关系");
            }
            
            // 第四步：自动重建关系
            log.info("第四步：开始自动重建关系...");
            Map<String, Object> rebuildResult = rebuildCreatorRelationships(updateBy);
            log.info("第四步完成：关系重建完成，结果：{}", rebuildResult);
            
            // 构建返回结果
            result.put("success", true);
            result.put("orphanGroups", orphanGroups.size());
            result.put("createdVirtualCreators", createdVirtualCreators.size());
            result.put("skippedGroups", skippedGroups);
            result.put("relationshipLevels", 3);
            result.put("rebuildResult", rebuildResult);
            result.put("virtualCreatorIds", createdVirtualCreators);
            
            log.info("=== 自动生成关系完成 ===");
            log.info("总结：孤儿组数量：{}，新创建虚拟上级：{}，跳过组数量：{}，重建关系：{}", 
                orphanGroups.size(), createdVirtualCreators.size(), skippedGroups, 
                rebuildResult.get("success"));
        }
        catch (Exception e)
        {
            log.error("=== 自动生成关系失败 ===", e);
            log.error("失败详情：操作人：{}，已处理孤儿组：{}，已创建虚拟上级：{}", 
                updateBy, orphanGroups.size(), createdVirtualCreators.size());
            result.put("success", false);
            result.put("error", e.getMessage());
            throw new ServiceException("自动生成关系失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 查找孤儿组（没有对应creator_id的组）
     */
    private List<String> findOrphanGroups()
    {
        List<String> orphanGroups = new ArrayList<>();
        
        // 查询所有组
        MonthlyPerformance queryParam = new MonthlyPerformance();
        List<MonthlyPerformance> allPerformances = monthlyPerformanceService.selectMonthlyPerformanceList(queryParam);
        
        // 获取所有不重复的组名
        Set<String> allGroups = allPerformances.stream()
                .map(MonthlyPerformance::getGroupName)
                .filter(groupName -> StringUtils.isNotEmpty(groupName))
                .collect(Collectors.toSet());
        
        // 检查每个组是否有对应的creator关系
        for (String groupName : allGroups)
        {
            GroupCreatorRelation relation = groupCreatorRelationService.selectGroupCreatorRelationByGroupName(groupName);
            if (relation == null || relation.getCreatorId() == null)
            {
                // 进一步检查creator是否存在
                if (relation != null && relation.getCreatorId() != null)
                {
                    Creator creator = creatorService.selectCreatorById(relation.getCreatorId());
                    if (creator == null)
                    {
                        orphanGroups.add(groupName);
                    }
                }
                else
                {
                    orphanGroups.add(groupName);
                }
            }
        }
        
        return orphanGroups;
    }
    
    /**
     * 建立虚拟上级层级关系
     * 将虚拟creator组织成3级层级结构
     */
    private void establishVirtualCreatorHierarchy(List<Long> virtualCreatorIds, String updateBy)
    {
        log.info("开始建立虚拟上级层级关系，虚拟creator数量：{}", virtualCreatorIds.size());
        log.info("待处理的虚拟creator ID列表：{}", virtualCreatorIds);
        
        if (virtualCreatorIds.size() <= 1)
        {
            // 只有一个或没有虚拟creator，无需建立层级
            log.info("虚拟creator数量 <= 1，无需建立层级关系");
            return;
        }
        
        // 按ID排序，确保稳定的层级结构
        List<Long> sortedIds = new ArrayList<>(virtualCreatorIds);
        Collections.sort(sortedIds);
        log.info("按ID排序后的虚拟creator列表：{}", sortedIds);
        
        // 分组策略：每2-3个为一组，形成树状结构
        int groupSize = Math.min(3, Math.max(2, sortedIds.size() / 3 + 1));
        log.info("计算的分组大小：{}", groupSize);
        
        List<List<Long>> groups = new ArrayList<>();
        for (int i = 0; i < sortedIds.size(); i += groupSize)
        {
            int endIndex = Math.min(i + groupSize, sortedIds.size());
            List<Long> group = sortedIds.subList(i, endIndex);
            groups.add(group);
            log.info("创建分组 {}：{}", groups.size(), group);
        }
        
        log.info("总共创建了 {} 个分组", groups.size());
        
        // 为每个组建立层级关系
        for (int groupIndex = 0; groupIndex < groups.size(); groupIndex++)
        {
            List<Long> group = groups.get(groupIndex);
            log.info("正在处理分组 {} (共{}个)：{}", groupIndex + 1, groups.size(), group);
            
            if (group.size() >= 2)
            {
                // 第一个作为顶级（2级）
                Long topLevel = group.get(0);
                log.info("分组 {} 的顶级虚拟creator：{}", groupIndex + 1, topLevel);
                
                // 其余的作为下级（3级）
                for (int i = 1; i < group.size(); i++)
                {
                    Long childId = group.get(i);
                    log.info("正在设置虚拟creator {} 的上级为 {}", childId, topLevel);
                    
                    Creator child = creatorService.selectCreatorById(childId);
                    if (child != null)
                    {
                        child.setParentId(topLevel);
                        child.setUpdateBy(updateBy);
                        creatorService.updateCreator(child);
                        log.info("✅ 成功设置虚拟creator {} (昵称: {}) 的上级为 {}", 
                            childId, child.getNickname(), topLevel);
                    }
                    else
                    {
                        log.error("❌ 无法找到虚拟creator：{}", childId);
                    }
                }
            }
            else
            {
                log.info("分组 {} 只有 {} 个虚拟creator，无需建立内部层级", groupIndex + 1, group.size());
            }
        }
        
        // 如果有多个组，将第一组的顶级设为所有其他组顶级的父级
        if (groups.size() > 1)
        {
            Long masterTopLevel = groups.get(0).get(0);
            log.info("多个分组情况，设置主顶级虚拟creator：{}", masterTopLevel);
            
            for (int i = 1; i < groups.size(); i++)
            {
                Long groupTopLevel = groups.get(i).get(0);
                log.info("正在设置分组 {} 顶级虚拟creator {} 的上级为主顶级 {}", 
                    i + 1, groupTopLevel, masterTopLevel);
                
                Creator groupTop = creatorService.selectCreatorById(groupTopLevel);
                if (groupTop != null)
                {
                    groupTop.setParentId(masterTopLevel);
                    groupTop.setUpdateBy(updateBy);
                    creatorService.updateCreator(groupTop);
                    log.info("✅ 成功设置分组 {} 顶级虚拟creator {} (昵称: {}) 的上级为主顶级 {}", 
                        i + 1, groupTopLevel, groupTop.getNickname(), masterTopLevel);
                }
                else
                {
                    log.error("❌ 无法找到分组顶级虚拟creator：{}", groupTopLevel);
                }
            }
        }
        else
        {
            log.info("只有一个分组，无需设置跨组层级关系");
        }
        
        log.info("✅ 虚拟上级层级关系建立完成");
    }

    /**
     * 解析Excel行数据
     */
    private Map<String, Object> parseRowData(Row row, String fileName) throws Exception
    {
        Map<String, Object> data = new HashMap<>();
        
        // 根据Excel列对应关系解析数据
        data.put("fileName", fileName);
        data.put("dataMonth", getCellStringValue(row.getCell(0)));
        data.put("creatorId", getCellStringValue(row.getCell(1)));
        data.put("nickname", getCellStringValue(row.getCell(2)));
        data.put("handle", getCellStringValue(row.getCell(3)));
        data.put("creatorNetworkManager", getCellStringValue(row.getCell(4)));
        data.put("groupName", getCellStringValue(row.getCell(5)));
        data.put("groupManager", getCellStringValue(row.getCell(6)));
        
        // 添加缺失的数值字段解析
        data.put("isViolative", getCellStringValue(row.getCell(7)));
        data.put("isRookie", getCellStringValue(row.getCell(8)));
        data.put("diamonds", getCellStringValue(row.getCell(9)));
        data.put("validDays", getCellStringValue(row.getCell(10)));
        data.put("liveDurationHours", getCellStringValue(row.getCell(11)));
        data.put("bonusEstimated", getCellStringValue(row.getCell(12)));
        data.put("bonusRookieM1Retention", getCellStringValue(row.getCell(13)));
        data.put("bonusRookieM2", getCellStringValue(row.getCell(14)));
        data.put("bonusRookieHalfMilestone", getCellStringValue(row.getCell(15)));
        data.put("bonusRookieM1", getCellStringValue(row.getCell(16)));
        data.put("bonusActiveness", getCellStringValue(row.getCell(17)));
        data.put("bonusRevenueScale", getCellStringValue(row.getCell(18)));
        data.put("bonusNewCreatorNetwork", getCellStringValue(row.getCell(19)));
        
        return data;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell)
    {
        if (cell == null) return "";
        
        switch (cell.getCellType())
        {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                double numericValue = cell.getNumericCellValue();
                if (numericValue == (long) numericValue)
                {
                    return String.valueOf((long) numericValue);
                }
                else
                {
                    return String.valueOf(numericValue);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return "";
        }
    }

    /**
     * 插入raw_imports数据
     */
    private boolean insertRawImport(Map<String, Object> rowData, String createBy)
    {
        // 简化实现，仅记录基础信息
        return true;
    }

    /**
     * 插入或更新creator数据（兼容旧版本）
     */
    private Creator insertOrUpdateCreator(Map<String, Object> rowData, String updateBy)
    {
        return insertOrUpdateCreator(rowData, updateBy, false);
    }

    /**
     * 插入或更新creator数据
     *
     * @param rowData 行数据
     * @param updateBy 操作人
     * @param useHistoricalCreateTime 是否使用历史创建时间
     *        - true: 新creator的创建时间设为数据月份，可能影响新人资格判定
     *        - false: 新creator的创建时间设为当前时间，不影响新人资格判定
     */
    private Creator insertOrUpdateCreator(Map<String, Object> rowData, String updateBy, boolean useHistoricalCreateTime)
    {
        try
        {
            String creatorIdStr = (String) rowData.get("creatorId");
            if (StringUtils.isEmpty(creatorIdStr)) return null;

            Long creatorId = Long.parseLong(creatorIdStr);
            String nickname = (String) rowData.get("nickname");
            String handle = (String) rowData.get("handle");

            // 调试日志：输出关键参数
            log.info("历史数据导入：处理creator {}，useHistoricalCreateTime = {}", creatorId, useHistoricalCreateTime);

            if (StringUtils.isEmpty(handle)) return null;

            Creator creator = creatorService.selectCreatorById(creatorId);
            if (creator == null)
            {
                // 新增creator时的创建时间处理策略
                creator = new Creator();
                creator.setId(creatorId);
                creator.setNickname(nickname);
                creator.setHandle(handle);
                creator.setCreateBy(updateBy);
                creator.setUpdateBy(updateBy);

                if (useHistoricalCreateTime)
                {
                    // 使用历史数据月份作为创建时间
                    String dataMonthStr = (String) rowData.get("dataMonth");
                    log.info("历史数据导入：准备使用历史时间策略，dataMonth = {}", dataMonthStr);

                    if (StringUtils.isNotEmpty(dataMonthStr))
                    {
                        try
                        {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                            Date historicalDate = sdf.parse(dataMonthStr);
                            creator.setCreateTime(historicalDate);
                            log.info("历史数据导入：为creator {} 设置创建时间为历史月份 {} -> {}",
                                creatorId, dataMonthStr, historicalDate);
                        }
                        catch (Exception e)
                        {
                            log.warn("解析历史月份失败，使用当前时间：{}", e.getMessage());
                            creator.setCreateTime(DateUtils.getNowDate());
                        }
                    }
                    else
                    {
                        log.warn("历史数据导入：dataMonth为空，使用当前时间");
                        creator.setCreateTime(DateUtils.getNowDate());
                    }
                }
                else
                {
                    // 默认策略：使用当前时间，避免影响新人资格判定
                    creator.setCreateTime(DateUtils.getNowDate());
                    log.info("历史数据导入：为creator {} 设置创建时间为当前时间，避免影响新人资格判定", creatorId);
                }

                // 调试：输出最终设置的创建时间
                log.info("历史数据导入：creator {} 最终的创建时间为：{}", creatorId, creator.getCreateTime());

                if (creatorService.insertCreator(creator) > 0)
                {
                    return creator;
                }
                return null;
            }
            else
            {
                // 更新现有creator
                creator.setNickname(nickname);
                creator.setUpdateBy(updateBy);

                // 如果使用历史时间策略，也更新创建时间
                if (useHistoricalCreateTime)
                {
                    String dataMonthStr = (String) rowData.get("dataMonth");
                    log.info("历史数据导入：更新现有creator {}，准备使用历史时间策略，dataMonth = {}", creatorId, dataMonthStr);

                    if (StringUtils.isNotEmpty(dataMonthStr))
                    {
                        try
                        {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                            Date historicalDate = sdf.parse(dataMonthStr);
                            creator.setCreateTime(historicalDate);
                            log.info("历史数据导入：更新现有creator {} 的创建时间为历史月份 {} -> {}",
                                creatorId, dataMonthStr, historicalDate);
                        }
                        catch (Exception e)
                        {
                            log.warn("解析历史月份失败，保持原有创建时间：{}", e.getMessage());
                        }
                    }
                    else
                    {
                        log.warn("历史数据导入：dataMonth为空，保持原有创建时间");
                    }
                }
                else
                {
                    log.info("历史数据导入：更新现有creator {}，使用默认策略，保持原有创建时间 {}",
                        creatorId, creator.getCreateTime());
                }

                if (creatorService.updateCreator(creator) > 0)
                {
                    return creator;
                }
                return null;
            }
        }
        catch (Exception e)
        {
            log.error("插入或更新creator失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 插入或更新monthly_performance数据
     */
    private boolean insertOrUpdateMonthlyPerformance(Map<String, Object> rowData, String updateBy)
    {
        try
        {
            String creatorIdStr = (String) rowData.get("creatorId");
            String dataMonthStr = (String) rowData.get("dataMonth");
            
            if (StringUtils.isEmpty(creatorIdStr) || StringUtils.isEmpty(dataMonthStr)) return false;
            
            Long creatorId = Long.parseLong(creatorIdStr);
            
            // 解析日期 (假设格式为 202505)
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            Date dataMonth = sdf.parse(dataMonthStr);
            
            // 检查记录是否已存在
            MonthlyPerformance existingPerformance = monthlyPerformanceService.selectMonthlyPerformanceByCreatorAndMonth(creatorId, dataMonth);
            
            MonthlyPerformance performance = existingPerformance != null ? existingPerformance : new MonthlyPerformance();
            
            // 设置基本信息
            performance.setCreatorId(creatorId);
            performance.setDataMonth(dataMonth);
            performance.setGroupName((String) rowData.get("groupName"));
            performance.setGroupManager((String) rowData.get("groupManager"));
            performance.setCreatorNetworkManager((String) rowData.get("creatorNetworkManager"));
            
            // 解析和设置数值字段
            performance.setIsViolative(parseBooleanToInteger((String) rowData.get("isViolative")));
            performance.setIsRookie(parseBooleanToInteger((String) rowData.get("isRookie")));
            performance.setDiamonds(parseLongValue((String) rowData.get("diamonds")));
            performance.setValidDays(parseIntegerValue((String) rowData.get("validDays")));
            performance.setLiveDurationHours(parseBigDecimalValue((String) rowData.get("liveDurationHours")));
            performance.setBonusEstimated(parseBigDecimalValue((String) rowData.get("bonusEstimated")));
            performance.setBonusRookieM1Retention((String) rowData.get("bonusRookieM1Retention"));
            performance.setBonusRookieM2(parseBigDecimalValue((String) rowData.get("bonusRookieM2")));
            performance.setBonusRookieHalfMilestone(parseBigDecimalValue((String) rowData.get("bonusRookieHalfMilestone")));
            performance.setBonusRookieM1(parseBigDecimalValue((String) rowData.get("bonusRookieM1")));
            performance.setBonusActiveness(parseBigDecimalValue((String) rowData.get("bonusActiveness")));
            performance.setBonusRevenueScale(parseBigDecimalValue((String) rowData.get("bonusRevenueScale")));
            performance.setBonusNewCreatorNetwork(parseBigDecimalValue((String) rowData.get("bonusNewCreatorNetwork")));
            
            if (existingPerformance != null)
            {
                // 更新现有记录
                performance.setUpdateBy(updateBy);
                return monthlyPerformanceService.updateMonthlyPerformance(performance) > 0;
            }
            else
            {
                // 插入新记录
                performance.setCreateBy(updateBy);
                performance.setUpdateBy(updateBy);
                return monthlyPerformanceService.insertMonthlyPerformance(performance) > 0;
            }
        }
        catch (Exception e)
        {
            return false;
        }
    }
    
    /**
     * 解析布尔值字符串为Integer（1=true, 0=false）
     */
    private Integer parseBooleanToInteger(String value)
    {
        if (StringUtils.isEmpty(value)) return 0;
        
        value = value.trim().toLowerCase();
        if ("true".equals(value) || "1".equals(value) || "是".equals(value) || "yes".equals(value))
        {
            return 1;
        }
        else if ("false".equals(value) || "0".equals(value) || "否".equals(value) || "no".equals(value))
        {
            return 0;
        }
        
        return 0; // 默认为false
    }
    
    /**
     * 解析Long值
     */
    private Long parseLongValue(String value)
    {
        if (StringUtils.isEmpty(value)) return 0L;
        
        try
        {
            // 移除可能的逗号分隔符
            value = value.replace(",", "").trim();
            return Long.parseLong(value);
        }
        catch (NumberFormatException e)
        {
            return 0L;
        }
    }
    
    /**
     * 解析Integer值
     */
    private Integer parseIntegerValue(String value)
    {
        if (StringUtils.isEmpty(value)) return 0;
        
        try
        {
            // 移除可能的逗号分隔符
            value = value.replace(",", "").trim();
            return Integer.parseInt(value);
        }
        catch (NumberFormatException e)
        {
            return 0;
        }
    }
    
    /**
     * 解析BigDecimal值
     */
    private BigDecimal parseBigDecimalValue(String value)
    {
        if (StringUtils.isEmpty(value)) return BigDecimal.ZERO;
        
        try
        {
            // 移除可能的逗号分隔符
            value = value.replace(",", "").trim();
            return new BigDecimal(value);
        }
        catch (NumberFormatException e)
        {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 独立的用户同步服务，用于避免AOP代理问题
     */
    @Component("historicalUserSyncService")
    public static class HistoricalUserSyncService {
        @Autowired
        private ISysUserService sysUserService;
        
        private static final Logger log = LoggerFactory.getLogger(HistoricalUserSyncService.class);
        
        @DataSource(DataSourceType.MASTER)
        @Transactional(propagation = Propagation.REQUIRES_NEW)
        public void synchronizeToRuoyiUserSystemWithMasterDB(List<Creator> creators, String operName) {
            log.info("=== 开始独立用户同步服务 ===");
            log.info("独立服务的数据源注解: @DataSource(DataSourceType.MASTER)");
            
            if (creators == null || creators.isEmpty()) {
                log.info("没有需要同步的主播数据到ruoyi用户系统");
                return;
            }

            log.info("开始同步 {} 个主播到ruoyi用户系统...", creators.size());
            int newUsersCount = 0;
            int updatedUsersCount = 0;
            int skippedUsersCount = 0;

            for (Creator creator : creators) {
                try {
                    log.info("准备同步主播: {} (ID: {})", creator.getHandle(), creator.getId());
                    boolean isNewUser = synchronizeToRuoyiUserSystem(creator, operName);
                    if (isNewUser) {
                        newUsersCount++;
                        log.info("✅ 新增用户同步成功：{}", creator.getHandle());
                    } else {
                        updatedUsersCount++;
                        log.info("✅ 更新用户同步成功：{}", creator.getHandle());
                    }
                } catch (Exception e) {
                    log.error("❌ 同步主播 '{}' (ID: {}) 到ruoyi用户系统失败: {}", creator.getHandle(), creator.getId(), e.getMessage(), e);
                    skippedUsersCount++;
                }
            }
            log.info("Ruoyi用户系统同步完成：新增 {} 个，更新 {} 个，跳过 {} 个。", newUsersCount, updatedUsersCount, skippedUsersCount);
            log.info("=== 独立用户同步服务结束 ===");
        }
        
        /**
         * 同步Creator数据到ruoyi用户系统
         * @param creator 主播信息
         * @param operName 操作人
         * @return 是否为新用户
         */
        private boolean synchronizeToRuoyiUserSystem(Creator creator, String operName) {
            // 构建用户名：使用Creator.id作为userName
            String userName = String.valueOf(creator.getId());
            
            log.info("开始查询用户是否存在: userName={}", userName);
            log.info("即将调用 sysUserService.selectUserByUserName");
            
            // 检查用户是否已存在
            SysUser existingUser;
            try {
                existingUser = sysUserService.selectUserByUserName(userName);
                log.info("用户查询完成: existingUser={}", existingUser != null ? "存在" : "不存在");
            } catch (Exception e) {
                log.error("查询用户失败: {}", e.getMessage());
                throw e;
            }
            
            SysUser user;
            boolean isNewUser = (existingUser == null);
            
            if (isNewUser) {
                log.info("创建新用户: {}", userName);
                user = new SysUser();
                user.setUserName(userName);
                // 设置默认值
                user.setDeptId(100L); // 设置默认部门ID，可根据实际情况调整
                user.setStatus("0"); // 正常状态
                user.setDelFlag("0"); // 未删除
                user.setSex("2"); // 未知性别
                
                // 直接设置默认密码，避免数据源切换复杂性
                String defaultPassword = "123456"; // 默认密码
                user.setPassword(SecurityUtils.encryptPassword(defaultPassword));
                user.setCreateBy(operName);
            } else {
                log.info("更新现有用户: {}", userName);
                user = existingUser;
                user.setUpdateBy(operName);
            }
            
            // 设置昵称：使用Creator.nickname
            user.setNickName(creator.getNickname());
            
            if (isNewUser) {
                sysUserService.insertUser(user);
                sysUserService.insertUserAuth(user.getUserId(), new Long[] {104L});
                log.debug("成功创建ruoyi用户：用户名={}, 昵称={}", userName, creator.getNickname());
            } else {
                sysUserService.updateUser(user);
                sysUserService.insertUserAuth(user.getUserId(), new Long[] {104L});
                log.debug("成功更新ruoyi用户：用户名={}, 昵称={}", userName, creator.getNickname());
            }
            
            return isNewUser;
        }
    }

    /**
     * 清理重复的虚拟上级
     * @param updateBy 更新者
     * @return 清理结果
     */
    @Transactional
    public Map<String, Object> cleanupDuplicateVirtualCreators(String updateBy)
    {
        Map<String, Object> result = new HashMap<>();
        int cleanedCreators = 0;
        List<String> cleanedCreatorIds = new ArrayList<>();
        
        try
        {
            // 查询所有虚拟creator（ID以888开头）
            Creator queryParam = new Creator();
            List<Creator> allCreators = creatorService.selectCreatorList(queryParam);
            
            List<Creator> virtualCreators = allCreators.stream()
                .filter(creator -> creator.getId() != null && creator.getId().toString().startsWith("888"))
                .collect(Collectors.toList());
            
            // 按昵称分组，找出重复的
            Map<String, List<Creator>> groupedByNickname = virtualCreators.stream()
                .collect(Collectors.groupingBy(Creator::getNickname));
            
            for (Map.Entry<String, List<Creator>> entry : groupedByNickname.entrySet())
            {
                String nickname = entry.getKey();
                List<Creator> duplicates = entry.getValue();
                
                if (duplicates.size() > 1)
                {
                    log.info("发现重复的虚拟上级：{}，数量：{}", nickname, duplicates.size());
                    
                    // 保留第一个（按ID排序），删除其他的
                    duplicates.sort(Comparator.comparing(Creator::getId));
                    Creator keepCreator = duplicates.get(0);
                    
                    for (int i = 1; i < duplicates.size(); i++)
                    {
                        Creator duplicateCreator = duplicates.get(i);
                        
                        // 检查是否有下级依赖
                        Creator childQuery = new Creator();
                        childQuery.setParentId(duplicateCreator.getId());
                        List<Creator> children = creatorService.selectCreatorList(childQuery);
                        
                        // 将下级转移到保留的creator
                        for (Creator child : children)
                        {
                            child.setParentId(keepCreator.getId());
                            child.setUpdateBy(updateBy);
                            creatorService.updateCreator(child);
                        }
                        
                        // 检查是否有组关系依赖
                        GroupCreatorRelation groupRelation = groupCreatorRelationService.selectGroupCreatorRelationByCreatorId(duplicateCreator.getId());
                        if (groupRelation != null)
                        {
                            // 转移组关系到保留的creator
                            groupRelation.setCreatorId(keepCreator.getId());
                            groupRelation.setUpdateBy(updateBy);
                            groupCreatorRelationService.updateGroupCreatorRelation(groupRelation);
                        }
                        
                        // 删除重复的虚拟creator
                        creatorService.deleteCreatorById(duplicateCreator.getId());
                        cleanedCreators++;
                        cleanedCreatorIds.add(duplicateCreator.getId().toString());
                        
                        log.info("已删除重复虚拟上级：{}，ID：{}，依赖已转移到ID：{}", 
                            nickname, duplicateCreator.getId(), keepCreator.getId());
                    }
                }
            }
            
            result.put("success", true);
            result.put("cleanedCreators", cleanedCreators);
            result.put("cleanedCreatorIds", cleanedCreatorIds);
            result.put("message", "清理完成，删除了 " + cleanedCreators + " 个重复的虚拟上级");
        }
        catch (Exception e)
        {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("清理重复虚拟上级失败", e);
        }
        
        return result;
    }
} 