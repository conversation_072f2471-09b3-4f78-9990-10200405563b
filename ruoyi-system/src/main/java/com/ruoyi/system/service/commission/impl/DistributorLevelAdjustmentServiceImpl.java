package com.ruoyi.system.service.commission.impl;

import java.util.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.service.commission.IDistributorLevelAdjustmentService;
import com.ruoyi.system.service.commission.ICommissionSettingsService;
import com.ruoyi.system.service.commission.ICommissionLevelRulesService;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.system.service.business.ICreatorRelationshipService;
import com.ruoyi.system.domain.commission.CommissionSettings;
import com.ruoyi.system.domain.commission.CommissionLevelRules;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.MonthlyPerformance;

/**
 * 分销员等级调整Service业务层处理
 * 实现"防守"体系的月度业绩考核与等级调整机制
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class DistributorLevelAdjustmentServiceImpl implements IDistributorLevelAdjustmentService
{
    private static final Logger log = LoggerFactory.getLogger(DistributorLevelAdjustmentServiceImpl.class);

    // 常量定义
    private static final String SYSTEM_LEVEL_ADJUSTMENT = "SYSTEM_LEVEL_ADJUSTMENT";
    private static final String SYSTEM_BATCH_ADJUSTMENT = "SYSTEM_BATCH_ADJUSTMENT";
    private static final String BATCH_LEVEL_ADJUSTMENT = "批量等级调整";
    private static final String DATE_FORMAT_PATTERN = "yyyy-MM";

    // 调整类型常量
    private static final String ADJUSTMENT_TYPE_PROTECTED = "PROTECTED";
    private static final String ADJUSTMENT_TYPE_DOWNGRADED = "DOWNGRADED";
    private static final String ADJUSTMENT_TYPE_MAINTAINED = "MAINTAINED";

    // 保护期类型常量
    private static final String PROTECTION_TYPE_NEW_USER = "NEW_USER_GRACE_PERIOD";
    private static final String PROTECTION_TYPE_UPGRADE = "UPGRADE_PROTECTION_PERIOD";
    
    @Autowired
    private ICommissionSettingsService commissionSettingsService;
    
    @Autowired
    private ICommissionLevelRulesService commissionLevelRulesService;
    
    @Autowired
    private ICreatorService creatorService;
    
    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;
    
    @Autowired
    private ICreatorRelationshipService creatorRelationshipService;
    
    /**
     * 执行月度等级调整任务
     * 作为"一键计算"流程中的前置任务二执行
     */
    @Override
    @Transactional
    public Map<String, Object> executeMonthlyLevelAdjustment(Date dataMonth) {
        SimpleDateFormat monthFormat = new SimpleDateFormat(DATE_FORMAT_PATTERN);
        String monthStr = monthFormat.format(dataMonth);
        log.info("=== 开始执行 {} 月度等级调整任务 ===", monthStr);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 加载配置参数
            Map<String, Object> config = getLevelAdjustmentConfig();
            
            // 获取所有有等级的分销员
            List<Creator> distributors = getDistributorsWithLevel();
            
            int totalProcessed = 0;
            int protectedCount = 0;
            int downgradedCount = 0;
            int maintainedCount = 0;
            List<Map<String, Object>> adjustmentDetails = new ArrayList<>();
            
            for (Creator distributor : distributors) {
                try {
                    Map<String, Object> adjustmentResult = processDistributorLevelAdjustment(
                        distributor, dataMonth, config);
                    
                    totalProcessed++;
                    String adjustmentType = (String) adjustmentResult.get("adjustmentType");
                    
                    switch (adjustmentType) {
                        case ADJUSTMENT_TYPE_PROTECTED:
                            protectedCount++;
                            break;
                        case ADJUSTMENT_TYPE_DOWNGRADED:
                            downgradedCount++;
                            break;
                        case ADJUSTMENT_TYPE_MAINTAINED:
                            maintainedCount++;
                            break;
                    }
                    
                    adjustmentDetails.add(adjustmentResult);
                    
                } catch (Exception e) {
                    log.error("处理分销员 {} 等级调整失败：{}", distributor.getId(), e.getMessage(), e);
                }
            }
            
            log.info("=== 完成 {} 月度等级调整任务 ===", monthStr);
            log.info("处理总数: {}, 保护期: {}, 降级: {}, 维持: {}", 
                totalProcessed, protectedCount, downgradedCount, maintainedCount);
            
            result.put("success", true);
            result.put("dataMonth", monthStr);
            result.put("totalProcessed", totalProcessed);
            result.put("protectedCount", protectedCount);
            result.put("downgradedCount", downgradedCount);
            result.put("maintainedCount", maintainedCount);
            result.put("adjustmentDetails", adjustmentDetails);
            
        } catch (Exception e) {
            log.error("执行月度等级调整任务失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "等级调整任务执行失败：" + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 处理单个分销员的等级调整
     */
    private Map<String, Object> processDistributorLevelAdjustment(Creator distributor, Date dataMonth, 
            Map<String, Object> config) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("creatorId", distributor.getId());
        result.put("nickname", distributor.getNickname());
        result.put("currentLevelId", distributor.getCurrentDistributorLevelId());
        
        // 1. 检查保护期
        Map<String, Object> protectionInfo = checkProtectionPeriod(distributor.getId(), dataMonth);
        boolean isProtected = (Boolean) protectionInfo.get("isProtected");
        
        if (isProtected) {
            result.put("adjustmentType", ADJUSTMENT_TYPE_PROTECTED);
            result.put("protectionInfo", protectionInfo);
            result.put("newLevelId", distributor.getCurrentDistributorLevelId());
            log.debug("分销员 {} 处于保护期，跳过等级考核", distributor.getId());
            return result;
        }
        
        // 2. 计算防守等级
        Integer defensiveLevel = calculateDefensiveLevel(distributor.getId(), dataMonth);
        
        if (defensiveLevel.equals(distributor.getCurrentDistributorLevelId())) {
            // 维持当前等级
            result.put("adjustmentType", ADJUSTMENT_TYPE_MAINTAINED);
            result.put("newLevelId", defensiveLevel);
        } else {
            // 需要降级
            String reason = String.format("月度业绩考核未达标，从等级%d降至等级%d",
                distributor.getCurrentDistributorLevelId(), defensiveLevel);

            Map<String, Object> downgradeResult = executeDowngrade(
                distributor.getId(), distributor.getCurrentDistributorLevelId(), reason);

            result.put("adjustmentType", ADJUSTMENT_TYPE_DOWNGRADED);
            result.put("newLevelId", defensiveLevel);
            result.put("downgradeResult", downgradeResult);
        }
        
        return result;
    }
    
    /**
     * 获取所有有等级的分销员
     */
    private List<Creator> getDistributorsWithLevel() {
        Creator query = new Creator();
        List<Creator> allCreators = creatorService.selectCreatorList(query);
        
        List<Creator> distributors = new ArrayList<>();
        for (Creator creator : allCreators) {
            if (creator.getCurrentDistributorLevelId() != null && creator.getCurrentDistributorLevelId() > 0) {
                distributors.add(creator);
            }
        }
        
        return distributors;
    }
    
    /**
     * 计算单个分销员的防守等级
     */
    @Override
    public Integer calculateDefensiveLevel(Long creatorId, Date dataMonth) {
        // 获取当前等级
        Creator creator = creatorService.selectCreatorById(creatorId);
        if (creator == null || creator.getCurrentDistributorLevelId() == null) {
            return null;
        }
        
        Integer currentLevelId = creator.getCurrentDistributorLevelId();
        
        // 计算月度环比增长率
        Map<String, Object> growthRateResult = calculateMonthlyGrowthRates(creatorId, dataMonth);
        Boolean passedAssessment = (Boolean) growthRateResult.get("passedAssessment");
        
        if (passedAssessment) {
            // 通过考核，维持当前等级
            return currentLevelId;
        } else {
            // 未通过考核，降级
            return getDowngradedLevel(currentLevelId);
        }
    }
    
    /**
     * 获取降级后的等级ID
     */
    private Integer getDowngradedLevel(Integer currentLevelId) {
        List<CommissionLevelRules> levelRules = commissionLevelRulesService.selectActiveCommissionLevelRules();

        // 按等级ID排序，找到当前等级的下一级
        levelRules.sort(Comparator.comparing(CommissionLevelRules::getId));

        for (int i = 0; i < levelRules.size(); i++) {
            if (levelRules.get(i).getId().equals(currentLevelId)) {
                if (i > 0) {
                    // 返回前一个等级（更低等级）
                    return levelRules.get(i - 1).getId();
                } else {
                    // 已经是最低等级，不再降级
                    return currentLevelId;
                }
            }
        }

        // 如果找不到当前等级，返回最低等级
        return levelRules.isEmpty() ? null : levelRules.get(0).getId();
    }

    /**
     * 检查分销员是否处于保护期
     */
    @Override
    public Map<String, Object> checkProtectionPeriod(Long creatorId, Date dataMonth) {
        Map<String, Object> result = new HashMap<>();
        result.put("isProtected", false);
        result.put("protectionType", null);
        result.put("remainingMonths", 0);

        Creator creator = creatorService.selectCreatorById(creatorId);
        if (creator == null) {
            return result;
        }

        // 获取配置参数
        Map<String, Object> config = getLevelAdjustmentConfig();
        int newUserGracePeriodMonths = (Integer) config.get("newUserGracePeriodMonths");
        int upgradeProtectionPeriodMonths = (Integer) config.get("upgradeProtectionPeriodMonths");

        // 检查新手保护期
        if (creator.getCreateTime() != null) {
            long monthsSinceCreation = getMonthsBetween(creator.getCreateTime(), dataMonth);
            if (monthsSinceCreation < newUserGracePeriodMonths) {
                result.put("isProtected", true);
                result.put("protectionType", PROTECTION_TYPE_NEW_USER);
                result.put("remainingMonths", newUserGracePeriodMonths - (int) monthsSinceCreation);
                return result;
            }
        }

        // 检查升级保护期
        if (creator.getLevelUpdatedAt() != null) {
            long monthsSinceUpgrade = getMonthsBetween(creator.getLevelUpdatedAt(), dataMonth);
            if (monthsSinceUpgrade < upgradeProtectionPeriodMonths) {
                result.put("isProtected", true);
                result.put("protectionType", PROTECTION_TYPE_UPGRADE);
                result.put("remainingMonths", upgradeProtectionPeriodMonths - (int) monthsSinceUpgrade);
                return result;
            }
        }

        return result;
    }

    /**
     * 计算两个日期之间的月数差
     */
    private long getMonthsBetween(Date startDate, Date endDate) {
        Calendar start = Calendar.getInstance();
        start.setTime(startDate);
        Calendar end = Calendar.getInstance();
        end.setTime(endDate);

        int yearDiff = end.get(Calendar.YEAR) - start.get(Calendar.YEAR);
        int monthDiff = end.get(Calendar.MONTH) - start.get(Calendar.MONTH);

        return yearDiff * 12L + monthDiff;
    }

    /**
     * 计算月度环比增长率
     */
    @Override
    public Map<String, Object> calculateMonthlyGrowthRates(Long creatorId, Date dataMonth) {
        Map<String, Object> result = new HashMap<>();

        // 获取配置参数
        Map<String, Object> config = getLevelAdjustmentConfig();
        BigDecimal targetGrowthRate = (BigDecimal) config.get("retentionTargetAvgGrowthRate");
        BigDecimal growthFromZero = (BigDecimal) config.get("retentionGrowthFromZero");
        BigDecimal growthRateCap = (BigDecimal) config.get("retentionGrowthRateCap");

        // 获取过去4个月的业绩数据（包括当前月）
        List<BigDecimal> monthlyPerformances = new ArrayList<>();
        List<BigDecimal> growthRates = new ArrayList<>();

        Calendar cal = Calendar.getInstance();
        cal.setTime(dataMonth);

        // 获取过去4个月的业绩数据
        for (int i = 3; i >= 0; i--) {
            cal.add(Calendar.MONTH, -i);
            Date monthToCheck = cal.getTime();
            cal.setTime(dataMonth); // 重置到基准月份

            BigDecimal performance = getTeamPerformanceForMonth(creatorId, monthToCheck);
            monthlyPerformances.add(performance);
        }

        // 计算月度环比增长率
        for (int i = 1; i < monthlyPerformances.size(); i++) {
            BigDecimal currentMonth = monthlyPerformances.get(i);
            BigDecimal previousMonth = monthlyPerformances.get(i - 1);

            BigDecimal growthRate = calculateGrowthRate(previousMonth, currentMonth, growthFromZero, growthRateCap);
            growthRates.add(growthRate);
        }

        // 计算平均增长率（最近3个月）
        BigDecimal avgGrowthRate = BigDecimal.ZERO;
        if (!growthRates.isEmpty()) {
            BigDecimal sum = growthRates.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            avgGrowthRate = sum.divide(BigDecimal.valueOf(growthRates.size()), 4, RoundingMode.HALF_UP);
        }

        // 判断是否通过考核
        boolean passedAssessment = avgGrowthRate.compareTo(targetGrowthRate) >= 0;

        result.put("monthlyPerformances", monthlyPerformances);
        result.put("growthRates", growthRates);
        result.put("avgGrowthRate", avgGrowthRate);
        result.put("targetGrowthRate", targetGrowthRate);
        result.put("passedAssessment", passedAssessment);

        return result;
    }

    /**
     * 获取指定月份的团队业绩
     * 包括个人钻石收入 + 直属一级（depth=1）团队钻石总收入
     */
    private BigDecimal getTeamPerformanceForMonth(Long creatorId, Date dataMonth) {
        // 获取个人业绩
        MonthlyPerformance personalPerformance = monthlyPerformanceService
            .selectMonthlyPerformanceByCreatorAndMonth(creatorId, dataMonth);

        BigDecimal personalDiamonds = BigDecimal.ZERO;
        if (personalPerformance != null && personalPerformance.getDiamonds() != null) {
            personalDiamonds = BigDecimal.valueOf(personalPerformance.getDiamonds());
        }

        // 获取直属下级业绩
        List<Long> directChildren = creatorRelationshipService.getDirectChildrenIds(creatorId);
        BigDecimal teamDiamonds = BigDecimal.ZERO;

        for (Long childId : directChildren) {
            MonthlyPerformance childPerformance = monthlyPerformanceService
                .selectMonthlyPerformanceByCreatorAndMonth(childId, dataMonth);

            if (childPerformance != null && childPerformance.getDiamonds() != null) {
                teamDiamonds = teamDiamonds.add(BigDecimal.valueOf(childPerformance.getDiamonds()));
            }
        }

        return personalDiamonds.add(teamDiamonds);
    }

    /**
     * 计算单月环比增长率，包含风险控制规则
     */
    private BigDecimal calculateGrowthRate(BigDecimal previousMonth, BigDecimal currentMonth,
            BigDecimal growthFromZero, BigDecimal growthRateCap) {

        // 处理"除以零"情况
        if (previousMonth.compareTo(BigDecimal.ZERO) == 0) {
            if (currentMonth.compareTo(BigDecimal.ZERO) > 0) {
                // 上月为0，本月不为0，按固定值计算
                return growthFromZero;
            } else {
                // 上月和本月都为0，增长率为0
                return BigDecimal.ZERO;
            }
        }

        // 正常计算增长率：(本月 - 上月) / 上月
        BigDecimal growthRate = currentMonth.subtract(previousMonth)
            .divide(previousMonth, 4, RoundingMode.HALF_UP);

        // 应用增长率上限
        if (growthRate.compareTo(growthRateCap) > 0) {
            return growthRateCap;
        }

        return growthRate;
    }

    /**
     * 获取分销员的团队业绩数据
     */
    @Override
    public Map<String, Object> getTeamPerformanceData(Long creatorId, Date dataMonth) {
        Map<String, Object> result = new HashMap<>();

        BigDecimal totalPerformance = getTeamPerformanceForMonth(creatorId, dataMonth);

        // 获取个人业绩
        MonthlyPerformance personalPerformance = monthlyPerformanceService
            .selectMonthlyPerformanceByCreatorAndMonth(creatorId, dataMonth);

        BigDecimal personalDiamonds = BigDecimal.ZERO;
        if (personalPerformance != null && personalPerformance.getDiamonds() != null) {
            personalDiamonds = BigDecimal.valueOf(personalPerformance.getDiamonds());
        }

        // 计算团队业绩（不包括个人）
        BigDecimal teamDiamonds = totalPerformance.subtract(personalDiamonds);

        result.put("personalDiamonds", personalDiamonds);
        result.put("teamDiamonds", teamDiamonds);
        result.put("totalPerformance", totalPerformance);

        SimpleDateFormat monthFormat = new SimpleDateFormat(DATE_FORMAT_PATTERN);
        result.put("dataMonth", monthFormat.format(dataMonth));

        return result;
    }

    /**
     * 执行等级降级操作
     */
    @Override
    @Transactional
    public Map<String, Object> executeDowngrade(Long creatorId, Integer currentLevelId, String reason) {
        Map<String, Object> result = new HashMap<>();

        try {
            Creator creator = creatorService.selectCreatorById(creatorId);
            if (creator == null) {
                result.put("success", false);
                result.put("message", "分销员不存在");
                return result;
            }

            Integer newLevelId = getDowngradedLevel(currentLevelId);

            // 更新分销员等级
            creator.setCurrentDistributorLevelId(newLevelId);
            creator.setLevelUpdatedAt(new Date());
            creator.setUpdateBy(SYSTEM_LEVEL_ADJUSTMENT);

            int updateResult = creatorService.updateCreator(creator);

            if (updateResult > 0) {
                result.put("success", true);
                result.put("oldLevelId", currentLevelId);
                result.put("newLevelId", newLevelId);
                result.put("reason", reason);

                log.info("分销员 {} 等级调整成功：{} -> {}，原因：{}",
                    creatorId, currentLevelId, newLevelId, reason);
            } else {
                result.put("success", false);
                result.put("message", "数据库更新失败");
            }

        } catch (Exception e) {
            log.error("执行等级降级失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "降级操作失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取等级调整的配置参数
     */
    @Override
    public Map<String, Object> getLevelAdjustmentConfig() {
        Map<String, Object> config = new HashMap<>();

        try {
            // 读取配置参数
            config.put("retentionTargetAvgGrowthRate",
                getConfigValue("retention_target_avg_growth_rate", "0.15", BigDecimal.class));
            config.put("retentionGrowthFromZero",
                getConfigValue("retention_growth_from_zero", "1.00", BigDecimal.class));
            config.put("retentionGrowthRateCap",
                getConfigValue("retention_growth_rate_cap", "3.00", BigDecimal.class));
            config.put("newUserGracePeriodMonths",
                getConfigValue("new_user_grace_period_months", "4", Integer.class));
            config.put("upgradeProtectionPeriodMonths",
                getConfigValue("upgrade_protection_period_months", "2", Integer.class));

        } catch (Exception e) {
            log.error("加载等级调整配置参数失败：{}", e.getMessage(), e);
            // 使用默认值
            config.put("retentionTargetAvgGrowthRate", new BigDecimal("0.15"));
            config.put("retentionGrowthFromZero", new BigDecimal("1.00"));
            config.put("retentionGrowthRateCap", new BigDecimal("3.00"));
            config.put("newUserGracePeriodMonths", 4);
            config.put("upgradeProtectionPeriodMonths", 2);
        }

        return config;
    }

    /**
     * 获取配置值并转换为指定类型
     */
    @SuppressWarnings("unchecked")
    private <T> T getConfigValue(String key, String defaultValue, Class<T> type) {
        CommissionSettings setting = commissionSettingsService.selectCommissionSettingsByKey(key);
        String value = (setting != null && setting.getSettingValue() != null)
            ? setting.getSettingValue() : defaultValue;

        if (type == BigDecimal.class) {
            return (T) new BigDecimal(value);
        } else if (type == Integer.class) {
            return (T) Integer.valueOf(value);
        } else if (type == String.class) {
            return (T) value;
        }

        throw new IllegalArgumentException("不支持的配置值类型：" + type.getName());
    }

    /**
     * 验证等级调整计算结果
     */
    @Override
    public Map<String, Object> validateLevelAdjustmentCalculation(Long creatorId, Date dataMonth) {
        Map<String, Object> result = new HashMap<>();

        try {
            Creator creator = creatorService.selectCreatorById(creatorId);
            if (creator == null) {
                result.put("error", "分销员不存在");
                return result;
            }

            result.put("creatorInfo", creator);
            result.put("protectionPeriod", checkProtectionPeriod(creatorId, dataMonth));
            result.put("growthRateAnalysis", calculateMonthlyGrowthRates(creatorId, dataMonth));
            result.put("teamPerformance", getTeamPerformanceData(creatorId, dataMonth));
            result.put("defensiveLevel", calculateDefensiveLevel(creatorId, dataMonth));
            result.put("config", getLevelAdjustmentConfig());

        } catch (Exception e) {
            log.error("验证等级调整计算失败：{}", e.getMessage(), e);
            result.put("error", "验证失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取月度等级调整统计报告
     */
    @Override
    public Map<String, Object> getMonthlyAdjustmentReport(Date dataMonth) {
        Map<String, Object> report = new HashMap<>();

        try {
            List<Creator> distributors = getDistributorsWithLevel();

            int totalDistributors = distributors.size();
            int protectedCount = 0;
            int maintainedCount = 0;
            int downgradedCount = 0;

            for (Creator distributor : distributors) {
                Map<String, Object> protectionInfo = checkProtectionPeriod(distributor.getId(), dataMonth);
                boolean isProtected = (Boolean) protectionInfo.get("isProtected");

                if (isProtected) {
                    protectedCount++;
                } else {
                    Integer defensiveLevel = calculateDefensiveLevel(distributor.getId(), dataMonth);
                    if (defensiveLevel.equals(distributor.getCurrentDistributorLevelId())) {
                        maintainedCount++;
                    } else {
                        downgradedCount++;
                    }
                }
            }

            SimpleDateFormat monthFormat = new SimpleDateFormat(DATE_FORMAT_PATTERN);
            report.put("dataMonth", monthFormat.format(dataMonth));
            report.put("totalDistributors", totalDistributors);
            report.put("protectedCount", protectedCount);
            report.put("maintainedCount", maintainedCount);
            report.put("downgradedCount", downgradedCount);
            report.put("protectedRate", totalDistributors > 0 ?
                BigDecimal.valueOf(protectedCount).divide(BigDecimal.valueOf(totalDistributors), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            report.put("downgradedRate", totalDistributors > 0 ?
                BigDecimal.valueOf(downgradedCount).divide(BigDecimal.valueOf(totalDistributors), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);

        } catch (Exception e) {
            log.error("生成月度等级调整统计报告失败：{}", e.getMessage(), e);
            report.put("error", "报告生成失败：" + e.getMessage());
        }

        return report;
    }

    /**
     * 批量更新分销员基础等级
     */
    @Override
    @Transactional
    public Map<String, Object> batchUpdateDistributorLevels(List<Map<String, Object>> adjustments) {
        Map<String, Object> result = new HashMap<>();

        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (Map<String, Object> adjustment : adjustments) {
            try {
                Long creatorId = Long.valueOf(adjustment.get("creatorId").toString());
                Integer newLevelId = Integer.valueOf(adjustment.get("newLevelId").toString());
                String reason = (String) adjustment.getOrDefault("reason", BATCH_LEVEL_ADJUSTMENT);

                Creator creator = creatorService.selectCreatorById(creatorId);
                if (creator != null) {
                    creator.setCurrentDistributorLevelId(newLevelId);
                    creator.setLevelUpdatedAt(new Date());
                    creator.setUpdateBy(SYSTEM_BATCH_ADJUSTMENT);

                    int updateResult = creatorService.updateCreator(creator);
                    if (updateResult > 0) {
                        successCount++;
                        log.debug("批量更新分销员 {} 等级成功：{} -> {}，原因：{}",
                            creatorId, creator.getCurrentDistributorLevelId(), newLevelId, reason);
                    } else {
                        failCount++;
                        errors.add("分销员 " + creatorId + " 更新失败");
                    }
                } else {
                    failCount++;
                    errors.add("分销员 " + creatorId + " 不存在");
                }

            } catch (Exception e) {
                failCount++;
                errors.add("处理调整记录失败：" + e.getMessage());
            }
        }

        result.put("totalProcessed", adjustments.size());
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);

        return result;
    }
}
