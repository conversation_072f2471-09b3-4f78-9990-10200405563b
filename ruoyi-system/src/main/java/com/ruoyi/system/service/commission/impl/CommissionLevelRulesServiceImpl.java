package com.ruoyi.system.service.commission.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionLevelRulesMapper;
import com.ruoyi.system.domain.commission.CommissionLevelRules;
import com.ruoyi.system.service.commission.ICommissionLevelRulesService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 分销员等级规则Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionLevelRulesServiceImpl implements ICommissionLevelRulesService 
{
    @Autowired
    private CommissionLevelRulesMapper commissionLevelRulesMapper;

    /**
     * 查询分销员等级规则
     * 
     * @param id 分销员等级规则主键
     * @return 分销员等级规则
     */
    @Override
    public CommissionLevelRules selectCommissionLevelRulesById(Integer id)
    {
        return commissionLevelRulesMapper.selectCommissionLevelRulesById(id);
    }

    /**
     * 查询分销员等级规则列表
     * 
     * @param commissionLevelRules 分销员等级规则
     * @return 分销员等级规则
     */
    @Override
    public List<CommissionLevelRules> selectCommissionLevelRulesList(CommissionLevelRules commissionLevelRules)
    {
        return commissionLevelRulesMapper.selectCommissionLevelRulesList(commissionLevelRules);
    }

    /**
     * 新增分销员等级规则
     * 
     * @param commissionLevelRules 分销员等级规则
     * @return 结果
     */
    @Override
    
    public int insertCommissionLevelRules(CommissionLevelRules commissionLevelRules)
    {
        return commissionLevelRulesMapper.insertCommissionLevelRules(commissionLevelRules);
    }

    /**
     * 修改分销员等级规则
     * 
     * @param commissionLevelRules 分销员等级规则
     * @return 结果
     */
    @Override
    
    public int updateCommissionLevelRules(CommissionLevelRules commissionLevelRules)
    {
        return commissionLevelRulesMapper.updateCommissionLevelRules(commissionLevelRules);
    }

    /**
     * 批量删除分销员等级规则
     * 
     * @param ids 需要删除的分销员等级规则主键
     * @return 结果
     */
    @Override
    
    public int deleteCommissionLevelRulesByIds(Integer[] ids)
    {
        return commissionLevelRulesMapper.deleteCommissionLevelRulesByIds(ids);
    }

    /**
     * 删除分销员等级规则信息
     * 
     * @param id 分销员等级规则主键
     * @return 结果
     */
    @Override
    
    public int deleteCommissionLevelRulesById(Integer id)
    {
        return commissionLevelRulesMapper.deleteCommissionLevelRulesById(id);
    }

    /**
     * 查询启用的分销员等级规则列表
     * 
     * @return 启用的分销员等级规则集合
     */
    @Override
    public List<CommissionLevelRules> selectActiveCommissionLevelRules()
    {
        return commissionLevelRulesMapper.selectActiveCommissionLevelRules();
    }
} 