package com.ruoyi.system.service.business;

import java.util.List;
import com.ruoyi.system.domain.business.RawImport;
import com.ruoyi.system.domain.business.Creator;
import org.springframework.web.multipart.MultipartFile;

/**
 * 原始Excel数据导入记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface IRawImportService
{
    /**
     * 查询原始Excel数据导入记录
     *
     * @param id 原始Excel数据导入记录主键
     * @return 原始Excel数据导入记录
     */
    public RawImport selectRawImportById(Long id);

    /**
     * 查询原始Excel数据导入记录列表
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 原始Excel数据导入记录集合
     */
    public List<RawImport> selectRawImportList(RawImport rawImport);

    /**
     * 新增原始Excel数据导入记录
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 结果
     */
    public int insertRawImport(RawImport rawImport);

    /**
     * 批量新增原始Excel数据导入记录
     *
     * @param rawImportList 原始Excel数据导入记录列表
     * @return 结果
     */
    public int batchInsertRawImport(List<RawImport> rawImportList);

    /**
     * 修改原始Excel数据导入记录
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 结果
     */
    public int updateRawImport(RawImport rawImport);

    /**
     * 批量删除原始Excel数据导入记录
     *
     * @param ids 需要删除的原始Excel数据导入记录主键集合
     * @return 结果
     */
    public int deleteRawImportByIds(Long[] ids);

    /**
     * 删除原始Excel数据导入记录信息
     *
     * @param id 原始Excel数据导入记录主键
     * @return 结果
     */
    public int deleteRawImportById(Long id);

    /**
     * 从Excel文件导入数据到raw_imports表
     *
     * @param file Excel文件
     * @param createBy 操作用户
     * @return 导入结果信息，例如成功导入N条，失败M条
     * @throws Exception 导入过程中发生的异常
     */
    public String importRawDataFromExcel(MultipartFile file, String createBy) throws Exception;


}
