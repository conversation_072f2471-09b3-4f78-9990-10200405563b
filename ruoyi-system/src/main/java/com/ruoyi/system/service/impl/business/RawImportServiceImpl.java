package com.ruoyi.system.service.impl.business;

import java.io.InputStream;
import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.mapper.business.RawImportMapper;
import com.ruoyi.system.domain.business.RawImport;
import com.ruoyi.system.service.business.IRawImportService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.business.ICreatorRelationshipService;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.math.BigDecimal;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.common.utils.SecurityUtils;
import java.util.ArrayList;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.springframework.stereotype.Component;

/**
 * 原始Excel数据导入记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class RawImportServiceImpl implements IRawImportService
{
    private static final Logger log = LoggerFactory.getLogger(RawImportServiceImpl.class);

    @Autowired
    private RawImportMapper rawImportMapper;

    @Autowired
    private ICreatorService creatorService;

    @Autowired
    private ICreatorRelationshipService creatorRelationshipService;

    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 查询原始Excel数据导入记录
     *
     * @param id 原始Excel数据导入记录主键
     * @return 原始Excel数据导入记录
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public RawImport selectRawImportById(Long id)
    {
        return rawImportMapper.selectRawImportById(id);
    }

    /**
     * 查询原始Excel数据导入记录列表
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 原始Excel数据导入记录
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<RawImport> selectRawImportList(RawImport rawImport)
    {
        return rawImportMapper.selectRawImportList(rawImport);
    }

    /**
     * 新增原始Excel数据导入记录
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    @Transactional
    public int insertRawImport(RawImport rawImport)
    {
        rawImport.setCreateTime(DateUtils.getNowDate());
        rawImport.setImportedAt(DateUtils.getNowDate());
        return rawImportMapper.insertRawImport(rawImport);
    }

    /**
     * 批量新增原始Excel数据导入记录
     *
     * @param rawImportList 原始Excel数据导入记录列表
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    @Transactional
    public int batchInsertRawImport(List<RawImport> rawImportList) {
        if (rawImportList == null || rawImportList.isEmpty()) {
            return 0;
        }
        for (RawImport rawImport : rawImportList) {
            rawImport.setCreateTime(DateUtils.getNowDate());
            if (rawImport.getImportedAt() == null) {
                rawImport.setImportedAt(DateUtils.getNowDate());
            }
        }
        return rawImportMapper.batchInsertRawImport(rawImportList);
    }

    /**
     * 修改原始Excel数据导入记录
     *
     * @param rawImport 原始Excel数据导入记录
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    @Transactional
    public int updateRawImport(RawImport rawImport)
    {
        rawImport.setUpdateTime(DateUtils.getNowDate());
        return rawImportMapper.updateRawImport(rawImport);
    }

    /**
     * 批量删除原始Excel数据导入记录
     *
     * @param ids 需要删除的原始Excel数据导入记录主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    @Transactional
    public int deleteRawImportByIds(Long[] ids)
    {
        return rawImportMapper.deleteRawImportByIds(ids);
    }

    /**
     * 删除原始Excel数据导入记录信息
     *
     * @param id 原始Excel数据导入记录主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    @Transactional
    public int deleteRawImportById(Long id)
    {
        return rawImportMapper.deleteRawImportById(id);
    }

    /**
     * 从Excel文件导入数据到raw_imports表
     *
     * @param file Excel文件
     * @param createBy 操作人
     * @return 结果
     */
    @Override
    // @Transactional(rollbackFor = Exception.class)
    public String importRawDataFromExcel(MultipartFile file, String createBy) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new Exception("导入文件不能为空");
        }
        String originalFilename = file.getOriginalFilename();
        InputStream inputStream = file.getInputStream();

        ExcelUtil<RawImport> util = new ExcelUtil<>(RawImport.class);
        List<RawImport> rawImportList = util.importExcel(inputStream);

        if (rawImportList == null || rawImportList.isEmpty()) {
            return "导入数据为空或格式不正确";
        }

        log.info("从 Excel 文件 '{}' 中读取到 {} 条记录。", originalFilename, rawImportList.size());
        int rowNum = 1;
        for (RawImport rawImport : rawImportList) {
            log.debug("Excel 行 [{}]: {}", rowNum++, rawImport.toString());
        }

        Date now = DateUtils.getNowDate();
        for (RawImport rawImport : rawImportList) {
            rawImport.setFileName(originalFilename);
            if (rawImport.getImportedAt() == null) {
                rawImport.setImportedAt(now);
            }
            rawImport.setCreateBy(createBy);
            rawImport.setCreateTime(now);
        }

        int insertedRawCount = this.batchInsertRawImport(rawImportList);
        log.info("成功向 raw_imports 表插入 {} 条原始数据", insertedRawCount);

        List<Creator> syncedCreators = synchronizeCreatorsAndUpdateHierarchy(rawImportList, createBy);

        synchronizeMonthlyPerformanceData(rawImportList, createBy);

        try {
            log.info("开始重建创作者层级关系表 (creator_relationships)...");
            creatorRelationshipService.rebuildCreatorRelationships();
            log.info("创作者层级关系表重建成功。");
        } catch (Exception e) {
            log.error("重建创作者层级关系表失败。", e);
            throw new Exception("重建创作者层级关系表失败", e);
        }

        // 在事务外同步到ruoyi用户系统，避免数据源冲突
        try {
            // 使用独立的服务Bean，确保@DataSource注解生效
            UserSyncService userSyncService = SpringUtils.getBean(UserSyncService.class);
            userSyncService.synchronizeToRuoyiUserSystemWithMasterDB(syncedCreators, createBy);
        } catch (Exception e) {
            log.error("同步用户到ruoyi系统失败，但不影响主要导入流程: {}", e.getMessage(), e);
        }

        return "数据导入和处理成功完成。";
    }



    /**
     * 同步Creator表并更新其层级关系中的parentId
     * @param rawImportList 从Excel解析的原始数据列表
     * @param operName 操作人
     * @return 成功同步的Creator列表
     */
    private List<Creator> synchronizeCreatorsAndUpdateHierarchy(List<RawImport> rawImportList, String operName) {
        log.info("开始同步 Creators 表并更新 Parent ID...");
        int newCreatorsCount = 0;
        int updatedCreatorsCount = 0;
        int skippedCount = 0;
        List<Creator> syncedCreators = new ArrayList<>();

        for (RawImport rawImport : rawImportList) {
            log.info("正在处理 Excel 行数据: {}", rawImport.toString());

            if (StringUtils.isBlank(rawImport.getHandle())) {
                log.warn("跳过记录：Handle 为空。数据: {}", rawImport);
                skippedCount++;
                continue;
            }

            if (StringUtils.isBlank(rawImport.getCreatorId())) {
                log.error("跳过记录：Creator ID 为空。Handle: {}, 数据: {}", rawImport.getHandle(), rawImport);
                skippedCount++;
                continue;
            }

            Long creatorIdFromExcel;
            try {
                creatorIdFromExcel = parseLong(rawImport.getCreatorId());
                if (creatorIdFromExcel == null) throw new NumberFormatException("Parsed to null");
            } catch (NumberFormatException e) {
                log.error("跳过记录：Creator ID '{}' 格式错误。Handle: {}, 数据: {}", rawImport.getCreatorId(), rawImport.getHandle(), rawImport, e);
                skippedCount++;
                continue;
            }

            Creator existingCreatorById = creatorService.selectCreatorById(creatorIdFromExcel);
            Creator existingCreatorByHandle = creatorService.selectCreatorByHandle(rawImport.getHandle());

            Creator creatorToSave;
            boolean isNewCreator = false;

            if (existingCreatorById != null) { // ID exists
                creatorToSave = existingCreatorById;
                if (existingCreatorByHandle != null && !existingCreatorById.getId().equals(existingCreatorByHandle.getId())) {
                    log.error("跳过记录：数据冲突，ID '{}' 和 Handle '{}' 指向不同用户。", creatorIdFromExcel, rawImport.getHandle());
                    skippedCount++;
                    continue;
                }
                if (!creatorToSave.getHandle().equals(rawImport.getHandle())) {
                    log.warn("数据警告：ID '{}' 的 Handle 将从 '{}' 更新为 '{}'。", creatorIdFromExcel, creatorToSave.getHandle(), rawImport.getHandle());
                    creatorToSave.setHandle(rawImport.getHandle());
                }
            } else if (existingCreatorByHandle != null) { // Handle exists but ID does not
                log.error("跳过记录：数据冲突，Handle '{}' 已存在，但其 ID 与 Excel 中的 ID '{}' 不符。", rawImport.getHandle(), creatorIdFromExcel);
                skippedCount++;
                continue;
            } else { // Entirely new user
                isNewCreator = true;
                creatorToSave = new Creator();
                creatorToSave.setId(creatorIdFromExcel);
                creatorToSave.setHandle(rawImport.getHandle());
            }

            if (StringUtils.isNotBlank(rawImport.getCreatorNickname())) {
                creatorToSave.setNickname(rawImport.getCreatorNickname());
            } else if (isNewCreator) {
                creatorToSave.setNickname(rawImport.getHandle());
            }

            if (StringUtils.isNotBlank(rawImport.getCreatorNetworkManager())) {
                // 解析 "上级名称-上级ID" 格式的字段，例如 "Manager_L0-7500000000000000000"
                String managerInfo = rawImport.getCreatorNetworkManager().trim();
                Long parentId = parseParentIdFromManagerInfo(managerInfo);
                
                if (parentId != null) {
                    // 验证上级ID是否存在
                    Creator manager = creatorService.selectCreatorById(parentId);
                    if (manager != null) {
                        creatorToSave.setParentId(parentId);
                        log.debug("为主播 '{}' 设置上级ID: {}", rawImport.getHandle(), parentId);
                    } else {
                        log.warn("上级ID '{}' 在数据库中不存在，主播 '{}' 的 ParentID 将设为 0。管理器信息: '{}'", 
                                parentId, rawImport.getHandle(), managerInfo);
                        if (isNewCreator) creatorToSave.setParentId(0L);
                    }
                } else {
                    log.warn("无法从管理器信息 '{}' 中解析出上级ID，主播 '{}' 的 ParentID 将设为 0。", 
                            managerInfo, rawImport.getHandle());
                    if (isNewCreator) creatorToSave.setParentId(0L);
                }
            } else if (isNewCreator) {
                creatorToSave.setParentId(0L);
            }

            try {
                if (isNewCreator) {
                    creatorToSave.setCreateBy(operName);
                    creatorService.insertCreator(creatorToSave);
                    newCreatorsCount++;
                } else {
                    creatorToSave.setUpdateBy(operName);
                    creatorService.updateCreator(creatorToSave);
                    updatedCreatorsCount++;
                }

                // 添加到成功同步的列表中
                syncedCreators.add(creatorToSave);

            } catch (Exception e) {
                log.error("保存主播 '{}' (ID: {}) 到数据库时失败: {}", creatorToSave.getHandle(), creatorToSave.getId(), e.getMessage(), e);
                skippedCount++;
            }
        }
        log.info("Creators 表同步完成：新增 {} 个，更新 {} 个，跳过 {} 个。", newCreatorsCount, updatedCreatorsCount, skippedCount);
        return syncedCreators;
    }



    /**
     * 同步月度业绩数据
     * @param rawImportList 从Excel解析的原始数据列表
     * @param operName 操作人
     */
    private void synchronizeMonthlyPerformanceData(List<RawImport> rawImportList, String operName) {
        log.info("开始同步 MonthlyPerformance 表...");
        int synchronizedCount = 0;
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyyMM"); // 修改格式以匹配202504这样的格式

        for (RawImport rawImport : rawImportList) {
            try {
                if (StringUtils.isBlank(rawImport.getHandle()) || StringUtils.isBlank(rawImport.getDataMonth())) {
                    log.warn("Handle 或 DataMonth 为空，跳过月度业绩同步: {}", rawImport);
                    continue;
                }

                Creator creator = creatorService.selectCreatorByHandle(rawImport.getHandle());
                if (creator == null) {
                    log.warn("在 Creators 表中未找到 Handle 为 '{}' 的主播，无法同步其月度业绩。", rawImport.getHandle());
                    continue;
                }

                Date monthDate = monthFormat.parse(rawImport.getDataMonth());

                MonthlyPerformance queryParam = new MonthlyPerformance();
                queryParam.setCreatorId(creator.getId());
                queryParam.setDataMonth(monthDate);
                List<MonthlyPerformance> mpList = monthlyPerformanceService.selectMonthlyPerformanceList(queryParam);

                MonthlyPerformance mp = null;
                if (mpList != null && !mpList.isEmpty()) {
                    mp = mpList.get(0);
                }

                boolean isNewPerformance = (mp == null);
                if (isNewPerformance) {
                    mp = new MonthlyPerformance();
                    mp.setCreatorId(creator.getId());
                    mp.setDataMonth(monthDate);
                }

                mp.setGroupName(rawImport.getGroupName());
                mp.setGroupManager(rawImport.getGroupManager());

                mp.setIsViolative(parseBooleanString(rawImport.getIsViolativeCreators()));
                mp.setIsRookie(parseBooleanString(rawImport.getTheCreatorWasRookie()));

                mp.setDiamonds(parseLong(rawImport.getDiamonds()));
                mp.setValidDays(parseInteger(rawImport.getValidDays()));
                mp.setLiveDurationHours(parseBigDecimal(rawImport.getLiveDurationH()));
                mp.setBonusEstimated(parseBigDecimal(rawImport.getEstimatedBonus()));
                mp.setBonusRookieM1Retention(rawImport.getEstBonusRookieM1Retention());
                mp.setBonusRookieM2(parseBigDecimal(rawImport.getEstBonusRookieM2()));
                mp.setBonusRookieHalfMilestone(parseBigDecimal(rawImport.getEstBonusRookieHalfMilestone()));
                mp.setBonusRookieM1(parseBigDecimal(rawImport.getEstBonusRookieM1()));
                mp.setBonusActiveness(parseBigDecimal(rawImport.getEstBonusActivenessTask()));
                mp.setBonusRevenueScale(parseBigDecimal(rawImport.getEstBonusRevenueScaleTask()));
                mp.setBonusNewCreatorNetwork(parseBigDecimal(rawImport.getEstBonusNewCreatorNetworkTask()));

                if (isNewPerformance) {
                    mp.setCreateBy(operName);
                } else {
                    mp.setUpdateBy(operName);
                }

                monthlyPerformanceService.saveOrUpdateMonthlyPerformance(mp);
                synchronizedCount++;
            } catch (ParseException e) {
                log.error("解析数据月份 '{}' 失败，主播 Handle: {}，跳过此条月度业绩同步。", rawImport.getDataMonth(), rawImport.getHandle(), e);
            } catch (Exception e) {
                log.error("同步主播 '{}' 的月度业绩数据失败: {}", rawImport.getHandle(), e.getMessage(), e);
            }
        }
        log.info("MonthlyPerformance 表同步完成：处理 {} 条记录。", synchronizedCount);
    }

    private Long parseLong(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            String cleanedValue = value.trim().replaceAll("[^\\d.-]", "");
            if (StringUtils.isBlank(cleanedValue)) return null;
            return Long.parseLong(cleanedValue);
        } catch (NumberFormatException e) {
            log.warn("无法将字符串 '{}' 解析为 Long。", value, e);
            return null;
        }
    }

    private BigDecimal parseBigDecimal(String value) {
        if (StringUtils.isBlank(value)) {
            return null; 
        }
        String trimmedValue = value.trim();
        
        // 处理特殊文本值
        if ("Did not participate".equalsIgnoreCase(trimmedValue) || 
            "No".equalsIgnoreCase(trimmedValue) ||
            "N/A".equalsIgnoreCase(trimmedValue)) {
            return BigDecimal.ZERO; // 或者返回null，根据业务需求
        }
        
        try {
            return new BigDecimal(trimmedValue);
        } catch (NumberFormatException e) {
            log.warn("无法将字符串 '{}' 解析为 BigDecimal，使用默认值 0。", value);
            return BigDecimal.ZERO;
        }
    }

    private Integer parseInteger(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            log.warn("无法将字符串 '{}' 解析为 Integer。", value, e);
            return null;
        }
    }

    private Integer parseBooleanString(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        String trimmedValue = value.trim();
        
        // 处理中文布尔值
        if ("是".equals(trimmedValue)) {
            return 1;
        } else if ("否".equals(trimmedValue)) {
            return 0;
        }
        
        // 处理英文布尔值
        if ("Yes".equalsIgnoreCase(trimmedValue) || "True".equalsIgnoreCase(trimmedValue)) {
            return 1;
        } else if ("No".equalsIgnoreCase(trimmedValue) || "False".equalsIgnoreCase(trimmedValue)) {
            return 0;
        }
        
        // 处理特殊值 - 如果是人名或其他文本，可能表示特殊状态
        if (trimmedValue.length() > 0 && !trimmedValue.matches("\\d+")) {
            // 非数字文本，可能是人名等，根据业务逻辑处理
            log.debug("检测到非标准布尔值 '{}', 可能是人名或特殊标识，设为 false", value);
            return 0; // 或根据业务需求设为其他值
        }
        
        log.warn("无法识别的布尔字符串值: '{}'，使用默认值 false.", value);
        return 0;
    }

    /**
     * 从管理器信息中解析出上级ID
     * 格式: "上级名称-上级ID"，例如 "Manager_L0-7500000000000000000"
     * @param managerInfo 管理器信息字符串
     * @return 上级ID，如果解析失败返回null
     */
    private Long parseParentIdFromManagerInfo(String managerInfo) {
        if (StringUtils.isBlank(managerInfo)) {
            return null;
        }
        
        try {
            // 查找最后一个连字符的位置
            int lastDashIndex = managerInfo.lastIndexOf('-');
            if (lastDashIndex == -1 || lastDashIndex == managerInfo.length() - 1) {
                log.warn("管理器信息格式不正确，未找到连字符或连字符在末尾: '{}'", managerInfo);
                return null;
            }
            
            // 提取连字符后的ID部分
            String idPart = managerInfo.substring(lastDashIndex + 1).trim();
            if (StringUtils.isBlank(idPart)) {
                log.warn("管理器信息中ID部分为空: '{}'", managerInfo);
                return null;
            }
            
            return Long.parseLong(idPart);
        } catch (NumberFormatException e) {
            log.warn("无法从管理器信息 '{}' 中解析出有效的ID", managerInfo, e);
            return null;
        }
    }

    /**
     * 独立的用户同步服务，用于避免AOP代理问题
     */
    @Component
    public static class UserSyncService {
        @Autowired
        private ISysUserService sysUserService;
        
        private static final Logger log = LoggerFactory.getLogger(UserSyncService.class);
        
        @DataSource(DataSourceType.MASTER)
        public void synchronizeToRuoyiUserSystemWithMasterDB(List<Creator> creators, String operName) {
            log.info("=== 开始独立用户同步服务 ===");
            log.info("独立服务的数据源注解: @DataSource(DataSourceType.MASTER)");
            
            if (creators == null || creators.isEmpty()) {
                log.info("没有需要同步的主播数据到ruoyi用户系统");
                return;
            }

            log.info("开始同步 {} 个主播到ruoyi用户系统...", creators.size());
            int newUsersCount = 0;
            int updatedUsersCount = 0;
            int skippedUsersCount = 0;

            for (Creator creator : creators) {
                try {
                    log.info("准备同步主播: {} (ID: {})", creator.getHandle(), creator.getId());
                    boolean isNewUser = synchronizeToRuoyiUserSystem(creator, operName);
                    if (isNewUser) {
                        newUsersCount++;
                    } else {
                        updatedUsersCount++;
                    }
                } catch (Exception e) {
                    log.error("同步主播 '{}' (ID: {}) 到ruoyi用户系统失败: {}", creator.getHandle(), creator.getId(), e.getMessage(), e);
                    skippedUsersCount++;
                }
            }
            log.info("Ruoyi用户系统同步完成：新增 {} 个，更新 {} 个，跳过 {} 个。", newUsersCount, updatedUsersCount, skippedUsersCount);
            log.info("=== 独立用户同步服务结束 ===");
        }
        
        /**
         * 同步Creator数据到ruoyi用户系统
         * @param creator 主播信息
         * @param operName 操作人
         * @return 是否为新用户
         */
        private boolean synchronizeToRuoyiUserSystem(Creator creator, String operName) {
            // 构建用户名：使用Creator.id作为userName
            String userName = String.valueOf(creator.getId());
            
            log.info("开始查询用户是否存在: userName={}", userName);
            log.info("即将调用 sysUserService.selectUserByUserName");
            
            // 检查用户是否已存在
            SysUser existingUser;
            try {
                existingUser = sysUserService.selectUserByUserName(userName);
                log.info("用户查询完成: existingUser={}", existingUser != null ? "存在" : "不存在");
            } catch (Exception e) {
                log.error("查询用户失败: {}", e.getMessage());
                throw e;
            }
            
            SysUser user;
            boolean isNewUser = (existingUser == null);
            
            if (isNewUser) {
                log.info("创建新用户: {}", userName);
                user = new SysUser();
                user.setUserName(userName);
                // 设置默认值
                user.setDeptId(100L); // 设置默认部门ID，可根据实际情况调整
                user.setStatus("0"); // 正常状态
                user.setDelFlag("0"); // 未删除
                user.setSex("2"); // 未知性别
                
                // 直接设置默认密码，避免数据源切换复杂性
                String defaultPassword = "123456"; // 默认密码
                user.setPassword(SecurityUtils.encryptPassword(defaultPassword));
                user.setCreateBy(operName);
            } else {
                log.info("更新现有用户: {}", userName);
                user = existingUser;
                user.setUpdateBy(operName);
            }
            
            // 设置昵称：使用Creator.nickname
            user.setNickName(creator.getNickname());
            
            if (isNewUser) {
                sysUserService.insertUser(user);
                sysUserService.insertUserAuth(user.getUserId(), new Long[] {104L});
                log.debug("成功创建ruoyi用户：用户名={}, 昵称={}", userName, creator.getNickname());
            } else {
                sysUserService.updateUser(user);
                sysUserService.insertUserAuth(user.getUserId(), new Long[] {104L});
                log.debug("成功更新ruoyi用户：用户名={}, 昵称={}", userName, creator.getNickname());
            }
            
            return isNewUser;
        }
    }
}
