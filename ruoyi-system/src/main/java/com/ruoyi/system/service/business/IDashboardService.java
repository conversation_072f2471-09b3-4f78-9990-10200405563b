package com.ruoyi.system.service.business;

import com.ruoyi.system.domain.dto.DashboardResponse;

/**
 * 个人主仪表盘Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDashboardService
{
    /**
     * 获取指定分销员在指定月份的完整仪表盘聚合数据
     * 
     * @param creatorId 分销员ID
     * @param month 月份，格式为 YYYY-MM
     * @return 仪表盘数据
     */
    DashboardResponse getDashboardData(Long creatorId, String month);
}
