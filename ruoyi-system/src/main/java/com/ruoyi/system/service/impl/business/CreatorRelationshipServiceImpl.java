package com.ruoyi.system.service.impl.business;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.CreatorRelationship;
import com.ruoyi.system.mapper.business.CreatorMapper;
import com.ruoyi.system.mapper.business.CreatorRelationshipMapper;
import com.ruoyi.system.service.business.ICreatorRelationshipService;

/**
 * 主播层级关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CreatorRelationshipServiceImpl implements ICreatorRelationshipService
{
    private static final Logger log = LoggerFactory.getLogger(CreatorRelationshipServiceImpl.class);

    @Autowired
    private CreatorRelationshipMapper creatorRelationshipMapper;

    @Autowired
    private CreatorMapper creatorMapper;

    /**
     * 查询主播层级关系
     *
     * @param ancestorId 祖先ID
     * @param descendantId 后代ID
     * @return 主播层级关系
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public CreatorRelationship selectCreatorRelationshipById(Long ancestorId, Long descendantId)
    {
        return creatorRelationshipMapper.selectCreatorRelationshipById(ancestorId, descendantId);
    }

    /**
     * 查询主播层级关系列表
     *
     * @param creatorRelationship 主播层级关系
     * @return 主播层级关系
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<CreatorRelationship> selectCreatorRelationshipList(CreatorRelationship creatorRelationship)
    {
        return creatorRelationshipMapper.selectCreatorRelationshipList(creatorRelationship);
    }

    /**
     * 新增主播层级关系
     *
     * @param creatorRelationship 主播层级关系
     * @return 结果
     */
    @Override
    public int insertCreatorRelationship(CreatorRelationship creatorRelationship)
    {
        return creatorRelationshipMapper.insertCreatorRelationship(creatorRelationship);
    }

    /**
     * 删除主播层级关系信息
     *
     * @param ancestorId 祖先ID
     * @param descendantId 后代ID
     * @return 结果
     */
    @Override
    public int deleteCreatorRelationshipById(Long ancestorId, Long descendantId)
    {
        return creatorRelationshipMapper.deleteCreatorRelationshipById(ancestorId, descendantId);
    }

    /**
     * 重建主播层级关系表
     * 1. 清空表
     * 2. 获取所有主播及其父级ID
     * 3. 插入所有主播的自身关系 (depth=0)
     * 4. 迭代插入父子关系 (depth=1)
     * 5. 迭代扩展层级关系 (depth > 1)
     */
    @Override
    @Transactional
    public void rebuildCreatorRelationships() {
        log.info("开始重建主播层级关系表...");

        // 1. 清空表
        creatorRelationshipMapper.deleteAllCreatorRelationships();
        log.info("已清空 creator_relationships 表。");

        // 2. 获取所有主播及其父级ID
        List<Creator> allCreators = creatorMapper.selectCreatorList(new Creator());
        if (allCreators.isEmpty()) {
            log.info("系统中没有主播数据，无需重建层级关系。");
            return;
        }

        List<CreatorRelationship> relationshipsToInsert = new ArrayList<>();

        // 3. 插入所有主播的自身关系 (depth=0)
        for (Creator creator : allCreators) {
            CreatorRelationship selfLoop = new CreatorRelationship();
            selfLoop.setAncestorId(creator.getId());
            selfLoop.setDescendantId(creator.getId());
            selfLoop.setDepth(0);
            relationshipsToInsert.add(selfLoop);
        }
        if (!relationshipsToInsert.isEmpty()) {
            creatorRelationshipMapper.batchInsertCreatorRelationship(relationshipsToInsert);
            log.info("已插入 {} 条自身层级关系 (depth=0)。", relationshipsToInsert.size());
            relationshipsToInsert.clear();
        }


        // 4. 构建直接父子关系 (depth=1)
        Map<Long, Long> childToParentMap = new HashMap<>();
        for (Creator creator : allCreators) {
            if (creator.getParentId() != null && creator.getParentId() > 0) {
                childToParentMap.put(creator.getId(), creator.getParentId());

                CreatorRelationship directLink = new CreatorRelationship();
                directLink.setAncestorId(creator.getParentId());
                directLink.setDescendantId(creator.getId());
                directLink.setDepth(1);
                relationshipsToInsert.add(directLink);
            }
        }
        if (!relationshipsToInsert.isEmpty()) {
             // 去重，以防万一有重复的父子关系数据（理论上不应该）
            List<CreatorRelationship> distinctDirectLinks = relationshipsToInsert.stream().distinct().collect(Collectors.toList());
            creatorRelationshipMapper.batchInsertCreatorRelationship(distinctDirectLinks);
            log.info("已插入 {} 条直接父子层级关系 (depth=1)。", distinctDirectLinks.size());
        }

        // 5. 迭代扩展层级关系 (depth > 1)
        // 使用一个循环，每次循环都尝试找到新的间接关系
        // A -> B (depth N), B -> C (depth 1)  => A -> C (depth N+1)
        int currentDepth = 1;
        while (true) {
            List<CreatorRelationship> newFoundRelationships = new ArrayList<>();
            // 获取当前深度的所有关系
            CreatorRelationship queryForDepthN = new CreatorRelationship();
            queryForDepthN.setDepth(currentDepth);
            List<CreatorRelationship> depthNRelationships = creatorRelationshipMapper.selectCreatorRelationshipList(queryForDepthN);

            if (depthNRelationships.isEmpty()) {
                log.info("在深度 {} 未找到关系，迭代终止。", currentDepth);
                break; // 没有上一层的关系，无法扩展
            }

            // 获取所有深度为1的关系
            CreatorRelationship queryForDepth1 = new CreatorRelationship();
            queryForDepth1.setDepth(1);
            List<CreatorRelationship> depth1Relationships = creatorRelationshipMapper.selectCreatorRelationshipList(queryForDepth1);


            for (CreatorRelationship relN : depthNRelationships) { // A -> B (depth N)
                for (CreatorRelationship rel1 : depth1Relationships) { // B' -> C (depth 1)
                    if (relN.getDescendantId().equals(rel1.getAncestorId())) { // B == B'
                        // 找到了 A -> B -> C 的路径
                        Long ancestorA = relN.getAncestorId();
                        Long descendantC = rel1.getDescendantId();
                        int newDepth = relN.getDepth() + rel1.getDepth();

                        // 检查此关系是否已存在
                        if (creatorRelationshipMapper.selectCreatorRelationshipById(ancestorA, descendantC) == null) {
                            CreatorRelationship indirectLink = new CreatorRelationship();
                            indirectLink.setAncestorId(ancestorA);
                            indirectLink.setDescendantId(descendantC);
                            indirectLink.setDepth(newDepth);
                            newFoundRelationships.add(indirectLink);
                        }
                    }
                }
            }

            if (newFoundRelationships.isEmpty()) {
                log.info("在当前迭代未发现新的间接层级关系 (基于深度 {})，重建完成。", currentDepth);
                break; // 没有新的关系可以添加了
            }
            
            // 去重并批量插入新发现的关系
            List<CreatorRelationship> distinctNewFoundRelationships = newFoundRelationships.stream().distinct().collect(Collectors.toList());
            creatorRelationshipMapper.batchInsertCreatorRelationship(distinctNewFoundRelationships);
            log.info("在深度 {} 扩展，新插入 {} 条间接层级关系。", currentDepth + 1, distinctNewFoundRelationships.size());
            
            currentDepth++;
            if (currentDepth > 20) { // 防止死循环，设定一个最大深度阈值
                log.warn("层级关系重建达到最大迭代深度 {}，可能存在循环引用或层级过深。", currentDepth);
                break;
            }
        }
        log.info("主播层级关系表重建完成。");
    }


    /**
     * 查询指定主播的所有后代（可指定深度范围）
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<CreatorRelationship> getDescendants(Long ancestorId, Integer minDepth, Integer maxDepth) {
        CreatorRelationship query = new CreatorRelationship();
        query.setAncestorId(ancestorId);
        // Mapper XML 中 depth > 0 默认排除自己
        // 如果 minDepth 和 maxDepth 都为null, 则查询所有后代 (depth > 0)
        // 如果 minDepth 不为null, 则 depth >= minDepth
        // 如果 maxDepth 不为null, 则 depth <= maxDepth

        List<CreatorRelationship> allDescendants = creatorRelationshipMapper.selectCreatorRelationshipList(query);
        return allDescendants.stream()
                .filter(r -> r.getDepth() > 0) // 确保是后代，而不是自己
                .filter(r -> (minDepth == null || r.getDepth() >= minDepth))
                .filter(r -> (maxDepth == null || r.getDepth() <= maxDepth))
                .collect(Collectors.toList());
    }

    /**
     * 查询指定主播的所有祖先（可指定深度范围）
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<CreatorRelationship> getAncestors(Long descendantId, Integer minDepth, Integer maxDepth) {
        CreatorRelationship query = new CreatorRelationship();
        query.setDescendantId(descendantId);
        // Mapper XML 中 depth > 0 默认排除自己
        List<CreatorRelationship> allAncestors = creatorRelationshipMapper.selectCreatorRelationshipList(query);
        return allAncestors.stream()
                .filter(r -> r.getDepth() > 0) // 确保是祖先，而不是自己
                .filter(r -> (minDepth == null || r.getDepth() >= minDepth))
                .filter(r -> (maxDepth == null || r.getDepth() <= maxDepth))
                .collect(Collectors.toList());
    }

    /**
     * 查询指定主播的直接上级
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public Long getDirectParentId(Long creatorId) {
        CreatorRelationship query = new CreatorRelationship();
        query.setDescendantId(creatorId);
        query.setDepth(1); // 直接上级，深度为1
        List<CreatorRelationship> parents = creatorRelationshipMapper.selectCreatorRelationshipList(query);
        if (parents != null && !parents.isEmpty()) {
            return parents.get(0).getAncestorId();
        }
        return null;
    }

    /**
     * 查询指定主播的所有直接下级
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<Long> getDirectChildrenIds(Long creatorId) {
        return creatorRelationshipMapper.selectDirectChildrenIds(creatorId);
    }

    /**
     * 删除所有创建者关系
     * @return 删除的记录数
     */
    @Override
    public int deleteAllCreatorRelationships() {
        return creatorRelationshipMapper.deleteAllCreatorRelationships();
    }

    /**
     * 重建所有创建者关系
     * @return 重建的关系数量
     */
    @Override
    @Transactional
    public int rebuildAllRelationships() {
        rebuildCreatorRelationships();
        
        // 统计重建后的关系数量
        CreatorRelationship query = new CreatorRelationship();
        List<CreatorRelationship> allRelations = creatorRelationshipMapper.selectCreatorRelationshipList(query);
        return allRelations.size();
    }
}
