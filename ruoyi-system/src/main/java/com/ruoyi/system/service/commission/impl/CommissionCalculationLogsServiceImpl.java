package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionCalculationLogsMapper;
import com.ruoyi.system.domain.commission.CommissionCalculationLogs;
import com.ruoyi.system.service.commission.ICommissionCalculationLogsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 佣金计算日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionCalculationLogsServiceImpl implements ICommissionCalculationLogsService 
{
    @Autowired
    private CommissionCalculationLogsMapper commissionCalculationLogsMapper;

    /**
     * 查询佣金计算日志
     * 
     * @param id 佣金计算日志主键
     * @return 佣金计算日志
     */
    @Override
    public CommissionCalculationLogs selectCommissionCalculationLogsById(Long id)
    {
        return commissionCalculationLogsMapper.selectCommissionCalculationLogsById(id);
    }

    /**
     * 查询佣金计算日志列表
     * 
     * @param commissionCalculationLogs 佣金计算日志
     * @return 佣金计算日志
     */
    @Override
    public List<CommissionCalculationLogs> selectCommissionCalculationLogsList(CommissionCalculationLogs commissionCalculationLogs)
    {
        return commissionCalculationLogsMapper.selectCommissionCalculationLogsList(commissionCalculationLogs);
    }

    /**
     * 根据数据月份查询计算日志列表
     * 
     * @param dataMonth 数据月份
     * @return 佣金计算日志集合
     */
    @Override
    public List<CommissionCalculationLogs> selectCommissionCalculationLogsByMonth(Date dataMonth)
    {
        return commissionCalculationLogsMapper.selectCommissionCalculationLogsByMonth(dataMonth);
    }

    /**
     * 根据分销员ID和数据月份查询日志列表
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 佣金计算日志集合
     */
    @Override
    public List<CommissionCalculationLogs> selectCommissionCalculationLogsByCreatorAndMonth(Long creatorId, Date dataMonth)
    {
        return commissionCalculationLogsMapper.selectCommissionCalculationLogsByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 新增佣金计算日志
     * 
     * @param commissionCalculationLogs 佣金计算日志
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertCommissionCalculationLogs(CommissionCalculationLogs commissionCalculationLogs)
    {
        commissionCalculationLogs.setCreatedAt(DateUtils.getNowDate());
        return commissionCalculationLogsMapper.insertCommissionCalculationLogs(commissionCalculationLogs);
    }

    /**
     * 批量新增佣金计算日志
     * 
     * @param logsList 佣金计算日志列表
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int batchInsertCommissionCalculationLogs(List<CommissionCalculationLogs> logsList)
    {
        if (logsList == null || logsList.isEmpty()) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        for (CommissionCalculationLogs log : logsList) {
            if (log.getCreatedAt() == null) {
                log.setCreatedAt(now);
            }
        }
        
        return commissionCalculationLogsMapper.batchInsertCommissionCalculationLogs(logsList);
    }

    /**
     * 修改佣金计算日志
     * 
     * @param commissionCalculationLogs 佣金计算日志
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int updateCommissionCalculationLogs(CommissionCalculationLogs commissionCalculationLogs)
    {
        return commissionCalculationLogsMapper.updateCommissionCalculationLogs(commissionCalculationLogs);
    }

    /**
     * 批量删除佣金计算日志
     * 
     * @param ids 需要删除的佣金计算日志主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionCalculationLogsByIds(Long[] ids)
    {
        return commissionCalculationLogsMapper.deleteCommissionCalculationLogsByIds(ids);
    }

    /**
     * 删除佣金计算日志信息
     * 
     * @param id 佣金计算日志主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionCalculationLogsById(Long id)
    {
        return commissionCalculationLogsMapper.deleteCommissionCalculationLogsById(id);
    }

    /**
     * 清理指定日期之前的历史日志
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionCalculationLogsBeforeDate(Date beforeDate)
    {
        return commissionCalculationLogsMapper.deleteCommissionCalculationLogsBeforeDate(beforeDate);
    }
} 