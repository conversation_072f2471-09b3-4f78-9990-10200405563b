package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SystemConfigMapper;
import com.ruoyi.system.domain.SystemConfig;
import com.ruoyi.system.service.ISystemConfigService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 全局系统配置Service业务层处理
 *
 * <AUTHOR>
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class SystemConfigServiceImpl implements ISystemConfigService
{
    @Autowired
    private SystemConfigMapper systemConfigMapper;

    /**
     * 查询全局系统配置列表 (从库)
     *
     * @param systemConfig 全局系统配置
     * @return 全局系统配置
     */
    @Override
    public List<SystemConfig> selectSystemConfigList(SystemConfig systemConfig)
    {
        return systemConfigMapper.selectSystemConfigList(systemConfig);
    }

    /**
     * 根据配置键查询全局系统配置 (从库)
     *
     * @param configKey 配置键
     * @return 全局系统配置
     */
    @Override
    public SystemConfig selectSystemConfigByKey(String configKey)
    {
        return systemConfigMapper.selectSystemConfigByKey(configKey);
    }


    /**
     * 修改全局系统配置值 (主库)
     * 只允许修改 config_value
     *
     * @param systemConfig 全局系统配置
     * @return 结果
     */
    @Override
    public int updateSystemConfigValue(SystemConfig systemConfig)
    {
        // 创建一个新的对象只包含 key 和 value，防止更新其他字段
        SystemConfig updateEntity = new SystemConfig();
        updateEntity.setConfigKey(systemConfig.getConfigKey());
        updateEntity.setConfigValue(systemConfig.getConfigValue());
        // 如果需要允许更新 description 和 groupName，可以在这里设置
        // updateEntity.setDescription(systemConfig.getDescription());
        // updateEntity.setGroupName(systemConfig.getGroupName());
        return systemConfigMapper.updateSystemConfig(updateEntity);
    }
}
