package com.ruoyi.system.service.commission;

import java.util.List;
import com.ruoyi.system.domain.commission.CommissionRecruitmentBonusRules;

/**
 * 拉新奖励规则Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionRecruitmentBonusRulesService 
{
    /**
     * 查询拉新奖励规则
     * 
     * @param id 拉新奖励规则主键
     * @return 拉新奖励规则
     */
    public CommissionRecruitmentBonusRules selectCommissionRecruitmentBonusRulesById(Integer id);

    /**
     * 查询拉新奖励规则列表
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 拉新奖励规则集合
     */
    public List<CommissionRecruitmentBonusRules> selectCommissionRecruitmentBonusRulesList(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules);

    /**
     * 查询启用的拉新奖励规则列表
     * 
     * @return 拉新奖励规则集合
     */
    public List<CommissionRecruitmentBonusRules> selectActiveCommissionRecruitmentBonusRules();

    /**
     * 新增拉新奖励规则
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 结果
     */
    public int insertCommissionRecruitmentBonusRules(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules);

    /**
     * 修改拉新奖励规则
     * 
     * @param commissionRecruitmentBonusRules 拉新奖励规则
     * @return 结果
     */
    public int updateCommissionRecruitmentBonusRules(CommissionRecruitmentBonusRules commissionRecruitmentBonusRules);

    /**
     * 批量删除拉新奖励规则
     * 
     * @param ids 需要删除的拉新奖励规则主键集合
     * @return 结果
     */
    public int deleteCommissionRecruitmentBonusRulesByIds(Integer[] ids);

    /**
     * 删除拉新奖励规则信息
     * 
     * @param id 拉新奖励规则主键
     * @return 结果
     */
    public int deleteCommissionRecruitmentBonusRulesById(Integer id);
} 