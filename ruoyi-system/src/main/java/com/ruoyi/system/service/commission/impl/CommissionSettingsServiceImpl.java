package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionSettingsMapper;
import com.ruoyi.system.domain.commission.CommissionSettings;
import com.ruoyi.system.service.commission.ICommissionSettingsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 全局佣金配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionSettingsServiceImpl implements ICommissionSettingsService 
{
    @Autowired
    private CommissionSettingsMapper commissionSettingsMapper;

    /**
     * 查询全局佣金配置
     * 
     * @param settingKey 全局佣金配置主键
     * @return 全局佣金配置
     */
    @Override
    public CommissionSettings selectCommissionSettingsByKey(String settingKey)
    {
        return commissionSettingsMapper.selectCommissionSettingsByKey(settingKey);
    }

    /**
     * 查询全局佣金配置列表
     * 
     * @param commissionSettings 全局佣金配置
     * @return 全局佣金配置
     */
    @Override
    public List<CommissionSettings> selectCommissionSettingsList(CommissionSettings commissionSettings)
    {
        return commissionSettingsMapper.selectCommissionSettingsList(commissionSettings);
    }

    /**
     * 新增全局佣金配置
     * 
     * @param commissionSettings 全局佣金配置
     * @return 结果
     */
    @Override
    public int insertCommissionSettings(CommissionSettings commissionSettings)
    {
        commissionSettings.setCreateTime(new Date());
        return commissionSettingsMapper.insertCommissionSettings(commissionSettings);
    }

    /**
     * 修改全局佣金配置
     * 
     * @param commissionSettings 全局佣金配置
     * @return 结果
     */
    @Override
    public int updateCommissionSettings(CommissionSettings commissionSettings)
    {
        commissionSettings.setCreateTime(new Date());
        return commissionSettingsMapper.updateCommissionSettings(commissionSettings);
    }

    /**
     * 批量删除全局佣金配置
     * 
     * @param settingKeys 需要删除的全局佣金配置主键
     * @return 结果
     */
    @Override
    public int deleteCommissionSettingsByKeys(String[] settingKeys)
    {
        return commissionSettingsMapper.deleteCommissionSettingsByKeys(settingKeys);
    }

    /**
     * 删除全局佣金配置信息
     * 
     * @param settingKey 全局佣金配置主键
     * @return 结果
     */
    @Override
    public int deleteCommissionSettingsByKey(String settingKey)
    {
        return commissionSettingsMapper.deleteCommissionSettingsByKey(settingKey);
    }
} 