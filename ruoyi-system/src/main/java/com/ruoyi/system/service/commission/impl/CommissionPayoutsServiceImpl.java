package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionPayoutsMapper;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import com.ruoyi.system.service.commission.ICommissionPayoutsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 分销员月度实收金额Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionPayoutsServiceImpl implements ICommissionPayoutsService
{
    @Autowired
    private CommissionPayoutsMapper commissionPayoutsMapper;

    /**
     * 查询分销员月度实收金额
     * 
     * @param id 分销员月度实收金额主键
     * @return 分销员月度实收金额
     */
    @Override
    public CommissionPayouts selectCommissionPayoutsById(Long id)
    {
        return commissionPayoutsMapper.selectCommissionPayoutsById(id);
    }

    /**
     * 根据分销员ID和月份查询实收金额
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 分销员月度实收金额
     */
    @Override
    public CommissionPayouts selectCommissionPayoutsByCreatorAndMonth(Long creatorId, Date dataMonth)
    {
        return commissionPayoutsMapper.selectCommissionPayoutsByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 查询分销员月度实收金额列表
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 分销员月度实收金额
     */
    @Override
    public List<CommissionPayouts> selectCommissionPayoutsList(CommissionPayouts commissionPayouts)
    {
        return commissionPayoutsMapper.selectCommissionPayoutsList(commissionPayouts);
    }

    /**
     * 查询分销员月度实收金额列表（带主播信息）
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 分销员月度实收金额
     */
    @Override
    public List<CommissionPayouts> selectCommissionPayoutsListWithCreator(CommissionPayouts commissionPayouts)
    {
        return commissionPayoutsMapper.selectCommissionPayoutsListWithCreator(commissionPayouts);
    }

    /**
     * 查询指定月份的分销员收入统计
     * 
     * @param dataMonth 数据月份
     * @return 分销员月度实收金额
     */
    @Override
    public List<CommissionPayouts> selectCommissionPayoutsByMonth(Date dataMonth)
    {
        return commissionPayoutsMapper.selectCommissionPayoutsByMonth(dataMonth);
    }

    /**
     * 新增分销员月度实收金额
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertCommissionPayouts(CommissionPayouts commissionPayouts)
    {
        commissionPayouts.setCreateTime(DateUtils.getNowDate());
        return commissionPayoutsMapper.insertCommissionPayouts(commissionPayouts);
    }

    /**
     * 新增或更新分销员月度实收金额（处理重复记录）
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertOrUpdateCommissionPayouts(CommissionPayouts commissionPayouts)
    {
        commissionPayouts.setCreateTime(DateUtils.getNowDate());
        return commissionPayoutsMapper.insertOrUpdateCommissionPayouts(commissionPayouts);
    }

    /**
     * 修改分销员月度实收金额
     * 
     * @param commissionPayouts 分销员月度实收金额
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int updateCommissionPayouts(CommissionPayouts commissionPayouts)
    {
        commissionPayouts.setUpdateTime(DateUtils.getNowDate());
        return commissionPayoutsMapper.updateCommissionPayouts(commissionPayouts);
    }

    /**
     * 批量删除分销员月度实收金额
     * 
     * @param ids 需要删除的分销员月度实收金额主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionPayoutsByIds(Long[] ids)
    {
        return commissionPayoutsMapper.deleteCommissionPayoutsByIds(ids);
    }

    /**
     * 删除分销员月度实收金额信息
     * 
     * @param id 分销员月度实收金额主键
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionPayoutsById(Long id)
    {
        return commissionPayoutsMapper.deleteCommissionPayoutsById(id);
    }

    /**
     * 根据月份删除分销员月度实收金额
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionPayoutsByMonth(Date dataMonth)
    {
        return commissionPayoutsMapper.deleteCommissionPayoutsByMonth(dataMonth);
    }
} 