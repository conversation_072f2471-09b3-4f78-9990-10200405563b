package com.ruoyi.system.service.commission;

import java.util.Date;
import java.util.Map;
import java.util.List;

/**
 * 分销员等级调整Service接口
 * 实现"防守"体系的月度业绩考核与等级调整机制
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IDistributorLevelAdjustmentService 
{
    /**
     * 执行月度等级调整任务
     * 作为"一键计算"流程中的前置任务二执行
     * 
     * @param dataMonth 数据月份
     * @return 调整结果统计信息
     */
    public Map<String, Object> executeMonthlyLevelAdjustment(Date dataMonth);

    /**
     * 计算单个分销员的防守等级
     * 基于过去三个月的业绩表现进行考核
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 考核月份
     * @return 防守等级ID，如果降级则返回降级后的等级ID
     */
    public Integer calculateDefensiveLevel(Long creatorId, Date dataMonth);

    /**
     * 检查分销员是否处于保护期
     * 包括新手保护期和升级保护期
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 检查月份
     * @return 保护期信息，包括是否在保护期、保护期类型、剩余月数等
     */
    public Map<String, Object> checkProtectionPeriod(Long creatorId, Date dataMonth);

    /**
     * 计算月度环比增长率
     * 实现核心保级算法，包含风险控制规则
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 计算月份
     * @return 增长率计算结果，包括各月增长率和平均值
     */
    public Map<String, Object> calculateMonthlyGrowthRates(Long creatorId, Date dataMonth);

    /**
     * 获取分销员的团队业绩数据
     * 包括个人钻石收入 + 直属一级（depth=1）团队钻石总收入
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 团队业绩数据
     */
    public Map<String, Object> getTeamPerformanceData(Long creatorId, Date dataMonth);

    /**
     * 执行等级降级操作
     * 将分销员的基础等级降低一级
     * 
     * @param creatorId 分销员ID
     * @param currentLevelId 当前等级ID
     * @param reason 降级原因
     * @return 降级结果
     */
    public Map<String, Object> executeDowngrade(Long creatorId, Integer currentLevelId, String reason);

    /**
     * 获取等级调整的配置参数
     * 从commission_settings表中读取相关配置
     * 
     * @return 配置参数Map
     */
    public Map<String, Object> getLevelAdjustmentConfig();

    /**
     * 验证等级调整计算结果
     * 用于调试和验证特定分销员的等级调整逻辑
     * 
     * @param creatorId 分销员ID
     * @param dataMonth 数据月份
     * @return 详细的计算过程和结果
     */
    public Map<String, Object> validateLevelAdjustmentCalculation(Long creatorId, Date dataMonth);

    /**
     * 获取月度等级调整统计报告
     * 
     * @param dataMonth 数据月份
     * @return 统计报告，包括调整人数、降级人数、保护期人数等
     */
    public Map<String, Object> getMonthlyAdjustmentReport(Date dataMonth);

    /**
     * 批量更新分销员基础等级
     * 
     * @param adjustments 等级调整列表，包含creatorId和新等级ID
     * @return 更新结果
     */
    public Map<String, Object> batchUpdateDistributorLevels(List<Map<String, Object>> adjustments);
}
