package com.ruoyi.system.service.business;

import java.util.Date;
import java.util.Map;
import java.util.List;

/**
 * 拉新用户资格判定Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface INewRecruitQualificationService 
{
    /**
     * 执行全量拉新资格判定
     * 作为佣金计算的前置任务，判定所有符合条件的新用户并标记为合格拉新
     * 
     * @param dataMonth 考核月份
     * @return 判定结果统计信息
     */
    public Map<String, Object> executeNewRecruitQualification(Date dataMonth);

    /**
     * 预览拉新资格判定结果（不保存到数据库）
     * 用于调试和验证判定逻辑
     * 
     * @param dataMonth 考核月份
     * @return 预览结果信息
     */
    public Map<String, Object> previewNewRecruitQualification(Date dataMonth);

    /**
     * 检查单个用户的拉新资格
     * 
     * @param creatorId 用户ID
     * @param dataMonth 考核月份
     * @return 资格检查结果
     */
    public Map<String, Object> checkSingleCreatorQualification(Long creatorId, Date dataMonth);

    /**
     * 获取指定月份的拉新资格统计信息
     * 
     * @param dataMonth 考核月份
     * @return 统计信息
     */
    public Map<String, Object> getQualificationStatistics(Date dataMonth);

    /**
     * 获取已合格拉新用户列表
     * 
     * @param dataMonth 考核月份（可选，为null时返回所有已合格用户）
     * @param recruiterId 招募人ID（可选，为null时返回所有招募人的数据）
     * @return 已合格拉新用户列表
     */
    public List<Map<String, Object>> getQualifiedNewRecruits(Date dataMonth, Long recruiterId);

    /**
     * 重置指定月份的拉新资格判定结果
     * 将指定月份标记为合格的用户重置为未判定状态
     * 
     * @param dataMonth 考核月份
     * @return 重置结果信息
     */
    public Map<String, Object> resetQualificationForMonth(Date dataMonth);

    /**
     * 获取拉新资格判定的配置参数
     * 
     * @return 配置参数信息
     */
    public Map<String, Object> getQualificationConfig();

    /**
     * 更新拉新资格判定的配置参数
     * 
     * @param config 配置参数
     * @return 更新结果
     */
    public Map<String, Object> updateQualificationConfig(Map<String, Object> config);
}
