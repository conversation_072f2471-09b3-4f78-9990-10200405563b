package com.ruoyi.system.service.impl.business;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.business.CreatorMapper;
import com.ruoyi.system.domain.business.Creator;
import com.ruoyi.system.domain.business.CreatorTreeNode;
import com.ruoyi.system.domain.business.CreatorMonthlyTreeNode;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import com.ruoyi.system.domain.commission.CommissionPayoutBreakdowns;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.service.business.ICreatorService;
import com.ruoyi.system.service.commission.ICommissionPayoutsService;
import com.ruoyi.system.service.commission.ICommissionPayoutBreakdownsService;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 主播信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CreatorServiceImpl implements ICreatorService
{
    @Autowired
    private CreatorMapper creatorMapper;

    @Autowired
    private ICommissionPayoutsService commissionPayoutsService;

    @Autowired
    private ICommissionPayoutBreakdownsService commissionPayoutBreakdownsService;

    @Autowired
    private IMonthlyPerformanceService monthlyPerformanceService;

    /**
     * 查询主播信息
     *
     * @param id 主播信息主键
     * @return 主播信息
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public Creator selectCreatorById(Long id)
    {
        return creatorMapper.selectCreatorById(id);
    }

    /**
     * 查询主播信息列表
     *
     * @param creator 主播信息
     * @return 主播信息
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<Creator> selectCreatorList(Creator creator)
    {
        return creatorMapper.selectCreatorList(creator);
    }

    /**
     * 新增主播信息
     *
     * @param creator 主播信息
     * @return 结果
     */
    @Override
    public int insertCreator(Creator creator)
    {
        // 只有当创建时间为空时才设置当前时间，避免覆盖已设置的历史时间
        if (creator.getCreateTime() == null) {
            creator.setCreateTime(DateUtils.getNowDate());
        }
        return creatorMapper.insertCreator(creator);
    }

    /**
     * 修改主播信息
     *
     * @param creator 主播信息
     * @return 结果
     */
    @Override
    public int updateCreator(Creator creator)
    {
        creator.setUpdateTime(DateUtils.getNowDate());
        return creatorMapper.updateCreator(creator);
    }

    /**
     * 批量删除主播信息
     *
     * @param ids 需要删除的主播信息主键
     * @return 结果
     */
    @Override
    public int deleteCreatorByIds(Long[] ids)
    {
        return creatorMapper.deleteCreatorByIds(ids);
    }

    /**
     * 删除主播信息信息
     *
     * @param id 主播信息主键
     * @return 结果
     */
    @Override
    public int deleteCreatorById(Long id)
    {
        return creatorMapper.deleteCreatorById(id);
    }

    /**
     * 根据Handle查询主播信息
     *
     * @param handle 主播handle
     * @return 主播信息
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public Creator selectCreatorByHandle(String handle)
    {
        return creatorMapper.selectCreatorByHandle(handle);
    }

    /**
     * 保存或更新主播信息
     * <p>
     * 如果主播ID为空，则执行插入操作；否则执行更新操作。
     * </p>
     *
     * @param creator 主播信息
     * @return 结果
     */
    @Override
    public int saveOrUpdateCreator(Creator creator)
    {
        if (creator.getId() == null || creator.getId() == 0L)
        {
            // 如果 ID 为 null 或 0L，这通常意味着 Excel 中没有提供有效的 ID，
            // 或者 Excel 中的 ID 本身就是 null/0，并且业务逻辑期望插入这样的记录。
            // 鉴于之前的错误 "Field 'id' doesn't have a default value"，
            // 如果此时 creator.getId() 仍为 null，插入会失败（除非 id 列允许 null 且不是主键，这不太可能）。
            // 此处假设如果 ID 为 null/0L，则仍尝试按原逻辑插入（可能需要调用方确保 ID 已设置，或此路径不应发生）。
            if (creator.getCreateTime() == null) {
                creator.setCreateTime(DateUtils.getNowDate());
            }
            return creatorMapper.insertCreator(creator);
        }
        else
        {
            // ID 由 Excel 提供且不为 null/0L
            Creator existingCreator = creatorMapper.selectCreatorById(creator.getId());
            if (existingCreator != null)
            {
                // 记录已存在，执行更新
                creator.setUpdateTime(DateUtils.getNowDate());
                return creatorMapper.updateCreator(creator);
            }
            else
            {
                // 记录不存在，执行插入（使用 Excel 提供的 ID）
                if (creator.getCreateTime() == null) {
                    creator.setCreateTime(DateUtils.getNowDate());
                }
                return creatorMapper.insertCreator(creator);
            }
        }
    }

    /**
     * 查询主播树状结构
     * 返回以根节点为起点的完整树状关系图
     *
     * @return 主播树状结构列表
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<CreatorTreeNode> selectCreatorTree()
    {
        // 查询所有根节点
        List<Creator> rootCreators = creatorMapper.selectRootCreators();
        List<CreatorTreeNode> treeNodes = new ArrayList<>();
        
        // 为每个根节点构建树
        for (Creator rootCreator : rootCreators) 
        {
            CreatorTreeNode rootNode = buildTreeNode(rootCreator);
            treeNodes.add(rootNode);
        }
        
        return treeNodes;
    }

    /**
     * 查询主播月度数据树状结构
     * 返回包含月度业绩数据的完整树状关系图
     * 只返回在指定月份有 monthly_performance 数据的主播或分销员
     *
     * @param dataMonth 数据月份 (必传)
     * @param creatorId 主播ID (可选)
     * @param nickname 主播昵称 (可选)
     * @return 主播月度树状结构列表
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<CreatorMonthlyTreeNode> selectCreatorMonthlyTree(Date dataMonth, Long creatorId, String nickname)
    {
        // 查询根节点（支持按条件过滤）
        List<Creator> rootCreators = creatorMapper.selectRootCreatorsWithFilter(creatorId, nickname);
        
        // 一次性查询所有主播数据，避免递归过程中的数据源切换问题
        List<Creator> allCreators = creatorMapper.selectCreatorList(new Creator());
        Map<Long, List<Creator>> creatorChildrenMap = buildCreatorChildrenMap(allCreators);
        
        // 查询在指定月份有 monthly_performance 数据的所有 creator_id
        List<Long> creatorsWithData = monthlyPerformanceService.selectCreatorIdsWithDataByMonth(dataMonth);
        
        List<CreatorMonthlyTreeNode> treeNodes = new ArrayList<>();
        
        // 为每个根节点构建包含月度数据的树（只包含有数据的节点）
        for (Creator rootCreator : rootCreators) 
        {
            CreatorMonthlyTreeNode rootNode = buildMonthlyTreeNodeWithMapFiltered(rootCreator, dataMonth, creatorChildrenMap, creatorsWithData);
            if (rootNode != null) {
                treeNodes.add(rootNode);
            }
        }
        
        return treeNodes;
    }

    /**
     * 递归构建树节点及其子节点
     *
     * @param creator 当前主播信息
     * @return 构建的树节点
     */
    private CreatorTreeNode buildTreeNode(Creator creator)
    {
        // 创建当前节点
        CreatorTreeNode treeNode = new CreatorTreeNode(creator.getNickname(), creator.getId());
        
        // 查询直属子节点
        List<Creator> children = creatorMapper.selectDirectChildren(creator.getId());
        
        if (children != null && !children.isEmpty()) 
        {
            List<CreatorTreeNode> childNodes = new ArrayList<>();
            
            // 递归构建子节点
            for (Creator child : children) 
            {
                CreatorTreeNode childNode = buildTreeNode(child);
                childNodes.add(childNode);
            }
            
            treeNode.setChildren(childNodes);
        }
        
        return treeNode;
    }

    /**
     * 递归构建包含月度数据的树节点及其子节点
     *
     * @param creator 当前主播信息
     * @param dataMonth 数据月份
     * @return 构建的月度树节点
     */
    @DataSource(value = DataSourceType.SLAVE)
    public CreatorMonthlyTreeNode buildMonthlyTreeNode(Creator creator, Date dataMonth)
    {
        // 查询直属子节点
        List<Creator> children = creatorMapper.selectDirectChildren(creator.getId());
        
        // 判断节点类型：有下级的是分销员，没有下级的是主播
        String nodeType = (children != null && !children.isEmpty()) ? "分销员" : "主播";
        
        // 创建当前节点
        CreatorMonthlyTreeNode treeNode = new CreatorMonthlyTreeNode(creator.getNickname(), creator.getId(), nodeType);
        
        // 填充月度数据
        populateMonthlyData(treeNode.getLabel(), creator.getId(), dataMonth);
        
        if (children != null && !children.isEmpty()) 
        {
            List<CreatorMonthlyTreeNode> childNodes = new ArrayList<>();
            
            // 递归构建子节点
            for (Creator child : children) 
            {
                CreatorMonthlyTreeNode childNode = buildMonthlyTreeNode(child, dataMonth);
                childNodes.add(childNode);
            }
            
            treeNode.setChildren(childNodes);
        }
        
        return treeNode;
    }

    /**
     * 填充节点的月度数据
     *
     * @param label 节点标签
     * @param creatorId 主播ID
     * @param dataMonth 数据月份
     */
    @DataSource(value = DataSourceType.SLAVE)
    public void populateMonthlyData(CreatorMonthlyTreeNode.CreatorMonthlyLabel label, Long creatorId, Date dataMonth)
    {
        // 查询分销员数据汇总
        CommissionPayouts commissionPayouts = commissionPayoutsService.selectCommissionPayoutsByCreatorAndMonth(creatorId, dataMonth);
        
        // 查询收入明细
        List<CommissionPayoutBreakdowns> breakdowns = commissionPayoutBreakdownsService.selectCommissionPayoutBreakdownsByCreatorAndMonth(creatorId, dataMonth);
        
        // 查询个人主播数据
        MonthlyPerformance monthlyPerformance = monthlyPerformanceService.selectMonthlyPerformanceByCreatorAndMonth(creatorId, dataMonth);
        
        // 计算并填充 CommissionPayouts 中的依赖字段
        if (commissionPayouts != null) 
        {
            // 计算个人钻石收入：来自 MonthlyPerformance
            if (monthlyPerformance != null && monthlyPerformance.getDiamonds() != null) 
            {
                commissionPayouts.setPersonalDiamonds(monthlyPerformance.getDiamonds());
            } 
            else 
            {
                commissionPayouts.setPersonalDiamonds(0L);
            }
            
            // 计算团队钻石收入和拉新人数：来自 CommissionPayoutBreakdowns
            Long teamDiamonds = 0L;
            Integer newRecruitsCount = 0;
            
            if (breakdowns != null && !breakdowns.isEmpty()) 
            {
                for (CommissionPayoutBreakdowns breakdown : breakdowns) 
                {
                    // 团队钻石收入：当 sourceType 为 LEVEL_COMMISSION 时，取 baseAmountDiamonds
                    if ("LEVEL_COMMISSION".equals(breakdown.getSourceType()) && breakdown.getBaseAmountDiamonds() != null) 
                    {
                        teamDiamonds += breakdown.getBaseAmountDiamonds();
                    }
                    
                    // 统计拉新人数：收入来源类型为 RECRUITMENT_BONUS 的记录数
                    if ("RECRUITMENT_BONUS".equals(breakdown.getSourceType())) 
                    {
                        newRecruitsCount++;
                    }
                }
            }
            
            commissionPayouts.setTeamDiamonds(teamDiamonds);
            commissionPayouts.setNewRecruitsCount(newRecruitsCount);
            
            label.setCommissionPayouts(commissionPayouts);
            label.setCommissionPayoutBreakdowns(breakdowns);
        }
        
        if (monthlyPerformance != null) 
        {
            label.setMonthlyPerformance(monthlyPerformance);
        }
    }

    /**
     * 构建主播子节点映射
     * 将所有主播按照parent_id分组，避免递归查询
     *
     * @param allCreators 所有主播列表
     * @return 子节点映射
     */
    private Map<Long, List<Creator>> buildCreatorChildrenMap(List<Creator> allCreators)
    {
        Map<Long, List<Creator>> childrenMap = new HashMap<>();
        
        for (Creator creator : allCreators) 
        {
            Long parentId = creator.getParentId();
            if (parentId != null && parentId != 0L) 
            {
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(creator);
            }
        }
        
        return childrenMap;
    }

    /**
     * 使用预构建的映射递归构建包含月度数据的树节点
     * 避免递归过程中的数据库查询和数据源切换问题
     *
     * @param creator 当前主播信息
     * @param dataMonth 数据月份
     * @param creatorChildrenMap 子节点映射
     * @return 构建的月度树节点
     */
    private CreatorMonthlyTreeNode buildMonthlyTreeNodeWithMap(Creator creator, Date dataMonth, Map<Long, List<Creator>> creatorChildrenMap)
    {
        // 获取直属子节点
        List<Creator> children = creatorChildrenMap.get(creator.getId());
        
        // 判断节点类型：有下级的是分销员，没有下级的是主播
        String nodeType = (children != null && !children.isEmpty()) ? "分销员" : "主播";
        
        // 创建当前节点
        CreatorMonthlyTreeNode treeNode = new CreatorMonthlyTreeNode(creator.getNickname(), creator.getId(), nodeType);
        
        // 填充月度数据
        populateMonthlyData(treeNode.getLabel(), creator.getId(), dataMonth);
        
        if (children != null && !children.isEmpty()) 
        {
            List<CreatorMonthlyTreeNode> childNodes = new ArrayList<>();
            
            // 递归构建子节点
            for (Creator child : children) 
            {
                CreatorMonthlyTreeNode childNode = buildMonthlyTreeNodeWithMap(child, dataMonth, creatorChildrenMap);
                childNodes.add(childNode);
            }
            
            treeNode.setChildren(childNodes);
        }
        
        return treeNode;
    }

    /**
     * 使用预构建的映射递归构建包含月度数据的树节点（带过滤功能）
     * 只包含在指定月份有 monthly_performance 数据的节点
     *
     * @param creator 当前主播信息
     * @param dataMonth 数据月份
     * @param creatorChildrenMap 子节点映射
     * @param creatorsWithData 有数据的主播ID列表
     * @return 构建的月度树节点，如果当前节点及其所有子节点都没有数据则返回null
     */
    private CreatorMonthlyTreeNode buildMonthlyTreeNodeWithMapFiltered(Creator creator, Date dataMonth, Map<Long, List<Creator>> creatorChildrenMap, List<Long> creatorsWithData)
    {
        // 获取直属子节点
        List<Creator> children = creatorChildrenMap.get(creator.getId());
        
        // 先处理子节点，收集有数据的子节点
        List<CreatorMonthlyTreeNode> validChildNodes = new ArrayList<>();
        if (children != null && !children.isEmpty()) 
        {
            for (Creator child : children) 
            {
                CreatorMonthlyTreeNode childNode = buildMonthlyTreeNodeWithMapFiltered(child, dataMonth, creatorChildrenMap, creatorsWithData);
                if (childNode != null) {
                    validChildNodes.add(childNode);
                }
            }
        }
        
        // 判断当前节点是否应该包含在结果中
        // 条件：当前节点有数据 或者 有任何子节点有数据
        boolean currentNodeHasData = creatorsWithData.contains(creator.getId());
        boolean hasValidChildren = !validChildNodes.isEmpty();
        
        if (!currentNodeHasData && !hasValidChildren) {
            // 当前节点没有数据，且没有有效的子节点，不包含此节点
            return null;
        }
        
        // 判断节点类型：有下级的是分销员，没有下级的是主播
        String nodeType = (children != null && !children.isEmpty()) ? "分销员" : "主播";
        
        // 创建当前节点
        CreatorMonthlyTreeNode treeNode = new CreatorMonthlyTreeNode(creator.getNickname(), creator.getId(), nodeType);
        
        // 填充月度数据
        populateMonthlyData(treeNode.getLabel(), creator.getId(), dataMonth);
        
        // 设置有效的子节点
        if (!validChildNodes.isEmpty()) 
        {
            treeNode.setChildren(validChildNodes);
        }
        
        return treeNode;
    }
}
