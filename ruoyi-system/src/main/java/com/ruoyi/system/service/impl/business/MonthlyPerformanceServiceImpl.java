package com.ruoyi.system.service.impl.business;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.business.MonthlyPerformanceMapper;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.vo.MonthlyPerformanceVO;
import com.ruoyi.system.service.business.IMonthlyPerformanceService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 主播月度业绩Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class MonthlyPerformanceServiceImpl implements IMonthlyPerformanceService
{
    @Autowired
    private MonthlyPerformanceMapper monthlyPerformanceMapper;

    /**
     * 查询主播月度业绩
     *
     * @param id 主播月度业绩主键
     * @return 主播月度业绩
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public MonthlyPerformance selectMonthlyPerformanceById(Integer id)
    {
        return monthlyPerformanceMapper.selectMonthlyPerformanceById(id);
    }

    /**
     * 查询主播月度业绩列表
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 主播月度业绩
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<MonthlyPerformance> selectMonthlyPerformanceList(MonthlyPerformance monthlyPerformance)
    {
        return monthlyPerformanceMapper.selectMonthlyPerformanceList(monthlyPerformance);
    }

    /**
     * 查询主播月度业绩列表（关联查询主播信息）
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 主播月度业绩VO（包含主播信息）
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<MonthlyPerformanceVO> selectMonthlyPerformanceWithCreatorList(MonthlyPerformance monthlyPerformance)
    {
        return monthlyPerformanceMapper.selectMonthlyPerformanceWithCreatorList(monthlyPerformance);
    }

    /**
     * 根据主播ID和数据月份查询主播月度业绩
     *
     * @param creatorId 主播ID
     * @param dataMonth 数据月份
     * @return 主播月度业绩
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public MonthlyPerformance selectMonthlyPerformanceByCreatorAndMonth(Long creatorId, Date dataMonth)
    {
        return monthlyPerformanceMapper.selectMonthlyPerformanceByCreatorAndMonth(creatorId, dataMonth);
    }

    /**
     * 新增主播月度业绩
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 结果
     */
    @Override
    public int insertMonthlyPerformance(MonthlyPerformance monthlyPerformance)
    {
        return monthlyPerformanceMapper.insertMonthlyPerformance(monthlyPerformance);
    }

    /**
     * 修改主播月度业绩
     *
     * @param monthlyPerformance 主播月度业绩
     * @return 结果
     */
    @Override
    public int updateMonthlyPerformance(MonthlyPerformance monthlyPerformance)
    {
        return monthlyPerformanceMapper.updateMonthlyPerformance(monthlyPerformance);
    }

    /**
     * 批量删除主播月度业绩
     *
     * @param ids 需要删除的主播月度业绩主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyPerformanceByIds(Integer[] ids)
    {
        return monthlyPerformanceMapper.deleteMonthlyPerformanceByIds(ids);
    }

    /**
     * 删除主播月度业绩信息
     *
     * @param id 主播月度业绩主键
     * @return 结果
     */
    @Override
    public int deleteMonthlyPerformanceById(Integer id)
    {
        return monthlyPerformanceMapper.deleteMonthlyPerformanceById(id);
    }

    @Override
    public int saveOrUpdateMonthlyPerformance(MonthlyPerformance monthlyPerformance) {
        if (monthlyPerformance.getCreatorId() == null || monthlyPerformance.getDataMonth() == null) {
            // Consider throwing an IllegalArgumentException for invalid input
            return 0; 
        }

        MonthlyPerformance existingPerformance = monthlyPerformanceMapper.selectMonthlyPerformanceByCreatorAndMonth(
                monthlyPerformance.getCreatorId(), monthlyPerformance.getDataMonth());

        if (existingPerformance != null) {
            monthlyPerformance.setId(existingPerformance.getId());
            return monthlyPerformanceMapper.updateMonthlyPerformance(monthlyPerformance);
        } else {
            return monthlyPerformanceMapper.insertMonthlyPerformance(monthlyPerformance);
        }
    }

    /**
     * 查询在指定月份有数据的主播ID列表
     *
     * @param dataMonth 数据月份
     * @return 主播ID列表
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<Long> selectCreatorIdsWithDataByMonth(Date dataMonth)
    {
        return monthlyPerformanceMapper.selectCreatorIdsWithDataByMonth(dataMonth);
    }

    /**
     * 批量查询指定创作者在指定月份的业绩数据
     *
     * @param creatorIds 创作者ID列表
     * @param dataMonth 数据月份
     * @return 主播月度业绩集合
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<MonthlyPerformance> selectMonthlyPerformanceByCreatorIdsAndMonth(List<Long> creatorIds, Date dataMonth)
    {
        return monthlyPerformanceMapper.selectMonthlyPerformanceByCreatorIdsAndMonth(creatorIds, dataMonth);
    }
}
