package com.ruoyi.system.service.commission.impl;

import java.util.List;
import java.util.Date;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.commission.CommissionRecruitmentStatsMapper;
import com.ruoyi.system.domain.commission.CommissionRecruitmentStats;
import com.ruoyi.system.service.commission.ICommissionRecruitmentStatsService;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

/**
 * 招募统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DataSource(value = DataSourceType.SLAVE)
public class CommissionRecruitmentStatsServiceImpl implements ICommissionRecruitmentStatsService 
{
    @Autowired
    private CommissionRecruitmentStatsMapper commissionRecruitmentStatsMapper;

    /**
     * 查询招募统计
     * 
     * @param recruiterId 招募人ID
     * @param dataMonth 数据月份
     * @return 招募统计
     */
    @Override
    public CommissionRecruitmentStats selectCommissionRecruitmentStatsByRecruiterAndMonth(Long recruiterId, Date dataMonth)
    {
        return commissionRecruitmentStatsMapper.selectCommissionRecruitmentStatsByRecruiterAndMonth(recruiterId, dataMonth);
    }

    /**
     * 查询招募统计列表
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 招募统计
     */
    @Override
    public List<CommissionRecruitmentStats> selectCommissionRecruitmentStatsList(CommissionRecruitmentStats commissionRecruitmentStats)
    {
        return commissionRecruitmentStatsMapper.selectCommissionRecruitmentStatsList(commissionRecruitmentStats);
    }

    /**
     * 根据数据月份查询招募统计列表
     * 
     * @param dataMonth 数据月份
     * @return 招募统计集合
     */
    @Override
    public List<CommissionRecruitmentStats> selectCommissionRecruitmentStatsByMonth(Date dataMonth)
    {
        return commissionRecruitmentStatsMapper.selectCommissionRecruitmentStatsByMonth(dataMonth);
    }

    /**
     * 根据招募人ID查询历史招募统计
     * 
     * @param recruiterId 招募人ID
     * @return 招募统计集合
     */
    @Override
    public List<CommissionRecruitmentStats> selectCommissionRecruitmentStatsByRecruiter(Long recruiterId)
    {
        return commissionRecruitmentStatsMapper.selectCommissionRecruitmentStatsByRecruiter(recruiterId);
    }

    /**
     * 查询月度招募排行榜
     * 
     * @param dataMonth 数据月份
     * @param limit 返回条数限制
     * @return 招募统计集合（按新招募人数降序）
     */
    @Override
    public List<CommissionRecruitmentStats> selectTopRecruitmentStatsByMonth(Date dataMonth, Integer limit)
    {
        return commissionRecruitmentStatsMapper.selectTopRecruitmentStatsByMonth(dataMonth, limit);
    }

    /**
     * 新增招募统计
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertCommissionRecruitmentStats(CommissionRecruitmentStats commissionRecruitmentStats)
    {
        commissionRecruitmentStats.setCreateTime(DateUtils.getNowDate());
        return commissionRecruitmentStatsMapper.insertCommissionRecruitmentStats(commissionRecruitmentStats);
    }

    /**
     * 批量新增招募统计
     * 
     * @param statsList 招募统计列表
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int batchInsertCommissionRecruitmentStats(List<CommissionRecruitmentStats> statsList)
    {
        if (statsList == null || statsList.isEmpty()) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        for (CommissionRecruitmentStats stats : statsList) {
            if (stats.getCreateTime() == null) {
                stats.setCreateTime(now);
            }
        }
        
        return commissionRecruitmentStatsMapper.batchInsertCommissionRecruitmentStats(statsList);
    }

    /**
     * 插入或更新招募统计
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int insertOrUpdateCommissionRecruitmentStats(CommissionRecruitmentStats commissionRecruitmentStats)
    {
        if (commissionRecruitmentStats.getCreateTime() == null) {
            commissionRecruitmentStats.setCreateTime(DateUtils.getNowDate());
        }
        commissionRecruitmentStats.setUpdateTime(DateUtils.getNowDate());
        
        return commissionRecruitmentStatsMapper.insertOrUpdateCommissionRecruitmentStats(commissionRecruitmentStats);
    }

    /**
     * 修改招募统计
     * 
     * @param commissionRecruitmentStats 招募统计
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int updateCommissionRecruitmentStats(CommissionRecruitmentStats commissionRecruitmentStats)
    {
        commissionRecruitmentStats.setUpdateTime(DateUtils.getNowDate());
        return commissionRecruitmentStatsMapper.updateCommissionRecruitmentStats(commissionRecruitmentStats);
    }

    /**
     * 删除招募统计
     * 
     * @param recruiterId 招募人ID
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionRecruitmentStatsByRecruiterAndMonth(Long recruiterId, Date dataMonth)
    {
        return commissionRecruitmentStatsMapper.deleteCommissionRecruitmentStatsByRecruiterAndMonth(recruiterId, dataMonth);
    }

    /**
     * 根据招募人ID删除招募统计
     * 
     * @param recruiterId 招募人ID
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionRecruitmentStatsByRecruiter(Long recruiterId)
    {
        return commissionRecruitmentStatsMapper.deleteCommissionRecruitmentStatsByRecruiter(recruiterId);
    }

    /**
     * 根据数据月份删除招募统计
     * 
     * @param dataMonth 数据月份
     * @return 结果
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionRecruitmentStatsByMonth(Date dataMonth)
    {
        return commissionRecruitmentStatsMapper.deleteCommissionRecruitmentStatsByMonth(dataMonth);
    }

    /**
     * 检查指定招募人和月份的记录是否存在
     * 
     * @param recruiterId 招募人ID
     * @param dataMonth 数据月份
     * @return 是否存在
     */
    @Override
    public boolean checkCommissionRecruitmentStatsExists(Long recruiterId, Date dataMonth)
    {
        int count = commissionRecruitmentStatsMapper.checkCommissionRecruitmentStatsExists(recruiterId, dataMonth);
        return count > 0;
    }

    /**
     * 清理指定日期之前的历史招募统计
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public int deleteCommissionRecruitmentStatsBeforeDate(Date beforeDate)
    {
        return commissionRecruitmentStatsMapper.deleteCommissionRecruitmentStatsBeforeDate(beforeDate);
    }
} 