package com.ruoyi.system.service.commission;

import java.util.List;
import java.util.Date;
import com.ruoyi.system.domain.commission.CommissionConfigSnapshots;

/**
 * 月度配置快照Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface ICommissionConfigSnapshotsService 
{
    /**
     * 查询月度配置快照
     * 
     * @param id 月度配置快照主键
     * @return 月度配置快照
     */
    public CommissionConfigSnapshots selectCommissionConfigSnapshotsById(Long id);

    /**
     * 查询月度配置快照列表
     * 
     * @param commissionConfigSnapshots 月度配置快照
     * @return 月度配置快照集合
     */
    public List<CommissionConfigSnapshots> selectCommissionConfigSnapshotsList(CommissionConfigSnapshots commissionConfigSnapshots);

    /**
     * 根据数据月份查询配置快照列表
     * 
     * @param dataMonth 数据月份
     * @return 月度配置快照集合
     */
    public List<CommissionConfigSnapshots> selectCommissionConfigSnapshotsByMonth(Date dataMonth);

    /**
     * 根据数据月份和配置类型查询快照
     * 
     * @param dataMonth 数据月份
     * @param configType 配置类型
     * @return 月度配置快照
     */
    public CommissionConfigSnapshots selectCommissionConfigSnapshotsByMonthAndType(Date dataMonth, String configType);

    /**
     * 新增月度配置快照
     * 
     * @param commissionConfigSnapshots 月度配置快照
     * @return 结果
     */
    public int insertCommissionConfigSnapshots(CommissionConfigSnapshots commissionConfigSnapshots);

    /**
     * 批量新增月度配置快照
     * 
     * @param snapshotsList 月度配置快照列表
     * @return 结果
     */
    public int batchInsertCommissionConfigSnapshots(List<CommissionConfigSnapshots> snapshotsList);

    /**
     * 修改月度配置快照
     * 
     * @param commissionConfigSnapshots 月度配置快照
     * @return 结果
     */
    public int updateCommissionConfigSnapshots(CommissionConfigSnapshots commissionConfigSnapshots);

    /**
     * 批量删除月度配置快照
     * 
     * @param ids 需要删除的月度配置快照主键集合
     * @return 结果
     */
    public int deleteCommissionConfigSnapshotsByIds(Long[] ids);

    /**
     * 删除月度配置快照信息
     * 
     * @param id 月度配置快照主键
     * @return 结果
     */
    public int deleteCommissionConfigSnapshotsById(Long id);

    /**
     * 检查指定月份和类型的快照是否存在
     * 
     * @param dataMonth 数据月份
     * @param configType 配置类型
     * @return 是否存在
     */
    public boolean checkCommissionConfigSnapshotExists(Date dataMonth, String configType);

    /**
     * 清理指定日期之前的历史快照
     * 
     * @param beforeDate 截止日期
     * @return 删除的记录数
     */
    public int deleteCommissionConfigSnapshotsBeforeDate(Date beforeDate);
} 