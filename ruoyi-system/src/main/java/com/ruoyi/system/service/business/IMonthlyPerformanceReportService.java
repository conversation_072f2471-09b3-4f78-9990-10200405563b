package com.ruoyi.system.service.business;

import com.ruoyi.system.domain.dto.MonthlyPerformanceReportResponse;

/**
 * 分销员月度业绩分析报告Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IMonthlyPerformanceReportService
{
    /**
     * 获取指定分销员在指定月份的完整业绩分析报告
     * 包含过去6个月的趋势数据和洞察
     * 
     * @param creatorId 分销员ID
     * @param month 基准月份，格式为 YYYY-MM
     * @return 业绩分析报告
     */
    MonthlyPerformanceReportResponse getMonthlyPerformanceReport(Long creatorId, String month);
}
