package com.ruoyi.system.service.business;

import java.util.List;
import com.ruoyi.system.domain.business.CreatorRelationship;

/**
 * 主播层级关系Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface ICreatorRelationshipService
{
    /**
     * 查询主播层级关系
     *
     * @param ancestorId 祖先ID
     * @param descendantId 后代ID
     * @return 主播层级关系
     */
    public CreatorRelationship selectCreatorRelationshipById(Long ancestorId, Long descendantId);

    /**
     * 查询主播层级关系列表
     *
     * @param creatorRelationship 主播层级关系
     * @return 主播层级关系集合
     */
    public List<CreatorRelationship> selectCreatorRelationshipList(CreatorRelationship creatorRelationship);

    /**
     * 新增主播层级关系
     *
     * @param creatorRelationship 主播层级关系
     * @return 结果
     */
    public int insertCreatorRelationship(CreatorRelationship creatorRelationship);

    /**
     * 删除主播层级关系信息
     *
     * @param ancestorId 祖先ID
     * @param descendantId 后代ID
     * @return 结果
     */
    public int deleteCreatorRelationshipById(Long ancestorId, Long descendantId);

    /**
     * 重建主播层级关系表
     * @return 结果
     */
    public void rebuildCreatorRelationships();

    /**
     * 查询指定主播的所有后代（可指定深度范围）
     * @param ancestorId 祖先ID
     * @param minDepth 最小深度 (可选, null表示不限制)
     * @param maxDepth 最大深度 (可选, null表示不限制)
     * @return 后代关系列表
     */
    public List<CreatorRelationship> getDescendants(Long ancestorId, Integer minDepth, Integer maxDepth);

    /**
     * 查询指定主播的所有祖先（可指定深度范围）
     * @param descendantId 后代ID
     * @param minDepth 最小深度 (可选, null表示不限制)
     * @param maxDepth 最大深度 (可选, null表示不限制)
     * @return 祖先关系列表
     */
    public List<CreatorRelationship> getAncestors(Long descendantId, Integer minDepth, Integer maxDepth);

    /**
     * 查询指定主播的直接上级
     * @param creatorId 主播ID
     * @return 直接上级ID，如果没有则返回null
     */
    public Long getDirectParentId(Long creatorId);

    /**
     * 查询指定主播的所有直接下级
     * @param creatorId 主播ID
     * @return 直接下级ID列表
     */
    public List<Long> getDirectChildrenIds(Long creatorId);

    /**
     * 删除所有创建者关系
     * @return 删除的记录数
     */
    public int deleteAllCreatorRelationships();

    /**
     * 重建所有创建者关系
     * @return 重建的关系数量
     */
    public int rebuildAllRelationships();
}
