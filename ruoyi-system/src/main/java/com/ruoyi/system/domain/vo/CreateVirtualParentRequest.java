package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;

/**
 * 创建虚拟上级请求实体
 *
 * <AUTHOR>
 */
@ApiModel(value = "CreateVirtualParentRequest", description = "创建虚拟上级请求参数")
public class CreateVirtualParentRequest
{
    /** 组名 */
    @ApiModelProperty(value = "组名", required = true, example = "Jason", notes = "虚拟上级昵称将自动生成为：组名-虚拟")
    @NotBlank(message = "组名不能为空")
    private String groupName;

    public String getGroupName()
    {
        return groupName;
    }

    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }
} 