package com.ruoyi.system.domain.business;

import java.util.Date;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 主播信息对象 creator
 * 对应表: creators
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@ApiModel(value = "Creator", description = "主播信息实体")
public class Creator extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主播的唯一ID (Creator ID) */
    @ApiModelProperty(value = "主播的唯一ID", example = "1")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 直接上级主播的ID, 顶级为NULL */
    @ApiModelProperty(value = "直接上级主播的ID, 顶级为NULL", example = "0")
    @Excel(name = "上级主播ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 首次被确认为合格拉新的月份 (YYYY-MM-01), 用于确保唯一性 */
    @ApiModelProperty(value = "首次被确认为合格拉新的月份", example = "2025-07-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次合格月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstQualifiedMonth;

    /** 确认其合格时的直接上级(parent_id)快照, 用于永久锁定业绩归属 */
    @ApiModelProperty(value = "确认其合格时的直接上级ID快照", example = "1001")
    @Excel(name = "合格时招募人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long qualifiedByRecruiterId;

    /** 主播昵称 */
    @ApiModelProperty(value = "主播昵称", example = "超级主播")
    @Excel(name = "主播昵称")
    private String nickname;

    /** 主播的Handle */
    @ApiModelProperty(value = "主播的Handle", example = "super_creator_handle", required = true)
    @Excel(name = "主播Handle")
    private String handle;

    /** 首次活跃月份，用于新人识别 */
    @ApiModelProperty(value = "首次活跃月份，用于新人识别", example = "2025-07-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次活跃月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstActiveMonth;

    /** 招募人ID */
    @ApiModelProperty(value = "招募人ID", example = "1001")
    @Excel(name = "招募人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long recruitedBy;

    /** 当前基础等级ID, 关联commission_level_rules.id, 由"防守"体系更新 */
    @ApiModelProperty(value = "当前基础等级ID", example = "1")
    private Integer currentDistributorLevelId;

    /** 基础等级最后更新时间, 用于判断升级保护期 */
    @ApiModelProperty(value = "基础等级最后更新时间", example = "2025-07-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "等级更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date levelUpdatedAt;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }
    public void setNickname(String nickname)
    {
        this.nickname = nickname;
    }

    public String getNickname()
    {
        return nickname;
    }
    public void setHandle(String handle)
    {
        this.handle = handle;
    }

    public String getHandle()
    {
        return handle;
    }

    public void setFirstQualifiedMonth(Date firstQualifiedMonth)
    {
        this.firstQualifiedMonth = firstQualifiedMonth;
    }

    public Date getFirstQualifiedMonth()
    {
        return firstQualifiedMonth;
    }

    public void setQualifiedByRecruiterId(Long qualifiedByRecruiterId)
    {
        this.qualifiedByRecruiterId = qualifiedByRecruiterId;
    }

    public Long getQualifiedByRecruiterId()
    {
        return qualifiedByRecruiterId;
    }

    public void setFirstActiveMonth(Date firstActiveMonth)
    {
        this.firstActiveMonth = firstActiveMonth;
    }

    public Date getFirstActiveMonth()
    {
        return firstActiveMonth;
    }

    public void setRecruitedBy(Long recruitedBy)
    {
        this.recruitedBy = recruitedBy;
    }

    public Long getRecruitedBy()
    {
        return recruitedBy;
    }

    public void setCurrentDistributorLevelId(Integer currentDistributorLevelId)
    {
        this.currentDistributorLevelId = currentDistributorLevelId;
    }

    public Integer getCurrentDistributorLevelId()
    {
        return currentDistributorLevelId;
    }

    public void setLevelUpdatedAt(Date levelUpdatedAt)
    {
        this.levelUpdatedAt = levelUpdatedAt;
    }

    public Date getLevelUpdatedAt()
    {
        return levelUpdatedAt;
    }

    // createTime 和 updateTime 继承自 BaseEntity

    /**
     * 重写继承字段的getter方法以添加Swagger注解
     */
    @Override
    @ApiModelProperty(value = "创建者", example = "admin")
    public String getCreateBy() {
        return super.getCreateBy();
    }

    @Override
    @ApiModelProperty(value = "创建时间", example = "2025-07-01 10:00:00")
    public Date getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    @ApiModelProperty(value = "更新者", example = "admin")
    public String getUpdateBy() {
        return super.getUpdateBy();
    }

    @Override
    @ApiModelProperty(value = "更新时间", example = "2025-07-01 10:00:00")
    public Date getUpdateTime() {
        return super.getUpdateTime();
    }

    @Override
    @ApiModelProperty(value = "备注", example = "备注信息")
    public String getRemark() {
        return super.getRemark();
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("parentId", getParentId())
            .append("firstQualifiedMonth", getFirstQualifiedMonth())
            .append("qualifiedByRecruiterId", getQualifiedByRecruiterId())
            .append("nickname", getNickname())
            .append("handle", getHandle())
            .append("firstActiveMonth", getFirstActiveMonth())
            .append("recruitedBy", getRecruitedBy())
            .append("currentDistributorLevelId", getCurrentDistributorLevelId())
            .append("levelUpdatedAt", getLevelUpdatedAt())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
