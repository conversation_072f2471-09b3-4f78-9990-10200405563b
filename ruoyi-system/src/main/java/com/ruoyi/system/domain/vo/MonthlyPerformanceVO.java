package com.ruoyi.system.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 主播月度业绩VO对象，包含Creator信息
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@ApiModel(value = "MonthlyPerformanceVO", description = "主播月度业绩视图对象（包含主播信息）")
public class MonthlyPerformanceVO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID", example = "1")
    private Integer id;

    /** 关联到creators.id */
    @ApiModelProperty(value = "主播ID", example = "1001", required = true)
    @Excel(name = "主播ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long creatorId;

    /** 数据月份 (存储为每月第一天) */
    @ApiModelProperty(value = "数据月份", example = "2025-06-01", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** Group */
    @ApiModelProperty(value = "Group", example = "Group A")
    @Excel(name = "Group")
    private String groupName;

    /** Group manager */
    @ApiModelProperty(value = "Group manager", example = "Manager X")
    @Excel(name = "Group manager")
    private String groupManager;

    /** 创作者网络管理员 */
    @ApiModelProperty(value = "创作者网络管理员", example = "Network Manager")
    @Excel(name = "创作者网络管理员")
    private String creatorNetworkManager;

    /** 是否违规主播 (0=否, 1=是) */
    @ApiModelProperty(value = "是否违规主播 (0=否, 1=是)", example = "0")
    @Excel(name = "是否违规主播", readConverterExp = "0=否,1=是")
    private Integer isViolative;

    /** 是否新人主播 (0=否, 1=是) */
    @ApiModelProperty(value = "是否新人主播 (0=否, 1=是)", example = "0")
    @Excel(name = "是否新人主播", readConverterExp = "0=否,1=是")
    private Integer isRookie;

    /** 钻石数 */
    @ApiModelProperty(value = "钻石数", example = "100000")
    @Excel(name = "钻石数")
    private Long diamonds;

    /** 有效天数(d) */
    @ApiModelProperty(value = "有效天数(d)", example = "20")
    @Excel(name = "有效天数(d)")
    private Integer validDays;

    /** 直播时长(h) */
    @ApiModelProperty(value = "直播时长(h)", example = "150.50")
    @Excel(name = "直播时长(h)")
    private BigDecimal liveDurationHours;

    /** 估计的奖金 */
    @ApiModelProperty(value = "估计的奖金", example = "2500.75")
    @Excel(name = "估计的奖金")
    private BigDecimal bonusEstimated;

    /** 新人里程碑1保留任务奖金 */
    @ApiModelProperty(value = "新人里程碑1保留任务奖金", example = "500.00")
    @Excel(name = "新人里程碑1保留任务奖金")
    private String bonusRookieM1Retention;

    /** 新人里程碑2任务奖金 */
    @ApiModelProperty(value = "新人里程碑2任务奖金", example = "1000.00")
    @Excel(name = "新人里程碑2任务奖金")
    private BigDecimal bonusRookieM2;

    /** 新人半里程碑任务奖金 */
    @ApiModelProperty(value = "新人半里程碑任务奖金", example = "300.00")
    @Excel(name = "新人半里程碑任务奖金")
    private BigDecimal bonusRookieHalfMilestone;

    /** 新人里程碑1任务奖金 */
    @ApiModelProperty(value = "新人里程碑1任务奖金", example = "600.00")
    @Excel(name = "新人里程碑1任务奖金")
    private BigDecimal bonusRookieM1;

    /** 活跃任务奖金 */
    @ApiModelProperty(value = "活跃任务奖金", example = "200.00")
    @Excel(name = "活跃任务奖金")
    private BigDecimal bonusActiveness;

    /** 收入规模任务奖金 */
    @ApiModelProperty(value = "收入规模任务奖金", example = "800.00")
    @Excel(name = "收入规模任务奖金")
    private BigDecimal bonusRevenueScale;

    /** 新创作者网络任务奖金 */
    @ApiModelProperty(value = "新创作者网络任务奖金", example = "150.00")
    @Excel(name = "新创作者网络任务奖金")
    private BigDecimal bonusNewCreatorNetwork;

    // Creator 相关字段
    /** 主播昵称 */
    @ApiModelProperty(value = "主播昵称", example = "超级主播")
    @Excel(name = "主播昵称")
    private String nickname;

    /** 直接上级主播的ID */
    @ApiModelProperty(value = "直接上级主播的ID", example = "100")
    @Excel(name = "上级主播ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /** 主播创建时间 */
    @ApiModelProperty(value = "主播创建时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "主播创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date creatorCreatedAt;

    /** 主播备注 */
    @ApiModelProperty(value = "主播备注", example = "这是一个优秀的主播")
    @Excel(name = "主播备注")
    private String creatorRemark;

    /** 父级主播昵称 */
    @ApiModelProperty(value = "父级主播昵称", example = "上级主播")
    @Excel(name = "父级主播昵称")
    private String parentNickname;

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId()
    {
        return id;
    }

    public void setCreatorId(Long creatorId)
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId()
    {
        return creatorId;
    }

    public void setDataMonth(Date dataMonth)
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth()
    {
        return dataMonth;
    }

    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }

    public String getGroupName()
    {
        return groupName;
    }

    public void setGroupManager(String groupManager)
    {
        this.groupManager = groupManager;
    }

    public String getGroupManager()
    {
        return groupManager;
    }

    public void setCreatorNetworkManager(String creatorNetworkManager)
    {
        this.creatorNetworkManager = creatorNetworkManager;
    }

    public String getCreatorNetworkManager()
    {
        return creatorNetworkManager;
    }

    public void setIsViolative(Integer isViolative)
    {
        this.isViolative = isViolative;
    }

    public Integer getIsViolative()
    {
        return isViolative;
    }

    public void setIsRookie(Integer isRookie)
    {
        this.isRookie = isRookie;
    }

    public Integer getIsRookie()
    {
        return isRookie;
    }

    public void setDiamonds(Long diamonds)
    {
        this.diamonds = diamonds;
    }

    public Long getDiamonds()
    {
        return diamonds;
    }

    public void setValidDays(Integer validDays)
    {
        this.validDays = validDays;
    }

    public Integer getValidDays()
    {
        return validDays;
    }

    public void setLiveDurationHours(BigDecimal liveDurationHours)
    {
        this.liveDurationHours = liveDurationHours;
    }

    public BigDecimal getLiveDurationHours()
    {
        return liveDurationHours;
    }

    public void setBonusEstimated(BigDecimal bonusEstimated)
    {
        this.bonusEstimated = bonusEstimated;
    }

    public BigDecimal getBonusEstimated()
    {
        return bonusEstimated;
    }

    public void setBonusRookieM1Retention(String bonusRookieM1Retention)
    {
        this.bonusRookieM1Retention = bonusRookieM1Retention;
    }

    public String getBonusRookieM1Retention()
    {
        return bonusRookieM1Retention;
    }

    public void setBonusRookieM2(BigDecimal bonusRookieM2)
    {
        this.bonusRookieM2 = bonusRookieM2;
    }

    public BigDecimal getBonusRookieM2()
    {
        return bonusRookieM2;
    }

    public void setBonusRookieHalfMilestone(BigDecimal bonusRookieHalfMilestone)
    {
        this.bonusRookieHalfMilestone = bonusRookieHalfMilestone;
    }

    public BigDecimal getBonusRookieHalfMilestone()
    {
        return bonusRookieHalfMilestone;
    }

    public void setBonusRookieM1(BigDecimal bonusRookieM1)
    {
        this.bonusRookieM1 = bonusRookieM1;
    }

    public BigDecimal getBonusRookieM1()
    {
        return bonusRookieM1;
    }

    public void setBonusActiveness(BigDecimal bonusActiveness)
    {
        this.bonusActiveness = bonusActiveness;
    }

    public BigDecimal getBonusActiveness()
    {
        return bonusActiveness;
    }

    public void setBonusRevenueScale(BigDecimal bonusRevenueScale)
    {
        this.bonusRevenueScale = bonusRevenueScale;
    }

    public BigDecimal getBonusRevenueScale()
    {
        return bonusRevenueScale;
    }

    public void setBonusNewCreatorNetwork(BigDecimal bonusNewCreatorNetwork)
    {
        this.bonusNewCreatorNetwork = bonusNewCreatorNetwork;
    }

    public BigDecimal getBonusNewCreatorNetwork()
    {
        return bonusNewCreatorNetwork;
    }

    public void setNickname(String nickname)
    {
        this.nickname = nickname;
    }

    public String getNickname()
    {
        return nickname;
    }

    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }

    public void setCreatorCreatedAt(Date creatorCreatedAt)
    {
        this.creatorCreatedAt = creatorCreatedAt;
    }

    public Date getCreatorCreatedAt()
    {
        return creatorCreatedAt;
    }

    public void setCreatorRemark(String creatorRemark)
    {
        this.creatorRemark = creatorRemark;
    }

    public String getCreatorRemark()
    {
        return creatorRemark;
    }

    public void setParentNickname(String parentNickname)
    {
        this.parentNickname = parentNickname;
    }

    public String getParentNickname()
    {
        return parentNickname;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("creatorId", getCreatorId())
            .append("dataMonth", getDataMonth())
            .append("groupName", getGroupName())
            .append("groupManager", getGroupManager())
            .append("creatorNetworkManager", getCreatorNetworkManager())
            .append("isViolative", getIsViolative())
            .append("isRookie", getIsRookie())
            .append("diamonds", getDiamonds())
            .append("validDays", getValidDays())
            .append("liveDurationHours", getLiveDurationHours())
            .append("bonusEstimated", getBonusEstimated())
            .append("bonusRookieM1Retention", getBonusRookieM1Retention())
            .append("bonusRookieM2", getBonusRookieM2())
            .append("bonusRookieHalfMilestone", getBonusRookieHalfMilestone())
            .append("bonusRookieM1", getBonusRookieM1())
            .append("bonusActiveness", getBonusActiveness())
            .append("bonusRevenueScale", getBonusRevenueScale())
            .append("bonusNewCreatorNetwork", getBonusNewCreatorNetwork())
            .append("nickname", getNickname())
            .append("parentId", getParentId())
            .append("creatorCreatedAt", getCreatorCreatedAt())
            .append("creatorRemark", getCreatorRemark())
            .append("parentNickname", getParentNickname())
            .toString();
    }
} 