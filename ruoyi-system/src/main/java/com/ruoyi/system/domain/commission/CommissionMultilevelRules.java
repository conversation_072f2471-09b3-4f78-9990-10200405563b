package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 多级提成规则对象 commission_multilevel_rules
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "多级提成规则对象")
public class CommissionMultilevelRules extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    @ApiModelProperty(value = "规则ID")
    private Integer id;

    /** 下级深度 (1=L1, 2=L2, 3=L3) */
    @ApiModelProperty(value = "下级深度", required = true)
    @NotNull(message = "下级深度不能为空")
    @Min(value = 1, message = "下级深度不能小于1")
    @Max(value = 3, message = "下级深度不能大于3")
    @Excel(name = "下级深度")
    private Integer depth;

    /** 该层级的提成比例 (例如 0.0100 代表 1%) */
    @ApiModelProperty(value = "提成比例", required = true)
    @NotNull(message = "提成比例不能为空")
    @DecimalMin(value = "0", message = "提成比例不能小于0")
    @DecimalMax(value = "1", message = "提成比例不能大于1")
    @Excel(name = "提成比例")
    private BigDecimal commissionRate;

    /** 是否启用 (1=是, 0=否) */
    @ApiModelProperty(value = "是否启用")
    @Excel(name = "是否启用", readConverterExp = "1=是,0=否")
    private Integer isActive;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setDepth(Integer depth) 
    {
        this.depth = depth;
    }

    public Integer getDepth() 
    {
        return depth;
    }
    public void setCommissionRate(BigDecimal commissionRate) 
    {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getCommissionRate() 
    {
        return commissionRate;
    }
    public void setIsActive(Integer isActive) 
    {
        this.isActive = isActive;
    }

    public Integer getIsActive() 
    {
        return isActive;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("depth", getDepth())
            .append("commissionRate", getCommissionRate())
            .append("isActive", getIsActive())
            .toString();
    }
} 