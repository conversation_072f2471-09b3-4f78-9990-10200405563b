package com.ruoyi.system.domain.business;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 团队动态与成就事件日志对象 team_events
 * 对应表: team_events
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(value = "TeamEvents", description = "团队动态与成就事件日志实体")
public class TeamEvents extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    /** 事件关联人ID (如: 团队领导) */
    @ApiModelProperty(value = "事件关联人ID", example = "1001", required = true)
    @Excel(name = "事件关联人ID")
    private Long actorCreatorId;

    /** 事件目标者ID (如: 新人, 晋升者) */
    @ApiModelProperty(value = "事件目标者ID", example = "1002")
    @Excel(name = "事件目标者ID")
    private Long targetCreatorId;

    /** 事件类型 (LEVEL_UP, NEW_RECRUIT, PERFORMANCE_MILESTONE, APPROACHING_MILESTONE) */
    @ApiModelProperty(value = "事件类型", example = "LEVEL_UP", required = true)
    @Excel(name = "事件类型")
    private String eventType;

    /** 事件发生的确切时间 */
    @ApiModelProperty(value = "事件发生时间", example = "2025-07-04 10:30:00", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date eventTimestamp;

    /** 事件详情 (如: {"new_level": "Gold", "current_value": 22, "target_value": 24}) */
    @ApiModelProperty(value = "事件详情JSON", example = "{\"new_level\": \"Gold\"}")
    @Excel(name = "事件详情")
    private String details;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setActorCreatorId(Long actorCreatorId) 
    {
        this.actorCreatorId = actorCreatorId;
    }

    public Long getActorCreatorId() 
    {
        return actorCreatorId;
    }

    public void setTargetCreatorId(Long targetCreatorId) 
    {
        this.targetCreatorId = targetCreatorId;
    }

    public Long getTargetCreatorId() 
    {
        return targetCreatorId;
    }

    public void setEventType(String eventType) 
    {
        this.eventType = eventType;
    }

    public String getEventType() 
    {
        return eventType;
    }

    public void setEventTimestamp(Date eventTimestamp) 
    {
        this.eventTimestamp = eventTimestamp;
    }

    public Date getEventTimestamp() 
    {
        return eventTimestamp;
    }

    public void setDetails(String details) 
    {
        this.details = details;
    }

    public String getDetails() 
    {
        return details;
    }

    @Override
    public String toString() {
        return "TeamEvents{" +
                "id=" + id +
                ", actorCreatorId=" + actorCreatorId +
                ", targetCreatorId=" + targetCreatorId +
                ", eventType='" + eventType + '\'' +
                ", eventTimestamp=" + eventTimestamp +
                ", details='" + details + '\'' +
                ", createBy='" + getCreateBy() + '\'' +
                ", createTime=" + getCreateTime() +
                ", updateBy='" + getUpdateBy() + '\'' +
                ", updateTime=" + getUpdateTime() +
                ", remark='" + getRemark() + '\'' +
                '}';
    }
}
