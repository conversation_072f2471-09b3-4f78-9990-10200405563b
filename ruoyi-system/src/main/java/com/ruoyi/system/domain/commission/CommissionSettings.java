package com.ruoyi.system.domain.commission;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 全局佣金配置对象 commission_settings
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "全局佣金配置对象")
public class CommissionSettings extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置项的唯一键 */
    @ApiModelProperty(value = "配置项的唯一键", required = true)
    @NotBlank(message = "配置项键不能为空")
    @Size(min = 0, max = 100, message = "配置项键长度不能超过100个字符")
    @Excel(name = "配置键")
    private String settingKey;

    /** 配置项的值 */
    @ApiModelProperty(value = "配置项的值", required = true)
    @NotBlank(message = "配置项值不能为空")
    @Size(min = 0, max = 255, message = "配置项值长度不能超过255个字符")
    @Excel(name = "配置值")
    private String settingValue;

    /** 配置项描述 */
    @ApiModelProperty(value = "配置项描述")
    @Size(min = 0, max = 500, message = "配置项描述长度不能超过500个字符")
    @Excel(name = "配置描述")
    private String description;

    public void setSettingKey(String settingKey) 
    {
        this.settingKey = settingKey;
    }

    public String getSettingKey() 
    {
        return settingKey;
    }
    public void setSettingValue(String settingValue) 
    {
        this.settingValue = settingValue;
    }

    public String getSettingValue() 
    {
        return settingValue;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("settingKey", getSettingKey())
            .append("settingValue", getSettingValue())
            .append("description", getDescription())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 