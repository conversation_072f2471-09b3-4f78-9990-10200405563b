package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;

/**
 * 佣金月度计算总览对象 commission_monthly_summary
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "佣金月度计算总览对象")
public class CommissionMonthlySummary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据月份 (YYYY-MM-01) */
    @ApiModelProperty(value = "数据月份", required = true)
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 当月预算收入 (来自monthly_settings) */
    @ApiModelProperty(value = "当月预算收入(USD)", required = true)
    @NotNull(message = "当月预算收入不能为空")
    @DecimalMin(value = "0", message = "当月预算收入不能小于0")
    @Excel(name = "当月预算收入(USD)")
    private BigDecimal budgetUsd;

    /** 当月支出上限 (USD) */
    @ApiModelProperty(value = "当月支出上限(USD)", required = true)
    @NotNull(message = "当月支出上限不能为空")
    @DecimalMin(value = "0", message = "当月支出上限不能小于0")
    @Excel(name = "当月支出上限(USD)")
    private BigDecimal payoutCapUsd;

    /** 实际总支出 (USD) */
    @ApiModelProperty(value = "实际总支出(USD)", required = true)
    @NotNull(message = "实际总支出不能为空")
    @DecimalMin(value = "0", message = "实际总支出不能小于0")
    @Excel(name = "实际总支出(USD)")
    private BigDecimal actualPayoutUsd;

    /** 支出/收入比例 */
    @ApiModelProperty(value = "支出/收入比例", required = true)
    @NotNull(message = "支出/收入比例不能为空")
    @DecimalMin(value = "0", message = "支出/收入比例不能小于0")
    @Excel(name = "支出/收入比例")
    private BigDecimal payoutToBudgetRatio;

    /** 超出预算金额 (若未超出则为0) */
    @ApiModelProperty(value = "超出预算金额(USD)", required = true)
    @NotNull(message = "超出预算金额不能为空")
    @DecimalMin(value = "0", message = "超出预算金额不能小于0")
    @Excel(name = "超出预算金额(USD)")
    private BigDecimal exceededAmountUsd;

    /** 计算状态 */
    @ApiModelProperty(value = "计算状态", required = true, allowableValues = "PENDING,PROCESSING,COMPLETED,FAILED")
    @Excel(name = "计算状态")
    private String calculationStatus;

    /** 计算执行时间 */
    @ApiModelProperty(value = "计算执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计算执行时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date calculatedAt;

    /** 当月主播奖金估计总额 (来自monthly_performance.bonus_estimated累计) */
    @ApiModelProperty(value = "当月主播奖金估计总额", required = false)
    @DecimalMin(value = "0", message = "当月主播奖金估计总额不能小于0")
    @Excel(name = "当月主播奖金估计总额")
    private BigDecimal totalBonusEstimated;

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setBudgetUsd(BigDecimal budgetUsd) 
    {
        this.budgetUsd = budgetUsd;
    }

    public BigDecimal getBudgetUsd() 
    {
        return budgetUsd;
    }

    public void setPayoutCapUsd(BigDecimal payoutCapUsd) 
    {
        this.payoutCapUsd = payoutCapUsd;
    }

    public BigDecimal getPayoutCapUsd() 
    {
        return payoutCapUsd;
    }

    public void setActualPayoutUsd(BigDecimal actualPayoutUsd) 
    {
        this.actualPayoutUsd = actualPayoutUsd;
    }

    public BigDecimal getActualPayoutUsd() 
    {
        return actualPayoutUsd;
    }

    public void setPayoutToBudgetRatio(BigDecimal payoutToBudgetRatio) 
    {
        this.payoutToBudgetRatio = payoutToBudgetRatio;
    }

    public BigDecimal getPayoutToBudgetRatio() 
    {
        return payoutToBudgetRatio;
    }

    public void setExceededAmountUsd(BigDecimal exceededAmountUsd) 
    {
        this.exceededAmountUsd = exceededAmountUsd;
    }

    public BigDecimal getExceededAmountUsd() 
    {
        return exceededAmountUsd;
    }

    public void setCalculationStatus(String calculationStatus) 
    {
        this.calculationStatus = calculationStatus;
    }

    public String getCalculationStatus() 
    {
        return calculationStatus;
    }

    public void setCalculatedAt(Date calculatedAt) 
    {
        this.calculatedAt = calculatedAt;
    }

    public Date getCalculatedAt() 
    {
        return calculatedAt;
    }

    public void setTotalBonusEstimated(BigDecimal totalBonusEstimated) 
    {
        this.totalBonusEstimated = totalBonusEstimated;
    }

    public BigDecimal getTotalBonusEstimated() 
    {
        return totalBonusEstimated;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dataMonth", getDataMonth())
            .append("budgetUsd", getBudgetUsd())
            .append("payoutCapUsd", getPayoutCapUsd())
            .append("actualPayoutUsd", getActualPayoutUsd())
            .append("payoutToBudgetRatio", getPayoutToBudgetRatio())
            .append("exceededAmountUsd", getExceededAmountUsd())
            .append("calculationStatus", getCalculationStatus())
            .append("calculatedAt", getCalculatedAt())
            .append("totalBonusEstimated", getTotalBonusEstimated())
            .toString();
    }
} 