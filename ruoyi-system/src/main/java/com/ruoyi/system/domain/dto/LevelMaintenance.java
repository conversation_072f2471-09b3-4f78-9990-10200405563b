package com.ruoyi.system.domain.dto;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 保级状态对象
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "保级状态")
public class LevelMaintenance
{
    /** 保级任务完成度百分比 */
    @ApiModelProperty(value = "保级任务完成度百分比")
    private BigDecimal progressPercentage;

    /** 保级任务的目标增长率 */
    @ApiModelProperty(value = "保级任务的目标增长率")
    private BigDecimal targetGrowthRate;

    public BigDecimal getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(BigDecimal progressPercentage) {
        this.progressPercentage = progressPercentage;
    }

    public BigDecimal getTargetGrowthRate() {
        return targetGrowthRate;
    }

    public void setTargetGrowthRate(BigDecimal targetGrowthRate) {
        this.targetGrowthRate = targetGrowthRate;
    }
}
