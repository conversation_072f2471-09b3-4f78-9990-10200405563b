package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 组关系显示VO
 * 用于显示组与creators的关系
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@ApiModel(value = "GroupRelationVO", description = "组关系显示VO")
public class GroupRelationVO
{
    /** 组名 */
    @ApiModelProperty(value = "组名", example = "GroupA")
    private String groupName;

    /** 创建者ID */
    @ApiModelProperty(value = "创建者ID", example = "1001")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long creatorId;

    /** 组类型：0-分销组，1-非分销组 */
    @ApiModelProperty(value = "组类型", example = "0", notes = "0-分销组，1-非分销组")
    private Integer groupType;

    /** 创建者昵称 */
    @ApiModelProperty(value = "创建者昵称", example = "主播昵称")
    private String nickname;

    /** 创建者Handle */
    @ApiModelProperty(value = "创建者Handle", example = "creator_handle")
    private String handle;

    /** 关系建立时间 */
    @ApiModelProperty(value = "关系建立时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 是否已指定上级 */
    @ApiModelProperty(value = "是否已指定上级", example = "true")
    private Boolean hasParent;

    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }

    public String getGroupName()
    {
        return groupName;
    }

    public void setCreatorId(Long creatorId)
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId()
    {
        return creatorId;
    }

    public void setGroupType(Integer groupType)
    {
        this.groupType = groupType;
    }

    public Integer getGroupType()
    {
        return groupType;
    }

    public void setNickname(String nickname)
    {
        this.nickname = nickname;
    }

    public String getNickname()
    {
        return nickname;
    }

    public void setHandle(String handle)
    {
        this.handle = handle;
    }

    public String getHandle()
    {
        return handle;
    }

    public void setCreatedAt(Date createdAt)
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt()
    {
        return createdAt;
    }

    public void setHasParent(Boolean hasParent)
    {
        this.hasParent = hasParent;
    }

    public Boolean getHasParent()
    {
        return hasParent;
    }
} 