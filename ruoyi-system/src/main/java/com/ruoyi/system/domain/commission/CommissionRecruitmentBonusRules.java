package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;

/**
 * 拉新奖励规则对象 commission_recruitment_bonus_rules
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "拉新奖励规则对象")
public class CommissionRecruitmentBonusRules extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    @ApiModelProperty(value = "规则ID")
    private Integer id;

    /** 月拉新人数要求 (阶梯下限) */
    @ApiModelProperty(value = "月拉新人数要求", required = true)
    @NotNull(message = "月拉新人数要求不能为空")
    @Min(value = 0, message = "月拉新人数要求不能小于0")
    @Excel(name = "月拉新人数要求")
    private Integer minNewRecruits;

    /** 对应的奖励金额 (USD) */
    @ApiModelProperty(value = "奖励金额(USD)", required = true)
    @NotNull(message = "奖励金额不能为空")
    @DecimalMin(value = "0", message = "奖励金额不能小于0")
    @Excel(name = "奖励金额(USD)")
    private BigDecimal bonusUsd;

    /** 是否启用 (1=是, 0=否) */
    @ApiModelProperty(value = "是否启用")
    @Excel(name = "是否启用", readConverterExp = "1=是,0=否")
    private Integer isActive;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setMinNewRecruits(Integer minNewRecruits) 
    {
        this.minNewRecruits = minNewRecruits;
    }

    public Integer getMinNewRecruits() 
    {
        return minNewRecruits;
    }
    public void setBonusUsd(BigDecimal bonusUsd) 
    {
        this.bonusUsd = bonusUsd;
    }

    public BigDecimal getBonusUsd() 
    {
        return bonusUsd;
    }
    public void setIsActive(Integer isActive) 
    {
        this.isActive = isActive;
    }

    public Integer getIsActive() 
    {
        return isActive;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("minNewRecruits", getMinNewRecruits())
            .append("bonusUsd", getBonusUsd())
            .append("isActive", getIsActive())
            .toString();
    }
} 