package com.ruoyi.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分销员月度业绩分析报告响应对象
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "分销员月度业绩分析报告响应对象")
public class MonthlyPerformanceReportResponse
{
    /** 选择的基准月份 */
    @ApiModelProperty(value = "选择的基准月份", example = "2024-12")
    private String selectedMonth;

    /** 顶部摘要信息 */
    @ApiModelProperty(value = "顶部摘要信息")
    private Summary summary;

    /** 6个月趋势数据点数组 */
    @ApiModelProperty(value = "6个月趋势数据点数组")
    private List<TrendData> trendData;

    /** 数据洞察 */
    @ApiModelProperty(value = "数据洞察")
    private Insights insights;

    /**
     * 顶部摘要信息
     */
    @ApiModel(description = "顶部摘要信息")
    public static class Summary
    {
        /** 截至所选月份末的年度累计总收入 */
        @ApiModelProperty(value = "截至所选月份末的年度累计总收入", example = "456800.00")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal totalIncomeToDate;

        /** 所选月份的收入环比增长率 */
        @ApiModelProperty(value = "所选月份的收入环比增长率", example = "0.152")
        private BigDecimal selectedMonthGrowth;

        public BigDecimal getTotalIncomeToDate() {
            return totalIncomeToDate;
        }

        public void setTotalIncomeToDate(BigDecimal totalIncomeToDate) {
            this.totalIncomeToDate = totalIncomeToDate;
        }

        public BigDecimal getSelectedMonthGrowth() {
            return selectedMonthGrowth;
        }

        public void setSelectedMonthGrowth(BigDecimal selectedMonthGrowth) {
            this.selectedMonthGrowth = selectedMonthGrowth;
        }
    }

    /**
     * 趋势数据点
     */
    @ApiModel(description = "趋势数据点")
    public static class TrendData
    {
        /** 月份 */
        @ApiModelProperty(value = "月份", example = "2024-07")
        private String month;

        /** 收入金额 */
        @ApiModelProperty(value = "收入金额", example = "12500")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal income;

        /** 环比增长率 */
        @ApiModelProperty(value = "环比增长率", example = "0.512")
        private BigDecimal growth;

        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public BigDecimal getIncome() {
            return income;
        }

        public void setIncome(BigDecimal income) {
            this.income = income;
        }

        public BigDecimal getGrowth() {
            return growth;
        }

        public void setGrowth(BigDecimal growth) {
            this.growth = growth;
        }
    }

    /**
     * 数据洞察
     */
    @ApiModel(description = "数据洞察")
    public static class Insights
    {
        /** 最佳表现月份 */
        @ApiModelProperty(value = "最佳表现月份")
        private BestMonth bestMonth;

        /** 增长趋势 */
        @ApiModelProperty(value = "增长趋势")
        private GrowthTrend growthTrend;

        public BestMonth getBestMonth() {
            return bestMonth;
        }

        public void setBestMonth(BestMonth bestMonth) {
            this.bestMonth = bestMonth;
        }

        public GrowthTrend getGrowthTrend() {
            return growthTrend;
        }

        public void setGrowthTrend(GrowthTrend growthTrend) {
            this.growthTrend = growthTrend;
        }
    }

    /**
     * 最佳表现月份
     */
    @ApiModel(description = "最佳表现月份")
    public static class BestMonth
    {
        /** 月份 */
        @ApiModelProperty(value = "月份", example = "2024-12")
        private String month;

        /** 收入金额 */
        @ApiModelProperty(value = "收入金额", example = "38600")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private BigDecimal income;

        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public BigDecimal getIncome() {
            return income;
        }

        public void setIncome(BigDecimal income) {
            this.income = income;
        }
    }

    /**
     * 增长趋势
     */
    @ApiModel(description = "增长趋势")
    public static class GrowthTrend
    {
        /** 趋势类型 */
        @ApiModelProperty(value = "趋势类型", example = "UPWARD", allowableValues = "UPWARD,DOWNWARD,STABLE")
        private String type;

        /** 趋势描述信息 */
        @ApiModelProperty(value = "趋势描述信息", example = "Continuous upward trend, excellent performance")
        private String message;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    // 主类的getter和setter方法
    public String getSelectedMonth() {
        return selectedMonth;
    }

    public void setSelectedMonth(String selectedMonth) {
        this.selectedMonth = selectedMonth;
    }

    public Summary getSummary() {
        return summary;
    }

    public void setSummary(Summary summary) {
        this.summary = summary;
    }

    public List<TrendData> getTrendData() {
        return trendData;
    }

    public void setTrendData(List<TrendData> trendData) {
        this.trendData = trendData;
    }

    public Insights getInsights() {
        return insights;
    }

    public void setInsights(Insights insights) {
        this.insights = insights;
    }
}
