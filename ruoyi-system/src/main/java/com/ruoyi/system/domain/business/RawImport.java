package com.ruoyi.system.domain.business;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 原始Excel数据导入记录对象 raw_imports
 * 对应表: raw_imports
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@ApiModel(value = "RawImport", description = "原始Excel数据导入记录实体")
public class RawImport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /** 源文件名 */
    @ApiModelProperty(value = "源文件名", example = "主播数据.xlsx")
    @Excel(name = "源文件名")
    private String fileName;

    /** 导入时间 */
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "Import Time", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date importedAt;

    /** 数据月份 */
    @ApiModelProperty(value = "数据月份", example = "2023-01")
    @Excel(name = "Data Month")
    private String dataMonth;

    /** 主播ID */
    @ApiModelProperty(value = "主播ID", example = "creator123")
    @Excel(name = "Creator ID")
    private String creatorId;

    /** 主播昵称 */
    @ApiModelProperty(value = "主播昵称", example = "主播小明")
    @Excel(name = "Creator nickname")
    private String creatorNickname;

    /** Handle (可能是主播的唯一标识或账号) */
    @ApiModelProperty(value = "Handle", example = "ming")
    @Excel(name = "Handle")
    private String handle;

    /** 主播运营 */
    @ApiModelProperty(value = "主播运营", example = "运营A")
    @Excel(name = "Creator Network manager")
    private String creatorNetworkManager;

    /** 小组名称 */
    @ApiModelProperty(value = "小组名称", example = "一组")
    @Excel(name = "Group")
    private String groupName;

    /** 小组组长 */
    @ApiModelProperty(value = "小组组长", example = "组长B")
    @Excel(name = "Group manager")
    private String groupManager;

    /** 是否违规主播 */
    @ApiModelProperty(value = "是否违规主播", example = "否")
    @Excel(name = "Is violative creators")
    private String isViolativeCreators;

    /** 是否新秀主播 */
    @ApiModelProperty(value = "是否新秀主播", example = "是")
    @Excel(name = "The creator was Rookie at the time of first joining")
    private String theCreatorWasRookie;

    /** 钻石数 */
    @ApiModelProperty(value = "钻石数", example = "10000")
    @Excel(name = "Diamonds")
    private String diamonds;

    /** 有效天数 */
    @ApiModelProperty(value = "有效天数", example = "20")
    @Excel(name = "Valid days(d)")
    private String validDays;

    /** 直播时长(小时) */
    @ApiModelProperty(value = "直播时长(小时)", example = "80.5")
    @Excel(name = "LIVE duration(h)")
    private String liveDurationH;

    /** 预估奖金 */
    @ApiModelProperty(value = "预估奖金", example = "500.00")
    @Excel(name = "Estimated bonus")
    private String estimatedBonus;

    /** 新秀M1留存奖金 */
    @ApiModelProperty(value = "新秀M1留存奖金", example = "50.00")
    @Excel(name = "Estimated bonus - Rookie milestone 1 retention bonus task")
    private String estBonusRookieM1Retention;

    /** 新秀M2奖金 */
    @ApiModelProperty(value = "新秀M2奖金", example = "100.00")
    @Excel(name = "Estimated bonus - Rookie milestone 2 bonus task")
    private String estBonusRookieM2;

    /** 新秀半程碑奖金 */
    @ApiModelProperty(value = "新秀半程碑奖金", example = "80.00")
    @Excel(name = "Estimated bonus - Rookie half-milestone bonus task")
    private String estBonusRookieHalfMilestone;

    /** 新秀M1奖金 */
    @ApiModelProperty(value = "新秀M1奖金", example = "120.00")
    @Excel(name = "Estimated bonus - Rookie milestone 1 bonus task")
    private String estBonusRookieM1;

    /** 活跃任务奖金 */
    @ApiModelProperty(value = "活跃任务奖金", example = "30.00")
    @Excel(name = "Estimated bonus - Activeness task task")
    private String estBonusActivenessTask;

    /** 营收规模任务奖金 */
    @ApiModelProperty(value = "营收规模任务奖金", example = "40.00")
    @Excel(name = "Estimated bonus - Revenue scale task task")
    private String estBonusRevenueScaleTask;

    /** 新主播网络任务奖金 */
    @ApiModelProperty(value = "新主播网络任务奖金", example = "20.00")
    @Excel(name = "Estimated bonus - New Creator Network task task")
    private String estBonusNewCreatorNetworkTask;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getImportedAt() {
        return importedAt;
    }

    public void setImportedAt(Date importedAt) {
        this.importedAt = importedAt;
    }

    public String getDataMonth() {
        return dataMonth;
    }

    public void setDataMonth(String dataMonth) {
        this.dataMonth = dataMonth;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorNickname() {
        return creatorNickname;
    }

    public void setCreatorNickname(String creatorNickname) {
        this.creatorNickname = creatorNickname;
    }

    public String getHandle() {
        return handle;
    }

    public void setHandle(String handle) {
        this.handle = handle;
    }

    public String getCreatorNetworkManager() {
        return creatorNetworkManager;
    }

    public void setCreatorNetworkManager(String creatorNetworkManager) {
        this.creatorNetworkManager = creatorNetworkManager;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupManager() {
        return groupManager;
    }

    public void setGroupManager(String groupManager) {
        this.groupManager = groupManager;
    }

    public String getIsViolativeCreators() {
        return isViolativeCreators;
    }

    public void setIsViolativeCreators(String isViolativeCreators) {
        this.isViolativeCreators = isViolativeCreators;
    }

    public String getTheCreatorWasRookie() {
        return theCreatorWasRookie;
    }

    public void setTheCreatorWasRookie(String theCreatorWasRookie) {
        this.theCreatorWasRookie = theCreatorWasRookie;
    }

    public String getDiamonds() {
        return diamonds;
    }

    public void setDiamonds(String diamonds) {
        this.diamonds = diamonds;
    }

    public String getValidDays() {
        return validDays;
    }

    public void setValidDays(String validDays) {
        this.validDays = validDays;
    }

    public String getLiveDurationH() {
        return liveDurationH;
    }

    public void setLiveDurationH(String liveDurationH) {
        this.liveDurationH = liveDurationH;
    }

    public String getEstimatedBonus() {
        return estimatedBonus;
    }

    public void setEstimatedBonus(String estimatedBonus) {
        this.estimatedBonus = estimatedBonus;
    }

    public String getEstBonusRookieM1Retention() {
        return estBonusRookieM1Retention;
    }

    public void setEstBonusRookieM1Retention(String estBonusRookieM1Retention) {
        this.estBonusRookieM1Retention = estBonusRookieM1Retention;
    }

    public String getEstBonusRookieM2() {
        return estBonusRookieM2;
    }

    public void setEstBonusRookieM2(String estBonusRookieM2) {
        this.estBonusRookieM2 = estBonusRookieM2;
    }

    public String getEstBonusRookieHalfMilestone() {
        return estBonusRookieHalfMilestone;
    }

    public void setEstBonusRookieHalfMilestone(String estBonusRookieHalfMilestone) {
        this.estBonusRookieHalfMilestone = estBonusRookieHalfMilestone;
    }

    public String getEstBonusRookieM1() {
        return estBonusRookieM1;
    }

    public void setEstBonusRookieM1(String estBonusRookieM1) {
        this.estBonusRookieM1 = estBonusRookieM1;
    }

    public String getEstBonusActivenessTask() {
        return estBonusActivenessTask;
    }

    public void setEstBonusActivenessTask(String estBonusActivenessTask) {
        this.estBonusActivenessTask = estBonusActivenessTask;
    }

    public String getEstBonusRevenueScaleTask() {
        return estBonusRevenueScaleTask;
    }

    public void setEstBonusRevenueScaleTask(String estBonusRevenueScaleTask) {
        this.estBonusRevenueScaleTask = estBonusRevenueScaleTask;
    }

    public String getEstBonusNewCreatorNetworkTask() {
        return estBonusNewCreatorNetworkTask;
    }

    public void setEstBonusNewCreatorNetworkTask(String estBonusNewCreatorNetworkTask) {
        this.estBonusNewCreatorNetworkTask = estBonusNewCreatorNetworkTask;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("fileName", getFileName())
            .append("importedAt", getImportedAt())
            .append("dataMonth", getDataMonth())
            .append("creatorId", getCreatorId())
            .append("creatorNickname", getCreatorNickname())
            .append("handle", getHandle())
            .append("creatorNetworkManager", getCreatorNetworkManager())
            .append("groupName", getGroupName())
            .append("groupManager", getGroupManager())
            .append("isViolativeCreators", getIsViolativeCreators())
            .append("theCreatorWasRookie", getTheCreatorWasRookie())
            .append("diamonds", getDiamonds())
            .append("validDays", getValidDays())
            .append("liveDurationH", getLiveDurationH())
            .append("estimatedBonus", getEstimatedBonus())
            .append("estBonusRookieM1Retention", getEstBonusRookieM1Retention())
            .append("estBonusRookieM2", getEstBonusRookieM2())
            .append("estBonusRookieHalfMilestone", getEstBonusRookieHalfMilestone())
            .append("estBonusRookieM1", getEstBonusRookieM1())
            .append("estBonusActivenessTask", getEstBonusActivenessTask())
            .append("estBonusRevenueScaleTask", getEstBonusRevenueScaleTask())
            .append("estBonusNewCreatorNetworkTask", getEstBonusNewCreatorNetworkTask())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
