package com.ruoyi.system.domain.commission;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 佣金计算日志对象 commission_calculation_logs
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "佣金计算日志对象")
public class CommissionCalculationLogs extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    @ApiModelProperty(value = "日志ID")
    private Long id;

    /** 数据月份 */
    @ApiModelProperty(value = "数据月份", required = true)
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 计算类型 */
    @ApiModelProperty(value = "计算类型", required = true)
    @NotBlank(message = "计算类型不能为空")
    @Excel(name = "计算类型")
    private String calculationType;

    /** 分销员ID */
    @ApiModelProperty(value = "分销员ID")
    @Excel(name = "分销员ID")
    private Long creatorId;

    /** 日志级别 */
    @ApiModelProperty(value = "日志级别", allowableValues = "INFO,WARN,ERROR")
    @NotBlank(message = "日志级别不能为空")
    @Excel(name = "日志级别")
    private String logLevel;

    /** 日志消息 */
    @ApiModelProperty(value = "日志消息", required = true)
    @NotBlank(message = "日志消息不能为空")
    @Excel(name = "日志消息")
    private String message;

    /** 详细信息 */
    @ApiModelProperty(value = "详细信息")
    private String details;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setCalculationType(String calculationType) 
    {
        this.calculationType = calculationType;
    }

    public String getCalculationType() 
    {
        return calculationType;
    }

    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }

    public void setLogLevel(String logLevel) 
    {
        this.logLevel = logLevel;
    }

    public String getLogLevel() 
    {
        return logLevel;
    }

    public void setMessage(String message) 
    {
        this.message = message;
    }

    public String getMessage() 
    {
        return message;
    }

    public void setDetails(String details) 
    {
        this.details = details;
    }

    public String getDetails() 
    {
        return details;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataMonth", getDataMonth())
            .append("calculationType", getCalculationType())
            .append("creatorId", getCreatorId())
            .append("logLevel", getLogLevel())
            .append("message", getMessage())
            .append("details", getDetails())
            .append("createdAt", getCreatedAt())
            .toString();
    }
} 