package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 全局系统配置对象 system_config
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SystemConfig", description = "全局系统配置表")
public class SystemConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置键 (唯一标识符) */
    @ApiModelProperty(value = "配置键 (唯一标识符)", required = true)
    @NotBlank(message = "配置键不能为空")
    @Size(max = 150, message = "配置键长度不能超过150个字符")
    @Excel(name = "配置键")
    private String configKey;

    /** 配置值 */
    @ApiModelProperty(value = "配置值")
    @Excel(name = "配置值")
    private String configValue;

    /** 配置项描述 */
    @ApiModelProperty(value = "配置项描述")
    @Size(max = 255, message = "配置项描述长度不能超过255个字符")
    @Excel(name = "描述")
    private String description;

    /** 配置分组 */
    @ApiModelProperty(value = "配置分组")
    @Size(max = 50, message = "配置分组长度不能超过50个字符")
    @Excel(name = "分组")
    private String groupName;

}
