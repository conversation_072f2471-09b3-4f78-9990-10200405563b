package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;

/**
 * 分销员月度实收金额对象 commission_payouts
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "分销员月度实收金额对象")
public class CommissionPayouts extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 分销员ID (关联creators.id) */
    @ApiModelProperty(value = "分销员ID", required = true)
    @NotNull(message = "分销员ID不能为空")
    @Excel(name = "分销员ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long creatorId;

    /** 数据月份 (YYYY-MM-01) */
    @ApiModelProperty(value = "数据月份", required = true)
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 当月达成的分销员等级 */
    @ApiModelProperty(value = "当月达成的分销员等级")
    @Excel(name = "分销员等级")
    private String distributorLevel;

    /** 最终实收总额(USD, 保留2位小数) */
    @ApiModelProperty(value = "最终实收总额(USD)", required = true)
    @NotNull(message = "最终实收总额不能为空")
    @DecimalMin(value = "0", message = "最终实收总额不能小于0")
    @Excel(name = "最终实收总额(USD)")
    private BigDecimal finalPayoutUsd;

    /** 主播昵称 (关联查询字段，非表字段) */
    @ApiModelProperty(value = "主播昵称")
    @Excel(name = "主播昵称")
    private String creatorNickname;

    /** 主播Handle (关联查询字段，非表字段) */
    @ApiModelProperty(value = "主播Handle")
    @Excel(name = "主播Handle")
    private String creatorHandle;

    /** 个人钻石收入 (关联查询字段，非表字段) */
    @ApiModelProperty(value = "个人钻石收入")
    @Excel(name = "个人钻石收入")
    private Long personalDiamonds;

    /** 团队钻石收入 (关联查询字段，非表字段) */
    @ApiModelProperty(value = "团队钻石收入")
    @Excel(name = "团队钻石收入")
    private Long teamDiamonds;

    /** 拉新人数 (关联查询字段，非表字段) */
    @ApiModelProperty(value = "拉新人数")
    @Excel(name = "拉新人数")
    private Integer newRecruitsCount;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setDistributorLevel(String distributorLevel) 
    {
        this.distributorLevel = distributorLevel;
    }

    public String getDistributorLevel() 
    {
        return distributorLevel;
    }

    public void setFinalPayoutUsd(BigDecimal finalPayoutUsd) 
    {
        this.finalPayoutUsd = finalPayoutUsd;
    }

    public BigDecimal getFinalPayoutUsd() 
    {
        return finalPayoutUsd;
    }

    public void setCreatorNickname(String creatorNickname) 
    {
        this.creatorNickname = creatorNickname;
    }

    public String getCreatorNickname() 
    {
        return creatorNickname;
    }

    public void setCreatorHandle(String creatorHandle) 
    {
        this.creatorHandle = creatorHandle;
    }

    public String getCreatorHandle() 
    {
        return creatorHandle;
    }

    public void setPersonalDiamonds(Long personalDiamonds) 
    {
        this.personalDiamonds = personalDiamonds;
    }

    public Long getPersonalDiamonds() 
    {
        return personalDiamonds;
    }

    public void setTeamDiamonds(Long teamDiamonds) 
    {
        this.teamDiamonds = teamDiamonds;
    }

    public Long getTeamDiamonds() 
    {
        return teamDiamonds;
    }

    public void setNewRecruitsCount(Integer newRecruitsCount) 
    {
        this.newRecruitsCount = newRecruitsCount;
    }

    public Integer getNewRecruitsCount() 
    {
        return newRecruitsCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("creatorId", getCreatorId())
            .append("dataMonth", getDataMonth())
            .append("distributorLevel", getDistributorLevel())
            .append("finalPayoutUsd", getFinalPayoutUsd())
            .append("createTime", getCreateTime())
            .toString();
    }
} 