package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;

/**
 * 分销员动态门槛计算记录对象 commission_dynamic_thresholds
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "分销员动态门槛计算记录对象")
public class CommissionDynamicThresholds extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分销员ID */
    @ApiModelProperty(value = "分销员ID", required = true)
    @NotNull(message = "分销员ID不能为空")
    @Excel(name = "分销员ID")
    private Long creatorId;

    /** 数据月份 */
    @ApiModelProperty(value = "数据月份", required = true)
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 上月钻石收入 */
    @ApiModelProperty(value = "上月钻石收入", required = true)
    @NotNull(message = "上月钻石收入不能为空")
    @Excel(name = "上月钻石收入")
    private Long lastMonthDiamonds;

    /** 计算出的动态门槛 */
    @ApiModelProperty(value = "计算出的动态门槛", required = true)
    @NotNull(message = "计算出的动态门槛不能为空")
    @Excel(name = "计算出的动态门槛")
    private Long calculatedThreshold;

    /** 基础门槛 */
    @ApiModelProperty(value = "基础门槛", required = true)
    @NotNull(message = "基础门槛不能为空")
    @Excel(name = "基础门槛")
    private Long baseThreshold;

    /** 上浮比例 */
    @ApiModelProperty(value = "上浮比例", required = true)
    @NotNull(message = "上浮比例不能为空")
    @Excel(name = "上浮比例")
    private BigDecimal thresholdIncreaseRate;

    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setLastMonthDiamonds(Long lastMonthDiamonds) 
    {
        this.lastMonthDiamonds = lastMonthDiamonds;
    }

    public Long getLastMonthDiamonds() 
    {
        return lastMonthDiamonds;
    }

    public void setCalculatedThreshold(Long calculatedThreshold) 
    {
        this.calculatedThreshold = calculatedThreshold;
    }

    public Long getCalculatedThreshold() 
    {
        return calculatedThreshold;
    }

    public void setBaseThreshold(Long baseThreshold) 
    {
        this.baseThreshold = baseThreshold;
    }

    public Long getBaseThreshold() 
    {
        return baseThreshold;
    }

    public void setThresholdIncreaseRate(BigDecimal thresholdIncreaseRate) 
    {
        this.thresholdIncreaseRate = thresholdIncreaseRate;
    }

    public BigDecimal getThresholdIncreaseRate() 
    {
        return thresholdIncreaseRate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("creatorId", getCreatorId())
            .append("dataMonth", getDataMonth())
            .append("lastMonthDiamonds", getLastMonthDiamonds())
            .append("calculatedThreshold", getCalculatedThreshold())
            .append("baseThreshold", getBaseThreshold())
            .append("thresholdIncreaseRate", getThresholdIncreaseRate())
            .toString();
    }
} 