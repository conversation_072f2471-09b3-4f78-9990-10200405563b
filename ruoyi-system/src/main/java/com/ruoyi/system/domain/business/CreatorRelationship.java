package com.ruoyi.system.domain.business;

import java.io.Serializable;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 主播层级关系对象 creator_relationships
 * 对应表: creator_relationships
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@ApiModel(value = "CreatorRelationship", description = "主播层级关系实体")
public class CreatorRelationship implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 祖先ID (关联到creators.id) */
    @ApiModelProperty(value = "祖先ID", example = "1001", required = true)
    @Excel(name = "祖先ID")
    private Long ancestorId;

    /** 后代ID (关联到creators.id) */
    @ApiModelProperty(value = "后代ID", example = "1002", required = true)
    @Excel(name = "后代ID")
    private Long descendantId;

    /** 层级深度 (0表示自身) */
    @ApiModelProperty(value = "层级深度", example = "1", required = true)
    @Excel(name = "层级深度")
    private Integer depth;

    // Getter and Setter methods
    public void setAncestorId(Long ancestorId)
    {
        this.ancestorId = ancestorId;
    }

    public Long getAncestorId()
    {
        return ancestorId;
    }
    public void setDescendantId(Long descendantId)
    {
        this.descendantId = descendantId;
    }

    public Long getDescendantId()
    {
        return descendantId;
    }
    public void setDepth(Integer depth)
    {
        this.depth = depth;
    }

    public Integer getDepth()
    {
        return depth;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ancestorId", getAncestorId())
            .append("descendantId", getDescendantId())
            .append("depth", getDepth())
            .toString();
    }
}
