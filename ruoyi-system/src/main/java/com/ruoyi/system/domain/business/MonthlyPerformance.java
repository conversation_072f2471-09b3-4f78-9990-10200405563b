package com.ruoyi.system.domain.business;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 主播月度业绩对象 monthly_performance
 * 对应表: monthly_performance
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@ApiModel(value = "MonthlyPerformance", description = "主播月度业绩实体")
public class MonthlyPerformance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID", example = "1")
    private Integer id;

    /** 关联到creators.id */
    @ApiModelProperty(value = "主播ID", example = "1001", required = true)
    @Excel(name = "主播ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long creatorId;

    /** 数据月份 (存储为每月第一天) */
    @ApiModelProperty(value = "数据月份", example = "2025-06-01", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** Group */
    @ApiModelProperty(value = "Group", example = "Group A")
    @Excel(name = "Group")
    private String groupName;

    /** Group manager */
    @ApiModelProperty(value = "Group manager", example = "Manager X")
    @Excel(name = "Group manager")
    private String groupManager;

    /** 创作者网络管理员 */
    @ApiModelProperty(value = "创作者网络管理员", example = "Network Manager")
    @Excel(name = "创作者网络管理员")
    private String creatorNetworkManager;

    /** 是否违规主播 (0=否, 1=是) */
    @ApiModelProperty(value = "是否违规主播 (0=否, 1=是)", example = "0")
    @Excel(name = "是否违规主播", readConverterExp = "0=否,1=是")
    private Integer isViolative;

    /** 是否新人主播 (0=否, 1=是) */
    @ApiModelProperty(value = "是否新人主播 (0=否, 1=是)", example = "0")
    @Excel(name = "是否新人主播", readConverterExp = "0=否,1=是")
    private Integer isRookie;

    /** 钻石数 */
    @ApiModelProperty(value = "钻石数", example = "100000")
    @Excel(name = "钻石数")
    private Long diamonds;

    /** 有效天数(d) */
    @ApiModelProperty(value = "有效天数(d)", example = "20")
    @Excel(name = "有效天数(d)")
    private Integer validDays;

    /** 直播时长(h) */
    @ApiModelProperty(value = "直播时长(h)", example = "150.50")
    @Excel(name = "直播时长(h)")
    private BigDecimal liveDurationHours;

    /** 估计的奖金 */
    @ApiModelProperty(value = "估计的奖金", example = "2500.75")
    @Excel(name = "估计的奖金")
    private BigDecimal bonusEstimated;

    /** 新人里程碑1保留任务奖金 */
    @ApiModelProperty(value = "新人里程碑1保留任务奖金", example = "500.00")
    @Excel(name = "新人里程碑1保留任务奖金")
    private String bonusRookieM1Retention;

    /** 新人里程碑2任务奖金 */
    @ApiModelProperty(value = "新人里程碑2任务奖金", example = "1000.00")
    @Excel(name = "新人里程碑2任务奖金")
    private BigDecimal bonusRookieM2;

    /** 新人半里程碑任务奖金 */
    @ApiModelProperty(value = "新人半里程碑任务奖金", example = "300.00")
    @Excel(name = "新人半里程碑任务奖金")
    private BigDecimal bonusRookieHalfMilestone;

    /** 新人里程碑1任务奖金 */
    @ApiModelProperty(value = "新人里程碑1任务奖金", example = "600.00")
    @Excel(name = "新人里程碑1任务奖金")
    private BigDecimal bonusRookieM1;

    /** 活跃任务奖金 */
    @ApiModelProperty(value = "活跃任务奖金", example = "200.00")
    @Excel(name = "活跃任务奖金")
    private BigDecimal bonusActiveness;

    /** 收入规模任务奖金 */
    @ApiModelProperty(value = "收入规模任务奖金", example = "800.00")
    @Excel(name = "收入规模任务奖金")
    private BigDecimal bonusRevenueScale;

    /** 新创作者网络任务奖金 */
    @ApiModelProperty(value = "新创作者网络任务奖金", example = "150.00")
    @Excel(name = "新创作者网络任务奖金")
    private BigDecimal bonusNewCreatorNetwork;

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId()
    {
        return id;
    }
    public void setCreatorId(Long creatorId)
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId()
    {
        return creatorId;
    }
    public void setDataMonth(Date dataMonth)
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth()
    {
        return dataMonth;
    }
    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }

    public String getGroupName()
    {
        return groupName;
    }
    public void setGroupManager(String groupManager)
    {
        this.groupManager = groupManager;
    }

    public String getGroupManager()
    {
        return groupManager;
    }
    public void setCreatorNetworkManager(String creatorNetworkManager)
    {
        this.creatorNetworkManager = creatorNetworkManager;
    }

    public String getCreatorNetworkManager()
    {
        return creatorNetworkManager;
    }
    public void setIsViolative(Integer isViolative)
    {
        this.isViolative = isViolative;
    }

    public Integer getIsViolative()
    {
        return isViolative;
    }
    public void setIsRookie(Integer isRookie)
    {
        this.isRookie = isRookie;
    }

    public Integer getIsRookie()
    {
        return isRookie;
    }
    public void setDiamonds(Long diamonds)
    {
        this.diamonds = diamonds;
    }

    public Long getDiamonds()
    {
        return diamonds;
    }
    public void setValidDays(Integer validDays)
    {
        this.validDays = validDays;
    }

    public Integer getValidDays()
    {
        return validDays;
    }
    public void setLiveDurationHours(BigDecimal liveDurationHours)
    {
        this.liveDurationHours = liveDurationHours;
    }

    public BigDecimal getLiveDurationHours()
    {
        return liveDurationHours;
    }
    public void setBonusEstimated(BigDecimal bonusEstimated)
    {
        this.bonusEstimated = bonusEstimated;
    }

    public BigDecimal getBonusEstimated()
    {
        return bonusEstimated;
    }
    public void setBonusRookieM1Retention(String bonusRookieM1Retention)
    {
        this.bonusRookieM1Retention = bonusRookieM1Retention;
    }

    public String getBonusRookieM1Retention()
    {
        return bonusRookieM1Retention;
    }
    public void setBonusRookieM2(BigDecimal bonusRookieM2)
    {
        this.bonusRookieM2 = bonusRookieM2;
    }

    public BigDecimal getBonusRookieM2()
    {
        return bonusRookieM2;
    }
    public void setBonusRookieHalfMilestone(BigDecimal bonusRookieHalfMilestone)
    {
        this.bonusRookieHalfMilestone = bonusRookieHalfMilestone;
    }

    public BigDecimal getBonusRookieHalfMilestone()
    {
        return bonusRookieHalfMilestone;
    }
    public void setBonusRookieM1(BigDecimal bonusRookieM1)
    {
        this.bonusRookieM1 = bonusRookieM1;
    }

    public BigDecimal getBonusRookieM1()
    {
        return bonusRookieM1;
    }
    public void setBonusActiveness(BigDecimal bonusActiveness)
    {
        this.bonusActiveness = bonusActiveness;
    }

    public BigDecimal getBonusActiveness()
    {
        return bonusActiveness;
    }
    public void setBonusRevenueScale(BigDecimal bonusRevenueScale)
    {
        this.bonusRevenueScale = bonusRevenueScale;
    }

    public BigDecimal getBonusRevenueScale()
    {
        return bonusRevenueScale;
    }
    public void setBonusNewCreatorNetwork(BigDecimal bonusNewCreatorNetwork)
    {
        this.bonusNewCreatorNetwork = bonusNewCreatorNetwork;
    }

    public BigDecimal getBonusNewCreatorNetwork()
    {
        return bonusNewCreatorNetwork;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("creatorId", getCreatorId())
            .append("dataMonth", getDataMonth())
            .append("groupName", getGroupName())
            .append("groupManager", getGroupManager())
            .append("creatorNetworkManager", getCreatorNetworkManager())
            .append("isViolative", getIsViolative())
            .append("isRookie", getIsRookie())
            .append("diamonds", getDiamonds())
            .append("validDays", getValidDays())
            .append("liveDurationHours", getLiveDurationHours())
            .append("bonusEstimated", getBonusEstimated())
            .append("bonusRookieM1Retention", getBonusRookieM1Retention())
            .append("bonusRookieM2", getBonusRookieM2())
            .append("bonusRookieHalfMilestone", getBonusRookieHalfMilestone())
            .append("bonusRookieM1", getBonusRookieM1())
            .append("bonusActiveness", getBonusActiveness())
            .append("bonusRevenueScale", getBonusRevenueScale())
            .append("bonusNewCreatorNetwork", getBonusNewCreatorNetwork())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
