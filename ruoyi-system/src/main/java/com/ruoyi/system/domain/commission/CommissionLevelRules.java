package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;

/**
 * 分销员等级规则对象 commission_level_rules
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "分销员等级规则对象")
public class CommissionLevelRules extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    @ApiModelProperty(value = "规则ID")
    private Integer id;

    /** 分销员等级名称 (如: 金牌, 银牌, 铜牌) */
    @ApiModelProperty(value = "等级名称", required = true)
    @NotBlank(message = "等级名称不能为空")
    @Size(min = 0, max = 50, message = "等级名称长度不能超过50个字符")
    @Excel(name = "等级名称")
    private String levelName;

    /** 该等级要求的最低月度合格拉新人数 */
    @ApiModelProperty(value = "最低月度合格拉新人数", required = true)
    @NotNull(message = "最低月度合格拉新人数不能为空")
    @Min(value = 0, message = "最低月度合格拉新人数不能小于0")
    @Excel(name = "最低月度合格拉新人数")
    private Integer minQualifiedRecruits;

    /** 该等级的分成比例 (例如 0.0200 代表 2%) */
    @ApiModelProperty(value = "分成比例", required = true)
    @NotNull(message = "分成比例不能为空")
    @DecimalMin(value = "0", message = "分成比例不能小于0")
    @DecimalMax(value = "1", message = "分成比例不能大于1")
    @Excel(name = "分成比例")
    private BigDecimal commissionRate;

    /** 佣金收益深度 (1=下1级, 2=下2级, 3=下3级) */
    @ApiModelProperty(value = "佣金收益深度", required = true)
    @NotNull(message = "佣金收益深度不能为空")
    @Min(value = 1, message = "佣金收益深度不能小于1")
    @Excel(name = "佣金收益深度")
    private Integer payoutDepth;

    /** 用于排序显示 */
    @ApiModelProperty(value = "显示顺序")
    @Excel(name = "显示顺序")
    private Integer displayOrder;

    /** 是否启用 (1=是, 0=否) */
    @ApiModelProperty(value = "是否启用")
    @Excel(name = "是否启用", readConverterExp = "1=是,0=否")
    private Integer isActive;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setLevelName(String levelName) 
    {
        this.levelName = levelName;
    }

    public String getLevelName() 
    {
        return levelName;
    }
    public void setMinQualifiedRecruits(Integer minQualifiedRecruits)
    {
        this.minQualifiedRecruits = minQualifiedRecruits;
    }

    public Integer getMinQualifiedRecruits()
    {
        return minQualifiedRecruits;
    }
    public void setCommissionRate(BigDecimal commissionRate)
    {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getCommissionRate()
    {
        return commissionRate;
    }

    public void setPayoutDepth(Integer payoutDepth)
    {
        this.payoutDepth = payoutDepth;
    }

    public Integer getPayoutDepth()
    {
        return payoutDepth;
    }
    public void setDisplayOrder(Integer displayOrder) 
    {
        this.displayOrder = displayOrder;
    }

    public Integer getDisplayOrder() 
    {
        return displayOrder;
    }
    public void setIsActive(Integer isActive) 
    {
        this.isActive = isActive;
    }

    public Integer getIsActive() 
    {
        return isActive;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("levelName", getLevelName())
            .append("minQualifiedRecruits", getMinQualifiedRecruits())
            .append("commissionRate", getCommissionRate())
            .append("payoutDepth", getPayoutDepth())
            .append("displayOrder", getDisplayOrder())
            .append("isActive", getIsActive())
            .toString();
    }
} 