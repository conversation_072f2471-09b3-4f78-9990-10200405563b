package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;

/**
 * 分销员收入构成明细对象 commission_payout_breakdowns
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "分销员收入构成明细对象")
public class CommissionPayoutBreakdowns extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @ApiModelProperty(value = "ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 关联到commission_payouts.id */
    @ApiModelProperty(value = "关联支付记录ID", required = true)
    @NotNull(message = "关联支付记录ID不能为空")
    @Excel(name = "支付记录ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long payoutId;

    /** 分销员ID (冗余字段, 便于查询) */
    @ApiModelProperty(value = "分销员ID", required = true)
    @NotNull(message = "分销员ID不能为空")
    @Excel(name = "分销员ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long creatorId;

    /** 数据月份 (冗余字段, 便于查询) */
    @ApiModelProperty(value = "数据月份", required = true)
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 收入来源类型 */
    @ApiModelProperty(value = "收入来源类型", required = true, allowableValues = "LEVEL_COMMISSION,MULTI_LEVEL_COMMISSION,RECRUITMENT_BONUS")
    @NotNull(message = "收入来源类型不能为空")
    @Excel(name = "收入来源类型", readConverterExp = "LEVEL_COMMISSION=等级分成,MULTI_LEVEL_COMMISSION=多级提成,RECRUITMENT_BONUS=拉新奖励")
    private String sourceType;

    /** 计算基数 (钻石), 如团队总收入 | 类型为RECRUITMENT_BONUS时为拉新人数 */
    @ApiModelProperty(value = "计算基数(钻石/拉新人数)")
    @Excel(name = "计算基数(钻石/拉新人数)")
    private Long baseAmountDiamonds;

    /** 计算出的钻石金额 */
    @ApiModelProperty(value = "计算出的钻石金额")
    @Excel(name = "计算出的钻石金额")
    private Long calculatedAmountDiamonds;

    /** 计算出的USD金额 (拉新奖励直接记录, 其他为换算后) */
    @ApiModelProperty(value = "计算出的USD金额", required = true)
    @NotNull(message = "计算出的USD金额不能为空")
    @DecimalMin(value = "0", message = "计算出的USD金额不能小于0")
    @Excel(name = "计算出的USD金额")
    private BigDecimal calculatedAmountUsd;

    /** 提成比例 (关联查询字段，非表字段) */
    @ApiModelProperty(value = "提成比例")
    @Excel(name = "提成比例")
    private BigDecimal commissionRate;

    /** 收入来源名称 (关联查询字段，非表字段) */
    @ApiModelProperty(value = "收入来源名称")
    @Excel(name = "收入来源名称")
    private String sourceTypeName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPayoutId(Long payoutId) 
    {
        this.payoutId = payoutId;
    }

    public Long getPayoutId() 
    {
        return payoutId;
    }

    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setSourceType(String sourceType) 
    {
        this.sourceType = sourceType;
    }

    public String getSourceType() 
    {
        return sourceType;
    }

    public void setBaseAmountDiamonds(Long baseAmountDiamonds) 
    {
        this.baseAmountDiamonds = baseAmountDiamonds;
    }

    public Long getBaseAmountDiamonds() 
    {
        return baseAmountDiamonds;
    }

    public void setCalculatedAmountDiamonds(Long calculatedAmountDiamonds) 
    {
        this.calculatedAmountDiamonds = calculatedAmountDiamonds;
    }

    public Long getCalculatedAmountDiamonds() 
    {
        return calculatedAmountDiamonds;
    }

    public void setCalculatedAmountUsd(BigDecimal calculatedAmountUsd) 
    {
        this.calculatedAmountUsd = calculatedAmountUsd;
    }

    public BigDecimal getCalculatedAmountUsd() 
    {
        return calculatedAmountUsd;
    }

    public void setCommissionRate(BigDecimal commissionRate) 
    {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getCommissionRate() 
    {
        return commissionRate;
    }

    public void setSourceTypeName(String sourceTypeName) 
    {
        this.sourceTypeName = sourceTypeName;
    }

    public String getSourceTypeName() 
    {
        return sourceTypeName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("payoutId", getPayoutId())
            .append("creatorId", getCreatorId())
            .append("dataMonth", getDataMonth())
            .append("sourceType", getSourceType())
            .append("baseAmountDiamonds", getBaseAmountDiamonds())
            .append("calculatedAmountDiamonds", getCalculatedAmountDiamonds())
            .append("calculatedAmountUsd", getCalculatedAmountUsd())
            .toString();
    }
} 