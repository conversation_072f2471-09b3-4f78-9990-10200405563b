package com.ruoyi.system.domain.commission;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;

/**
 * 分销员资格记录对象 commission_distributor_qualifications
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "分销员资格记录对象")
public class CommissionDistributorQualifications extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分销员ID */
    @ApiModelProperty(value = "分销员ID", required = true)
    @NotNull(message = "分销员ID不能为空")
    @Excel(name = "分销员ID")
    private Long creatorId;

    /** 数据月份 */
    @ApiModelProperty(value = "数据月份", required = true)
    @NotNull(message = "数据月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 是否符合分销员资格 */
    @ApiModelProperty(value = "是否符合分销员资格", required = true)
    @Excel(name = "是否符合分销员资格", readConverterExp = "0=否,1=是")
    private Integer isQualified;

    /** 达成的等级 */
    @ApiModelProperty(value = "达成的等级")
    @Excel(name = "达成的等级")
    private String achievedLevel;

    /** 个人钻石收入 */
    @ApiModelProperty(value = "个人钻石收入")
    @Excel(name = "个人钻石收入")
    private Long personalDiamonds;

    /** 团队钻石收入 */
    @ApiModelProperty(value = "团队钻石收入")
    @Excel(name = "团队钻石收入")
    private Long teamDiamonds;

    /** 直属下级数量 */
    @ApiModelProperty(value = "直属下级数量")
    @Excel(name = "直属下级数量")
    private Integer directDownlinesCount;

    /** 动态门槛 */
    @ApiModelProperty(value = "动态门槛")
    @Excel(name = "动态门槛")
    private Long dynamicThreshold;

    /** 是否达到门槛 */
    @ApiModelProperty(value = "是否达到门槛")
    @Excel(name = "是否达到门槛", readConverterExp = "0=否,1=是")
    private Integer thresholdMet;

    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setIsQualified(Integer isQualified) 
    {
        this.isQualified = isQualified;
    }

    public Integer getIsQualified() 
    {
        return isQualified;
    }

    public void setAchievedLevel(String achievedLevel) 
    {
        this.achievedLevel = achievedLevel;
    }

    public String getAchievedLevel() 
    {
        return achievedLevel;
    }

    public void setPersonalDiamonds(Long personalDiamonds) 
    {
        this.personalDiamonds = personalDiamonds;
    }

    public Long getPersonalDiamonds() 
    {
        return personalDiamonds;
    }

    public void setTeamDiamonds(Long teamDiamonds) 
    {
        this.teamDiamonds = teamDiamonds;
    }

    public Long getTeamDiamonds() 
    {
        return teamDiamonds;
    }

    public void setDirectDownlinesCount(Integer directDownlinesCount) 
    {
        this.directDownlinesCount = directDownlinesCount;
    }

    public Integer getDirectDownlinesCount() 
    {
        return directDownlinesCount;
    }

    public void setDynamicThreshold(Long dynamicThreshold) 
    {
        this.dynamicThreshold = dynamicThreshold;
    }

    public Long getDynamicThreshold() 
    {
        return dynamicThreshold;
    }

    public void setThresholdMet(Integer thresholdMet) 
    {
        this.thresholdMet = thresholdMet;
    }

    public Integer getThresholdMet() 
    {
        return thresholdMet;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("creatorId", getCreatorId())
            .append("dataMonth", getDataMonth())
            .append("isQualified", getIsQualified())
            .append("achievedLevel", getAchievedLevel())
            .append("personalDiamonds", getPersonalDiamonds())
            .append("teamDiamonds", getTeamDiamonds())
            .append("directDownlinesCount", getDirectDownlinesCount())
            .append("dynamicThreshold", getDynamicThreshold())
            .append("thresholdMet", getThresholdMet())
            .toString();
    }
} 