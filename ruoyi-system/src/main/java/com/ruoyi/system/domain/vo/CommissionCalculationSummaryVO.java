package com.ruoyi.system.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.system.domain.commission.CommissionPayoutBreakdowns;

/**
 * 佣金计算总览VO对象
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "佣金计算总览VO对象")
public class CommissionCalculationSummaryVO
{
    /** 数据月份 */
    @ApiModelProperty(value = "数据月份")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dataMonth;

    /** 公会月总收入(预算) */
    @ApiModelProperty(value = "公会月总收入(预算)")
    private BigDecimal budgetUsd;

    /** 支出上限 */
    @ApiModelProperty(value = "支出上限")
    private BigDecimal payoutCapUsd;

    /** 实际总支出 */
    @ApiModelProperty(value = "实际总支出")
    private BigDecimal actualPayoutUsd;

    /** 支出/收入比例 */
    @ApiModelProperty(value = "支出/收入比例")
    private BigDecimal payoutToBudgetRatio;

    /** 预算状态 */
    @ApiModelProperty(value = "预算状态", allowableValues = "预算内,超出预算")
    private String budgetStatus;

    /** 超出预算金额 */
    @ApiModelProperty(value = "超出预算金额")
    private BigDecimal exceededAmountUsd;

    /** 当月主播奖金估计总额 */
    @ApiModelProperty(value = "当月主播奖金估计总额")
    private BigDecimal totalBonusEstimated;

    /** 钻石兑美元汇率 */
    @ApiModelProperty(value = "钻石兑美元汇率")
    private BigDecimal diamondToUsdRate;

    /** 合格分销员总数 */
    @ApiModelProperty(value = "合格分销员总数")
    private Integer qualifiedDistributorCount;

    /** 总等级分成(USD) */
    @ApiModelProperty(value = "总等级分成(USD)")
    private BigDecimal totalLevelCommissionUsd;

    /** 总多级提成(USD) */
    @ApiModelProperty(value = "总多级提成(USD)")
    private BigDecimal totalMultiLevelCommissionUsd;

    /** 总拉新奖励(USD) */
    @ApiModelProperty(value = "总拉新奖励(USD)")
    private BigDecimal totalRecruitmentBonusUsd;

    /** 计算状态 */
    @ApiModelProperty(value = "计算状态")
    private String calculationStatus;

    /** 计算执行时间 */
    @ApiModelProperty(value = "计算执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date calculatedAt;

    public Date getDataMonth() {
        return dataMonth;
    }

    public void setDataMonth(Date dataMonth) {
        this.dataMonth = dataMonth;
    }

    public BigDecimal getBudgetUsd() {
        return budgetUsd;
    }

    public void setBudgetUsd(BigDecimal budgetUsd) {
        this.budgetUsd = budgetUsd;
    }

    public BigDecimal getPayoutCapUsd() {
        return payoutCapUsd;
    }

    public void setPayoutCapUsd(BigDecimal payoutCapUsd) {
        this.payoutCapUsd = payoutCapUsd;
    }

    public BigDecimal getActualPayoutUsd() {
        return actualPayoutUsd;
    }

    public void setActualPayoutUsd(BigDecimal actualPayoutUsd) {
        this.actualPayoutUsd = actualPayoutUsd;
    }

    public BigDecimal getPayoutToBudgetRatio() {
        return payoutToBudgetRatio;
    }

    public void setPayoutToBudgetRatio(BigDecimal payoutToBudgetRatio) {
        this.payoutToBudgetRatio = payoutToBudgetRatio;
    }

    public String getBudgetStatus() {
        return budgetStatus;
    }

    public void setBudgetStatus(String budgetStatus) {
        this.budgetStatus = budgetStatus;
    }

    public BigDecimal getExceededAmountUsd() {
        return exceededAmountUsd;
    }

    public void setExceededAmountUsd(BigDecimal exceededAmountUsd) {
        this.exceededAmountUsd = exceededAmountUsd;
    }

    public BigDecimal getTotalBonusEstimated() {
        return totalBonusEstimated;
    }

    public void setTotalBonusEstimated(BigDecimal totalBonusEstimated) {
        this.totalBonusEstimated = totalBonusEstimated;
    }

    public BigDecimal getDiamondToUsdRate() {
        return diamondToUsdRate;
    }

    public void setDiamondToUsdRate(BigDecimal diamondToUsdRate) {
        this.diamondToUsdRate = diamondToUsdRate;
    }

    public Integer getQualifiedDistributorCount() {
        return qualifiedDistributorCount;
    }

    public void setQualifiedDistributorCount(Integer qualifiedDistributorCount) {
        this.qualifiedDistributorCount = qualifiedDistributorCount;
    }

    public BigDecimal getTotalLevelCommissionUsd() {
        return totalLevelCommissionUsd;
    }

    public void setTotalLevelCommissionUsd(BigDecimal totalLevelCommissionUsd) {
        this.totalLevelCommissionUsd = totalLevelCommissionUsd;
    }

    public BigDecimal getTotalMultiLevelCommissionUsd() {
        return totalMultiLevelCommissionUsd;
    }

    public void setTotalMultiLevelCommissionUsd(BigDecimal totalMultiLevelCommissionUsd) {
        this.totalMultiLevelCommissionUsd = totalMultiLevelCommissionUsd;
    }

    public BigDecimal getTotalRecruitmentBonusUsd() {
        return totalRecruitmentBonusUsd;
    }

    public void setTotalRecruitmentBonusUsd(BigDecimal totalRecruitmentBonusUsd) {
        this.totalRecruitmentBonusUsd = totalRecruitmentBonusUsd;
    }

    public String getCalculationStatus() {
        return calculationStatus;
    }

    public void setCalculationStatus(String calculationStatus) {
        this.calculationStatus = calculationStatus;
    }

    public Date getCalculatedAt() {
        return calculatedAt;
    }

    public void setCalculatedAt(Date calculatedAt) {
        this.calculatedAt = calculatedAt;
    }
} 