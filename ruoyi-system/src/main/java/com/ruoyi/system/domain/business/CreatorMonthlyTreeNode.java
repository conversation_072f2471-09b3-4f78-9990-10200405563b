package com.ruoyi.system.domain.business;

import java.io.Serializable;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.commission.CommissionPayouts;
import com.ruoyi.system.domain.commission.CommissionPayoutBreakdowns;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 主播月度数据树状节点对象
 * 用于构建包含月度业绩数据的主播关系树
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@ApiModel(value = "CreatorMonthlyTreeNode", description = "主播月度数据树状节点实体")
public class CreatorMonthlyTreeNode implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点标签信息 */
    @ApiModelProperty(value = "节点标签信息", required = true)
    @JsonProperty("label")
    private CreatorMonthlyLabel label;

    /** 子节点列表 */
    @ApiModelProperty(value = "子节点列表")
    @JsonProperty("children")
    private List<CreatorMonthlyTreeNode> children;

    /**
     * 节点标签信息内部类
     */
    @ApiModel(value = "CreatorMonthlyLabel", description = "主播月度标签信息")
    public static class CreatorMonthlyLabel implements Serializable
    {
        private static final long serialVersionUID = 1L;

        /** 主播昵称 */
        @ApiModelProperty(value = "主播昵称", example = "Level one 1", required = true)
        @JsonProperty("nickname")
        private String nickname;

        /** 主播ID */
        @ApiModelProperty(value = "主播ID", example = "7500000000000000000", required = true)
        @JsonProperty("id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long id;

        /** 节点类型：分销员|主播 */
        @ApiModelProperty(value = "节点类型", example = "分销员", required = true)
        @JsonProperty("type")
        private String type;

        /** 当月分销员数据汇总 */
        @ApiModelProperty(value = "当月分销员数据汇总")
        @JsonProperty("commission_payouts")
        private CommissionPayouts commissionPayouts;

        /** 收入明细列表 */
        @ApiModelProperty(value = "收入明细列表")
        @JsonProperty("commission_payout_breakdowns")
        private List<CommissionPayoutBreakdowns> commissionPayoutBreakdowns;

        /** 个人主播数据 */
        @ApiModelProperty(value = "个人主播数据")
        @JsonProperty("monthly_performance")
        private MonthlyPerformance monthlyPerformance;

        public CreatorMonthlyLabel() {}

        public CreatorMonthlyLabel(String nickname, Long id, String type)
        {
            this.nickname = nickname;
            this.id = id;
            this.type = type;
        }

        // Getter and Setter methods
        public String getNickname()
        {
            return nickname;
        }

        public void setNickname(String nickname)
        {
            this.nickname = nickname;
        }

        public Long getId()
        {
            return id;
        }

        public void setId(Long id)
        {
            this.id = id;
        }

        public String getType()
        {
            return type;
        }

        public void setType(String type)
        {
            this.type = type;
        }

        public CommissionPayouts getCommissionPayouts()
        {
            return commissionPayouts;
        }

        public void setCommissionPayouts(CommissionPayouts commissionPayouts)
        {
            this.commissionPayouts = commissionPayouts;
        }

        public List<CommissionPayoutBreakdowns> getCommissionPayoutBreakdowns()
        {
            return commissionPayoutBreakdowns;
        }

        public void setCommissionPayoutBreakdowns(List<CommissionPayoutBreakdowns> commissionPayoutBreakdowns)
        {
            this.commissionPayoutBreakdowns = commissionPayoutBreakdowns;
        }

        public MonthlyPerformance getMonthlyPerformance()
        {
            return monthlyPerformance;
        }

        public void setMonthlyPerformance(MonthlyPerformance monthlyPerformance)
        {
            this.monthlyPerformance = monthlyPerformance;
        }
    }

    public CreatorMonthlyTreeNode() {}

    public CreatorMonthlyTreeNode(CreatorMonthlyLabel label)
    {
        this.label = label;
    }

    public CreatorMonthlyTreeNode(String nickname, Long id, String type)
    {
        this.label = new CreatorMonthlyLabel(nickname, id, type);
    }

    public CreatorMonthlyLabel getLabel()
    {
        return label;
    }

    public void setLabel(CreatorMonthlyLabel label)
    {
        this.label = label;
    }

    public List<CreatorMonthlyTreeNode> getChildren()
    {
        return children;
    }

    public void setChildren(List<CreatorMonthlyTreeNode> children)
    {
        this.children = children;
    }
} 