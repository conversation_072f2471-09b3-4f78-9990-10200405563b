package com.ruoyi.system.domain.business;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 分销员月度汇总对象 distributor_monthly_summary
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "分销员月度汇总对象")
public class DistributorMonthlySummary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分销员ID */
    @ApiModelProperty(value = "分销员ID", required = true)
    @Excel(name = "分销员ID")
    private Long creatorId;

    /** 数据月份 */
    @ApiModelProperty(value = "数据月份", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 团队总收入(USD) - 包含自己和所有下级的收入总和 */
    @ApiModelProperty(value = "团队总收入(USD)", required = true)
    @Excel(name = "团队总收入(USD)")
    private BigDecimal totalTeamIncomeUsd;

    /** 团队总人数 - 包含自己和所有下级的人数总和 */
    @ApiModelProperty(value = "团队总人数", required = true)
    @Excel(name = "团队总人数")
    private Integer totalTeamSize;

    /** 直属下级人数 - L1层级的人数 */
    @ApiModelProperty(value = "直属下级人数", required = true)
    @Excel(name = "直属下级人数")
    private Integer directSubordinatesCount;

    /** 个人收入(USD) - 仅自己的收入 */
    @ApiModelProperty(value = "个人收入(USD)", required = true)
    @Excel(name = "个人收入(USD)")
    private BigDecimal personalIncomeUsd;

    /** 团队收入(USD) - 不包含自己，仅下级团队的收入总和 */
    @ApiModelProperty(value = "团队收入(USD)", required = true)
    @Excel(name = "团队收入(USD)")
    private BigDecimal teamIncomeUsd;

    public void setCreatorId(Long creatorId)
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId()
    {
        return creatorId;
    }

    public void setDataMonth(Date dataMonth)
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth()
    {
        return dataMonth;
    }

    public void setTotalTeamIncomeUsd(BigDecimal totalTeamIncomeUsd)
    {
        this.totalTeamIncomeUsd = totalTeamIncomeUsd;
    }

    public BigDecimal getTotalTeamIncomeUsd()
    {
        return totalTeamIncomeUsd;
    }

    public void setTotalTeamSize(Integer totalTeamSize)
    {
        this.totalTeamSize = totalTeamSize;
    }

    public Integer getTotalTeamSize()
    {
        return totalTeamSize;
    }

    public void setDirectSubordinatesCount(Integer directSubordinatesCount)
    {
        this.directSubordinatesCount = directSubordinatesCount;
    }

    public Integer getDirectSubordinatesCount()
    {
        return directSubordinatesCount;
    }

    public void setPersonalIncomeUsd(BigDecimal personalIncomeUsd)
    {
        this.personalIncomeUsd = personalIncomeUsd;
    }

    public BigDecimal getPersonalIncomeUsd()
    {
        return personalIncomeUsd;
    }

    public void setTeamIncomeUsd(BigDecimal teamIncomeUsd)
    {
        this.teamIncomeUsd = teamIncomeUsd;
    }

    public BigDecimal getTeamIncomeUsd()
    {
        return teamIncomeUsd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("creatorId", getCreatorId())
            .append("dataMonth", getDataMonth())
            .append("totalTeamIncomeUsd", getTotalTeamIncomeUsd())
            .append("totalTeamSize", getTotalTeamSize())
            .append("directSubordinatesCount", getDirectSubordinatesCount())
            .append("personalIncomeUsd", getPersonalIncomeUsd())
            .append("teamIncomeUsd", getTeamIncomeUsd())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}