package com.ruoyi.system.domain.business;

import java.io.Serializable;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 主播树状节点对象
 * 用于构建主播关系树
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@ApiModel(value = "CreatorTreeNode", description = "主播树状节点实体")
public class CreatorTreeNode implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点标签信息 */
    @ApiModelProperty(value = "节点标签信息", required = true)
    @JsonProperty("label")
    private CreatorLabel label;

    /** 子节点列表 */
    @ApiModelProperty(value = "子节点列表")
    @JsonProperty("children")
    private List<CreatorTreeNode> children;

    /**
     * 节点标签信息内部类
     */
    @ApiModel(value = "CreatorLabel", description = "主播标签信息")
    public static class CreatorLabel implements Serializable
    {
        private static final long serialVersionUID = 1L;

        /** 主播昵称 */
        @ApiModelProperty(value = "主播昵称", example = "Level one 1", required = true)
        @JsonProperty("nickname")
        private String nickname;

        /** 主播ID */
        @ApiModelProperty(value = "主播ID", example = "7500000000000000000", required = true)
        @JsonProperty("id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long id;

        public CreatorLabel() {}

        public CreatorLabel(String nickname, Long id)
        {
            this.nickname = nickname;
            this.id = id;
        }

        public String getNickname()
        {
            return nickname;
        }

        public void setNickname(String nickname)
        {
            this.nickname = nickname;
        }

        public Long getId()
        {
            return id;
        }

        public void setId(Long id)
        {
            this.id = id;
        }
    }

    public CreatorTreeNode() {}

    public CreatorTreeNode(CreatorLabel label)
    {
        this.label = label;
    }

    public CreatorTreeNode(String nickname, Long id)
    {
        this.label = new CreatorLabel(nickname, id);
    }

    public CreatorLabel getLabel()
    {
        return label;
    }

    public void setLabel(CreatorLabel label)
    {
        this.label = label;
    }

    public List<CreatorTreeNode> getChildren()
    {
        return children;
    }

    public void setChildren(List<CreatorTreeNode> children)
    {
        this.children = children;
    }
} 