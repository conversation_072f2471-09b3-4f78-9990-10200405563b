package com.ruoyi.system.domain.dto;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 个人主仪表盘响应数据对象
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "个人主仪表盘响应数据")
public class DashboardResponse
{
    /** 用户信息 */
    @ApiModelProperty(value = "用户信息")
    private UserInfo user;

    /** 用户在所选月份的最终等级 */
    @ApiModelProperty(value = "用户等级")
    private String level;

    /** 返回用户请求的月份 YYYY-MM */
    @ApiModelProperty(value = "所选月份")
    private String selectedMonth;

    /** 截至所选月份末的年度累计总收入 */
    @ApiModelProperty(value = "年度累计总收入(USD)")
    private BigDecimal totalIncome;

    /** 所选月份的当月总收入 */
    @ApiModelProperty(value = "当月总收入(USD)")
    private BigDecimal monthlyIncome;

    /** 月度收入环比增长率 */
    @ApiModelProperty(value = "月度收入环比增长率")
    private BigDecimal monthlyIncomeGrowth;

    /** 保级状态 */
    @ApiModelProperty(value = "保级状态")
    private LevelMaintenance levelMaintenance;

    /** 绩效分析 */
    @ApiModelProperty(value = "绩效分析")
    private PerformanceAnalytics performanceAnalytics;

    public UserInfo getUser() {
        return user;
    }

    public void setUser(UserInfo user) {
        this.user = user;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getSelectedMonth() {
        return selectedMonth;
    }

    public void setSelectedMonth(String selectedMonth) {
        this.selectedMonth = selectedMonth;
    }

    public BigDecimal getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(BigDecimal totalIncome) {
        this.totalIncome = totalIncome;
    }

    public BigDecimal getMonthlyIncome() {
        return monthlyIncome;
    }

    public void setMonthlyIncome(BigDecimal monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }

    public BigDecimal getMonthlyIncomeGrowth() {
        return monthlyIncomeGrowth;
    }

    public void setMonthlyIncomeGrowth(BigDecimal monthlyIncomeGrowth) {
        this.monthlyIncomeGrowth = monthlyIncomeGrowth;
    }

    public LevelMaintenance getLevelMaintenance() {
        return levelMaintenance;
    }

    public void setLevelMaintenance(LevelMaintenance levelMaintenance) {
        this.levelMaintenance = levelMaintenance;
    }

    public PerformanceAnalytics getPerformanceAnalytics() {
        return performanceAnalytics;
    }

    public void setPerformanceAnalytics(PerformanceAnalytics performanceAnalytics) {
        this.performanceAnalytics = performanceAnalytics;
    }
}
