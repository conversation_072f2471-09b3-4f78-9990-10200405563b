package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;

/**
 * 修改虚拟创建者请求实体
 *
 * <AUTHOR>
 */
@ApiModel(value = "UpdateVirtualCreatorRequest", description = "修改虚拟创建者请求参数")
public class UpdateVirtualCreatorRequest
{
    /** 昵称 */
    @ApiModelProperty(value = "昵称", required = true, example = "虚拟上级-Jason")
    @NotBlank(message = "昵称不能为空")
    private String nickname;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "为Jason组创建的虚拟上级")
    private String remark;

    public String getNickname()
    {
        return nickname;
    }

    public void setNickname(String nickname)
    {
        this.nickname = nickname;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }
} 