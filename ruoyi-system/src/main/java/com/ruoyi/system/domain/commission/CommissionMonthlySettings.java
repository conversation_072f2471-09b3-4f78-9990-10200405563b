package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;

/**
 * 月度佣金配置对象 commission_monthly_settings
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "月度佣金配置对象")
public class CommissionMonthlySettings extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据月份 (YYYY-MM-01) */
    @ApiModelProperty(value = "数据月份", required = true)
    @NotNull(message = "数据月份不能为空")
    @Excel(name = "数据月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 管理员输入的当月预算收入(USD) */
    @ApiModelProperty(value = "预算收入(USD)", required = true)
    @NotNull(message = "预算收入不能为空")
    @DecimalMin(value = "0", message = "预算收入不能小于0")
    @Excel(name = "预算收入(USD)")
    private BigDecimal manualIncomeUsd;

    /** 支出上限比例 (例如 0.3000 代表 30%) */
    @ApiModelProperty(value = "支出上限比例", required = true)
    @NotNull(message = "支出上限比例不能为空")
    @DecimalMin(value = "0", message = "支出上限比例不能小于0")
    @DecimalMax(value = "1", message = "支出上限比例不能大于1")
    @Excel(name = "支出上限比例")
    private BigDecimal payoutCapRate;

    /** 当月生效的钻石兑美元汇率 */
    @ApiModelProperty(value = "钻石兑美元汇率", required = true)
    @NotNull(message = "钻石兑美元汇率不能为空")
    @DecimalMin(value = "0", message = "钻石兑美元汇率不能小于0")
    @Excel(name = "钻石兑美元汇率")
    private BigDecimal diamondToUsdRate;

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }
    public void setManualIncomeUsd(BigDecimal manualIncomeUsd) 
    {
        this.manualIncomeUsd = manualIncomeUsd;
    }

    public BigDecimal getManualIncomeUsd() 
    {
        return manualIncomeUsd;
    }
    public void setPayoutCapRate(BigDecimal payoutCapRate) 
    {
        this.payoutCapRate = payoutCapRate;
    }

    public BigDecimal getPayoutCapRate() 
    {
        return payoutCapRate;
    }
    public void setDiamondToUsdRate(BigDecimal diamondToUsdRate) 
    {
        this.diamondToUsdRate = diamondToUsdRate;
    }

    public BigDecimal getDiamondToUsdRate() 
    {
        return diamondToUsdRate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dataMonth", getDataMonth())
            .append("manualIncomeUsd", getManualIncomeUsd())
            .append("payoutCapRate", getPayoutCapRate())
            .append("diamondToUsdRate", getDiamondToUsdRate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 