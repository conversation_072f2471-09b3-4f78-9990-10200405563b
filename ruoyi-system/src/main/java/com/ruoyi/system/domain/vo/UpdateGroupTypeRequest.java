package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 修改组类型请求实体
 *
 * <AUTHOR>
 */
@ApiModel(value = "UpdateGroupTypeRequest", description = "修改组类型请求参数")
public class UpdateGroupTypeRequest
{
    /** 组名 */
    @ApiModelProperty(value = "组名", required = true, example = "Jason")
    @NotBlank(message = "组名不能为空")
    private String groupName;

    /** 组类型：0-分销组，1-非分销组 */
    @ApiModelProperty(value = "组类型", required = true, example = "0", notes = "0-分销组，1-非分销组")
    @NotNull(message = "组类型不能为空")
    @Min(value = 0, message = "组类型值必须为0或1")
    @Max(value = 1, message = "组类型值必须为0或1")
    private Integer groupType;

    public String getGroupName()
    {
        return groupName;
    }

    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }

    public Integer getGroupType()
    {
        return groupType;
    }

    public void setGroupType(Integer groupType)
    {
        this.groupType = groupType;
    }
}
