package com.ruoyi.system.domain.dto;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 关键绩效指标对象
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "关键绩效指标")
public class PerformanceKPIs
{
    /** 个人收入占比 */
    @ApiModelProperty(value = "个人收入占比")
    private BigDecimal personalIncomeRatio;

    /** 团队总人数 */
    @ApiModelProperty(value = "团队总人数")
    private Integer totalTeamMembers;

    public BigDecimal getPersonalIncomeRatio() {
        return personalIncomeRatio;
    }

    public void setPersonalIncomeRatio(BigDecimal personalIncomeRatio) {
        this.personalIncomeRatio = personalIncomeRatio;
    }

    public Integer getTotalTeamMembers() {
        return totalTeamMembers;
    }

    public void setTotalTeamMembers(Integer totalTeamMembers) {
        this.totalTeamMembers = totalTeamMembers;
    }
}
