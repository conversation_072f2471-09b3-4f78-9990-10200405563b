package com.ruoyi.system.domain.dto;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 收入构成对象
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "收入构成")
public class IncomeComponents
{
    /** 个人收入(USD) */
    @ApiModelProperty(value = "个人收入(USD)")
    private BigDecimal personalIncomeUSD;

    /** 团队收入(USD) */
    @ApiModelProperty(value = "团队收入(USD)")
    private BigDecimal teamIncomeUSD;

    public BigDecimal getPersonalIncomeUSD() {
        return personalIncomeUSD;
    }

    public void setPersonalIncomeUSD(BigDecimal personalIncomeUSD) {
        this.personalIncomeUSD = personalIncomeUSD;
    }

    public BigDecimal getTeamIncomeUSD() {
        return teamIncomeUSD;
    }

    public void setTeamIncomeUSD(BigDecimal teamIncomeUSD) {
        this.teamIncomeUSD = teamIncomeUSD;
    }
}
