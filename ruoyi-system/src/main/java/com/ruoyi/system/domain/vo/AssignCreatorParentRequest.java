package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;

/**
 * 指定创建者上级请求实体
 *
 * <AUTHOR>
 */
@ApiModel(value = "AssignCreatorParentRequest", description = "指定创建者上级请求参数")
public class AssignCreatorParentRequest
{
    /** 创建者ID */
    @ApiModelProperty(value = "创建者ID", required = true, example = "7505039326170988545")
    @NotNull(message = "创建者ID不能为空")
    private Long creatorId;

    /** 上级ID */
    @ApiModelProperty(value = "上级ID", example = "8881672891234567890", notes = "如果为null则移除上级关系")
    private Long parentId;

    public Long getCreatorId()
    {
        return creatorId;
    }

    public void setCreatorId(Long creatorId)
    {
        this.creatorId = creatorId;
    }

    public Long getParentId()
    {
        return parentId;
    }

    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }
} 