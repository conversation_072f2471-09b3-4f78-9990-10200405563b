package com.ruoyi.system.domain.dto;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 绩效分析对象
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "绩效分析")
public class PerformanceAnalytics
{
    /** 总绩效(USD) */
    @ApiModelProperty(value = "总绩效(USD)")
    private BigDecimal totalPerformanceUSD;

    /** 收入构成 */
    @ApiModelProperty(value = "收入构成")
    private IncomeComponents components;

    /** 关键绩效指标 */
    @ApiModelProperty(value = "关键绩效指标")
    private PerformanceKPIs kpis;

    public BigDecimal getTotalPerformanceUSD() {
        return totalPerformanceUSD;
    }

    public void setTotalPerformanceUSD(BigDecimal totalPerformanceUSD) {
        this.totalPerformanceUSD = totalPerformanceUSD;
    }

    public IncomeComponents getComponents() {
        return components;
    }

    public void setComponents(IncomeComponents components) {
        this.components = components;
    }

    public PerformanceKPIs getKpis() {
        return kpis;
    }

    public void setKpis(PerformanceKPIs kpis) {
        this.kpis = kpis;
    }
}
