package com.ruoyi.system.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.system.domain.business.MonthlyPerformance;
import com.ruoyi.system.domain.commission.CommissionPayouts;

/**
 * 主播业绩详情VO对象，用于主播视角接口返回
 *
 * <AUTHOR>
 * @date 2025-06-28
 */
@ApiModel(value = "AnchorPerformanceVO", description = "主播业绩详情视图对象")
public class AnchorPerformanceVO
{
    /** 分销员收入数据 */
    @ApiModelProperty(value = "分销员收入数据")
    private CommissionPayouts commissionPayouts;
    
    /** 当月月度业绩数据 */
    @ApiModelProperty(value = "当月月度业绩数据")
    private MonthlyPerformance currentMonthPerformance;
    
    /** 上月月度业绩数据 */
    @ApiModelProperty(value = "上月月度业绩数据")
    private MonthlyPerformance lastMonthPerformance;
    
    /** 直播时长环比数据 */
    @ApiModelProperty(value = "直播时长环比数据")
    private LiveDurationComparison liveDurationComparison;
    
    /**
     * 直播时长环比内部类
     */
    @ApiModel(value = "LiveDurationComparison", description = "直播时长环比数据")
    public static class LiveDurationComparison {
        /** 当月直播时长(h) */
        @ApiModelProperty(value = "当月直播时长(h)", example = "150.5")
        private BigDecimal currentMonthHours;
        
        /** 上月直播时长(h) */
        @ApiModelProperty(value = "上月直播时长(h)", example = "120.3")
        private BigDecimal lastMonthHours;
        
        /** 环比变化量(h) */
        @ApiModelProperty(value = "环比变化量(h)", example = "30.2")
        private BigDecimal changeAmount;
        
        /** 环比变化百分比(%) */
        @ApiModelProperty(value = "环比变化百分比(%)", example = "25.1")
        private BigDecimal changePercentage;
        
        /** 变化趋势描述 */
        @ApiModelProperty(value = "变化趋势描述", example = "增加")
        private String changeTrend;
        
        /** 当月有效天数(d) */
        @ApiModelProperty(value = "当月有效天数(d)", example = "20")
        private Integer currentMonthValidDays;
        
        /** 上月有效天数(d) */
        @ApiModelProperty(value = "上月有效天数(d)", example = "18")
        private Integer lastMonthValidDays;

        // Getters and Setters
        public BigDecimal getCurrentMonthHours() {
            return currentMonthHours;
        }

        public void setCurrentMonthHours(BigDecimal currentMonthHours) {
            this.currentMonthHours = currentMonthHours;
        }

        public BigDecimal getLastMonthHours() {
            return lastMonthHours;
        }

        public void setLastMonthHours(BigDecimal lastMonthHours) {
            this.lastMonthHours = lastMonthHours;
        }

        public BigDecimal getChangeAmount() {
            return changeAmount;
        }

        public void setChangeAmount(BigDecimal changeAmount) {
            this.changeAmount = changeAmount;
        }

        public BigDecimal getChangePercentage() {
            return changePercentage;
        }

        public void setChangePercentage(BigDecimal changePercentage) {
            this.changePercentage = changePercentage;
        }

        public String getChangeTrend() {
            return changeTrend;
        }

        public void setChangeTrend(String changeTrend) {
            this.changeTrend = changeTrend;
        }

        public Integer getCurrentMonthValidDays() {
            return currentMonthValidDays;
        }

        public void setCurrentMonthValidDays(Integer currentMonthValidDays) {
            this.currentMonthValidDays = currentMonthValidDays;
        }

        public Integer getLastMonthValidDays() {
            return lastMonthValidDays;
        }

        public void setLastMonthValidDays(Integer lastMonthValidDays) {
            this.lastMonthValidDays = lastMonthValidDays;
        }
    }

    // Getters and Setters
    public CommissionPayouts getCommissionPayouts() {
        return commissionPayouts;
    }

    public void setCommissionPayouts(CommissionPayouts commissionPayouts) {
        this.commissionPayouts = commissionPayouts;
    }

    public MonthlyPerformance getCurrentMonthPerformance() {
        return currentMonthPerformance;
    }

    public void setCurrentMonthPerformance(MonthlyPerformance currentMonthPerformance) {
        this.currentMonthPerformance = currentMonthPerformance;
    }

    public MonthlyPerformance getLastMonthPerformance() {
        return lastMonthPerformance;
    }

    public void setLastMonthPerformance(MonthlyPerformance lastMonthPerformance) {
        this.lastMonthPerformance = lastMonthPerformance;
    }

    public LiveDurationComparison getLiveDurationComparison() {
        return liveDurationComparison;
    }

    public void setLiveDurationComparison(LiveDurationComparison liveDurationComparison) {
        this.liveDurationComparison = liveDurationComparison;
    }
} 