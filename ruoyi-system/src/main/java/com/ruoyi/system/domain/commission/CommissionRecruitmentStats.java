package com.ruoyi.system.domain.commission;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;

/**
 * 招募统计对象 commission_recruitment_stats
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "招募统计对象")
public class CommissionRecruitmentStats extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 招募人ID */
    @ApiModelProperty(value = "招募人ID", required = true)
    @NotNull(message = "招募人ID不能为空")
    @Excel(name = "招募人ID")
    private Long recruiterId;

    /** 统计月份 */
    @ApiModelProperty(value = "统计月份", required = true)
    @NotNull(message = "统计月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 当月新招募人数 */
    @ApiModelProperty(value = "当月新招募人数", required = true)
    @NotNull(message = "当月新招募人数不能为空")
    @Excel(name = "当月新招募人数")
    private Integer newRecruitsCount;

    /** 符合条件的奖励金额 */
    @ApiModelProperty(value = "符合条件的奖励金额", required = true)
    @NotNull(message = "符合条件的奖励金额不能为空")
    @Excel(name = "符合条件的奖励金额")
    private BigDecimal qualifiedBonusUsd;

    public void setRecruiterId(Long recruiterId) 
    {
        this.recruiterId = recruiterId;
    }

    public Long getRecruiterId() 
    {
        return recruiterId;
    }

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setNewRecruitsCount(Integer newRecruitsCount) 
    {
        this.newRecruitsCount = newRecruitsCount;
    }

    public Integer getNewRecruitsCount() 
    {
        return newRecruitsCount;
    }

    public void setQualifiedBonusUsd(BigDecimal qualifiedBonusUsd) 
    {
        this.qualifiedBonusUsd = qualifiedBonusUsd;
    }

    public BigDecimal getQualifiedBonusUsd() 
    {
        return qualifiedBonusUsd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recruiterId", getRecruiterId())
            .append("dataMonth", getDataMonth())
            .append("newRecruitsCount", getNewRecruitsCount())
            .append("qualifiedBonusUsd", getQualifiedBonusUsd())
            .toString();
    }
} 