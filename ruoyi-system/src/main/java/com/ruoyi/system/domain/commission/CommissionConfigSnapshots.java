package com.ruoyi.system.domain.commission;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * 月度配置快照对象 commission_config_snapshots
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@ApiModel(description = "月度配置快照对象")
public class CommissionConfigSnapshots extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 快照ID */
    @ApiModelProperty(value = "快照ID")
    private Long id;

    /** 配置生效月份 */
    @ApiModelProperty(value = "配置生效月份", required = true)
    @NotNull(message = "配置生效月份不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "配置生效月份", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataMonth;

    /** 配置类型 */
    @ApiModelProperty(value = "配置类型", required = true)
    @NotBlank(message = "配置类型不能为空")
    @Excel(name = "配置类型")
    private String configType;

    /** 配置数据快照 */
    @ApiModelProperty(value = "配置数据快照", required = true)
    @NotBlank(message = "配置数据快照不能为空")
    private String configData;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDataMonth(Date dataMonth) 
    {
        this.dataMonth = dataMonth;
    }

    public Date getDataMonth() 
    {
        return dataMonth;
    }

    public void setConfigType(String configType) 
    {
        this.configType = configType;
    }

    public String getConfigType() 
    {
        return configType;
    }

    public void setConfigData(String configData) 
    {
        this.configData = configData;
    }

    public String getConfigData() 
    {
        return configData;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataMonth", getDataMonth())
            .append("configType", getConfigType())
            .append("configData", getConfigData())
            .append("createdAt", getCreatedAt())
            .toString();
    }
} 