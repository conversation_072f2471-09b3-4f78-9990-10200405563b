package com.ruoyi.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 指定组上级请求实体
 *
 * <AUTHOR>
 */
@ApiModel(value = "AssignGroupParentRequest", description = "指定组上级请求参数")
public class AssignGroupParentRequest
{
    /** 组名 */
    @ApiModelProperty(value = "组名", required = true, example = "Jason")
    @NotBlank(message = "组名不能为空")
    private String groupName;

    /** 创建者ID */
    @ApiModelProperty(value = "创建者ID", required = true, example = "8881672891234567890")
    @NotNull(message = "创建者ID不能为空")
    private Long creatorId;

    public String getGroupName()
    {
        return groupName;
    }

    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }

    public Long getCreatorId()
    {
        return creatorId;
    }

    public void setCreatorId(Long creatorId)
    {
        this.creatorId = creatorId;
    }
} 