package com.ruoyi.system.domain.business;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 组名与CreatorID关系对象 group_creator_relation
 * 对应表: group_creator_relation
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@ApiModel(value = "GroupCreatorRelation", description = "组名与CreatorID关系实体")
public class GroupCreatorRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 组名 */
    @ApiModelProperty(value = "组名", example = "GroupA", required = true)
    @Excel(name = "组名")
    private String groupName;

    /** 创建者ID, 关联creators表的主键 */
    @ApiModelProperty(value = "创建者ID", example = "1001", required = true)
    @Excel(name = "创建者ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long creatorId;

    /** 组类型：0-分销组，1-非分销组 */
    @ApiModelProperty(value = "组类型", example = "0", notes = "0-分销组，1-非分销组")
    @Excel(name = "组类型", readConverterExp = "0=分销组,1=非分销组")
    private Integer groupType;

    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }

    public String getGroupName()
    {
        return groupName;
    }

    public void setCreatorId(Long creatorId)
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId()
    {
        return creatorId;
    }

    public void setGroupType(Integer groupType)
    {
        this.groupType = groupType;
    }

    public Integer getGroupType()
    {
        return groupType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("groupName", getGroupName())
            .append("creatorId", getCreatorId())
            .append("groupType", getGroupType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}