# 事务分离修复验证

## 问题分析
之前的问题是在同一个事务中尝试切换数据源，导致@DataSource注解失效。

### 问题代码模式
```java
@Transactional  // 开启SLAVE数据源事务
public Long createVirtualParentForGroup(...) {
    // 1. 创建虚拟creator (SLAVE数据源)
    // 2. 建立组关系 (SLAVE数据源)
    // 3. 调用同步服务 (期望MASTER数据源，但实际还是SLAVE)
    HistoricalUserSyncService.synchronizeToRuoyiUserSystemWithMasterDB(...);
}
```

## 修复方案：事务分离

### 新的代码结构
```java
public Long createVirtualParentForGroup(...) {
    // 第一步：在SLAVE事务中创建虚拟上级
    Long virtualId = createVirtualParentInTransaction(...);
    
    // 第二步：在事务外同步到MASTER（新事务）
    syncVirtualParentToRuoyiSystem(virtualId, createBy);
    
    return virtualId;
}

@Transactional // SLAVE事务
private Long createVirtualParentInTransaction(...) {
    // 创建虚拟creator和建立关系
}

private void syncVirtualParentToRuoyiSystem(...) {
    // 获取HistoricalUserSyncService Bean
    // 调用同步方法（会开启新的MASTER事务）
}

@Component("historicalUserSyncService")
public static class HistoricalUserSyncService {
    @DataSource(DataSourceType.MASTER)
    @Transactional(propagation = Propagation.REQUIRES_NEW)  // 强制新事务
    public void synchronizeToRuoyiUserSystemWithMasterDB(...) {
        // 在新的MASTER事务中执行
    }
}
```

## 验证步骤

### 1. 测试虚拟上级创建
```bash
POST /h/historical/virtual-parent
Content-Type: application/json
Authorization: Bearer [token]

{
  "groupName": "TestGroup",
  "virtualNickname": "Test-虚拟",
  "remark": "测试事务分离"
}
```

**期望日志：**
```
开始为组 TestGroup 创建虚拟上级...
生成虚拟上级ID：888xxxxx
✅ 虚拟creator插入数据库成功
✅ 组关系建立成功
开始同步虚拟上级到ruoyi用户系统，virtualId: 888xxxxx
通过Bean名称获取HistoricalUserSyncService成功
=== 开始独立用户同步服务 ===
独立服务的数据源注解: @DataSource(DataSourceType.MASTER)
✅ 虚拟上级同步到ruoyi系统成功
```

### 2. 测试历史数据导入
```bash
POST /h/historical/import-data
Content-Type: multipart/form-data
file: [Excel文件]
```

**期望结果：**
- 不再出现 `Table 'streamer_distribution_system.sys_user' doesn't exist` 错误
- 数据正确同步到MASTER数据源的sys_user表

### 3. 验证数据源切换
检查ruoyi系统数据库（MASTER）：
```sql
SELECT user_name, nick_name, create_time 
FROM sys_user 
WHERE user_name LIKE '888%'
ORDER BY create_time DESC;
```

应该能看到新创建的虚拟用户。

## 关键改进点

1. **事务边界分离**：主业务逻辑和同步逻辑分别在不同的事务中
2. **强制新事务**：使用 `@Transactional(propagation = Propagation.REQUIRES_NEW)`
3. **显式Bean名称**：`@Component("historicalUserSyncService")` 避免Bean名称冲突
4. **数据源注解**：`@DataSource(DataSourceType.MASTER)` 在新事务中生效

## 预期结果
- ✅ 数据源切换正常工作
- ✅ 虚拟用户成功同步到ruoyi系统
- ✅ 历史数据导入功能完全正常
- ✅ 不再出现数据源相关错误 