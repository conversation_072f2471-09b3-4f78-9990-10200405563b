#!/bin/bash

# 脚本1：备份源MySQL数据库

# --- 配置 - 源服务器 ---
SOURCE_DB_HOST="*************"
SOURCE_DB_USER="root"
SOURCE_DB_PASS="123456" # 警告：在脚本中存储密码不安全。
SOURCE_DB_NAME="wise_wealth_ry"

# --- 备份文件配置 ---
# 获取脚本所在的绝对路径
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
BACKUP_DIR_LOCAL="${SCRIPT_DIR}" # 本地备份将存储在脚本所在目录
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME_ONLY="${SOURCE_DB_NAME}_backup_${TIMESTAMP}.sql"
LOCAL_BACKUP_PATH="${BACKUP_DIR_LOCAL}/${BACKUP_FILENAME_ONLY}"

# --- 辅助函数 ---
log_error() {
    echo "[错误] $(date +"%Y-%m-%d %H:%M:%S") - $1" >&2
}

log_info() {
    echo "[信息] $(date +"%Y-%m-%d %H:%M:%S") - $1"
}

# --- 主脚本 ---
log_info "开始从 '${SOURCE_DB_HOST}' 备份数据库 '${SOURCE_DB_NAME}'..."

# 检查 mysqldump
command -v mysqldump >/dev/null 2>&1 || { log_error "mysqldump 未安装。请先安装 MySQL 客户端工具。"; exit 1; }

mysqldump -h "${SOURCE_DB_HOST}" -u "${SOURCE_DB_USER}" -p"${SOURCE_DB_PASS}" --single-transaction --routines --triggers "${SOURCE_DB_NAME}" > "${LOCAL_BACKUP_PATH}"

if [ $? -ne 0 ]; then
    log_error "数据库 '${SOURCE_DB_NAME}' 备份失败。"
    rm -f "${LOCAL_BACKUP_PATH}" # 清理失败的备份文件
    exit 1
fi

log_info "数据库备份成功。文件: ${LOCAL_BACKUP_PATH}"
echo "${LOCAL_BACKUP_PATH}" # 将备份文件路径输出到stdout

exit 0
