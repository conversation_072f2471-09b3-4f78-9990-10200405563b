#!/bin/bash

# 脚本2：将备份文件传输到目标服务器

# --- 配置 - 目标服务器 (SSH/SCP) ---
TARGET_SERVER_IP="************"
TARGET_SERVER_USER="root"
# TARGET_SERVER_PASS="" # 假定使用SSH密钥认证
TARGET_REMOTE_PATH_BASE="/home/<USER>/ruoyi-admin" # 目标服务器上存放备份文件的基础路径

# --- 辅助函数 ---
log_error() {
    echo "[错误] $(date +"%Y-%m-%d %H:%M:%S") - $1" >&2
}

log_info() {
    echo "[信息] $(date +"%Y-%m-%d %H:%M:%S") - $1"
}

# --- 主脚本 ---
if [ -z "$1" ]; then
    log_error "用法: $0 <local_backup_file_path>"
    exit 1
fi

LOCAL_BACKUP_PATH="$1"
BACKUP_FILENAME_ONLY=$(basename "${LOCAL_BACKUP_PATH}")
REMOTE_BACKUP_PATH="${TARGET_REMOTE_PATH_BASE}/${BACKUP_FILENAME_ONLY}"

if [ ! -f "${LOCAL_BACKUP_PATH}" ]; then
    log_error "本地备份文件 '${LOCAL_BACKUP_PATH}' 未找到。"
    exit 1
fi

# 检查 scp 和 sshpass (如果需要)
command -v scp >/dev/null 2>&1 || { log_error "scp 未安装。请先安装 OpenSSH 客户端。"; exit 1; }

log_info "正在将备份文件 '${LOCAL_BACKUP_PATH}' 传输到 '${TARGET_SERVER_IP}:${REMOTE_BACKUP_PATH}'..."
SCP_COMMAND="scp -o ConnectTimeout=30 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"

# 由于 TARGET_SERVER_PASS 为空, 直接使用 scp (假定SSH密钥认证)
${SCP_COMMAND} "${LOCAL_BACKUP_PATH}" "${TARGET_SERVER_USER}@${TARGET_SERVER_IP}:${REMOTE_BACKUP_PATH}"

if [ $? -ne 0 ]; then
    log_error "未能将备份文件传输到目标服务器 '${TARGET_SERVER_IP}'。"
    exit 1
fi

log_info "备份文件成功传输到 '${TARGET_SERVER_IP}:${REMOTE_BACKUP_PATH}'。"
echo "${REMOTE_BACKUP_PATH}" # 将远程文件路径输出到stdout

exit 0
