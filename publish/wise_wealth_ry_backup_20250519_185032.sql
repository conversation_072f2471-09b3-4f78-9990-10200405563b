-- MySQL dump 10.13  Distrib 5.7.44, for osx10.18 (x86_64)
--
-- Host: *************    Database: wise_wealth_ry
-- ------------------------------------------------------
-- Server version	5.7.40-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `QRTZ_BLOB_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_BLOB_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_BLOB_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_BLOB_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Blob类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_BLOB_TRIGGERS`
--

LOCK TABLES `QRTZ_BLOB_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_BLOB_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_BLOB_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_CALENDARS`
--

DROP TABLE IF EXISTS `QRTZ_CALENDARS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_CALENDARS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`,`calendar_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日历信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_CALENDARS`
--

LOCK TABLES `QRTZ_CALENDARS` WRITE;
/*!40000 ALTER TABLE `QRTZ_CALENDARS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_CALENDARS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_CRON_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_CRON_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_CRON_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_CRON_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Cron类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_CRON_TRIGGERS`
--

LOCK TABLES `QRTZ_CRON_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_CRON_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_CRON_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_FIRED_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_FIRED_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_FIRED_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint(13) NOT NULL COMMENT '触发的时间',
  `sched_time` bigint(13) NOT NULL COMMENT '定时器制定的时间',
  `priority` int(11) NOT NULL COMMENT '优先级',
  `state` varchar(16) NOT NULL COMMENT '状态',
  `job_name` varchar(200) DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`,`entry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='已触发的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_FIRED_TRIGGERS`
--

LOCK TABLES `QRTZ_FIRED_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_FIRED_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_FIRED_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_JOB_DETAILS`
--

DROP TABLE IF EXISTS `QRTZ_JOB_DETAILS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_JOB_DETAILS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) NOT NULL COMMENT '任务组名',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_JOB_DETAILS`
--

LOCK TABLES `QRTZ_JOB_DETAILS` WRITE;
/*!40000 ALTER TABLE `QRTZ_JOB_DETAILS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_JOB_DETAILS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_LOCKS`
--

DROP TABLE IF EXISTS `QRTZ_LOCKS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_LOCKS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`,`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='存储的悲观锁信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_LOCKS`
--

LOCK TABLES `QRTZ_LOCKS` WRITE;
/*!40000 ALTER TABLE `QRTZ_LOCKS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_LOCKS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_PAUSED_TRIGGER_GRPS`
--

DROP TABLE IF EXISTS `QRTZ_PAUSED_TRIGGER_GRPS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='暂停的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_PAUSED_TRIGGER_GRPS`
--

LOCK TABLES `QRTZ_PAUSED_TRIGGER_GRPS` WRITE;
/*!40000 ALTER TABLE `QRTZ_PAUSED_TRIGGER_GRPS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_PAUSED_TRIGGER_GRPS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_SCHEDULER_STATE`
--

DROP TABLE IF EXISTS `QRTZ_SCHEDULER_STATE`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_SCHEDULER_STATE` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint(13) NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint(13) NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`,`instance_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调度器状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_SCHEDULER_STATE`
--

LOCK TABLES `QRTZ_SCHEDULER_STATE` WRITE;
/*!40000 ALTER TABLE `QRTZ_SCHEDULER_STATE` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_SCHEDULER_STATE` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_SIMPLE_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_SIMPLE_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_SIMPLE_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint(7) NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint(12) NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint(10) NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_SIMPLE_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简单触发器的信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_SIMPLE_TRIGGERS`
--

LOCK TABLES `QRTZ_SIMPLE_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_SIMPLE_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_SIMPLE_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_SIMPROP_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_SIMPROP_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_SIMPROP_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int(11) DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int(11) DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint(20) DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint(20) DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_SIMPROP_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步机制的行锁表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_SIMPROP_TRIGGERS`
--

LOCK TABLES `QRTZ_SIMPROP_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_SIMPROP_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_SIMPROP_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `QRTZ_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint(13) DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint(13) DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int(11) DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) NOT NULL COMMENT '触发器的类型',
  `start_time` bigint(13) NOT NULL COMMENT '开始时间',
  `end_time` bigint(13) DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint(2) DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`),
  CONSTRAINT `QRTZ_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `QRTZ_JOB_DETAILS` (`sched_name`, `job_name`, `job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_TRIGGERS`
--

LOCK TABLES `QRTZ_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table`
--

DROP TABLE IF EXISTS `gen_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gen_table` (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代码生成业务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table`
--

LOCK TABLES `gen_table` WRITE;
/*!40000 ALTER TABLE `gen_table` DISABLE KEYS */;
/*!40000 ALTER TABLE `gen_table` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table_column`
--

DROP TABLE IF EXISTS `gen_table_column`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gen_table_column` (
  `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint(20) DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) DEFAULT '' COMMENT '字典类型',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代码生成业务表字段';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table_column`
--

LOCK TABLES `gen_table_column` WRITE;
/*!40000 ALTER TABLE `gen_table_column` DISABLE KEYS */;
/*!40000 ALTER TABLE `gen_table_column` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_config`
--

DROP TABLE IF EXISTS `sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_config` (
  `config_id` int(5) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_config`
--

LOCK TABLES `sys_config` WRITE;
/*!40000 ALTER TABLE `sys_config` DISABLE KEYS */;
INSERT INTO `sys_config` VALUES (1,'主框架页-默认皮肤样式名称','sys.index.skinName','skin-blue','Y','admin','2025-04-09 14:22:36','',NULL,'蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),(2,'用户管理-账号初始密码','sys.user.initPassword','123456','Y','admin','2025-04-09 14:22:36','',NULL,'初始化密码 123456'),(3,'主框架页-侧边栏主题','sys.index.sideTheme','theme-dark','Y','admin','2025-04-09 14:22:37','',NULL,'深色主题theme-dark，浅色主题theme-light'),(4,'账号自助-验证码开关','sys.account.captchaEnabled','true','Y','admin','2025-04-09 14:22:37','',NULL,'是否开启验证码功能（true开启，false关闭）'),(5,'账号自助-是否开启用户注册功能','sys.account.registerUser','false','Y','admin','2025-04-09 14:22:37','',NULL,'是否开启注册用户功能（true开启，false关闭）'),(6,'用户登录-黑名单列表','sys.login.blackIPList','','Y','admin','2025-04-09 14:22:37','',NULL,'设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
/*!40000 ALTER TABLE `sys_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_dept` (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept`
--

LOCK TABLES `sys_dept` WRITE;
/*!40000 ALTER TABLE `sys_dept` DISABLE KEYS */;
INSERT INTO `sys_dept` VALUES (100,0,'0','若依科技',0,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:14','',NULL),(101,100,'0,100','深圳总公司',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:14','',NULL),(102,100,'0,100','长沙分公司',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:14','',NULL),(103,101,'0,100,101','研发部门',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:15','',NULL),(104,101,'0,100,101','市场部门',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:15','',NULL),(105,101,'0,100,101','测试部门',3,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:15','',NULL),(106,101,'0,100,101','财务部门',4,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:15','',NULL),(107,101,'0,100,101','运维部门',5,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:15','',NULL),(108,102,'0,100,102','市场部门',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:15','',NULL),(109,102,'0,100,102','财务部门',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-09 14:22:15','',NULL);
/*!40000 ALTER TABLE `sys_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_data`
--

DROP TABLE IF EXISTS `sys_dict_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_data`
--

LOCK TABLES `sys_dict_data` WRITE;
/*!40000 ALTER TABLE `sys_dict_data` DISABLE KEYS */;
INSERT INTO `sys_dict_data` VALUES (1,1,'男','0','sys_user_sex','','','Y','0','admin','2025-04-09 14:22:34','',NULL,'性别男'),(2,2,'女','1','sys_user_sex','','','N','0','admin','2025-04-09 14:22:34','',NULL,'性别女'),(3,3,'未知','2','sys_user_sex','','','N','0','admin','2025-04-09 14:22:34','',NULL,'性别未知'),(4,1,'显示','0','sys_show_hide','','primary','Y','0','admin','2025-04-09 14:22:34','',NULL,'显示菜单'),(5,2,'隐藏','1','sys_show_hide','','danger','N','0','admin','2025-04-09 14:22:34','',NULL,'隐藏菜单'),(6,1,'正常','0','sys_normal_disable','','primary','Y','0','admin','2025-04-09 14:22:34','',NULL,'正常状态'),(7,2,'停用','1','sys_normal_disable','','danger','N','0','admin','2025-04-09 14:22:35','',NULL,'停用状态'),(8,1,'正常','0','sys_job_status','','primary','Y','0','admin','2025-04-09 14:22:35','',NULL,'正常状态'),(9,2,'暂停','1','sys_job_status','','danger','N','0','admin','2025-04-09 14:22:35','',NULL,'停用状态'),(10,1,'默认','DEFAULT','sys_job_group','','','Y','0','admin','2025-04-09 14:22:35','',NULL,'默认分组'),(11,2,'系统','SYSTEM','sys_job_group','','','N','0','admin','2025-04-09 14:22:35','',NULL,'系统分组'),(12,1,'是','Y','sys_yes_no','','primary','Y','0','admin','2025-04-09 14:22:35','',NULL,'系统默认是'),(13,2,'否','N','sys_yes_no','','danger','N','0','admin','2025-04-09 14:22:35','',NULL,'系统默认否'),(14,1,'通知','1','sys_notice_type','','warning','Y','0','admin','2025-04-09 14:22:35','',NULL,'通知'),(15,2,'公告','2','sys_notice_type','','success','N','0','admin','2025-04-09 14:22:35','',NULL,'公告'),(16,1,'正常','0','sys_notice_status','','primary','Y','0','admin','2025-04-09 14:22:35','',NULL,'正常状态'),(17,2,'关闭','1','sys_notice_status','','danger','N','0','admin','2025-04-09 14:22:35','',NULL,'关闭状态'),(18,99,'其他','0','sys_oper_type','','info','N','0','admin','2025-04-09 14:22:35','',NULL,'其他操作'),(19,1,'新增','1','sys_oper_type','','info','N','0','admin','2025-04-09 14:22:35','',NULL,'新增操作'),(20,2,'修改','2','sys_oper_type','','info','N','0','admin','2025-04-09 14:22:35','',NULL,'修改操作'),(21,3,'删除','3','sys_oper_type','','danger','N','0','admin','2025-04-09 14:22:35','',NULL,'删除操作'),(22,4,'授权','4','sys_oper_type','','primary','N','0','admin','2025-04-09 14:22:35','',NULL,'授权操作'),(23,5,'导出','5','sys_oper_type','','warning','N','0','admin','2025-04-09 14:22:36','',NULL,'导出操作'),(24,6,'导入','6','sys_oper_type','','warning','N','0','admin','2025-04-09 14:22:36','',NULL,'导入操作'),(25,7,'强退','7','sys_oper_type','','danger','N','0','admin','2025-04-09 14:22:36','',NULL,'强退操作'),(26,8,'生成代码','8','sys_oper_type','','warning','N','0','admin','2025-04-09 14:22:36','',NULL,'生成操作'),(27,9,'清空数据','9','sys_oper_type','','danger','N','0','admin','2025-04-09 14:22:36','',NULL,'清空操作'),(28,1,'成功','0','sys_common_status','','primary','N','0','admin','2025-04-09 14:22:36','',NULL,'正常状态'),(29,2,'失败','1','sys_common_status','','danger','N','0','admin','2025-04-09 14:22:36','',NULL,'停用状态');
/*!40000 ALTER TABLE `sys_dict_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_type`
--

DROP TABLE IF EXISTS `sys_dict_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`),
  UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_type`
--

LOCK TABLES `sys_dict_type` WRITE;
/*!40000 ALTER TABLE `sys_dict_type` DISABLE KEYS */;
INSERT INTO `sys_dict_type` VALUES (1,'用户性别','sys_user_sex','0','admin','2025-04-09 14:22:33','',NULL,'用户性别列表'),(2,'菜单状态','sys_show_hide','0','admin','2025-04-09 14:22:33','',NULL,'菜单状态列表'),(3,'系统开关','sys_normal_disable','0','admin','2025-04-09 14:22:33','',NULL,'系统开关列表'),(4,'任务状态','sys_job_status','0','admin','2025-04-09 14:22:33','',NULL,'任务状态列表'),(5,'任务分组','sys_job_group','0','admin','2025-04-09 14:22:33','',NULL,'任务分组列表'),(6,'系统是否','sys_yes_no','0','admin','2025-04-09 14:22:33','',NULL,'系统是否列表'),(7,'通知类型','sys_notice_type','0','admin','2025-04-09 14:22:33','',NULL,'通知类型列表'),(8,'通知状态','sys_notice_status','0','admin','2025-04-09 14:22:34','',NULL,'通知状态列表'),(9,'操作类型','sys_oper_type','0','admin','2025-04-09 14:22:34','',NULL,'操作类型列表'),(10,'系统状态','sys_common_status','0','admin','2025-04-09 14:22:34','',NULL,'登录状态列表');
/*!40000 ALTER TABLE `sys_dict_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job`
--

DROP TABLE IF EXISTS `sys_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_job` (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='定时任务调度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job`
--

LOCK TABLES `sys_job` WRITE;
/*!40000 ALTER TABLE `sys_job` DISABLE KEYS */;
INSERT INTO `sys_job` VALUES (1,'系统默认（无参）','DEFAULT','ryTask.ryNoParams','0/10 * * * * ?','3','1','1','admin','2025-04-09 14:22:37','',NULL,''),(2,'系统默认（有参）','DEFAULT','ryTask.ryParams(\'ry\')','0/15 * * * * ?','3','1','1','admin','2025-04-09 14:22:37','',NULL,''),(3,'系统默认（多参）','DEFAULT','ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)','0/20 * * * * ?','3','1','1','admin','2025-04-09 14:22:38','',NULL,'');
/*!40000 ALTER TABLE `sys_job` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job_log`
--

DROP TABLE IF EXISTS `sys_job_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_job_log` (
  `job_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) DEFAULT NULL COMMENT '日志信息',
  `status` char(1) DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) DEFAULT '' COMMENT '异常信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务调度日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job_log`
--

LOCK TABLES `sys_job_log` WRITE;
/*!40000 ALTER TABLE `sys_job_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_job_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_logininfor`
--

DROP TABLE IF EXISTS `sys_logininfor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_logininfor` (
  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
  `status` char(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) DEFAULT '' COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`),
  KEY `idx_sys_logininfor_s` (`status`),
  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=281 DEFAULT CHARSET=utf8mb4 COMMENT='系统访问记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_logininfor`
--

LOCK TABLES `sys_logininfor` WRITE;
/*!40000 ALTER TABLE `sys_logininfor` DISABLE KEYS */;
INSERT INTO `sys_logininfor` VALUES (100,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-10 13:42:01'),(101,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-11 10:12:04'),(102,'admin','***********','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-04-11 15:45:02'),(103,'admin','***********','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-04-11 15:45:06'),(104,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-11 15:45:14'),(105,'admin','***********','内网IP','Safari','Mac OS X','0','登录成功','2025-04-11 15:54:36'),(106,'admin','***********','XX XX','Chrome Mobile','Android 7.x','1','验证码错误','2025-04-11 17:52:01'),(107,'admin','***********','XX XX','Chrome Mobile','Android 7.x','0','登录成功','2025-04-11 17:52:17'),(108,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-11 18:44:50'),(109,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-12 22:21:54'),(110,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 22:22:12'),(111,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 22:22:36'),(112,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 22:22:49'),(113,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 22:24:01'),(114,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 22:28:25'),(115,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 22:37:05'),(116,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 22:37:20'),(117,'admin','111.31.34.31','XX XX','Chrome Mobile','Android 7.x','0','登录成功','2025-04-12 22:38:56'),(118,'admin','111.31.34.31','XX XX','Chrome Mobile','Android 1.x','0','登录成功','2025-04-12 22:39:46'),(119,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 22:40:44'),(120,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-12 22:41:18'),(121,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 22:41:22'),(122,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-12 22:42:28'),(123,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-12 22:42:32'),(124,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:26:19'),(125,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:26:28'),(126,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:27:19'),(127,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:28:25'),(128,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:28:37'),(129,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:28:59'),(130,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:30:09'),(131,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:32:01'),(132,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:34:45'),(133,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-12 23:34:55'),(134,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:34:59'),(135,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:37:01'),(136,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-12 23:37:12'),(137,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:37:32'),(138,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:38:22'),(139,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:40:27'),(140,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:40:37'),(141,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:40:54'),(142,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:41:18'),(143,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-12 23:42:21'),(144,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:42:41'),(145,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:44:42'),(146,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:45:01'),(147,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-12 23:45:07'),(148,'admin','*************','XX XX','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-04-12 23:45:11'),(149,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-13 00:33:43'),(150,'admin','*************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-13 00:33:51'),(151,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-13 00:36:17'),(152,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-13 00:41:44'),(153,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-13 10:23:21'),(154,'admin','*************','XX XX','Mobile Safari','Mac OS X (iPhone)','0','登录成功','2025-04-13 12:37:32'),(155,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-13 13:52:49'),(156,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-13 14:05:18'),(157,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-14 14:03:16'),(158,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-15 09:47:40'),(159,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-16 09:55:01'),(160,'admin','***********','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-16 17:36:45'),(161,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-16 17:37:07'),(162,'admin','***********','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-16 17:41:31'),(163,'ry','***********','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-04-16 17:41:47'),(164,'ry','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-16 17:42:23'),(165,'ry','***********','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-16 17:42:29'),(166,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-16 17:42:33'),(167,'admin','***********','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-16 17:46:59'),(168,'lisi','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-16 17:47:08'),(169,'lisi','***********','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-16 17:47:29'),(170,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-16 17:55:24'),(171,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-16 19:55:16'),(172,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-16 20:31:12'),(173,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-16 20:31:22'),(174,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-17 10:05:03'),(175,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-17 11:58:16'),(176,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-17 14:34:21'),(177,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-18 09:44:30'),(178,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-18 17:44:33'),(179,'admin','***********','内网IP','Safari','Mac OS X','0','登录成功','2025-04-18 17:45:25'),(180,'admin','***********','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-20 14:11:31'),(181,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 14:15:14'),(182,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 14:34:11'),(183,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 14:40:41'),(184,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 14:54:23'),(185,'admin','************','XX XX','Chrome Mobile','Android 7.x','1','验证码错误','2025-04-20 15:00:31'),(186,'admin','************','XX XX','Chrome Mobile','Android 7.x','1','验证码错误','2025-04-20 15:00:36'),(187,'admin','************','XX XX','Chrome Mobile','Android 7.x','0','登录成功','2025-04-20 15:00:40'),(188,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-04-20 15:02:54'),(189,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 15:03:47'),(190,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 15:20:26'),(191,'admin','119.13.62.223','XX XX','Chrome 11','Windows 10','0','登录成功','2025-04-20 15:54:19'),(192,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 16:01:04'),(193,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-20 16:11:20'),(194,'admin','**************','XX XX','Chrome 11','Windows 10','1','验证码错误','2025-04-20 17:05:00'),(195,'admin','**************','XX XX','Chrome 11','Windows 10','1','验证码错误','2025-04-20 17:05:04'),(196,'admin','**************','XX XX','Chrome 11','Windows 10','1','验证码错误','2025-04-20 17:05:07'),(197,'admin','**************','XX XX','Chrome 11','Windows 10','1','验证码错误','2025-04-20 17:05:11'),(198,'admin','**************','XX XX','Chrome 11','Windows 10','1','验证码错误','2025-04-20 17:05:18'),(199,'admin','**************','XX XX','Chrome 11','Windows 10','0','登录成功','2025-04-20 17:05:22'),(200,'admin','**************','XX XX','Chrome 11','Windows 10','0','登录成功','2025-04-20 17:16:59'),(201,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-21 09:54:34'),(202,'admin','***********','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-21 10:09:24'),(203,'admin','***********','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-04-21 10:10:04'),(204,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-21 10:10:08'),(205,'admin','***********','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-23 14:18:24'),(206,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-29 17:14:50'),(207,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-30 09:34:44'),(208,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-30 18:42:39'),(209,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-01 11:22:07'),(210,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-01 12:24:10'),(211,'admin','**************','XX XX','Chrome Mobile','Android 7.x','0','登录成功','2025-05-01 12:28:31'),(212,'admin','************','XX XX','Chrome Mobile','Android 7.x','0','登录成功','2025-05-01 12:49:11'),(213,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-01 13:56:28'),(214,'admin','**************','XX XX','Chrome Mobile','Android 1.x','0','登录成功','2025-05-01 14:49:11'),(215,'admin','**************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-05-01 15:06:58'),(216,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-01 15:07:05'),(217,'admin','**************','XX XX','Chrome 11','Windows 10','0','登录成功','2025-05-01 15:25:20'),(218,'admin','*************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-05-01 19:30:27'),(219,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-01 19:30:31'),(220,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-01 21:18:24'),(221,'admin','**************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-05-02 09:28:46'),(222,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-02 09:28:54'),(223,'admin','**************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-05-02 10:12:42'),(224,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-02 10:12:48'),(225,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-03 23:27:01'),(226,'admin','**************','XX XX','Mobile Safari','Mac OS X (iPhone)','0','登录成功','2025-05-04 10:32:18'),(227,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-04 10:48:27'),(228,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-05 09:47:25'),(229,'admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2025-05-05 10:01:52'),(230,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-06 15:02:24'),(231,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-06 16:05:28'),(232,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-07 10:02:42'),(233,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-07 11:47:04'),(234,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-07 14:37:59'),(235,'admin','***************','XX XX','Chrome 13','Windows 10','1','验证码已失效','2025-05-07 23:08:19'),(236,'admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-07 23:08:25'),(237,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-08 10:44:05'),(238,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-09 09:58:01'),(239,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-09 16:31:34'),(240,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-09 18:28:50'),(241,'admin','*************','XX XX','Chrome Mobile','Android 7.x','0','登录成功','2025-05-09 18:32:11'),(242,'admin','************','XX XX','Mobile Safari','Mac OS X (iPhone)','0','登录成功','2025-05-09 18:34:49'),(243,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-09 18:38:01'),(244,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-10 09:32:33'),(245,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-11 19:29:38'),(246,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-13 16:36:57'),(247,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-13 19:10:06'),(248,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-14 10:02:53'),(249,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-14 10:16:42'),(250,'admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-14 14:52:32'),(251,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-14 20:03:02'),(252,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-15 11:55:49'),(253,'admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-15 13:45:16'),(254,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-15 16:05:46'),(255,'admin','***********','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-05-15 17:36:56'),(256,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-15 17:37:00'),(257,'admin','**************','XX XX','Chrome 11','Windows 10','0','登录成功','2025-05-15 21:12:27'),(258,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-16 11:13:50'),(259,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-16 14:42:44'),(260,'admin','************','XX XX','Chrome 13','Windows 10','1','验证码已失效','2025-05-16 15:05:33'),(261,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-16 15:05:40'),(262,'admin','**************','XX XX','Chrome 11','Windows 10','0','登录成功','2025-05-16 21:50:50'),(263,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-17 13:50:12'),(264,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-17 20:19:50'),(265,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-18 17:37:46'),(266,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-18 18:34:18'),(267,'admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-18 20:32:24'),(268,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-19 08:44:46'),(269,'admin','************','XX XX','Mobile Safari','Mac OS X (iPhone)','0','登录成功','2025-05-19 08:59:48'),(270,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-19 09:10:54'),(271,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-19 11:09:49'),(272,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-19 13:48:27'),(273,'admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-19 14:15:13'),(274,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-19 17:11:09'),(275,'admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-05-19 17:14:31'),(276,'admin','***********','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-05-19 17:24:45'),(277,'admin','***********','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-05-19 17:24:48'),(278,'admin','***********','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-05-19 17:24:59'),(279,'admin','***********','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-19 17:25:15'),(280,'admin','***********','内网IP','Chrome 13','Windows 10','0','登录成功','2025-05-19 17:36:00');
/*!40000 ALTER TABLE `sys_logininfor` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_menu` (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) DEFAULT '' COMMENT '路由名称',
  `is_frame` int(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2032 DEFAULT CHARSET=utf8mb4 COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (1,'系统管理',0,99,'system',NULL,'','',1,0,'M','0','0','','system','admin','2025-04-09 14:22:18','admin','2025-04-18 17:17:38','系统管理目录'),(2,'系统监控',0,2,'monitor',NULL,'','',1,0,'M','1','1','','monitor','admin','2025-04-09 14:22:18','admin','2025-04-17 17:32:06','系统监控目录'),(3,'系统工具',0,3,'tool',NULL,'','',1,0,'M','1','1','','tool','admin','2025-04-09 14:22:18','admin','2025-04-17 17:32:10','系统工具目录'),(100,'用户管理',1,1,'user','system/user/index','','',1,0,'C','0','0','system:user:list','user','admin','2025-04-09 14:22:18','',NULL,'用户管理菜单'),(101,'角色管理',1,2,'role','system/role/index','','',1,0,'C','0','0','system:role:list','peoples','admin','2025-04-09 14:22:18','',NULL,'角色管理菜单'),(102,'菜单管理',1,3,'menu','system/menu/index','','',1,0,'C','0','0','system:menu:list','tree-table','admin','2025-04-09 14:22:18','',NULL,'菜单管理菜单'),(103,'部门管理',1,4,'dept','system/dept/index','','',1,0,'C','0','0','system:dept:list','tree','admin','2025-04-09 14:22:19','',NULL,'部门管理菜单'),(104,'岗位管理',1,5,'post','system/post/index','','',1,0,'C','0','0','system:post:list','post','admin','2025-04-09 14:22:19','',NULL,'岗位管理菜单'),(105,'字典管理',1,6,'dict','system/dict/index','','',1,0,'C','0','0','system:dict:list','dict','admin','2025-04-09 14:22:19','',NULL,'字典管理菜单'),(106,'参数设置',1,7,'config','system/config/index','','',1,0,'C','0','0','system:config:list','edit','admin','2025-04-09 14:22:19','',NULL,'参数设置菜单'),(107,'通知公告',1,8,'notice','system/notice/index','','',1,0,'C','0','0','system:notice:list','message','admin','2025-04-09 14:22:19','',NULL,'通知公告菜单'),(108,'日志管理',1,9,'log','','','',1,0,'M','0','0','','log','admin','2025-04-09 14:22:19','',NULL,'日志管理菜单'),(109,'在线用户',2,1,'online','monitor/online/index','','',1,0,'C','0','0','monitor:online:list','online','admin','2025-04-09 14:22:19','',NULL,'在线用户菜单'),(110,'定时任务',2,2,'job','monitor/job/index','','',1,0,'C','0','0','monitor:job:list','job','admin','2025-04-09 14:22:19','',NULL,'定时任务菜单'),(111,'数据监控',2,3,'druid','monitor/druid/index','','',1,0,'C','0','0','monitor:druid:list','druid','admin','2025-04-09 14:22:19','',NULL,'数据监控菜单'),(112,'服务监控',2,4,'server','monitor/server/index','','',1,0,'C','0','0','monitor:server:list','server','admin','2025-04-09 14:22:19','',NULL,'服务监控菜单'),(113,'缓存监控',2,5,'cache','monitor/cache/index','','',1,0,'C','0','0','monitor:cache:list','redis','admin','2025-04-09 14:22:19','',NULL,'缓存监控菜单'),(114,'缓存列表',2,6,'cacheList','monitor/cache/list','','',1,0,'C','0','0','monitor:cache:list','redis-list','admin','2025-04-09 14:22:19','',NULL,'缓存列表菜单'),(115,'表单构建',3,1,'build','tool/build/index','','',1,0,'C','0','0','tool:build:list','build','admin','2025-04-09 14:22:19','',NULL,'表单构建菜单'),(116,'代码生成',3,2,'gen','tool/gen/index','','',1,0,'C','0','0','tool:gen:list','code','admin','2025-04-09 14:22:19','',NULL,'代码生成菜单'),(117,'系统接口',3,3,'swagger','tool/swagger/index','','',1,0,'C','0','0','tool:swagger:list','swagger','admin','2025-04-09 14:22:20','',NULL,'系统接口菜单'),(500,'操作日志',108,1,'operlog','monitor/operlog/index','','',1,0,'C','0','0','monitor:operlog:list','form','admin','2025-04-09 14:22:20','',NULL,'操作日志菜单'),(501,'登录日志',108,2,'logininfor','monitor/logininfor/index','','',1,0,'C','0','0','monitor:logininfor:list','logininfor','admin','2025-04-09 14:22:20','',NULL,'登录日志菜单'),(1000,'用户查询',100,1,'','','','',1,0,'F','0','0','system:user:query','#','admin','2025-04-09 14:22:20','',NULL,''),(1001,'用户新增',100,2,'','','','',1,0,'F','0','0','system:user:add','#','admin','2025-04-09 14:22:20','',NULL,''),(1002,'用户修改',100,3,'','','','',1,0,'F','0','0','system:user:edit','#','admin','2025-04-09 14:22:20','',NULL,''),(1003,'用户删除',100,4,'','','','',1,0,'F','0','0','system:user:remove','#','admin','2025-04-09 14:22:20','',NULL,''),(1004,'用户导出',100,5,'','','','',1,0,'F','0','0','system:user:export','#','admin','2025-04-09 14:22:20','',NULL,''),(1005,'用户导入',100,6,'','','','',1,0,'F','0','0','system:user:import','#','admin','2025-04-09 14:22:20','',NULL,''),(1006,'重置密码',100,7,'','','','',1,0,'F','0','0','system:user:resetPwd','#','admin','2025-04-09 14:22:20','',NULL,''),(1007,'角色查询',101,1,'','','','',1,0,'F','0','0','system:role:query','#','admin','2025-04-09 14:22:20','',NULL,''),(1008,'角色新增',101,2,'','','','',1,0,'F','0','0','system:role:add','#','admin','2025-04-09 14:22:20','',NULL,''),(1009,'角色修改',101,3,'','','','',1,0,'F','0','0','system:role:edit','#','admin','2025-04-09 14:22:20','',NULL,''),(1010,'角色删除',101,4,'','','','',1,0,'F','0','0','system:role:remove','#','admin','2025-04-09 14:22:20','',NULL,''),(1011,'角色导出',101,5,'','','','',1,0,'F','0','0','system:role:export','#','admin','2025-04-09 14:22:20','',NULL,''),(1012,'菜单查询',102,1,'','','','',1,0,'F','0','0','system:menu:query','#','admin','2025-04-09 14:22:20','',NULL,''),(1013,'菜单新增',102,2,'','','','',1,0,'F','0','0','system:menu:add','#','admin','2025-04-09 14:22:20','',NULL,''),(1014,'菜单修改',102,3,'','','','',1,0,'F','0','0','system:menu:edit','#','admin','2025-04-09 14:22:20','',NULL,''),(1015,'菜单删除',102,4,'','','','',1,0,'F','0','0','system:menu:remove','#','admin','2025-04-09 14:22:21','',NULL,''),(1016,'部门查询',103,1,'','','','',1,0,'F','0','0','system:dept:query','#','admin','2025-04-09 14:22:21','',NULL,''),(1017,'部门新增',103,2,'','','','',1,0,'F','0','0','system:dept:add','#','admin','2025-04-09 14:22:21','',NULL,''),(1018,'部门修改',103,3,'','','','',1,0,'F','0','0','system:dept:edit','#','admin','2025-04-09 14:22:21','',NULL,''),(1019,'部门删除',103,4,'','','','',1,0,'F','0','0','system:dept:remove','#','admin','2025-04-09 14:22:21','',NULL,''),(1020,'岗位查询',104,1,'','','','',1,0,'F','0','0','system:post:query','#','admin','2025-04-09 14:22:21','',NULL,''),(1021,'岗位新增',104,2,'','','','',1,0,'F','0','0','system:post:add','#','admin','2025-04-09 14:22:21','',NULL,''),(1022,'岗位修改',104,3,'','','','',1,0,'F','0','0','system:post:edit','#','admin','2025-04-09 14:22:21','',NULL,''),(1023,'岗位删除',104,4,'','','','',1,0,'F','0','0','system:post:remove','#','admin','2025-04-09 14:22:21','',NULL,''),(1024,'岗位导出',104,5,'','','','',1,0,'F','0','0','system:post:export','#','admin','2025-04-09 14:22:21','',NULL,''),(1025,'字典查询',105,1,'#','','','',1,0,'F','0','0','system:dict:query','#','admin','2025-04-09 14:22:21','',NULL,''),(1026,'字典新增',105,2,'#','','','',1,0,'F','0','0','system:dict:add','#','admin','2025-04-09 14:22:21','',NULL,''),(1027,'字典修改',105,3,'#','','','',1,0,'F','0','0','system:dict:edit','#','admin','2025-04-09 14:22:21','',NULL,''),(1028,'字典删除',105,4,'#','','','',1,0,'F','0','0','system:dict:remove','#','admin','2025-04-09 14:22:21','',NULL,''),(1029,'字典导出',105,5,'#','','','',1,0,'F','0','0','system:dict:export','#','admin','2025-04-09 14:22:21','',NULL,''),(1030,'参数查询',106,1,'#','','','',1,0,'F','0','0','system:config:query','#','admin','2025-04-09 14:22:21','',NULL,''),(1031,'参数新增',106,2,'#','','','',1,0,'F','0','0','system:config:add','#','admin','2025-04-09 14:22:21','',NULL,''),(1032,'参数修改',106,3,'#','','','',1,0,'F','0','0','system:config:edit','#','admin','2025-04-09 14:22:21','',NULL,''),(1033,'参数删除',106,4,'#','','','',1,0,'F','0','0','system:config:remove','#','admin','2025-04-09 14:22:22','',NULL,''),(1034,'参数导出',106,5,'#','','','',1,0,'F','0','0','system:config:export','#','admin','2025-04-09 14:22:22','',NULL,''),(1035,'公告查询',107,1,'#','','','',1,0,'F','0','0','system:notice:query','#','admin','2025-04-09 14:22:22','',NULL,''),(1036,'公告新增',107,2,'#','','','',1,0,'F','0','0','system:notice:add','#','admin','2025-04-09 14:22:22','',NULL,''),(1037,'公告修改',107,3,'#','','','',1,0,'F','0','0','system:notice:edit','#','admin','2025-04-09 14:22:22','',NULL,''),(1038,'公告删除',107,4,'#','','','',1,0,'F','0','0','system:notice:remove','#','admin','2025-04-09 14:22:22','',NULL,''),(1039,'操作查询',500,1,'#','','','',1,0,'F','0','0','monitor:operlog:query','#','admin','2025-04-09 14:22:22','',NULL,''),(1040,'操作删除',500,2,'#','','','',1,0,'F','0','0','monitor:operlog:remove','#','admin','2025-04-09 14:22:22','',NULL,''),(1041,'日志导出',500,3,'#','','','',1,0,'F','0','0','monitor:operlog:export','#','admin','2025-04-09 14:22:22','',NULL,''),(1042,'登录查询',501,1,'#','','','',1,0,'F','0','0','monitor:logininfor:query','#','admin','2025-04-09 14:22:22','',NULL,''),(1043,'登录删除',501,2,'#','','','',1,0,'F','0','0','monitor:logininfor:remove','#','admin','2025-04-09 14:22:22','',NULL,''),(1044,'日志导出',501,3,'#','','','',1,0,'F','0','0','monitor:logininfor:export','#','admin','2025-04-09 14:22:22','',NULL,''),(1045,'账户解锁',501,4,'#','','','',1,0,'F','0','0','monitor:logininfor:unlock','#','admin','2025-04-09 14:22:22','',NULL,''),(1046,'在线查询',109,1,'#','','','',1,0,'F','0','0','monitor:online:query','#','admin','2025-04-09 14:22:23','',NULL,''),(1047,'批量强退',109,2,'#','','','',1,0,'F','0','0','monitor:online:batchLogout','#','admin','2025-04-09 14:22:23','',NULL,''),(1048,'单条强退',109,3,'#','','','',1,0,'F','0','0','monitor:online:forceLogout','#','admin','2025-04-09 14:22:23','',NULL,''),(1049,'任务查询',110,1,'#','','','',1,0,'F','0','0','monitor:job:query','#','admin','2025-04-09 14:22:23','',NULL,''),(1050,'任务新增',110,2,'#','','','',1,0,'F','0','0','monitor:job:add','#','admin','2025-04-09 14:22:23','',NULL,''),(1051,'任务修改',110,3,'#','','','',1,0,'F','0','0','monitor:job:edit','#','admin','2025-04-09 14:22:23','',NULL,''),(1052,'任务删除',110,4,'#','','','',1,0,'F','0','0','monitor:job:remove','#','admin','2025-04-09 14:22:23','',NULL,''),(1053,'状态修改',110,5,'#','','','',1,0,'F','0','0','monitor:job:changeStatus','#','admin','2025-04-09 14:22:23','',NULL,''),(1054,'任务导出',110,6,'#','','','',1,0,'F','0','0','monitor:job:export','#','admin','2025-04-09 14:22:23','',NULL,''),(1055,'生成查询',116,1,'#','','','',1,0,'F','0','0','tool:gen:query','#','admin','2025-04-09 14:22:23','',NULL,''),(1056,'生成修改',116,2,'#','','','',1,0,'F','0','0','tool:gen:edit','#','admin','2025-04-09 14:22:23','',NULL,''),(1057,'生成删除',116,3,'#','','','',1,0,'F','0','0','tool:gen:remove','#','admin','2025-04-09 14:22:23','',NULL,''),(1058,'导入代码',116,4,'#','','','',1,0,'F','0','0','tool:gen:import','#','admin','2025-04-09 14:22:23','',NULL,''),(1059,'预览代码',116,5,'#','','','',1,0,'F','0','0','tool:gen:preview','#','admin','2025-04-09 14:22:23','',NULL,''),(1060,'生成代码',116,6,'#','','','',1,0,'F','0','0','tool:gen:code','#','admin','2025-04-09 14:22:24','',NULL,''),(2000,'系统配置',0,4,'sc','pages/system',NULL,'',1,0,'C','0','0','','404','admin','2025-04-09 14:31:43','admin','2025-04-29 17:34:51',''),(2001,'用户管理',0,5,'customer',NULL,NULL,'',1,0,'M','0','0','','404','admin','2025-04-09 14:41:17','admin','2025-04-29 17:31:06',''),(2002,'会员管理',2001,1,'list','pages/user/index',NULL,'customerList',1,0,'C','0','0','system','404','admin','2025-04-09 14:43:11','admin','2025-04-29 17:36:21',''),(2003,'团队奖励',2001,2,'config/level','pages/user/userLevelConfig',NULL,'',1,0,'C','0','0','system','404','admin','2025-04-09 14:45:00','admin','2025-05-14 20:48:19',''),(2004,'积分商城',0,6,'credits',NULL,NULL,'',1,0,'M','0','0','','404','admin','2025-04-09 14:45:46','admin','2025-05-06 15:05:51',''),(2005,'理财管理',0,7,'financial',NULL,NULL,'',1,0,'M','0','0',NULL,'404','admin','2025-04-09 14:46:13','',NULL,''),(2006,'内容管理',0,8,'content',NULL,NULL,'',1,0,'M','0','0',NULL,'404','admin','2025-04-09 14:46:51','',NULL,''),(2008,'收货地址',2004,2,'address','pages/credits/address',NULL,'',1,0,'C','0','0','system','404','admin','2025-04-09 14:48:46','admin','2025-05-01 14:01:43',''),(2010,'理财产品',2005,1,'shop','pages/financial/shop',NULL,'',1,0,'C','0','0','system','404','admin','2025-04-09 14:50:27','admin','2025-04-16 17:46:37',''),(2011,'产品标签',2005,2,'tag','pages/financial/tag',NULL,'',1,0,'C','0','0','system','404','admin','2025-04-09 14:51:31','admin','2025-04-16 17:46:40',''),(2012,'投资记录',2005,3,'order','pages/financial/order',NULL,'',1,0,'C','0','0','system','404','admin','2025-04-09 14:52:06','admin','2025-05-15 14:54:03',''),(2013,'Banner管理',2006,1,'banner','pages/banner/index',NULL,'',1,0,'C','0','0','system','404','admin','2025-04-09 14:52:47','admin','2025-04-16 17:46:52',''),(2014,'资讯管理',2006,2,'news','pages/news/index',NULL,'',1,0,'C','0','0','system','404','admin','2025-04-09 14:53:20','admin','2025-04-16 17:46:55',''),(2015,'商品管理',2004,1,'cs','pages/credits/shop',NULL,'cs',1,0,'C','0','0','system','404','admin','2025-04-09 15:38:37','admin','2025-04-16 17:46:24',''),(2016,'订单管理',2004,3,'co','pages/credits/order',NULL,'co',1,0,'C','0','0','system','404','admin','2025-04-09 15:39:22','admin','2025-04-16 17:46:31',''),(2017,'分类管理',2005,4,'category','pages/financial/category',NULL,'category',1,0,'C','0','0','system','404','admin','2025-04-16 10:27:04','admin','2025-04-16 17:46:46',''),(2018,'资讯标签管理',2006,3,'tag','pages/news/newsTab',NULL,'newsTag',1,0,'C','0','0',NULL,'404','admin','2025-04-17 17:39:06','',NULL,''),(2019,'合同管理',0,9,'contract',NULL,NULL,'',1,0,'M','0','0',NULL,'404','admin','2025-04-18 16:26:36','',NULL,''),(2020,'合同模板配置',2019,1,'config','pages/contract/index',NULL,'contractConfig',1,0,'C','0','0',NULL,'404','admin','2025-04-18 16:27:35','',NULL,''),(2021,'区域管理',2005,5,'region','pages/financial/region',NULL,'region',1,0,'C','0','0','system','404','admin','2025-04-29 17:27:00','',NULL,''),(2022,'合同条款',2019,2,'trems','pages/contract/trem',NULL,'trems',1,0,'C','0','0','system','404','admin','2025-04-29 17:29:32','',NULL,''),(2023,'充值记录',2030,2,'deposit','pages/user/chongzhi',NULL,'deposit',1,0,'C','0','0','system','404','admin','2025-04-29 17:36:07','admin','2025-05-19 14:34:27',''),(2024,'钱包交易',2030,3,'wallet','pages/user/qianbaojiaoyi',NULL,'wallet',1,0,'C','0','0','system','404','admin','2025-04-29 17:37:04','admin','2025-05-01 14:01:03',''),(2025,'实名认证',2001,4,'kyc','pages/user/shiming',NULL,'kycConfig',1,0,'C','0','0','system','404','admin','2025-04-29 17:37:47','admin','2025-04-29 17:47:10',''),(2026,'提现管理',2030,5,'withdrawal','pages/user/tixian',NULL,'withdrawal',1,0,'C','0','0','system','404','admin','2025-04-29 17:38:51','admin','2025-05-01 14:00:42',''),(2027,'提现地址',2001,6,'withdrawaladdress','pages/user/tixiandizhi',NULL,'withdrawaladdress',1,0,'C','0','0','system','404','admin','2025-04-29 17:39:40','',NULL,''),(2028,'提现审核',2001,7,'drawalaudit','pages/user/tixianshenhe',NULL,'drawalaudit',1,0,'C','0','0','system','404','admin','2025-04-29 17:40:35','',NULL,''),(2029,'投资记录-2',2005,8,'investment','pages/user/touzi',NULL,'investment',1,0,'C','1','1','system','404','admin','2025-04-29 17:41:30','admin','2025-05-15 14:53:58',''),(2030,'财务管理',0,10,'finance',NULL,NULL,'',1,0,'M','0','0',NULL,'404','admin','2025-05-01 14:00:02','',NULL,''),(2031,'积分明细',2030,6,'pointsdetail','pages/testPage',NULL,'pointsdetail',1,0,'C','0','0','system','404','admin','2025-05-01 14:03:11','admin','2025-05-19 14:34:40','');
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_notice`
--

DROP TABLE IF EXISTS `sys_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_notice` (
  `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) NOT NULL COMMENT '公告标题',
  `notice_type` char(1) NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='通知公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_notice`
--

LOCK TABLES `sys_notice` WRITE;
/*!40000 ALTER TABLE `sys_notice` DISABLE KEYS */;
INSERT INTO `sys_notice` VALUES (1,'温馨提醒：2018-07-01 若依新版本发布啦','2',_binary '<p>123123</p>','0','admin','2025-04-09 14:22:38','admin','2025-04-30 17:38:24','管理员'),(2,'维护通知：2018-07-01 若依系统凌晨维护','1',_binary '维护内容','0','admin','2025-04-09 14:22:38','',NULL,'管理员');
/*!40000 ALTER TABLE `sys_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_oper_log`
--

DROP TABLE IF EXISTS `sys_oper_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) DEFAULT '' COMMENT '返回参数',
  `status` int(1) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(20) DEFAULT '0' COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`),
  KEY `idx_sys_oper_log_bt` (`business_type`),
  KEY `idx_sys_oper_log_s` (`status`),
  KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=526 DEFAULT CHARSET=utf8mb4 COMMENT='操作日志记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_oper_log`
--

LOCK TABLES `sys_oper_log` WRITE;
/*!40000 ALTER TABLE `sys_oper_log` DISABLE KEYS */;
INSERT INTO `sys_oper_log` VALUES (100,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','华时代新闻社','/system/menu/4','***********','内网IP','{}','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-04-09 14:26:06',126),(101,'角色管理',3,'com.ruoyi.web.controller.system.SysRoleController.remove()','DELETE',1,'admin','华时代新闻社','/system/role/2','***********','内网IP','{}',NULL,1,'普通角色已分配,不能删除','2025-04-09 14:26:19',29),(102,'角色管理',4,'com.ruoyi.web.controller.system.SysRoleController.cancelAuthUser()','PUT',1,'admin','华时代新闻社','/system/role/authUser/cancel','***********','内网IP','{\"roleId\":2,\"userId\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:26:29',59),(103,'角色管理',3,'com.ruoyi.web.controller.system.SysRoleController.remove()','DELETE',1,'admin','华时代新闻社','/system/role/2','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:26:38',330),(104,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','华时代新闻社','/system/menu/4','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:26:46',63),(105,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"/pages/system\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"系统配置\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"system\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:31:43',91),(106,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/system\",\"createTime\":\"2025-04-09 14:31:43\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"系统配置\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"sc\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:38:22',64),(107,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"用户管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"customer\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:41:17',97),(108,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2001,\"path\":\"list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:43:11',95),(109,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/userLevelConfig\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员等级配置\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"path\":\"config/level\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:45:00',57),(110,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"积分管理\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":0,\"path\":\"credits\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:45:46',63),(111,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"理财管理\",\"menuType\":\"M\",\"orderNum\":7,\"params\":{},\"parentId\":0,\"path\":\"financial\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:46:13',87),(112,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"内容管理\",\"menuType\":\"M\",\"orderNum\":8,\"params\":{},\"parentId\":0,\"path\":\"content\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:46:51',102),(113,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:47:44',86),(114,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/address\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"地址管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2004,\"path\":\"address\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:48:46',83),(115,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/order\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2004,\"path\":\"order\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:49:31',105),(116,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/shop\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"理财产品\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2005,\"path\":\"shop\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:50:27',95),(117,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/tag\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"产品标签\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2005,\"path\":\"tag\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:51:31',61),(118,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/order\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2005,\"path\":\"order\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:52:06',87),(119,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/banner/index\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"Banner管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2006,\"path\":\"banner\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:52:47',83),(120,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"/pages/news/index\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"资讯管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2006,\"path\":\"news\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 14:53:20',65),(121,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:41:17\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"会员管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"customer\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:15:41',120),(122,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createTime\":\"2025-04-09 14:47:44\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:17:08',128),(123,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createTime\":\"2025-04-09 14:47:44\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:22:02',79),(124,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop.vue\",\"createTime\":\"2025-04-09 14:47:44\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:23:55',135),(125,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createTime\":\"2025-04-09 14:47:44\",\"icon\":\"404\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:24:50',60),(126,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/news/index\",\"createTime\":\"2025-04-09 14:53:20\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2014,\"menuName\":\"资讯管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2006,\"path\":\"news\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:26:36',58),(127,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/index\",\"createTime\":\"2025-04-09 14:47:44\",\"icon\":\"404\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:27:46',54),(128,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/index\",\"createTime\":\"2025-04-09 14:47:44\",\"icon\":\"404\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"index\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:29:25',406),(129,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createTime\":\"2025-04-09 14:47:44\",\"icon\":\"404\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:30:24',58),(130,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','华时代新闻社','/system/menu/2007','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:37:42',79),(131,'菜单管理',3,'com.ruoyi.web.controller.system.SysMenuController.remove()','DELETE',1,'admin','华时代新闻社','/system/menu/2009','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:37:45',57),(132,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"shop\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:38:37',77),(133,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/order\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2004,\"path\":\"order\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 15:39:22',85),(134,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createTime\":\"2025-04-09 15:38:37\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2015,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"cs\",\"perms\":\"\",\"routeName\":\"cs\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 16:21:44',173),(135,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','华时代新闻社','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/order\",\"createTime\":\"2025-04-09 15:39:22\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2016,\"menuName\":\"订单管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2004,\"path\":\"co\",\"perms\":\"\",\"routeName\":\"co\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-09 16:21:52',72),(136,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"黑马\",\"params\":{},\"phonenumber\":\"18988888888\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"yy\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-12 23:24:40',136),(137,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"黑马\",\"params\":{},\"phonenumber\":\"18988888888\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"yy\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-12 23:24:42',109),(138,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"黑马\",\"params\":{},\"phonenumber\":\"18988888888\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"啊哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-12 23:24:49',106),(139,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"黑马\",\"params\":{},\"phonenumber\":\"18988888888\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"啊哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-12 23:24:54',111),(140,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"黑马\",\"params\":{},\"phonenumber\":\"18988888888\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"啊哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-12 23:25:09',106),(141,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"哈哈\",\"params\":{},\"phonenumber\":\"18988888888\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"哈哈哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 00:37:52',106),(142,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"哈哈\",\"params\":{},\"phonenumber\":\"18988888888\",\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"哈哈哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 00:38:03',106),(143,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"email\":\"<EMAIL>\",\"nickName\":\"试试\",\"params\":{},\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"哈哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 00:39:50',107),(144,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"email\":\"<EMAIL>\",\"nickName\":\"试试\",\"params\":{},\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"哈哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 00:39:52',106),(145,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"email\":\"<EMAIL>\",\"nickName\":\"试试\",\"params\":{},\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"哈哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 00:39:55',111),(146,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"email\":\"<EMAIL>\",\"nickName\":\"试试\",\"params\":{},\"postIds\":[],\"roleIds\":[],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"哈哈\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 00:40:01',108),(147,'用户管理',3,'com.ruoyi.web.controller.system.SysUserController.remove()','DELETE',1,'admin','研发部门','/system/user/2','*************','XX XX','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-13 13:53:33',424),(148,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userName\":\"superadmin\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:55:12',103),(149,'角色管理',1,'com.ruoyi.web.controller.system.SysRoleController.add()','POST',1,'admin','研发部门','/system/role','*************','XX XX','{\"admin\":false,\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2000,2001,2002,2003,2004,2015,2008,2016,2005,2010,2011,2012,2006,2013,2014],\"params\":{},\"roleKey\":\"admin\",\"roleName\":\"系统管理员\",\"roleSort\":0,\"status\":\"0\"}','{\"msg\":\"新增角色\'系统管理员\'失败，角色权限已存在\",\"code\":500}',0,NULL,'2025-04-13 13:56:06',26),(150,'角色管理',1,'com.ruoyi.web.controller.system.SysRoleController.add()','POST',1,'admin','研发部门','/system/role','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2000,2001,2002,2003,2004,2015,2008,2016,2005,2010,2011,2012,2006,2013,2014],\"params\":{},\"roleId\":100,\"roleKey\":\"system\",\"roleName\":\"系统管理员\",\"roleSort\":0,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-13 13:56:19',341),(151,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userName\":\"super\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:56:53',113),(152,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userName\":\"super\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:56:54',104),(153,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"super\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:57:06',108),(154,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"guanliyuan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:57:30',106),(155,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"guanliyuan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:57:31',104),(156,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"guanliyuan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:58:22',104),(157,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"系统管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"guanliyuan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 13:58:46',104),(158,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":101,\"email\":\"<EMAIL>\",\"nickName\":\"赵海川\",\"params\":{},\"phonenumber\":\"***********\",\"postIds\":[2],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"zhaohaichuan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 14:00:34',110),(159,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','*************','XX XX','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":101,\"email\":\"<EMAIL>\",\"nickName\":\"赵海川\",\"params\":{},\"phonenumber\":\"***********\",\"postIds\":[2],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"zhaohaichuan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-13 14:00:36',118),(160,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":12,\"categoryId\":\"12\",\"categoryName\":\"12\",\"createdAt\":\"2025-04-15 16:41:08\",\"currency\":\"12\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":12,\"description\":\"12\",\"endTime\":\"2025-04-24 16:09:18\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":0,\"investmentManager\":\"12\",\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":0,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":12,\"params\":{},\"productCode\":\"12\",\"productName\":\"12\",\"productStatus\":0,\"productType\":1,\"regionId\":\"\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":12,\"startTime\":\"2025-04-15 05:02:09\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-04-15 16:41:08\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'product_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_code,             product_name,             category_id,             product_type,             investment_area,             region_id,             currency,             min_investment,             investment_unit,             total_scale,             remaining_scale,             annual_rate,             daily_rate,             investment_period,             max_investment_units,             total_units,             sold_units,             risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             start_time,             end_time,             description,             summary,             investment_manager,             custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'product_id\' doesn\'t have a default value\n; Field \'product_id\' doesn','2025-04-15 16:41:08',573),(161,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":12,\"categoryId\":\"12\",\"categoryName\":\"12\",\"createdAt\":\"2025-04-15 16:49:55\",\"currency\":\"12\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":12,\"description\":\"12\",\"endTime\":\"2025-04-15 00:06:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":0,\"investmentManager\":\"12\",\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":0,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":12,\"params\":{},\"productCode\":\"12\",\"productId\":\"\",\"productName\":\"12\",\"productStatus\":0,\"productType\":1,\"regionId\":\"\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":12,\"startTime\":\"2025-04-13 09:06:00\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-04-15 16:49:55\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'product_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_code,             product_name,             category_id,             product_type,             investment_area,             region_id,             currency,             min_investment,             investment_unit,             total_scale,             remaining_scale,             annual_rate,             daily_rate,             investment_period,             max_investment_units,             total_units,             sold_units,             risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             start_time,             end_time,             description,             summary,             investment_manager,             custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'product_id\' doesn\'t have a default value\n; Field \'product_id\' doesn','2025-04-15 16:49:55',7),(162,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":12,\"categoryId\":\"12\",\"categoryName\":\"12\",\"createdAt\":\"2025-04-15 16:53:00\",\"currency\":\"12\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":12,\"description\":\"12\",\"endTime\":\"2025-04-30 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":0,\"investmentManager\":\"12\",\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":0,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":12,\"params\":{},\"productCode\":\"12\",\"productId\":\"4c8ee6b1-13b3-411e-9f5b-35b64a181b25\",\"productName\":\"12\",\"productStatus\":0,\"productType\":1,\"regionId\":\"\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":12,\"startTime\":\"2025-04-15 16:52:51\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-04-15 16:53:00\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-15 16:53:01',130),(163,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":12,\"categoryId\":\"31\",\"categoryName\":\"13\",\"createdAt\":\"2025-04-15 16:53:00\",\"currency\":\"12\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":12,\"description\":\"12\",\"endTime\":\"2025-04-30 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":0,\"investmentManager\":\"12\",\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":0,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":12,\"params\":{},\"productCode\":\"13\",\"productId\":\"4c8ee6b1-13b3-411e-9f5b-35b64a181b25\",\"productName\":\"13\",\"productStatus\":0,\"productType\":1,\"regionId\":\"\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":12,\"startTime\":\"2025-04-15 16:52:51\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-04-15 16:59:13\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-15 16:59:13',126),(164,'产品信息',3,'com.ruoyi.web.controller.business.ProductInfoController.remove()','DELETE',1,'admin','研发部门','/system/productInfo/4c8ee6b1-13b3-411e-9f5b-35b64a181b25','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-15 17:04:52',158),(165,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/category\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"分类管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2005,\"path\":\"category\",\"routeName\":\"category\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 10:27:04',171),(166,'产品分类',1,'com.ruoyi.web.controller.business.ProductCategoryController.add()','POST',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"123123\",\"categoryId\":\"702f87c5-8a05-4072-bb48-90c0b93caf55\",\"categoryName\":\"123123\",\"createdAt\":\"2025-04-16 11:09:42\",\"params\":{},\"sortOrder\":3,\"status\":1,\"updatedAt\":\"2025-04-16 11:09:42\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 11:09:42',414),(167,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"123123\",\"categoryId\":\"702f87c5-8a05-4072-bb48-90c0b93caf55\",\"categoryName\":\"22222\",\"createdAt\":\"2025-04-16 11:09:42\",\"params\":{},\"sortOrder\":3,\"status\":0,\"updatedAt\":\"2025-04-16 11:12:06\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 11:12:06',90),(168,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"123123\",\"categoryId\":\"702f87c5-8a05-4072-bb48-90c0b93caf55\",\"categoryName\":\"22222\",\"createdAt\":\"2025-04-16 11:09:42\",\"params\":{},\"sortOrder\":1,\"status\":0,\"updatedAt\":\"2025-04-16 11:12:43\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 11:12:43',51),(169,'产品分类',3,'com.ruoyi.web.controller.business.ProductCategoryController.remove()','DELETE',1,'admin','研发部门','/system/productCategory/undefined','***********','内网IP','{}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 11:16:10',40),(170,'产品分类',3,'com.ruoyi.web.controller.business.ProductCategoryController.remove()','DELETE',1,'admin','研发部门','/system/productCategory/undefined','***********','内网IP','{}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 11:16:48',38),(171,'产品分类',3,'com.ruoyi.web.controller.business.ProductCategoryController.remove()','DELETE',1,'admin','研发部门','/system/productCategory/702f87c5-8a05-4072-bb48-90c0b93caf55','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 11:17:16',74),(172,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"bond\",\"categoryId\":\"4f8d1d9d-25f5-4f4f-9666-9e13e3e1c16c\",\"categoryName\":\"科技\",\"createdAt\":\"2025-04-10 09:39:05\",\"params\":{},\"sortOrder\":2,\"status\":1,\"updatedAt\":\"2025-04-16 13:45:27\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 13:45:27',57),(173,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"fund\",\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"金融\",\"createdAt\":\"2025-04-10 09:39:06\",\"params\":{},\"sortOrder\":3,\"status\":1,\"updatedAt\":\"2025-04-16 13:45:34\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 13:45:34',57),(174,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"stock\",\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-04-10 09:39:06\",\"params\":{},\"sortOrder\":4,\"status\":1,\"updatedAt\":\"2025-04-16 13:45:44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 13:45:44',39),(175,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"fixed_term\",\"categoryId\":\"accec33c-9f4f-4654-8760-6da061dcb7cf\",\"categoryName\":\"房地产\",\"createdAt\":\"2025-04-10 09:39:05\",\"params\":{},\"sortOrder\":1,\"status\":1,\"updatedAt\":\"2025-04-16 13:45:54\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 13:45:54',60),(176,'产品标签',1,'com.ruoyi.web.controller.business.ProductTagController.add()','POST',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 14:14:40\",\"isDisplay\":1,\"params\":{},\"tagId\":\"628dd1df-4508-46dc-9c7d-4b4c9ef43c2e\",\"tagName\":\"tag1\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:14:40',95),(177,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 14:14:40\",\"isDisplay\":1,\"params\":{},\"tagId\":\"628dd1df-4508-46dc-9c7d-4b4c9ef43c2e\",\"tagName\":\"tag0\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:14:49',62),(178,'产品标签',3,'com.ruoyi.web.controller.business.ProductTagController.remove()','DELETE',1,'admin','研发部门','/system/productTag/undefined','***********','内网IP','{}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 14:15:04',31),(179,'产品标签',3,'com.ruoyi.web.controller.business.ProductTagController.remove()','DELETE',1,'admin','研发部门','/system/productTag/628dd1df-4508-46dc-9c7d-4b4c9ef43c2e','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:15:17',177),(180,'产品标签',1,'com.ruoyi.web.controller.business.ProductTagController.add()','POST',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 14:16:22\",\"isDisplay\":1,\"params\":{},\"tagId\":\"069a5f7c-bcc9-4410-8903-aeebda0180fa\",\"tagName\":\"标签1\",\"tagType\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:16:22',174),(181,'产品标签',1,'com.ruoyi.web.controller.business.ProductTagController.add()','POST',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 14:16:38\",\"isDisplay\":1,\"params\":{},\"tagId\":\"b00bb387-4802-4587-b59b-c893e018b83f\",\"tagName\":\"标签2\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:16:38',61),(182,'产品标签',1,'com.ruoyi.web.controller.business.ProductTagController.add()','POST',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 14:16:47\",\"isDisplay\":1,\"params\":{},\"tagId\":\"d707c3e2-1a5e-4040-bb18-1b0f9fdff0cd\",\"tagName\":\"标签3\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:16:47',40),(183,'产品标签',1,'com.ruoyi.web.controller.business.ProductTagController.add()','POST',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 14:16:54\",\"isDisplay\":0,\"params\":{},\"tagId\":\"77c4e6c7-5739-4c2b-b6e5-bb86a62dd141\",\"tagName\":\"标签4\",\"tagType\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:16:54',57),(184,'产品标签',1,'com.ruoyi.web.controller.business.ProductTagController.add()','POST',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 14:17:10\",\"isDisplay\":0,\"params\":{},\"tagId\":\"9fe1fedb-4e49-46c9-8133-64f918369314\",\"tagName\":\"标签5\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 14:17:10',57),(185,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"d2cef9d8-687d-4ece-93c1-5931ed036a9c\",\"tagId\":\"9fe1fedb-4e49-46c9-8133-64f918369314\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 15:20:32',415),(186,'产品标签',1,'com.ruoyi.web.controller.business.ProductTagController.add()','POST',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 15:27:03\",\"isDisplay\":0,\"params\":{},\"tagId\":\"39c5109a-65ac-42df-8264-525f3019544c\",\"tagName\":\"标签6\",\"tagType\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 15:27:03',129),(187,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 15:27:03\",\"isDisplay\":1,\"params\":{},\"tagId\":\"39c5109a-65ac-42df-8264-525f3019544c\",\"tagName\":\"标签6\",\"tagType\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 15:27:08',114),(188,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','***********','内网IP','{\"createdAt\":\"2025-04-16 15:27:03\",\"isDisplay\":1,\"params\":{},\"tagId\":\"39c5109a-65ac-42df-8264-525f3019544c\",\"tagName\":\"标签6\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 15:27:11',40),(189,'产品标签',3,'com.ruoyi.web.controller.business.ProductTagController.remove()','DELETE',1,'admin','研发部门','/system/productTag/39c5109a-65ac-42df-8264-525f3019544c','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 15:27:14',57),(190,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"d2cef9d8-687d-4ece-93c1-5931ed036a9c\",\"tagId\":\"d707c3e2-1a5e-4040-bb18-1b0f9fdff0cd\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 16:37:55',202),(191,'产品-标签关联',3,'com.ruoyi.web.controller.business.ProductTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/productTagRelation/d2cef9d8-687d-4ece-93c1-5931ed036a9c/9fe1fedb-4e49-46c9-8133-64f918369314','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'productId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-16 16:52:24',5),(192,'产品-标签关联',3,'com.ruoyi.web.controller.business.ProductTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/productTagRelation/d2cef9d8-687d-4ece-93c1-5931ed036a9c/9fe1fedb-4e49-46c9-8133-64f918369314','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'productId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-16 16:54:03',3),(193,'产品-标签关联',3,'com.ruoyi.web.controller.business.ProductTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/productTagRelation/d2cef9d8-687d-4ece-93c1-5931ed036a9c/9fe1fedb-4e49-46c9-8133-64f918369314','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'productId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-16 17:09:04',3),(194,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"4ae7b7cb-15d1-4894-9004-100fdbe910a0\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'tag_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductTagRelationMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductTagRelationMapper.insertProductTagRelation-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_tag_relation          ( product_id )           values ( ? )\n### Cause: java.sql.SQLException: Field \'tag_id\' doesn\'t have a default value\n; Field \'tag_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'tag_id\' doesn\'t have a default value','2025-04-16 17:17:51',139),(195,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"4ae7b7cb-15d1-4894-9004-100fdbe910a0\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'tag_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductTagRelationMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductTagRelationMapper.insertProductTagRelation-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_tag_relation          ( product_id )           values ( ? )\n### Cause: java.sql.SQLException: Field \'tag_id\' doesn\'t have a default value\n; Field \'tag_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'tag_id\' doesn\'t have a default value','2025-04-16 17:19:01',4),(196,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"4ae7b7cb-15d1-4894-9004-100fdbe910a0\",\"tagId\":\"069a5f7c-bcc9-4410-8903-aeebda0180fa\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:19:42',49),(197,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','***********','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userName\":\"user1\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 17:21:17',147),(198,'产品-标签关联',3,'com.ruoyi.web.controller.business.ProductTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/productTagRelation/d2cef9d8-687d-4ece-93c1-5931ed036a9c/9fe1fedb-4e49-46c9-8133-64f918369314','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'productId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-16 17:22:24',3),(199,'产品-标签关联',3,'com.ruoyi.web.controller.business.ProductTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/productTagRelation/d2cef9d8-687d-4ece-93c1-5931ed036a9c/9fe1fedb-4e49-46c9-8133-64f918369314','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'productId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-16 17:24:03',29),(200,'产品-标签关联',3,'com.ruoyi.web.controller.business.ProductTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/productTagRelation/d2cef9d8-687d-4ece-93c1-5931ed036a9c/9fe1fedb-4e49-46c9-8133-64f918369314','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:26:23',95),(201,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','***********','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"管理\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userName\":\"zhangsan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 17:27:08',170),(202,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','***********','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"张三\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userName\":\"zhangsan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 17:27:53',106),(203,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','***********','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"张三\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userName\":\"uzinide\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 17:28:09',111),(204,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','***********','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"email\":\"<EMAIL>\",\"nickName\":\"张三\",\"params\":{},\"phonenumber\":\"13011111111\",\"postIds\":[2],\"roleIds\":[100],\"sex\":\"0\",\"status\":\"0\",\"userName\":\"zhangsan\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 17:35:34',112),(205,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','***********','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"张三\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userName\":\"zhangsan1\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-16 17:37:44',107),(206,'角色管理',2,'com.ruoyi.web.controller.system.SysRoleController.dataScope()','PUT',1,'admin','研发部门','/system/role/dataScope','***********','内网IP','{\"admin\":false,\"createTime\":\"2025-04-13 13:56:19\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"params\":{},\"roleId\":100,\"roleKey\":\"system\",\"roleName\":\"系统管理员\",\"roleSort\":0,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:43:43',124),(207,'用户管理',1,'com.ruoyi.web.controller.system.SysUserController.add()','POST',1,'admin','研发部门','/system/user','***********','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"李四\",\"params\":{},\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userId\":100,\"userName\":\"lisi\"}','{\"msg\":\"用户添加成功\",\"code\":200}',0,NULL,'2025-04-16 17:44:19',265),(208,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createTime\":\"2025-04-09 14:43:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2002,\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2001,\"path\":\"list\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:15',119),(209,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/userLevelConfig\",\"createTime\":\"2025-04-09 14:45:00\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"会员等级配置\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"path\":\"config/level\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:18',88),(210,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/shop\",\"createTime\":\"2025-04-09 15:38:37\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2015,\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"path\":\"cs\",\"perms\":\"system\",\"routeName\":\"cs\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:24',107),(211,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/address\",\"createTime\":\"2025-04-09 14:48:46\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2008,\"menuName\":\"地址管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2004,\"path\":\"address\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:27',55),(212,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/credits/order\",\"createTime\":\"2025-04-09 15:39:22\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2016,\"menuName\":\"订单管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2004,\"path\":\"co\",\"perms\":\"system\",\"routeName\":\"co\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:31',51),(213,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/shop\",\"createTime\":\"2025-04-09 14:50:27\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2010,\"menuName\":\"理财产品\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2005,\"path\":\"shop\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:37',94),(214,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/tag\",\"createTime\":\"2025-04-09 14:51:31\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2011,\"menuName\":\"产品标签\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2005,\"path\":\"tag\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:40',83),(215,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/order\",\"createTime\":\"2025-04-09 14:52:06\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2012,\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2005,\"path\":\"order\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:43',63),(216,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/category\",\"createTime\":\"2025-04-16 10:27:04\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2017,\"menuName\":\"分类管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2005,\"path\":\"category\",\"perms\":\"system\",\"routeName\":\"category\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:46',48),(217,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/banner/index\",\"createTime\":\"2025-04-09 14:52:47\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2013,\"menuName\":\"Banner管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2006,\"path\":\"banner\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:53',51),(218,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/news/index\",\"createTime\":\"2025-04-09 14:53:20\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2014,\"menuName\":\"资讯管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2006,\"path\":\"news\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-16 17:46:55',59),(219,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','*************','XX XX','{\"allowFollowInvestment\":1,\"categoryId\":\"\",\"categoryName\":\"\",\"createdAt\":\"2025-04-16 20:38:39\",\"currency\":\"USDT\",\"custodian\":\"\",\"custodianId\":\"\",\"dailyRate\":0.6,\"description\":\"\",\"endTime\":\"2025-05-16 00:00:00\",\"feeStructure\":\"\",\"fundType\":\"\",\"investmentArea\":1,\"investmentManager\":\"\",\"investmentPeriod\":30,\"investmentStrategy\":\"\",\"investmentUnit\":10000,\"isHot\":0,\"issuerId\":\"\",\"managerId\":\"\",\"maxInvestmentUnits\":1,\"minInvestment\":10000,\"params\":{},\"productCode\":\"GR5IT\",\"productId\":\"1afb10b2-f028-48c1-887c-21925852dec8\",\"productName\":\"希腊5年国债\",\"productStatus\":1,\"productType\":2,\"regionId\":\"\",\"remainingScale\":50000000,\"repaymentType\":1,\"riskDisclosure\":\"\",\"riskLevel\":1,\"startTime\":\"2025-04-16 00:00:00\",\"summary\":\"\",\"totalScale\":50000000,\"updatedAt\":\"2025-04-16 20:38:39\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,                          product_type,             investment_area,             region_id,             currency,             min_investment,             investment_unit,             total_scale,             remaining_scale,                          daily_rate,             investment_period,             max_investment_units,                                       risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             start_time,             end_time,             description,             summary,             investment_manager,             custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot )           values ( ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n; Field \'category_id\' doesn\'t have a def','2025-04-16 20:38:39',140),(220,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','***********','内网IP','{\"enabled\":false,\"id\":\"7d67e2c8-61bc-4c25-a500-940a080f5f27\",\"params\":{},\"points\":17,\"updateBy\":\"admin\",\"userLevel\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:04:01',91),(221,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','***********','内网IP','{\"enabled\":false,\"id\":\"7d67e2c8-61bc-4c25-a500-940a080f5f27\",\"params\":{},\"points\":17,\"updateBy\":\"admin\",\"userLevel\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:04:03',67),(222,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','***********','内网IP','{\"enabled\":true,\"id\":\"7d67e2c8-61bc-4c25-a500-940a080f5f27\",\"params\":{},\"points\":23,\"updateBy\":\"admin\",\"userLevel\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:05:09',54),(223,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','***********','内网IP','{\"enabled\":true,\"id\":\"7d67e2c8-61bc-4c25-a500-940a080f5f27\",\"params\":{},\"points\":23,\"updateBy\":\"admin\",\"userLevel\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:05:18',54),(224,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','***********','内网IP','{\"enabled\":false,\"id\":\"7d67e2c8-61bc-4c25-a500-940a080f5f27\",\"params\":{},\"points\":30,\"updateBy\":\"admin\",\"userLevel\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:05:47',46),(225,'业务用户管理',3,'com.ruoyi.web.controller.business.BusinessUserController.remove()','DELETE',1,'admin','研发部门','/business/user/1c353e47-24ba-48c0-b920-96f4d1c55ab4','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:10:07',192),(226,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:45:46\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2004,\"menuName\":\"积分管理\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":0,\"path\":\"credits\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:15:33',65),(227,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:45:46\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2004,\"menuName\":\"积分管理\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":0,\"path\":\"credits\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 11:15:44',53),(228,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:41:17\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"会员管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"customer\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 14:49:06',91),(229,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createTime\":\"2025-04-09 14:43:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2002,\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"list\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"修改菜单\'会员管理\'失败，菜单名称已存在\",\"code\":500}',0,NULL,'2025-04-17 14:49:14',28),(230,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:41:17\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"会员管理-t\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"customer\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 14:49:39',1828),(231,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createTime\":\"2025-04-09 14:43:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2002,\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"/customer/list\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 14:49:59',92),(232,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createTime\":\"2025-04-09 14:43:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2002,\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"customer/list\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 14:50:26',66),(233,'Banner管理',1,'com.ruoyi.web.controller.business.BannerInfoController.add()','POST',1,'admin','研发部门','/business/banner','***********','内网IP','{\"bannerId\":\"d35ce891-b23b-4996-8d0e-c34c11c87c04\",\"createBy\":\"admin\",\"imageUrl\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/96b3133f876644f8b46370e8d700b4bb.jpg\",\"params\":{},\"position\":1,\"priority\":4,\"redirectUrl\":\"*********\",\"startTime\":\"2025-04-17 00:00:00\",\"status\":1,\"title\":\"123123\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'end_time\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/BannerInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.BannerInfoMapper.insertBannerInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into banner_info          ( banner_id,             title,             image_url,             redirect_url,             position,             priority,             status,             start_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'end_time\' doesn\'t have a default value\n; Field \'end_time\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'end_time\' doesn\'t have a default value','2025-04-17 17:22:43',400),(234,'Banner管理',2,'com.ruoyi.web.controller.business.BannerInfoController.edit()','PUT',1,'admin','研发部门','/business/banner','***********','内网IP','{\"bannerId\":\"d35ce891-b23b-4996-8d0e-c34c11c87c04\",\"endTime\":\"2025-04-25 00:00:00\",\"imageUrl\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/96b3133f876644f8b46370e8d700b4bb.jpg\",\"params\":{},\"position\":1,\"priority\":4,\"redirectUrl\":\"*********\",\"startTime\":\"2025-04-17 00:00:00\",\"status\":1,\"title\":\"123123\",\"updateBy\":\"admin\"}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-17 17:23:07',34),(235,'Banner管理',1,'com.ruoyi.web.controller.business.BannerInfoController.add()','POST',1,'admin','研发部门','/business/banner','***********','内网IP','{\"bannerId\":\"3b6e042f-8f8c-4b8e-8074-584341a7c270\",\"createBy\":\"admin\",\"endTime\":\"2025-04-30 00:00:00\",\"imageUrl\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/34e1605fab674950b7039ef65648f810.jpg\",\"params\":{},\"position\":1,\"priority\":3,\"redirectUrl\":\"123123\",\"startTime\":\"2025-04-18 00:00:00\",\"status\":1,\"title\":\"123123\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:24:09',60),(236,'Banner管理',2,'com.ruoyi.web.controller.business.BannerInfoController.edit()','PUT',1,'admin','研发部门','/business/banner','***********','内网IP','{\"bannerId\":\"3b6e042f-8f8c-4b8e-8074-584341a7c270\",\"createTime\":\"2025-04-17 17:24:09\",\"endTime\":\"2025-04-30 00:00:00\",\"imageUrl\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/34e1605fab674950b7039ef65648f810.jpg\",\"params\":{},\"position\":1,\"priority\":3,\"redirectUrl\":\"3332222\",\"startTime\":\"2025-04-18 00:00:00\",\"status\":1,\"title\":\"123123\",\"updateBy\":\"admin\",\"updateTime\":\"2025-04-17 17:24:09\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:28:16',113),(237,'Banner管理',3,'com.ruoyi.web.controller.business.BannerInfoController.remove()','DELETE',1,'admin','研发部门','/business/banner/3b6e042f-8f8c-4b8e-8074-584341a7c270','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:30:33',105),(238,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/system\",\"createTime\":\"2025-04-09 14:31:43\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"系统配置\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"sc\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:31:47',150),(239,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:22:18\",\"icon\":\"monitor\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2,\"menuName\":\"系统监控\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"monitor\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:32:06',54),(240,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:22:18\",\"icon\":\"tool\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3,\"menuName\":\"系统工具\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"tool\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:32:10',53),(241,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/news/newsTab\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"资讯标签管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2006,\"path\":\"tag\",\"routeName\":\"newsTag\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:39:06',92),(242,'新闻标签',1,'com.ruoyi.web.controller.business.NewsTagController.add()','POST',1,'admin','研发部门','/system/news/tag','***********','内网IP','{\"isDisplay\":0,\"params\":{},\"status\":0,\"tagId\":\"de09f60dd33f4c5d80fe6bd71695d3fe\",\"tagName\":\"测试\",\"tagType\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 17:59:58',469),(243,'新闻标签',2,'com.ruoyi.web.controller.business.NewsTagController.edit()','PUT',1,'admin','研发部门','/system/news/tag','***********','内网IP','{\"isDisplay\":1,\"params\":{},\"status\":1,\"tagId\":\"de09f60dd33f4c5d80fe6bd71695d3fe\",\"tagName\":\"测试\",\"tagType\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 18:00:05',134),(244,'新闻标签',3,'com.ruoyi.web.controller.business.NewsTagController.remove()','DELETE',1,'admin','研发部门','/system/news/tag/de09f60dd33f4c5d80fe6bd71695d3fe','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-17 18:00:07',217),(245,'新闻内容',1,'com.ruoyi.web.controller.business.NewsContentController.add()','POST',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"123\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/0eab4aef1e014cd1b972626429f4a201.jpg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"d544bc801a75456ca9a03898747f13cb\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 14:02:43\",\"shareCount\":0,\"source\":\"123\",\"status\":0,\"summary\":\"123\",\"title\":\"123\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:02:44',522),(246,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"123\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/0eab4aef1e014cd1b972626429f4a201.jpg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"d544bc801a75456ca9a03898747f13cb\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 14:02:43\",\"shareCount\":0,\"source\":\"123\",\"status\":0,\"summary\":\"123\",\"title\":\"123\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:06:14',40),(247,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"123\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/0eab4aef1e014cd1b972626429f4a201.jpg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"d544bc801a75456ca9a03898747f13cb\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 14:02:43\",\"shareCount\":0,\"source\":\"123\",\"status\":0,\"summary\":\"123\",\"title\":\"123\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:08:46',39),(248,'新闻内容',3,'com.ruoyi.web.controller.business.NewsContentController.remove()','DELETE',1,'admin','研发部门','/system/news/content/d544bc801a75456ca9a03898747f13cb','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:19:47',187),(249,'新闻内容',1,'com.ruoyi.web.controller.business.NewsContentController.add()','POST',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试新闻作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/7166b5429612451c9304a40417f98b5d.jpg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"ddb0152c3c1f40838877b0a0aa6e082d\",\"newsType\":1,\"params\":{},\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":0,\"summary\":\"测试新闻摘要\",\"title\":\"测试新闻标题\",\"viewCount\":0}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'publish_time\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/NewsContentMapper.xml]\n### The error may involve com.ruoyi.system.mapper.NewsContentMapper.insertNewsContent-Inline\n### The error occurred while setting parameters\n### SQL: insert into news_content          ( news_id,             title,              author,             source,             cover_image,             content,             summary,             view_count,             like_count,             comment_count,             status,                          is_top,             news_type,             is_hot,             share_count )           values ( ?,             ?,              ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'publish_time\' doesn\'t have a default value\n; Field \'publish_time\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'publish_time\' doesn\'t have a default value','2025-04-18 14:21:03',146),(250,'新闻内容',1,'com.ruoyi.web.controller.business.NewsContentController.add()','POST',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":0,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:25:06',67),(251,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":0,\"isTop\":0,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":0,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:25:34',66),(252,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":0,\"isTop\":0,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 14:42:10\",\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":1,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:42:09',70),(253,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":0,\"isTop\":0,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":0,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:42:30',69),(254,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":0,\"isTop\":0,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 14:43:01\",\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":1,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:43:00',59),(255,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":0,\"isTop\":0,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":0,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 14:43:21',55),(256,'新闻标签',2,'com.ruoyi.web.controller.business.NewsTagController.edit()','PUT',1,'admin','研发部门','/system/news/tag','***********','内网IP','{\"isDisplay\":1,\"params\":{},\"status\":0,\"tagId\":\"34efa23e-183b-4271-b772-66e70dac9888\",\"tagName\":\"银行业\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:04:09',62),(257,'新闻标签',2,'com.ruoyi.web.controller.business.NewsTagController.edit()','PUT',1,'admin','研发部门','/system/news/tag','***********','内网IP','{\"isDisplay\":1,\"params\":{},\"status\":0,\"tagId\":\"296e4da4-b247-4b16-9f8c-051e49cb2e87\",\"tagName\":\"美联储加息\",\"tagType\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:04:17',77),(258,'新闻标签',2,'com.ruoyi.web.controller.business.NewsTagController.edit()','PUT',1,'admin','研发部门','/system/news/tag','***********','内网IP','{\"isDisplay\":1,\"params\":{},\"status\":0,\"tagId\":\"262a04ce-c47b-48ad-8f88-e97b3a043ec8\",\"tagName\":\"通货膨胀\",\"tagType\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:04:21',53),(259,'新闻标签',2,'com.ruoyi.web.controller.business.NewsTagController.edit()','PUT',1,'admin','研发部门','/system/news/tag','***********','内网IP','{\"isDisplay\":1,\"params\":{},\"status\":0,\"tagId\":\"1fd00ddb-e4eb-48d8-a69d-aa15b3f4a079\",\"tagName\":\"金融科技\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:04:24',112),(260,'新闻标签',2,'com.ruoyi.web.controller.business.NewsTagController.edit()','PUT',1,'admin','研发部门','/system/news/tag','***********','内网IP','{\"isDisplay\":1,\"params\":{},\"status\":0,\"tagId\":\"1cd2a18e-4093-449f-b148-e997c87582b2\",\"tagName\":\"全球市场\",\"tagType\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:04:27',58),(261,'新闻标签关联',1,'com.ruoyi.web.controller.business.NewsTagRelationController.add()','POST',1,'admin','研发部门','/system/news/tagrelation','***********','内网IP','{\"newsId\":\"4e850156-98df-451a-82f1-483f11a8c2bd\",\"params\":{},\"tagId\":\"93e1f8f76ef54845af8daf36e41f514b\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:27:11',370),(262,'新闻标签关联',1,'com.ruoyi.web.controller.business.NewsTagRelationController.add()','POST',1,'admin','研发部门','/system/news/tagrelation','***********','内网IP','{\"newsId\":\"4e850156-98df-451a-82f1-483f11a8c2bd\",\"params\":{},\"tagId\":\"93e1f8f76ef54845af8daf36e41f514b\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'4e850156-98df-451a-82f1-483f11a8c2bd-93e1f8f76ef54845af8daf36e41\' for key \'PRIMARY\'\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/NewsTagRelationMapper.xml]\n### The error may involve com.ruoyi.system.mapper.NewsTagRelationMapper.insertNewsTagRelation-Inline\n### The error occurred while setting parameters\n### SQL: insert into news_tag_relation          ( news_id,             tag_id )           values ( ?,             ? )\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'4e850156-98df-451a-82f1-483f11a8c2bd-93e1f8f76ef54845af8daf36e41\' for key \'PRIMARY\'\n; Duplicate entry \'4e850156-98df-451a-82f1-483f11a8c2bd-93e1f8f76ef54845af8daf36e41\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'4e850156-98df-451a-82f1-483f11a8c2bd-93e1f8f76ef54845af8daf36e41\' for key \'PRIMARY\'','2025-04-18 15:28:34',78),(263,'新闻标签关联',1,'com.ruoyi.web.controller.business.NewsTagRelationController.add()','POST',1,'admin','研发部门','/system/news/tagrelation','***********','内网IP','{\"newsId\":\"57322a6e-96eb-4274-812f-b2298d3d57d6\",\"params\":{},\"tagId\":\"93e1f8f76ef54845af8daf36e41f514b\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:42:40',50),(264,'新闻标签关联',1,'com.ruoyi.web.controller.business.NewsTagRelationController.add()','POST',1,'admin','研发部门','/system/news/tagrelation','***********','内网IP','{\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"params\":{},\"tagId\":\"4e850156-98df-451a-82f1-483f11a8c2bd\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 15:58:09',84),(265,'新闻标签关联',3,'com.ruoyi.web.controller.business.NewsTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/news/tagrelation/93e1f8f76ef54845af8daf36e41f514b/undefined','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'newsId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-18 16:02:35',4),(266,'新闻标签关联',3,'com.ruoyi.web.controller.business.NewsTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/news/tagrelation/93e1f8f76ef54845af8daf36e41f514b/4e850156-98df-451a-82f1-483f11a8c2bd','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'newsId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-18 16:04:14',3),(267,'新闻标签关联',3,'com.ruoyi.web.controller.business.NewsTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/news/tagrelation/93e1f8f76ef54845af8daf36e41f514b/4e850156-98df-451a-82f1-483f11a8c2bd','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'newsId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-18 16:04:47',3),(268,'新闻标签关联',3,'com.ruoyi.web.controller.business.NewsTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/news/tagrelation/93e1f8f76ef54845af8daf36e41f514b/4e850156-98df-451a-82f1-483f11a8c2bd','***********','内网IP','{}',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'newsId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-18 16:10:47',5),(269,'新闻标签关联',3,'com.ruoyi.web.controller.business.NewsTagRelationController.remove()','DELETE',1,'admin','研发部门','/system/news/tagrelation/93e1f8f76ef54845af8daf36e41f514b/4e850156-98df-451a-82f1-483f11a8c2bd','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 16:14:07',186),(270,'新闻标签关联',1,'com.ruoyi.web.controller.business.NewsTagRelationController.add()','POST',1,'admin','研发部门','/system/news/tagrelation','***********','内网IP','{\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"params\":{},\"tagId\":\"4e850156-98df-451a-82f1-483f11a8c2bd\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 16:14:22',55),(271,'新闻标签关联',1,'com.ruoyi.web.controller.business.NewsTagRelationController.add()','POST',1,'admin','研发部门','/system/news/tagrelation','***********','内网IP','{\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"params\":{},\"tagId\":\"d7cd2960-1811-42d8-bf63-1f1562004cd5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 16:14:23',76),(272,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"合同管理\",\"menuType\":\"M\",\"orderNum\":9,\"params\":{},\"parentId\":0,\"path\":\"contract\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 16:26:37',76),(273,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/contract/index\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"合同模板配置\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2019,\"path\":\"config\",\"routeName\":\"contractConfig\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 16:27:35',71),(274,'合同模板',1,'com.ruoyi.web.controller.business.ContractTemplateController.add()','POST',1,'admin','研发部门','/system/contract/template','***********','内网IP','{\"contentTemplate\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;p&gt;123&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"effectiveTime\":\"2025-04-18 16:51:28\",\"expireTime\":\"2025-04-25 00:00:00\",\"params\":{},\"status\":2,\"templateCode\":\"123\",\"templateId\":\"75efa0c0-f7e6-45e4-a809-6db2be9382f1\",\"templateName\":\"123\",\"version\":\"123\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'product_type\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ContractTemplateMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ContractTemplateMapper.insertContractTemplate-Inline\n### The error occurred while setting parameters\n### SQL: insert into contract_template          ( template_id,             template_name,             template_code,                          content_template,                                                    version,             status,             effective_time,             expire_time )           values ( ?,             ?,             ?,                          ?,                                                    ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'product_type\' doesn\'t have a default value\n; Field \'product_type\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'product_type\' doesn\'t have a default value','2025-04-18 16:51:31',162),(275,'合同模板',1,'com.ruoyi.web.controller.business.ContractTemplateController.add()','POST',1,'admin','研发部门','/system/contract/template','***********','内网IP','{\"contentTemplate\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;p&gt;123&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"effectiveTime\":\"2025-04-18 16:53:02\",\"expireTime\":\"2025-04-25 00:00:00\",\"params\":{},\"productType\":1,\"status\":2,\"templateCode\":\"123\",\"templateId\":\"05ed641b-ab27-4774-a974-e12a85fe467d\",\"templateName\":\"123\",\"version\":\"123\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 16:53:06',186),(276,'合同模板',2,'com.ruoyi.web.controller.business.ContractTemplateController.edit()','PUT',1,'admin','研发部门','/system/contract/template','***********','内网IP','{\"contentTemplate\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;p&gt;123&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"effectiveTime\":\"2025-04-18 16:53:02\",\"expireTime\":\"2025-04-25 00:00:00\",\"params\":{},\"productType\":1,\"status\":2,\"templateCode\":\"*********\",\"templateId\":\"05ed641b-ab27-4774-a974-e12a85fe467d\",\"templateName\":\"123\",\"version\":\"123\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:08:24',96),(277,'合同模板',3,'com.ruoyi.web.controller.business.ContractTemplateController.remove()','DELETE',1,'admin','研发部门','/system/contract/template/05ed641b-ab27-4774-a974-e12a85fe467d','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:08:59',55),(278,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/order\",\"createTime\":\"2025-04-09 14:52:06\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2012,\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2005,\"path\":\"order\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:17:15',95),(279,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:22:18\",\"icon\":\"system\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1,\"menuName\":\"系统管理\",\"menuType\":\"M\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:17:38',92),(280,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.091,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"createdAt\":\"2025-04-17 10:54:14\",\"currency\":\"USDT\",\"custodian\":\"花旗银行\",\"custodianId\":\"72d9ffc1-10e3-44a2-af9b-2e0c23f46504\",\"dailyRate\":0.000249,\"description\":\"日本蓝筹股指数基金(JP)是由摩根大通资产管理发行的一款日本市场投资产品，投资期限365天，预期年化收益率9.10%。\",\"endTime\":\"2025-06-19 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentManager\":\"摩根大通资产管理\",\"investmentPeriod\":365,\"investmentStrategy\":\"本产品主要投资于日本市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":288.0173,\"isHot\":0,\"issuerId\":\"614b470e-bfd1-4b3f-8025-771dc37d1ec8\",\"managerId\":\"b55f8e39-6619-4327-8464-e8a11a983637\",\"maxInvestmentUnits\":0,\"minInvestment\":8917.6563,\"params\":{},\"productCode\":\"GPJP020\",\"productId\":\"a2e58dcd-8bda-4763-8539-dd14197aabff\",\"productName\":\"日本蓝筹股指数基金(JP)\",\"productStatus\":1,\"productType\":1,\"regionId\":\"953e3b07-ce60-4acd-adca-96a682f84746\",\"remainingScale\":3907071.4191,\"repaymentType\":2,\"riskDisclosure\":\"投资有风险，本产品风险等级为3级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":3,\"soldUnits\":3862,\"startTime\":\"2025-03-21 10:53:18\",\"summary\":\"投资日本市场，期限365天，预期年化收益9.10%。\",\"totalScale\":6365905.6842,\"totalUnits\":10000,\"updatedAt\":\"2025-04-18 17:22:18\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:22:18',128),(281,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.091,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-04-17 10:54:14\",\"currency\":\"USDT\",\"custodian\":\"花旗银行\",\"custodianId\":\"72d9ffc1-10e3-44a2-af9b-2e0c23f46504\",\"dailyRate\":0.000249,\"description\":\"日本蓝筹股指数基金(JP)是由摩根大通资产管理发行的一款日本市场投资产品，投资期限365天，预期年化收益率9.10%。\",\"endTime\":\"2025-06-19 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentManager\":\"摩根大通资产管理\",\"investmentPeriod\":365,\"investmentStrategy\":\"本产品主要投资于日本市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":288.0173,\"isHot\":0,\"issuerId\":\"614b470e-bfd1-4b3f-8025-771dc37d1ec8\",\"managerId\":\"b55f8e39-6619-4327-8464-e8a11a983637\",\"maxInvestmentUnits\":0,\"minInvestment\":8917.6563,\"params\":{},\"productCode\":\"GPJP020\",\"productId\":\"a2e58dcd-8bda-4763-8539-dd14197aabff\",\"productName\":\"日本蓝筹股指数基金(JP)\",\"productStatus\":1,\"productType\":1,\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"remainingScale\":3907071.4191,\"repaymentType\":2,\"riskDisclosure\":\"投资有风险，本产品风险等级为3级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":3,\"soldUnits\":3862,\"startTime\":\"2025-03-21 10:53:18\",\"summary\":\"投资日本市场，期限365天，预期年化收益9.10%。\",\"totalScale\":6365905.6842,\"totalUnits\":10000,\"updatedAt\":\"2025-04-18 17:26:11\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:26:12',97),(282,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.1306,\"categoryId\":\"00535fba-a5f4-485d-8ff4-233013863b9e\",\"createdAt\":\"2025-04-17 10:54:14\",\"currency\":\"USDT\",\"custodian\":\"渣打银行\",\"custodianId\":\"a6914458-d766-42c5-b3a2-1016e0a6137a\",\"dailyRate\":0.000358,\"description\":\"新加坡蓝筹股指数基金(SG)是由瑞银资产管理发行的一款新加坡市场投资产品，投资期限365天，预期年化收益率13.06%。\",\"endTime\":\"2025-07-04 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentManager\":\"瑞银资产管理\",\"investmentPeriod\":365,\"investmentStrategy\":\"本产品主要投资于新加坡市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":277.707,\"isHot\":0,\"issuerId\":\"6bfc54b5-6ca5-435b-8257-6a4fd1c79720\",\"managerId\":\"3e123acb-f0f1-4a0c-b630-97f4ba4e7fda\",\"maxInvestmentUnits\":0,\"minInvestment\":5094.1588,\"params\":{},\"productCode\":\"GPSG018\",\"productId\":\"b0731442-c495-4ec2-a667-617fd1ffe8e9\",\"productName\":\"新加坡蓝筹股指数基金(SG)\",\"productStatus\":3,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"remainingScale\":187375.2338,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为3级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":3,\"soldUnits\":8568,\"startTime\":\"2025-04-07 10:53:18\",\"summary\":\"投资新加坡市场，期限365天，预期年化收益13.06%。\",\"totalScale\":1308809.9691,\"totalUnits\":10000,\"updatedAt\":\"2025-04-18 17:26:57\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:26:57',54),(283,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":0,\"isTop\":0,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 17:33:39\",\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":1,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:33:39',103),(284,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 17:33:39\",\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":1,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:33:49',46),(285,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"测试作者\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;h1&gt;标题1&lt;/h1&gt;&lt;h2&gt;标题2&lt;/h2&gt;&lt;h3&gt;标题3&lt;/h3&gt;&lt;p&gt;&lt;strong&gt;正文加粗&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;em&gt;正文倾斜&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;u&gt;正文下划线&lt;/u&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;正文加粗倾斜下划线&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;&lt;em&gt;&lt;u&gt;&lt;span class=&quot;ql-cursor&quot;&gt;﻿&lt;/span&gt;&lt;/u&gt;&lt;/em&gt;&lt;/strong&gt;&lt;img src=&quot;https://santalk.oss-cn-beijing.aliyuncs.com/image/ee878bf0746d434cb6083dd1d2759bd0.jpg&quot;&gt;&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/70b31dbc3023442dbfa03e8b2274014b.jpg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"93e1f8f76ef54845af8daf36e41f514b\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 17:33:39\",\"shareCount\":0,\"source\":\"测试新闻来源\",\"status\":1,\"summary\":\"测试摘要\",\"title\":\"测试标题\",\"viewCount\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:41:18',66),(286,'新闻内容',1,'com.ruoyi.web.controller.business.NewsContentController.add()','POST',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"张大海\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;p&gt;关于当前经济形势以及明年经济工作，此次会议释放三个重要信号。信号一，强力稳增长。会议明确，做好明年经济工作要“实施更加积极有为的宏观政策”。根据会议，明年要实施更加积极的财政政策和适度宽松的货币政策，充实完善政策工具箱，加强超常规逆周期调节。三里河注意到，在官方表述中，中国的财政货币政策组合此前多年都是“积极的财政政策和稳健的货币政策”。&lt;/p&gt;&lt;p&gt;&lt;img src=&quot;https://santalk.oss-cn-beijing.aliyuncs.com/image/063de0ef7669459f8bddac91080f3565.jpeg&quot;&gt;&lt;/p&gt;&lt;p&gt;如今，财政政策从“积极”变为“更加积极”，货币政策从“稳健”转向“适度宽松”，意味着在全球经济复苏趋缓，外部形势严峻复杂，不稳定、不确定因素增多之际，明年中国将因应形势变化，以更强有力的逆周期调节，为经济平稳增长提供支撑。此外，会议还提出要大力提振消费、提高投资效益，全方位扩大国内需求。今年以来，随着大规模设备更新和消费品以旧换新政策效应持续显现，市场消费已明显回暖。从“大力”“全方位”等表述中可以预期，明年中国在扩大内需上料将推出更多切实有力的增量政策。信号二，奋力促改革。会议明确，要发挥经济体制改革牵引作用，推动标志性改革举措落地见效。此前，《中共中央关于进一步全面深化改革、推进中国式现代化的决定》已就经济体制改革作出一系列工作部署，如深入破除市场准入壁垒，推进基础设施竞争性领域向经营主体公平开放；清理&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/ba1bff45facd4814857ef76a207ee07e.jpeg\",\"isHot\":0,\"isTop\":0,\"likeCount\":0,\"newsId\":\"4976525127af4b46b84ce331c66d67cd\",\"newsType\":1,\"params\":{},\"shareCount\":0,\"source\":\"文旅日报\",\"status\":0,\"summary\":\"是的是的\",\"title\":\"文旅国新办举行《上海东方枢纽国际商务合作区建设总体方案》新闻发布会\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:52:24',46),(287,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"张大海\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;p&gt;关于当前经济形势以及明年经济工作，此次会议释放三个重要信号。信号一，强力稳增长。会议明确，做好明年经济工作要“实施更加积极有为的宏观政策”。根据会议，明年要实施更加积极的财政政策和适度宽松的货币政策，充实完善政策工具箱，加强超常规逆周期调节。三里河注意到，在官方表述中，中国的财政货币政策组合此前多年都是“积极的财政政策和稳健的货币政策”。&lt;/p&gt;&lt;p&gt;&lt;img src=&quot;https://santalk.oss-cn-beijing.aliyuncs.com/image/063de0ef7669459f8bddac91080f3565.jpeg&quot;&gt;&lt;/p&gt;&lt;p&gt;如今，财政政策从“积极”变为“更加积极”，货币政策从“稳健”转向“适度宽松”，意味着在全球经济复苏趋缓，外部形势严峻复杂，不稳定、不确定因素增多之际，明年中国将因应形势变化，以更强有力的逆周期调节，为经济平稳增长提供支撑。此外，会议还提出要大力提振消费、提高投资效益，全方位扩大国内需求。今年以来，随着大规模设备更新和消费品以旧换新政策效应持续显现，市场消费已明显回暖。从“大力”“全方位”等表述中可以预期，明年中国在扩大内需上料将推出更多切实有力的增量政策。信号二，奋力促改革。会议明确，要发挥经济体制改革牵引作用，推动标志性改革举措落地见效。此前，《中共中央关于进一步全面深化改革、推进中国式现代化的决定》已就经济体制改革作出一系列工作部署，如深入破除市场准入壁垒，推进基础设施竞争性领域向经营主体公平开放；清理&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/ba1bff45facd4814857ef76a207ee07e.jpeg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"4976525127af4b46b84ce331c66d67cd\",\"newsType\":1,\"params\":{},\"shareCount\":0,\"source\":\"文旅日报\",\"status\":0,\"summary\":\"是的是的\",\"title\":\"文旅国新办举行《上海东方枢纽国际商务合作区建设总体方案》新闻发布会\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:53:09',75),(288,'新闻内容',2,'com.ruoyi.web.controller.business.NewsContentController.edit()','PUT',1,'admin','研发部门','/system/news/content','***********','内网IP','{\"author\":\"张大海\",\"commentCount\":0,\"content\":\"&lt;div class=&#x27;ql-snow&#x27;&gt;&lt;div class=&#x27;ql-editor&#x27;&gt;&lt;p&gt;关于当前经济形势以及明年经济工作，此次会议释放三个重要信号。信号一，强力稳增长。会议明确，做好明年经济工作要“实施更加积极有为的宏观政策”。根据会议，明年要实施更加积极的财政政策和适度宽松的货币政策，充实完善政策工具箱，加强超常规逆周期调节。三里河注意到，在官方表述中，中国的财政货币政策组合此前多年都是“积极的财政政策和稳健的货币政策”。&lt;/p&gt;&lt;p&gt;&lt;img src=&quot;https://santalk.oss-cn-beijing.aliyuncs.com/image/063de0ef7669459f8bddac91080f3565.jpeg&quot;&gt;&lt;/p&gt;&lt;p&gt;如今，财政政策从“积极”变为“更加积极”，货币政策从“稳健”转向“适度宽松”，意味着在全球经济复苏趋缓，外部形势严峻复杂，不稳定、不确定因素增多之际，明年中国将因应形势变化，以更强有力的逆周期调节，为经济平稳增长提供支撑。此外，会议还提出要大力提振消费、提高投资效益，全方位扩大国内需求。今年以来，随着大规模设备更新和消费品以旧换新政策效应持续显现，市场消费已明显回暖。从“大力”“全方位”等表述中可以预期，明年中国在扩大内需上料将推出更多切实有力的增量政策。信号二，奋力促改革。会议明确，要发挥经济体制改革牵引作用，推动标志性改革举措落地见效。此前，《中共中央关于进一步全面深化改革、推进中国式现代化的决定》已就经济体制改革作出一系列工作部署，如深入破除市场准入壁垒，推进基础设施竞争性领域向经营主体公平开放；清理&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;\",\"coverImage\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/ba1bff45facd4814857ef76a207ee07e.jpeg\",\"isHot\":1,\"isTop\":1,\"likeCount\":0,\"newsId\":\"4976525127af4b46b84ce331c66d67cd\",\"newsType\":1,\"params\":{},\"publishTime\":\"2025-04-18 17:53:15\",\"shareCount\":0,\"source\":\"文旅日报\",\"status\":1,\"summary\":\"是的是的\",\"title\":\"文旅国新办举行《上海东方枢纽国际商务合作区建设总体方案》新闻发布会\",\"viewCount\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-18 17:53:15',53),(289,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"categoryId\":\"\",\"categoryName\":\"\",\"createdAt\":\"2025-04-20 15:42:45\",\"currency\":\"USDT\",\"custodian\":\"\",\"custodianId\":\"\",\"dailyRate\":0.03,\"description\":\"\",\"feeStructure\":\"\",\"fundType\":\"\",\"investmentArea\":1,\"investmentManager\":\"\",\"investmentPeriod\":50,\"investmentStrategy\":\"\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"\",\"managerId\":\"\",\"maxInvestmentUnits\":1,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"ad813641-3347-455c-8fb0-bfc1ec978575\",\"productName\":\"WTI纽约原油\",\"productStatus\":0,\"productType\":1,\"regionId\":\"\",\"repaymentType\":1,\"riskDisclosure\":\"\",\"riskLevel\":1,\"summary\":\"\",\"totalScale\":500000,\"updatedAt\":\"2025-04-20 15:42:45\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,                          product_type,             investment_area,             region_id,             currency,             min_investment,             investment_unit,             total_scale,                                       daily_rate,             investment_period,             max_investment_units,                                       risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,                                       description,             summary,             investment_manager,             custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot )           values ( ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n; Field \'category_id\' doesn\'t have a default value; nested exception is java.sql.S','2025-04-20 15:42:46',112),(290,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','*************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"\",\"createdAt\":\"2025-04-20 16:02:35\",\"currency\":\"美元\",\"custodian\":\"123\",\"custodianId\":\"123\",\"dailyRate\":2.3,\"description\":\"123\",\"endTime\":\"2025-04-22 00:00:00\",\"feeStructure\":\"123\",\"fundType\":\"123\",\"investmentArea\":0,\"investmentManager\":\"123\",\"investmentPeriod\":1,\"investmentStrategy\":\"123\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"123\",\"managerId\":\"123\",\"maxInvestmentUnits\":12,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CQ110\",\"productId\":\"fb7356cd-2d37-4ca5-a0bb-b57c19e2dddf\",\"productName\":\"测试理财产品\",\"productStatus\":0,\"productType\":1,\"regionId\":\"1c13ca66-2a58-4000-9fc0-fe42db3e8373\",\"remainingScale\":1000000,\"repaymentType\":1,\"riskDisclosure\":\"123\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"123\",\"totalScale\":10000000,\"totalUnits\":431231,\"updatedAt\":\"2025-04-20 16:02:35\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 16:02:35',66),(291,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"\",\"createdAt\":\"2025-04-20 16:13:14\",\"currency\":\"USDT\",\"custodian\":\"\",\"custodianId\":\"\",\"dailyRate\":0.03,\"description\":\"\",\"feeStructure\":\"\",\"fundType\":\"\",\"investmentArea\":1,\"investmentManager\":\"\",\"investmentPeriod\":30,\"investmentStrategy\":\"\",\"investmentUnit\":100,\"isHot\":0,\"issuerId\":\"\",\"managerId\":\"\",\"maxInvestmentUnits\":1,\"minInvestment\":4000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"a3ecaaf3-6714-4c80-9865-76037d2acca5\",\"productName\":\"WTI纽约原油\",\"productStatus\":0,\"productType\":1,\"regionId\":\"b6d45e54-6b25-453f-a983-c0688c4e51c4\",\"repaymentType\":1,\"riskDisclosure\":\"\",\"riskLevel\":1,\"summary\":\"\",\"totalScale\":500000,\"totalUnits\":20,\"updatedAt\":\"2025-04-20 16:13:14\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'remaining_scale\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,             category_id,             product_type,             investment_area,             region_id,             currency,             min_investment,             investment_unit,             total_scale,                          annual_rate,             daily_rate,             investment_period,             max_investment_units,             total_units,                          risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,                                       description,             summary,             investment_manager,             custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'remaining_scale\' doesn\'t have a default value\n; Field \'remaining_scale\' doesn\'','2025-04-20 16:13:14',5),(292,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"\",\"createdAt\":\"2025-04-20 16:15:19\",\"currency\":\"USDT\",\"custodian\":\"\",\"custodianId\":\"\",\"dailyRate\":0.03,\"description\":\"\",\"feeStructure\":\"\",\"fundType\":\"\",\"investmentArea\":0,\"investmentManager\":\"\",\"investmentPeriod\":60,\"investmentStrategy\":\"\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"\",\"managerId\":\"\",\"maxInvestmentUnits\":50,\"minInvestment\":50000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"ccf70523-6fb5-4975-a045-843e4fbcc6b0\",\"productName\":\"WTI纽约原油\",\"productStatus\":0,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"repaymentType\":1,\"riskDisclosure\":\"\",\"riskLevel\":1,\"summary\":\"\",\"totalScale\":5000000,\"totalUnits\":5000,\"updatedAt\":\"2025-04-20 16:15:19\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'remaining_scale\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,             category_id,             product_type,             investment_area,             region_id,             currency,             min_investment,             investment_unit,             total_scale,                                       daily_rate,             investment_period,             max_investment_units,             total_units,                          risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,                                       description,             summary,             investment_manager,             custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'remaining_scale\' doesn\'t have a default value\n; Field \'remaining_scale\' doesn\'t have a defau','2025-04-20 16:15:19',5),(293,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"\",\"createdAt\":\"2025-04-20 16:29:11\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.03,\"description\":\"1\",\"endTime\":\"2025-04-25 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":0,\"investmentManager\":\"1\",\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1000,\"isHot\":1,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":1,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"13ebfbac-a972-4a7f-87d6-67942f886e94\",\"productName\":\"WTI纽约原油\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"remainingScale\":450000,\"repaymentType\":1,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"soldUnits\":1,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"1\",\"totalScale\":500000,\"totalUnits\":50,\"updatedAt\":\"2025-04-20 16:29:11\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 16:29:11',59),(294,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"金融\",\"createdAt\":\"2025-04-20 16:29:11\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.03,\"description\":\"1\",\"endTime\":\"2025-04-25 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":1,\"investmentManager\":\"1\",\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1000,\"isHot\":1,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":1,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"13ebfbac-a972-4a7f-87d6-67942f886e94\",\"productName\":\"WTI纽约原油\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"remainingScale\":450000,\"repaymentType\":1,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"soldUnits\":1,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"1\",\"totalScale\":500000,\"totalUnits\":50,\"updatedAt\":\"2025-04-20 16:35:13\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 16:35:13',67),(295,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-04-20 16:02:35\",\"currency\":\"美元\",\"custodian\":\"123\",\"custodianId\":\"123\",\"dailyRate\":2.3,\"description\":\"123\",\"endTime\":\"2025-04-22 00:00:00\",\"feeStructure\":\"123\",\"fundType\":\"123\",\"investmentArea\":0,\"investmentManager\":\"123\",\"investmentPeriod\":1,\"investmentStrategy\":\"123\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"123\",\"managerId\":\"123\",\"maxInvestmentUnits\":12,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CQ110\",\"productId\":\"fb7356cd-2d37-4ca5-a0bb-b57c19e2dddf\",\"productName\":\"测试理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"1c13ca66-2a58-4000-9fc0-fe42db3e8373\",\"remainingScale\":1000000,\"repaymentType\":1,\"riskDisclosure\":\"123\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"123\",\"totalScale\":10000000,\"totalUnits\":431231,\"updatedAt\":\"2025-04-20 17:05:35\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 17:05:36',49),(296,'业务用户管理',3,'com.ruoyi.web.controller.business.BusinessUserController.remove()','DELETE',1,'admin','研发部门','/business/user/ecf7af29-9564-4f24-8f4f-0be40c4e3d03','**************','XX XX','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 17:17:11',62),(297,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','*************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"金融\",\"createdAt\":\"2025-04-20 16:29:11\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.03,\"description\":\"1\",\"endTime\":\"2025-04-25 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":1,\"investmentManager\":\"1\",\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1000,\"isHot\":1,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":1,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"13ebfbac-a972-4a7f-87d6-67942f886e94\",\"productName\":\"WTI纽约原油\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"regionName\":\"美国\",\"remainingScale\":450000,\"repaymentType\":1,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"soldUnits\":1,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"1\",\"totalScale\":500000,\"totalUnits\":50,\"updatedAt\":\"2025-04-20 18:09:25\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 18:09:25',25),(298,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','*************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-04-20 16:02:35\",\"currency\":\"美元\",\"custodian\":\"123\",\"custodianId\":\"123\",\"dailyRate\":2.3,\"description\":\"123\",\"endTime\":\"2025-04-22 00:00:00\",\"feeStructure\":\"123\",\"fundType\":\"123\",\"investmentArea\":0,\"investmentManager\":\"123\",\"investmentPeriod\":1,\"investmentStrategy\":\"123\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"123\",\"managerId\":\"123\",\"maxInvestmentUnits\":12,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CQ110\",\"productId\":\"fb7356cd-2d37-4ca5-a0bb-b57c19e2dddf\",\"productName\":\"测试理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"1c13ca66-2a58-4000-9fc0-fe42db3e8373\",\"remainingScale\":1000000,\"repaymentType\":1,\"riskDisclosure\":\"123\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"123\",\"totalScale\":10000000,\"totalUnits\":431231,\"updatedAt\":\"2025-04-20 19:10:38\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 19:10:38',28),(299,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"stock\",\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-04-10 09:39:06\",\"params\":{},\"sortOrder\":4,\"status\":0,\"updatedAt\":\"2025-04-20 20:24:12\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:24:12',61),(300,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"fund\",\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"金融\",\"createdAt\":\"2025-04-10 09:39:06\",\"params\":{},\"sortOrder\":3,\"status\":0,\"updatedAt\":\"2025-04-20 20:24:16\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:24:16',62),(301,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"bond\",\"categoryId\":\"4f8d1d9d-25f5-4f4f-9666-9e13e3e1c16c\",\"categoryName\":\"科技\",\"createdAt\":\"2025-04-10 09:39:05\",\"params\":{},\"sortOrder\":2,\"status\":0,\"updatedAt\":\"2025-04-20 20:24:21\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:24:21',59),(302,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"fixed_term\",\"categoryId\":\"accec33c-9f4f-4654-8760-6da061dcb7cf\",\"categoryName\":\"房地产\",\"createdAt\":\"2025-04-10 09:39:05\",\"params\":{},\"sortOrder\":1,\"status\":0,\"updatedAt\":\"2025-04-20 20:24:25\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:24:25',44),(303,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"stock\",\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-04-10 09:39:06\",\"params\":{},\"sortOrder\":4,\"status\":1,\"updatedAt\":\"2025-04-20 20:25:22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:25:22',54),(304,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"fund\",\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"金融\",\"createdAt\":\"2025-04-10 09:39:06\",\"params\":{},\"sortOrder\":3,\"status\":1,\"updatedAt\":\"2025-04-20 20:59:16\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:59:17',52),(305,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"bond\",\"categoryId\":\"4f8d1d9d-25f5-4f4f-9666-9e13e3e1c16c\",\"categoryName\":\"科技\",\"createdAt\":\"2025-04-10 09:39:05\",\"params\":{},\"sortOrder\":2,\"status\":1,\"updatedAt\":\"2025-04-20 20:59:22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:59:22',54),(306,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','*************','XX XX','{\"categoryCode\":\"fixed_term\",\"categoryId\":\"accec33c-9f4f-4654-8760-6da061dcb7cf\",\"categoryName\":\"房地产\",\"createdAt\":\"2025-04-10 09:39:05\",\"params\":{},\"sortOrder\":1,\"status\":1,\"updatedAt\":\"2025-04-20 20:59:26\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 20:59:26',140),(307,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','*************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"金融\",\"createdAt\":\"2025-04-20 16:29:11\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.03,\"description\":\"1\",\"endTime\":\"2025-04-25 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":1,\"investmentManager\":\"1\",\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1000,\"isHot\":1,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":0,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"13ebfbac-a972-4a7f-87d6-67942f886e94\",\"productName\":\"WTI纽约原油\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"regionName\":\"美国\",\"remainingScale\":450000,\"repaymentType\":1,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"soldUnits\":1,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"1\",\"totalScale\":500000,\"totalUnits\":50,\"updatedAt\":\"2025-04-20 23:39:30\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-20 23:39:30',87),(308,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":12,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"\",\"createdAt\":\"2025-04-21 10:23:11\",\"currency\":\"USTD\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":12,\"description\":\"12\",\"endTime\":\"2025-04-24 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":1,\"investmentManager\":\"12\",\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":1,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":10,\"params\":{},\"productCode\":\"CBD12321\",\"productId\":\"1bfb788f-7692-4e26-a6f6-e498ea6e31a6\",\"productName\":\"国贸期货理财产品\",\"productStatus\":0,\"productType\":1,\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":12,\"startTime\":\"2025-04-21 10:23:01\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-04-21 10:23:11\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-21 10:23:11',73),(309,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":12,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"金融\",\"createdAt\":\"2025-04-21 10:23:11\",\"currency\":\"USTD\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":12,\"description\":\"12\",\"endTime\":\"2025-04-24 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":1,\"investmentManager\":\"12\",\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":1,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":10,\"params\":{},\"productCode\":\"CBD12321\",\"productId\":\"1bfb788f-7692-4e26-a6f6-e498ea6e31a6\",\"productName\":\"国贸期货理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"regionName\":\"澳大利亚\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":12,\"startTime\":\"2025-04-21 10:23:01\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-04-21 10:26:02\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-21 10:26:02',27),(310,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/region\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"区域管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2005,\"path\":\"region\",\"perms\":\"system\",\"routeName\":\"region\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:27:00',71),(311,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/contract/trem\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"合同条款\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2019,\"path\":\"trems\",\"perms\":\"system\",\"routeName\":\"trems\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:29:32',67),(312,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createTime\":\"2025-04-09 14:43:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2002,\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2001,\"path\":\"customer/list\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:30:36',70),(313,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:41:17\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"用户管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"customer\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:30:53',71),(314,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:41:17\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"用户管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"customer\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:31:06',65),(315,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createTime\":\"2025-04-09 14:43:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2002,\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2001,\"path\":\"list\",\"perms\":\"system\",\"routeName\":\"customerList\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:31:32',64),(316,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/userLevelConfig\",\"createTime\":\"2025-04-09 14:45:00\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"会员等级配置\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"path\":\"config/level\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:31:37',62),(317,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/system\",\"createTime\":\"2025-04-09 14:31:43\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"系统配置\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"sc\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:34:51',60),(318,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/chongzhi\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"充值管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"path\":\"deposit\",\"perms\":\"system\",\"routeName\":\"deposit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:36:07',50),(319,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/index\",\"createTime\":\"2025-04-09 14:43:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2002,\"menuName\":\"会员管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2001,\"path\":\"list\",\"perms\":\"system\",\"routeName\":\"customerList\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:36:21',61),(320,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/qianbaojiaoyi\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"钱包交易\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2001,\"path\":\"wallet\",\"perms\":\"system\",\"routeName\":\"wallet\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:37:04',66),(321,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/shiming\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"实名认证\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2001,\"path\":\"kyc\",\"perms\":\"system\",\"routeName\":\"kyc\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:37:47',73),(322,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/tixian\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"提现管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2001,\"path\":\"withdrawal\",\"perms\":\"system\",\"routeName\":\"withdrawal\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:38:51',60),(323,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/tixiandizhi\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"提现地址\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2001,\"path\":\"withdrawaladdress\",\"perms\":\"system\",\"routeName\":\"withdrawaladdress\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:39:40',69),(324,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/tixianshenhe\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"提现审核\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":2001,\"path\":\"drawalaudit\",\"perms\":\"system\",\"routeName\":\"drawalaudit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:40:35',189),(325,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/touzi\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":8,\"params\":{},\"parentId\":2001,\"path\":\"investment\",\"perms\":\"system\",\"routeName\":\"investment\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:41:30',54),(326,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/shiming\",\"createTime\":\"2025-04-29 17:37:47\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2025,\"menuName\":\"实名认证\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2001,\"path\":\"kyc\",\"perms\":\"system\",\"routeName\":\"kycConfig\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:47:11',81),(327,'全球市场区域',1,'com.ruoyi.web.controller.business.MarketRegionController.add()','POST',1,'admin','研发部门','/system/market/region','***********','内网IP','{\"currency\":\"USE\",\"marketCloseTime\":\"16:00:00\",\"marketOpenTime\":\"08:00:00\",\"params\":{},\"regionCode\":\"TEST\",\"regionId\":\"d2ce8cac94774dd294e6027ad028d8d1\",\"regionName\":\"测试区域\",\"sortOrder\":3,\"status\":2,\"timezone\":\"Asia\",\"tradingDays\":\"1,2,3,4\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 11:03:13',80),(328,'全球市场区域',1,'com.ruoyi.web.controller.business.MarketRegionController.add()','POST',1,'admin','研发部门','/system/market/region','***********','内网IP','{\"currency\":\"UDE\",\"marketCloseTime\":\"16:00:00\",\"marketOpenTime\":\"08:00:00\",\"params\":{},\"regionCode\":\"TEST\",\"regionId\":\"5f877943-121d-45fe-9125-492b33085794\",\"regionName\":\"测试区域\",\"sortOrder\":3,\"status\":2,\"timezone\":\"ASIA\",\"tradingDays\":\"1,2,3\"}','{\"msg\":\"新增全球市场区域失败，区域代码已存在\",\"code\":500}',0,NULL,'2025-04-30 11:10:10',4),(329,'全球市场区域',1,'com.ruoyi.web.controller.business.MarketRegionController.add()','POST',1,'admin','研发部门','/system/market/region','***********','内网IP','{\"currency\":\"UDE\",\"marketCloseTime\":\"16:00:00\",\"marketOpenTime\":\"08:00:00\",\"params\":{},\"regionCode\":\"TEST1\",\"regionId\":\"fe675561509e4f1f99e14beac68ba871\",\"regionName\":\"测试区域\",\"sortOrder\":3,\"status\":2,\"timezone\":\"ASIA\",\"tradingDays\":\"1,2,3\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 11:10:39',60),(330,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','***********','内网IP','{\"currency\":\"USE\",\"marketCloseTime\":\"16:00:00\",\"marketOpenTime\":\"08:00:00\",\"params\":{},\"regionCode\":\"TEST\",\"regionId\":\"d2ce8cac94774dd294e6027ad028d8d1\",\"regionName\":\"测试区域\",\"sortOrder\":3,\"status\":2,\"timezone\":\"Asia\",\"tradingDays\":\"1,2,3,4\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 11:10:51',56),(331,'全球市场区域',3,'com.ruoyi.web.controller.business.MarketRegionController.remove()','DELETE',1,'admin','研发部门','/system/market/region/undefined','***********','内网IP','{}','{\"msg\":\"操作失败\",\"code\":500}',0,NULL,'2025-04-30 11:11:35',39),(332,'全球市场区域',3,'com.ruoyi.web.controller.business.MarketRegionController.remove()','DELETE',1,'admin','研发部门','/system/market/region/d2ce8cac94774dd294e6027ad028d8d1','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 11:11:58',59),(333,'充值订单',1,'com.ruoyi.web.controller.business.DepositOrderController.add()','POST',1,'admin','研发部门','/business/depositOrder','***********','内网IP','{\"addressId\":\"**************.123\",\"amount\":123.00,\"currency\":\"USE\",\"depositAddress\":\"ASDFASDFA\",\"expireTime\":\"2025-04-30 09:09:13\",\"networkType\":\"TRC20\",\"orderId\":\"540e8269-322d-4808-a399-25744b6d5ce5\",\"orderNo\":\"***********\",\"orderStatus\":1,\"params\":{},\"qrCode\":\"asdfasdfasdfasdfasdfasdfasdf\",\"userId\":\"807f88fd-74e7-40f3-97b0-32fff4b3a39a\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'create_time\' in \'field list\'\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/DepositOrderMapper.xml]\n### The error may involve com.ruoyi.system.mapper.DepositOrderMapper.insertDepositOrder-Inline\n### The error occurred while setting parameters\n### SQL: insert into deposit_order          ( order_id,             order_no,             user_id,             amount,             currency,             network_type,             address_id,             deposit_address,             qr_code,             order_status,             expire_time,                                                    create_time, update_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    sysdate(), sysdate() )\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'create_time\' in \'field list\'\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'create_time\' in \'field list\'','2025-04-30 12:09:30',410),(334,'充值订单',1,'com.ruoyi.web.controller.business.DepositOrderController.add()','POST',1,'admin','研发部门','/business/depositOrder','***********','内网IP','{\"addressId\":\"12341234\",\"amount\":123,\"currency\":\"USD\",\"depositAddress\":\"12341234\",\"expireTime\":\"2025-04-30 00:09:10\",\"networkType\":\"TRC20\",\"orderId\":\"afeab966-2724-41b6-9dbf-89c9cfd699d2\",\"orderNo\":\"***********\",\"orderStatus\":1,\"params\":{},\"qrCode\":\"12341234\",\"userId\":\"807f88fd-74e7-40f3-97b0-32fff4b3a39a\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'create_time\' in \'field list\'\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/DepositOrderMapper.xml]\n### The error may involve com.ruoyi.system.mapper.DepositOrderMapper.insertDepositOrder-Inline\n### The error occurred while setting parameters\n### SQL: insert into deposit_order          ( order_id,             order_no,             user_id,             amount,             currency,             network_type,             address_id,             deposit_address,             qr_code,             order_status,             expire_time,                                                    create_time, update_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    sysdate(), sysdate() )\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'create_time\' in \'field list\'\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'create_time\' in \'field list\'','2025-04-30 14:05:48',7),(335,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','***********','内网IP','{\"authStatus\":4,\"kycId\":\"a6e36afd-5ac1-426c-ac27-89457934bcb3\",\"params\":{},\"rejectReason\":\"123\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:09:02',72),(336,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','***********','内网IP','{\"authStatus\":3,\"authTime\":\"2025-04-30 15:17:39\",\"kycId\":\"a6e36afd-5ac1-426c-ac27-89457934bcb3\",\"params\":{},\"rejectReason\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:17:39',74),(337,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeRiskLevel()','PUT',1,'admin','研发部门','/system/kyc/changeRiskLevel','***********','内网IP','{\"kycId\":\"a6e36afd-5ac1-426c-ac27-89457934bcb3\",\"params\":{},\"riskLevel\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:17:53',77),(338,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeRiskLevel()','PUT',1,'admin','研发部门','/system/kyc/changeRiskLevel','***********','内网IP','{\"kycId\":\"a6e36afd-5ac1-426c-ac27-89457934bcb3\",\"params\":{},\"riskLevel\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:18:20',56),(339,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeRiskLevel()','PUT',1,'admin','研发部门','/system/kyc/changeRiskLevel','***********','内网IP','{\"kycId\":\"a6e36afd-5ac1-426c-ac27-89457934bcb3\",\"params\":{},\"riskLevel\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:18:43',56),(340,'用户提现地址',2,'com.ruoyi.web.controller.business.UserWithdrawalAddressController.edit()','PUT',1,'admin','研发部门','/business/address','***********','内网IP','{\"address\":\"Riffs-3455dbrhjjrh29-tttttt\",\"addressId\":\"addr2c9e809909\",\"addressLabel\":\"我的提现地址-t\",\"createdAt\":\"2025-04-27 08:04:01\",\"idName\":\"Ty\",\"isDefault\":1,\"networkType\":\"ERC20\",\"params\":{},\"status\":2,\"updateTime\":\"2025-04-30 15:36:13\",\"updatedAt\":\"2025-04-27 08:04:01\",\"userId\":\"d15cc10e-94d6-4197-8998-ce29361c1499\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:36:13',90),(341,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/eacdebd3-16e0-4179-828c-45cb94b27df0','***********','内网IP','\"eacdebd3-16e0-4179-828c-45cb94b27df0\"',NULL,1,'nested exception is org.apache.ibatis.binding.BindingException: Parameter \'userId\' not found. Available parameters are [arg1, arg0, param1, param2]','2025-04-30 16:07:19',23),(342,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/eacdebd3-16e0-4179-828c-45cb94b27df0','***********','内网IP','\"eacdebd3-16e0-4179-828c-45cb94b27df0\"',NULL,1,'\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column \'u.user_id\' in \'on clause\'\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/UserWalletMapper.xml]\n### The error may involve defaultParameterMap\n### The error occurred while setting parameters\n### SQL: select w.wallet_id, w.user_id, w.currency, w.balance, w.frozen_amount, w.pending_principal,                 w.pending_interest, w.total_asset, w.total_income, w.total_expense, w.update_time,                 w.created_at, w.updated_at, u.nickname, u.phone         from user_wallet w         left join v_users u on w.user_id = u.user_id               where w.user_id = ? and w.currency = ?\n### Cause: java.sql.SQLSyntaxErrorException: Unknown column \'u.user_id\' in \'on clause\'\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'u.user_id\' in \'on clause\'','2025-04-30 16:14:05',105),(343,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/eacdebd3-16e0-4179-828c-45cb94b27df0','***********','内网IP','\"eacdebd3-16e0-4179-828c-45cb94b27df0\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 16:16:38',472),(344,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.reject()','PUT',1,'admin','研发部门','/business/withdrawal/audit/reject/539f5f85-8fc2-4b5e-9640-5d9dd2c9cdde','***********','内网IP','{\"reason\":\"123\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 16:29:56',456),(345,'通知公告',2,'com.ruoyi.web.controller.system.SysNoticeController.edit()','PUT',1,'admin','研发部门','/system/notice','***********','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-09 14:22:38\",\"noticeContent\":\"<p>123123</p>\",\"noticeId\":1,\"noticeTitle\":\"温馨提醒：2018-07-01 若依新版本发布啦\",\"noticeType\":\"2\",\"params\":{},\"remark\":\"管理员\",\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 17:38:24',164),(346,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','***********','内网IP','{\"configKey\":\"withdrawal.default.max_fee\",\"configValue\":\"100.00\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 17:57:21',79),(347,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/user/userLevelConfig\",\"createTime\":\"2025-04-09 14:45:00\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"会员等级配置\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"path\":\"config/level\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 13:57:05',88),(348,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"财务管理\",\"menuType\":\"M\",\"orderNum\":10,\"params\":{},\"parentId\":0,\"path\":\"finance\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:00:02',52),(349,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/user/chongzhi\",\"createTime\":\"2025-04-29 17:36:07\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2023,\"menuName\":\"充值管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2030,\"path\":\"deposit\",\"perms\":\"system\",\"routeName\":\"deposit\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:00:27',42),(350,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/user/tixian\",\"createTime\":\"2025-04-29 17:38:51\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2026,\"menuName\":\"提现管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2030,\"path\":\"withdrawal\",\"perms\":\"system\",\"routeName\":\"withdrawal\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:00:42',50),(351,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/user/qianbaojiaoyi\",\"createTime\":\"2025-04-29 17:37:04\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2024,\"menuName\":\"钱包交易\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2030,\"path\":\"wallet\",\"perms\":\"system\",\"routeName\":\"wallet\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:01:03',48),(352,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"createTime\":\"2025-04-09 14:45:46\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2004,\"menuName\":\"积分管理\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":0,\"path\":\"credits\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:01:31',121),(353,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/credits/address\",\"createTime\":\"2025-04-09 14:48:46\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2008,\"menuName\":\"收货地址\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2004,\"path\":\"address\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:01:43',60),(354,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"\",\"createBy\":\"admin\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"积分明细\",\"menuType\":\"C\",\"orderNum\":10,\"params\":{},\"parentId\":2030,\"path\":\"pointsdetail\",\"perms\":\"system\",\"routeName\":\"pointsdetail\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:03:12',87),(355,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/credits/order\",\"createTime\":\"2025-05-01 14:03:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2031,\"menuName\":\"积分明细\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2030,\"path\":\"pointsdetail\",\"perms\":\"system\",\"routeName\":\"pointsdetail\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 14:03:25',47),(356,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/user/touzi\",\"createTime\":\"2025-04-29 17:41:30\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2029,\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":8,\"params\":{},\"parentId\":2005,\"path\":\"investment\",\"perms\":\"system\",\"routeName\":\"investment\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"修改菜单\'投资记录\'失败，菜单名称已存在\",\"code\":500}',0,NULL,'2025-05-01 19:31:19',3),(357,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/financial/order\",\"createTime\":\"2025-04-09 14:52:06\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2012,\"menuName\":\"投资记录-1\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2005,\"path\":\"order\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 19:32:00',46),(358,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','*************','XX XX','{\"children\":[],\"component\":\"pages/user/touzi\",\"createTime\":\"2025-04-29 17:41:30\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2029,\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":8,\"params\":{},\"parentId\":2005,\"path\":\"investment\",\"perms\":\"system\",\"routeName\":\"investment\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-01 19:32:18',153),(359,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"createTime\":\"2025-04-09 14:45:46\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2004,\"menuName\":\"积分商城\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":0,\"path\":\"credits\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-06 15:05:51',66),(360,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"b0731442-c495-4ec2-a667-617fd1ffe8e9\",\"tagId\":\"505e9f7e-2f1f-8f0e-3a7f-f9f2f1f6a5f4\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-07 17:41:13',66),(361,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"b0731442-c495-4ec2-a667-617fd1ffe8e9\",\"tagId\":\"909f3f1f-6f5f-2f4f-7a1f-f3f6f5f0a9f8\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-07 17:41:14',44),(362,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"b0731442-c495-4ec2-a667-617fd1ffe8e9\",\"tagId\":\"707f1f9e-4f3f-0f2f-5a9f-f1f4f3f8a7f6\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-07 17:41:14',49),(363,'产品-标签关联',1,'com.ruoyi.web.controller.business.ProductTagRelationController.add()','POST',1,'admin','研发部门','/system/productTagRelation','***********','内网IP','{\"params\":{},\"productId\":\"b0731442-c495-4ec2-a667-617fd1ffe8e9\",\"tagId\":\"c12f6f4f-9f8f-5f7f-0a4f-f6f9f8f3acfb\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-07 17:41:15',172),(364,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.1306,\"categoryId\":\"accec33c-9f4f-4654-8760-6da061dcb7cf\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:14\",\"currency\":\"USDT\",\"custodian\":\"渣打银行\",\"custodianId\":\"a6914458-d766-42c5-b3a2-1016e0a6137a\",\"dailyRate\":0.000358,\"description\":\"新加坡蓝筹股指数基金(SG)是由瑞银资产管理发行的一款新加坡市场投资产品，投资期限365天，预期年化收益率13.06%。\",\"endTime\":\"2025-07-04 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentManager\":\"瑞银资产管理\",\"investmentPeriod\":365,\"investmentStrategy\":\"本产品主要投资于新加坡市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":277.707,\"isHot\":0,\"issuerId\":\"6bfc54b5-6ca5-435b-8257-6a4fd1c79720\",\"managerId\":\"3e123acb-f0f1-4a0c-b630-97f4ba4e7fda\",\"maxInvestmentUnits\":0,\"minInvestment\":5094.1588,\"params\":{},\"productCode\":\"GPSG018\",\"productId\":\"b0731442-c495-4ec2-a667-617fd1ffe8e9\",\"productName\":\"新加坡蓝筹股指数基金(SG)\",\"productStatus\":3,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"regionName\":\"美国\",\"remainingScale\":187375.2338,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为3级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":3,\"soldUnits\":8568,\"startTime\":\"2025-04-07 10:53:18\",\"summary\":\"投资新加坡市场，期限365天，预期年化收益13.06%。\",\"totalScale\":1308809.9691,\"totalUnits\":10000,\"updatedAt\":\"2025-05-07 17:41:41\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-07 17:41:41',67),(365,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/chongzhi\",\"createTime\":\"2025-04-29 17:36:07\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2023,\"menuName\":\"充值记录\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2030,\"path\":\"deposit\",\"perms\":\"system\",\"routeName\":\"deposit\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 09:58:59',50),(366,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:24:37',58),(367,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:27:58',6),(368,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:28:35',5),(369,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:30:41',36),(370,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:31:18',9),(371,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:32:09',5),(372,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:32:17',5),(373,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:35:50',41),(374,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:43:29',19870),(375,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 15:49:14',8544),(376,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作异常：null\",\"code\":500}',0,NULL,'2025-05-09 16:01:28',8445),(377,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作触发失败：钱包余额不足，无法归集。当前余额: 0.000000 USDT，最小归集金额: 1 USDT\",\"code\":500}',0,NULL,'2025-05-09 16:20:55',9727),(378,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','***********','内网IP','{\"address\":\"TR6M9dfnMULt4WbKB6sKzni9aqiYuNHPB3\"}','{\"msg\":\"归集操作触发失败：钱包余额不足，无法归集。当前余额: 0.000000 USDT，最小归集金额: 1 USDT\",\"code\":500}',0,NULL,'2025-05-09 16:23:00',9415),(379,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":123123,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"\",\"createdAt\":\"2025-05-09 17:27:07\",\"currency\":\"CTR\",\"custodian\":\"*********\",\"custodianId\":\"12\",\"dailyRate\":11,\"description\":\"*********\",\"endTime\":\"2025-05-21 00:00:00\",\"feeStructure\":\"*********\",\"fundType\":\"*********\",\"investmentArea\":1,\"investmentManager\":\"\",\"investmentPeriod\":23,\"investmentStrategy\":\"*********\",\"investmentUnit\":32131,\"isHot\":0,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":321,\"minInvestment\":123,\"params\":{},\"productCode\":\"cs1723\",\"productId\":\"14103c13-7927-4bf6-b293-f1c8baffbac8\",\"productName\":\"无投资人测试\",\"productStatus\":0,\"productType\":3,\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"*********\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-05-09 00:00:00\",\"summary\":\"*********\",\"totalScale\":123123,\"totalUnits\":123123,\"updatedAt\":\"2025-05-09 17:27:07\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 17:27:07',82),(380,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":123123,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-05-09 17:27:07\",\"currency\":\"CTR\",\"custodian\":\"*********\",\"custodianId\":\"12\",\"dailyRate\":11,\"description\":\"*********\",\"endTime\":\"2025-05-21 00:00:00\",\"feeStructure\":\"*********\",\"fundType\":\"*********\",\"investmentArea\":1,\"investmentManager\":\"\",\"investmentPeriod\":23,\"investmentStrategy\":\"*********\",\"investmentUnit\":32131,\"isHot\":0,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":321,\"minInvestment\":123,\"params\":{},\"productCode\":\"cs1723\",\"productId\":\"14103c13-7927-4bf6-b293-f1c8baffbac8\",\"productName\":\"无投资人测试\",\"productStatus\":1,\"productType\":3,\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"regionName\":\"欧洲\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"*********\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-05-09 00:00:00\",\"summary\":\"*********\",\"totalScale\":123123,\"totalUnits\":123123,\"updatedAt\":\"2025-05-09 17:28:26\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 17:28:26',53),(381,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.05,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"categoryName\":\"医药\",\"createdAt\":\"2025-05-09 17:27:07\",\"currency\":\"USDT\",\"custodian\":\"0\",\"custodianId\":\"0\",\"dailyRate\":0.02,\"description\":\"无投资人测试\",\"endTime\":\"2025-06-09 00:00:00\",\"feeStructure\":\"测试\",\"fundType\":\"0\",\"investmentArea\":0,\"investmentManager\":\"\",\"investmentPeriod\":30,\"investmentStrategy\":\"测试\",\"investmentUnit\":10,\"isHot\":0,\"issuerId\":\"0\",\"managerId\":\"0\",\"maxInvestmentUnits\":100,\"minInvestment\":10,\"params\":{},\"productCode\":\"cs1723\",\"productId\":\"14103c13-7927-4bf6-b293-f1c8baffbac8\",\"productName\":\"无投资人测试\",\"productStatus\":1,\"productType\":3,\"regionName\":\"欧洲\",\"remainingScale\":1000,\"repaymentType\":1,\"riskDisclosure\":\"测试\",\"riskLevel\":1,\"soldUnits\":0,\"startTime\":\"2025-05-09 00:00:00\",\"summary\":\"无投资人测试\",\"totalScale\":1000,\"totalUnits\":1000,\"updatedAt\":\"2025-05-09 17:33:08\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 17:33:08',65),(382,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','***********','内网IP','{\"configKey\":\"contract.manager.seal_image_url\",\"configValue\":\"https://santalk.oss-cn-beijing.aliyuncs.com/image/e4e54fa1458f42378b4a4dd7417bf87a.png\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 17:52:33',64),(383,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.07,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-09 18:03:06\",\"currency\":\"USDT\",\"custodian\":\"---\",\"custodianId\":\"0\",\"dailyRate\":0.03,\"description\":\"---\",\"endTime\":\"2025-06-09 00:00:00\",\"feeStructure\":\"---\",\"fundType\":\"---\",\"investmentArea\":0,\"investmentPeriod\":30,\"investmentStrategy\":\"---\",\"investmentUnit\":1,\"isHot\":0,\"issuerId\":\"0\",\"managerId\":\"0\",\"maxInvestmentUnits\":100,\"minInvestment\":1,\"params\":{},\"productCode\":\"cs1800\",\"productId\":\"b4334408-cdc1-4a7b-9bd8-fc3c71642320\",\"productName\":\"投资人null\",\"productStatus\":1,\"productType\":3,\"regionId\":\"\",\"remainingScale\":10000,\"repaymentType\":1,\"riskDisclosure\":\"---\",\"riskLevel\":1,\"soldUnits\":0,\"startTime\":\"2025-05-09 18:02:43\",\"summary\":\"---\",\"totalScale\":10000,\"totalUnits\":10000,\"updatedAt\":\"2025-05-09 18:03:06\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 18:03:07',486),(384,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.reject()','PUT',1,'admin','研发部门','/business/withdrawal/audit/reject/6157f43a-5759-42a7-8550-d9b18102bb0f','************','XX XX','{\"reason\":\"未完成投资流水\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 18:35:06',104),(385,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"enabled\":true,\"id\":\"bc27ab07-bb24-4725-9317-0fcc534a7ac1\",\"params\":{},\"points\":0,\"updateBy\":\"admin\",\"userLevel\":0}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 18:48:04',87),(386,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','************','XX XX','{\"authStatus\":3,\"authTime\":\"2025-05-09 23:16:42\",\"kycId\":\"07993afd-6acd-4a0c-99b2-b0cd08697ca5\",\"params\":{},\"rejectReason\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-09 23:16:42',109),(387,'产品分类',1,'com.ruoyi.web.controller.business.ProductCategoryController.add()','POST',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"wj\",\"categoryId\":\"cd9207ba-de8f-4e5b-9172-c948764096ec\",\"categoryName\":\"稳健理财\",\"createdAt\":\"2025-05-13 16:37:42\",\"params\":{},\"sortOrder\":5,\"status\":1,\"updatedAt\":\"2025-05-13 16:37:42\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-13 16:37:42',113),(388,'产品分类',1,'com.ruoyi.web.controller.business.ProductCategoryController.add()','POST',1,'admin','研发部门','/system/productCategory','***********','内网IP','{\"categoryCode\":\"jj\",\"categoryId\":\"209d40e8-81a0-4919-b6ec-be037107ed65\",\"categoryName\":\"进阶理财\",\"createdAt\":\"2025-05-13 16:37:56\",\"params\":{},\"sortOrder\":6,\"status\":1,\"updatedAt\":\"2025-05-13 16:37:56\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-13 16:37:56',82),(389,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.141,\"categoryId\":\"wj\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:14\",\"currency\":\"USDT\",\"custodian\":\"瑞银集团\",\"custodianId\":\"cff94be4-8598-4fe7-855e-1c096480010d\",\"dailyRate\":0.000386,\"description\":\"澳大利亚新兴市场基金(AU)是由先锋领航集团发行的一款澳大利亚市场投资产品，投资期限30天，预期年化收益率14.10%。\",\"endTime\":\"2025-05-28 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentPeriod\":30,\"investmentStrategy\":\"本产品主要投资于澳大利亚市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":284.1097,\"isHot\":0,\"issuerId\":\"20ec713c-cdbd-409d-9526-44b746a125d1\",\"managerId\":\"8dabb7e4-113e-46eb-8d57-8893f42af62d\",\"maxInvestmentUnits\":0,\"minInvestment\":5832.5903,\"params\":{},\"productCode\":\"GPAU019\",\"productId\":\"f48e80be-8a94-4731-8bc7-c113b268b0bc\",\"productName\":\"澳大利亚新兴市场基金(AU)\",\"productStatus\":0,\"productType\":5,\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"regionName\":\"澳大利亚\",\"remainingScale\":2711880.7926,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为3级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":3,\"soldUnits\":5483,\"startTime\":\"2025-04-01 10:53:18\",\"summary\":\"投资澳大利亚市场，期限30天，预期年化收益14.10%。\",\"totalScale\":6004108.5856,\"totalUnits\":10000,\"updatedAt\":\"2025-05-14 10:07:44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:07:44',76),(390,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.0812,\"categoryId\":\"wj\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:13\",\"currency\":\"USDT\",\"custodian\":\"摩根大通银行\",\"custodianId\":\"0ea2c272-8b80-4ae5-a98b-3b21998d3d1e\",\"dailyRate\":0.000222,\"description\":\"新加坡一年期政府债券(SG)是由道富环球投资管理发行的一款新加坡市场投资产品，投资期限30天，预期年化收益率8.12%。\",\"endTime\":\"2025-06-03 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"fundType\":\"指数型\",\"investmentArea\":1,\"investmentPeriod\":30,\"investmentStrategy\":\"本产品主要投资于新加坡市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":731.0685,\"isHot\":1,\"issuerId\":\"bf1ae1e9-cda4-434a-ac74-dbe35a51245e\",\"managerId\":\"8038fcce-28ba-4210-b1fa-09013756d907\",\"maxInvestmentUnits\":0,\"minInvestment\":1945.046,\"params\":{},\"productCode\":\"GPSG011\",\"productId\":\"0013d2fb-8002-42c4-b11b-00e81a57d383\",\"productName\":\"新加坡一年期政府债券(SG)\",\"productStatus\":1,\"productType\":3,\"regionId\":\"33c6818f-7206-4597-9460-6b0c595ad4c8\",\"regionName\":\"新加坡\",\"remainingScale\":5857893.7842,\"repaymentType\":2,\"riskDisclosure\":\"投资有风险，本产品风险等级为2级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":2,\"soldUnits\":2472,\"startTime\":\"2025-03-22 10:53:18\",\"summary\":\"投资新加坡市场，期限30天，预期年化收益8.12%。\",\"totalScale\":7781823.3386,\"totalUnits\":10000,\"updatedAt\":\"2025-05-14 10:07:54\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:07:54',149),(391,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.1335,\"categoryId\":\"jj\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:13\",\"currency\":\"USDT\",\"custodian\":\"瑞银集团\",\"custodianId\":\"202c8696-c74c-4c30-ac1c-22aa4cf58a0d\",\"dailyRate\":0.000366,\"description\":\"澳大利亚一年期政府债券(AU)是由太平洋投资管理公司发行的一款澳大利亚市场投资产品，投资期限30天，预期年化收益率13.35%。\",\"endTime\":\"2025-06-11 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentPeriod\":30,\"investmentStrategy\":\"本产品主要投资于澳大利亚市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":148.5149,\"isHot\":1,\"issuerId\":\"4422d444-69b2-4779-aaed-3edaae884d03\",\"managerId\":\"64f64594-0c1d-47c3-861e-9d4cb8af45a2\",\"maxInvestmentUnits\":0,\"minInvestment\":886.7224,\"params\":{},\"productCode\":\"GPAU006\",\"productId\":\"00cc32c6-3ac4-4842-bb42-a65624a103be\",\"productName\":\"澳大利亚一年期政府债券(AU)\",\"productStatus\":1,\"productType\":5,\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"regionName\":\"澳大利亚\",\"remainingScale\":1298458.5024,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为2级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":2,\"soldUnits\":2379,\"startTime\":\"2025-03-21 10:53:18\",\"summary\":\"投资澳大利亚市场，期限30天，预期年化收益13.35%。\",\"totalScale\":1703848.4614,\"totalUnits\":10000,\"updatedAt\":\"2025-05-14 10:08:03\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:08:03',58),(392,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.0948,\"categoryId\":\"jj\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:13\",\"currency\":\"USDT\",\"custodian\":\"花旗银行\",\"custodianId\":\"bd29c7a9-0db6-40d5-888a-35d6058fed1d\",\"dailyRate\":0.00026,\"description\":\"美国股票多元化投资(US)是由德意志资产管理发行的一款美国市场投资产品，投资期限90天，预期年化收益率9.48%。\",\"endTime\":\"2025-05-28 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentPeriod\":90,\"investmentStrategy\":\"本产品主要投资于美国市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":169.2186,\"isHot\":0,\"issuerId\":\"319ba22e-97cb-4c6d-96b9-b42999aabdcf\",\"managerId\":\"27997ab1-b558-470b-86b4-2034e1e20249\",\"maxInvestmentUnits\":0,\"minInvestment\":2318.2406,\"params\":{},\"productCode\":\"GPUS005\",\"productId\":\"1f2c892b-3c19-4e4f-aa46-12f8df0f5d4d\",\"productName\":\"美国股票多元化投资(US)\",\"productStatus\":1,\"productType\":5,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"regionName\":\"美国\",\"remainingScale\":775345.3516,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为1级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":1,\"soldUnits\":8094,\"startTime\":\"2025-03-19 10:53:18\",\"summary\":\"投资美国市场，期限90天，预期年化收益9.48%。\",\"totalScale\":4060941.3016,\"totalUnits\":10000,\"updatedAt\":\"2025-05-14 10:08:12\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:08:12',58),(393,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.0812,\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:13\",\"currency\":\"USDT\",\"custodian\":\"摩根大通银行\",\"custodianId\":\"0ea2c272-8b80-4ae5-a98b-3b21998d3d1e\",\"dailyRate\":0.000222,\"description\":\"新加坡一年期政府债券(SG)是由道富环球投资管理发行的一款新加坡市场投资产品，投资期限30天，预期年化收益率8.12%。\",\"endTime\":\"2025-06-03 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"fundType\":\"指数型\",\"investmentArea\":1,\"investmentPeriod\":30,\"investmentStrategy\":\"本产品主要投资于新加坡市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":731.0685,\"isHot\":1,\"issuerId\":\"bf1ae1e9-cda4-434a-ac74-dbe35a51245e\",\"managerId\":\"8038fcce-28ba-4210-b1fa-09013756d907\",\"maxInvestmentUnits\":0,\"minInvestment\":1945.046,\"params\":{},\"productCode\":\"GPSG011\",\"productId\":\"0013d2fb-8002-42c4-b11b-00e81a57d383\",\"productName\":\"新加坡一年期政府债券(SG)\",\"productStatus\":1,\"productType\":5,\"regionId\":\"33c6818f-7206-4597-9460-6b0c595ad4c8\",\"regionName\":\"新加坡\",\"remainingScale\":5857893.7842,\"repaymentType\":2,\"riskDisclosure\":\"投资有风险，本产品风险等级为2级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":2,\"soldUnits\":2472,\"startTime\":\"2025-03-22 10:53:18\",\"summary\":\"投资新加坡市场，期限30天，预期年化收益8.12%。\",\"totalScale\":7781823.3386,\"totalUnits\":10000,\"updatedAt\":\"2025-05-14 10:08:44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:08:44',72),(394,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.0812,\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:13\",\"currency\":\"USDT\",\"custodian\":\"摩根大通银行\",\"custodianId\":\"0ea2c272-8b80-4ae5-a98b-3b21998d3d1e\",\"dailyRate\":0.000222,\"description\":\"新加坡一年期政府债券(SG)是由道富环球投资管理发行的一款新加坡市场投资产品，投资期限30天，预期年化收益率8.12%。\",\"endTime\":\"2025-06-03 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"fundType\":\"指数型\",\"investmentArea\":1,\"investmentPeriod\":30,\"investmentStrategy\":\"本产品主要投资于新加坡市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":731.0685,\"isHot\":1,\"issuerId\":\"bf1ae1e9-cda4-434a-ac74-dbe35a51245e\",\"managerId\":\"8038fcce-28ba-4210-b1fa-09013756d907\",\"maxInvestmentUnits\":0,\"minInvestment\":1945.046,\"params\":{},\"productCode\":\"GPSG011\",\"productId\":\"0013d2fb-8002-42c4-b11b-00e81a57d383\",\"productName\":\"新加坡一年期政府债券(SG)\",\"productStatus\":1,\"productType\":3,\"regionId\":\"33c6818f-7206-4597-9460-6b0c595ad4c8\",\"regionName\":\"新加坡\",\"remainingScale\":5857893.7842,\"repaymentType\":2,\"riskDisclosure\":\"投资有风险，本产品风险等级为2级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":2,\"soldUnits\":2472,\"startTime\":\"2025-03-22 10:53:18\",\"summary\":\"投资新加坡市场，期限30天，预期年化收益8.12%。\",\"totalScale\":7781823.3386,\"totalUnits\":10000,\"updatedAt\":\"2025-05-14 10:09:07\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:09:07',53),(395,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.141,\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:14\",\"currency\":\"USDT\",\"custodian\":\"瑞银集团\",\"custodianId\":\"cff94be4-8598-4fe7-855e-1c096480010d\",\"dailyRate\":0.000386,\"description\":\"澳大利亚新兴市场基金(AU)是由先锋领航集团发行的一款澳大利亚市场投资产品，投资期限30天，预期年化收益率14.10%。\",\"endTime\":\"2025-05-28 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"investmentArea\":1,\"investmentPeriod\":30,\"investmentStrategy\":\"本产品主要投资于澳大利亚市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":284.1097,\"isHot\":0,\"issuerId\":\"20ec713c-cdbd-409d-9526-44b746a125d1\",\"managerId\":\"8dabb7e4-113e-46eb-8d57-8893f42af62d\",\"maxInvestmentUnits\":0,\"minInvestment\":5832.5903,\"params\":{},\"productCode\":\"GPAU019\",\"productId\":\"f48e80be-8a94-4731-8bc7-c113b268b0bc\",\"productName\":\"澳大利亚新兴市场基金(AU)\",\"productStatus\":1,\"productType\":5,\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"regionName\":\"澳大利亚\",\"remainingScale\":2711880.7926,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为3级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":3,\"soldUnits\":5483,\"startTime\":\"2025-04-01 10:53:18\",\"summary\":\"投资澳大利亚市场，期限30天，预期年化收益14.10%。\",\"totalScale\":6004108.5856,\"totalUnits\":10000,\"updatedAt\":\"2025-05-14 10:09:13\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:09:13',59),(396,'团队等级配置',1,'com.ruoyi.web.controller.business.TeamLevelConfigController.add()','POST',1,'admin','研发部门','/business/team/level','***********','内网IP','{\"createBy\":\"admin\",\"createdAt\":\"2025-05-14 10:52:00\",\"levelId\":\"17a9f282-40d9-4392-8324-bbb734bfe1ef\",\"levelName\":\"测试等级\",\"levelOrder\":0,\"minLeaderInvestment\":20,\"minTeamInvestment\":100,\"minTeamMembers\":0,\"params\":{},\"rewardAmount\":30,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"123123\",\"status\":0,\"updatedAt\":\"2025-05-14 10:52:00\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:52:00',99),(397,'团队等级配置',1,'com.ruoyi.web.controller.business.TeamLevelConfigController.add()','POST',1,'admin','研发部门','/business/team/level','***********','内网IP','{\"createBy\":\"admin\",\"createdAt\":\"2025-05-14 10:53:13\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"测试等级2\",\"levelOrder\":1,\"minLeaderInvestment\":3000,\"minTeamInvestment\":30000,\"minTeamMembers\":20,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"CHY\",\"rewardDescription\":\"123123\",\"status\":0,\"updatedAt\":\"2025-05-14 10:53:13\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:53:13',236),(398,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','***********','内网IP','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"测试等级2\",\"levelOrder\":1,\"minLeaderInvestment\":3000,\"minTeamInvestment\":30000,\"minTeamMembers\":20,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"CHY\",\"rewardDescription\":\"22333333\",\"status\":0,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 10:54:51\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:54:51',55),(399,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','***********','内网IP','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"测试等级2\",\"levelOrder\":1,\"minLeaderInvestment\":3000,\"minTeamInvestment\":30000,\"minTeamMembers\":20,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"CHY\",\"rewardDescription\":\"22333333\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 10:55:13\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:55:13',48),(400,'团队等级配置',3,'com.ruoyi.web.controller.business.TeamLevelConfigController.remove()','DELETE',1,'admin','研发部门','/business/team/level/17a9f282-40d9-4392-8324-bbb734bfe1ef','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 10:59:47',210),(401,'团队等级配置',1,'com.ruoyi.web.controller.business.TeamLevelConfigController.add()','POST',1,'admin','研发部门','/business/team/level','***********','内网IP','{\"createBy\":\"admin\",\"createdAt\":\"2025-05-14 11:01:01\",\"levelId\":\"17fcbe1b-f71d-4aa6-88e5-cd7d14055fda\",\"levelName\":\"1\",\"levelOrder\":0,\"minLeaderInvestment\":1,\"minTeamInvestment\":1,\"minTeamMembers\":0,\"params\":{},\"rewardAmount\":1,\"rewardCurrency\":\"1\",\"rewardDescription\":\"1\",\"status\":0,\"updatedAt\":\"2025-05-14 11:01:01\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 11:01:01',54),(402,'团队等级配置',3,'com.ruoyi.web.controller.business.TeamLevelConfigController.remove()','DELETE',1,'admin','研发部门','/business/team/level/17fcbe1b-f71d-4aa6-88e5-cd7d14055fda','***********','内网IP','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 11:01:19',57),(403,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','*************','XX XX','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"测试等级2\",\"levelOrder\":1,\"minLeaderInvestment\":1000,\"minTeamInvestment\":2000,\"minTeamMembers\":16,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"100\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:04:42\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:04:42',53),(404,'团队等级配置',1,'com.ruoyi.web.controller.business.TeamLevelConfigController.add()','POST',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createBy\":\"admin\",\"createdAt\":\"2025-05-14 20:08:50\",\"levelId\":\"96594cbb-2314-4ceb-8243-ed09b87feb63\",\"levelName\":\"测试等级3\",\"levelOrder\":2,\"minLeaderInvestment\":5000,\"minTeamInvestment\":26300,\"minTeamMembers\":25,\"params\":{},\"rewardAmount\":500,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"500\",\"status\":1,\"updatedAt\":\"2025-05-14 20:08:50\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:08:50',51),(405,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createdAt\":\"2025-05-14 20:08:51\",\"levelId\":\"96594cbb-2314-4ceb-8243-ed09b87feb63\",\"levelName\":\"一级\",\"levelOrder\":2,\"minLeaderInvestment\":5000,\"minTeamInvestment\":26300,\"minTeamMembers\":25,\"params\":{},\"rewardAmount\":500,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"500\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:09:33\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:09:33',65),(406,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"二级\",\"levelOrder\":1,\"minLeaderInvestment\":1000,\"minTeamInvestment\":2000,\"minTeamMembers\":16,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"100\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:09:42\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:09:42',58),(407,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"二级\",\"levelOrder\":8,\"minLeaderInvestment\":1000,\"minTeamInvestment\":2000,\"minTeamMembers\":16,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"100\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:10:07\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:10:07',51),(408,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"一级\",\"levelOrder\":1,\"minLeaderInvestment\":1000,\"minTeamInvestment\":2000,\"minTeamMembers\":16,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"100\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:10:46\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'一级\' for key \'level_name\'\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/TeamLevelConfigMapper.xml]\n### The error may involve com.ruoyi.system.mapper.TeamLevelConfigMapper.updateTeamLevelConfig-Inline\n### The error occurred while setting parameters\n### SQL: update team_level_config          SET level_name = ?,             level_order = ?,             min_team_members = ?,             min_team_investment = ?,             min_leader_investment = ?,             reward_amount = ?,             reward_currency = ?,             reward_description = ?,             status = ?,             updated_at = ?          where level_id = ?\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'一级\' for key \'level_name\'\n; Duplicate entry \'一级\' for key \'level_name\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'一级\' for key \'level_name\'','2025-05-14 20:10:46',120),(409,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"级\",\"levelOrder\":1,\"minLeaderInvestment\":1000,\"minTeamInvestment\":2000,\"minTeamMembers\":16,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"100\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:10:57\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:10:57',49),(410,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createdAt\":\"2025-05-14 20:08:51\",\"levelId\":\"96594cbb-2314-4ceb-8243-ed09b87feb63\",\"levelName\":\"二级\",\"levelOrder\":2,\"minLeaderInvestment\":5000,\"minTeamInvestment\":26300,\"minTeamMembers\":25,\"params\":{},\"rewardAmount\":500,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"500\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:11:13\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:11:13',73),(411,'团队等级配置',2,'com.ruoyi.web.controller.business.TeamLevelConfigController.edit()','PUT',1,'admin','研发部门','/business/team/level','************','XX XX','{\"createdAt\":\"2025-05-14 10:53:14\",\"levelId\":\"aece41e6-eda0-4780-bb0b-2fa406396a28\",\"levelName\":\"一级\",\"levelOrder\":1,\"minLeaderInvestment\":1000,\"minTeamInvestment\":2000,\"minTeamMembers\":16,\"params\":{},\"rewardAmount\":100,\"rewardCurrency\":\"USDT\",\"rewardDescription\":\"100\",\"status\":1,\"updateBy\":\"admin\",\"updatedAt\":\"2025-05-14 20:11:23\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:11:23',41),(412,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','************','XX XX','{\"children\":[],\"component\":\"pages/user/userLevelConfig\",\"createTime\":\"2025-04-09 14:45:00\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"团队奖励\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"path\":\"config/level\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:48:19',69),(413,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','************','XX XX','{\"configKey\":\"withdrawal.default.fee_rate\",\"configValue\":\"0.1\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:55:15',60),(414,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','************','XX XX','{\"configKey\":\"withdrawal.default.working_days\",\"configValue\":\"1,2,3,4,5,6,7\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:55:44',51),(415,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','************','XX XX','{\"configKey\":\"investment.reinvest.reward_percentage\",\"configValue\":\"0\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-14 20:56:07',55),(416,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/touzi\",\"createTime\":\"2025-04-29 17:41:30\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2029,\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":8,\"params\":{},\"parentId\":2005,\"path\":\"investment\",\"perms\":\"system\",\"routeName\":\"investment\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 14:53:46',77),(417,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/order\",\"createTime\":\"2025-04-09 14:52:06\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2012,\"menuName\":\"投资记录-1\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2005,\"path\":\"order\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 14:53:50',47),(418,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/touzi\",\"createTime\":\"2025-04-29 17:41:30\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2029,\"menuName\":\"投资记录-2\",\"menuType\":\"C\",\"orderNum\":8,\"params\":{},\"parentId\":2005,\"path\":\"investment\",\"perms\":\"system\",\"routeName\":\"investment\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 14:53:58',53),(419,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/financial/order\",\"createTime\":\"2025-04-09 14:52:06\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2012,\"menuName\":\"投资记录\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2005,\"path\":\"order\",\"perms\":\"system\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 14:54:03',50),(420,'用户管理',5,'com.ruoyi.web.controller.system.SysUserController.export()','POST',1,'admin','研发部门','/system/user/export','***********','内网IP','{\"pageSize\":\"10\",\"pageNum\":\"1\"}',NULL,0,NULL,'2025-05-15 15:44:29',1196),(421,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.7,\"categoryId\":\"wj\",\"categoryName\":\"\",\"commissionRateLevel1\":0.2,\"commissionRateLevel2\":0.5,\"commissionRateLevel3\":0.8,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-15 15:53:44\",\"currency\":\"USDT\",\"custodian\":\"0\",\"custodianId\":\"0\",\"dailyRate\":0.2,\"description\":\"0\",\"endTime\":\"2025-06-19 00:00:00\",\"feeStructure\":\"0\",\"fundType\":\"0\",\"investmentArea\":0,\"investmentPeriod\":90,\"investmentStrategy\":\"0\",\"investmentUnit\":10,\"isHot\":0,\"issuerId\":\"0\",\"managerId\":\"0\",\"maxInvestmentUnits\":100,\"minInvestment\":100,\"params\":{},\"productCode\":\"cd3091\",\"productId\":\"437ae3b3-61d5-440f-a760-03dc5cb3afa5\",\"productName\":\"三级分成\",\"productStatus\":0,\"productType\":5,\"regionId\":\"\",\"remainingScale\":100000,\"repaymentType\":1,\"riskDisclosure\":\"0\",\"riskLevel\":1,\"soldUnits\":0,\"startTime\":\"2025-05-15 15:53:24\",\"summary\":\"0\",\"totalScale\":1000000,\"totalUnits\":10000,\"updatedAt\":\"2025-05-15 15:53:44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 15:53:44',382),(422,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','***********','内网IP','{\"allowFollowInvestment\":0,\"annualRate\":0.7,\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.2,\"commissionRateLevel3\":0.3,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-15 15:53:44\",\"currency\":\"USDT\",\"custodian\":\"0\",\"custodianId\":\"0\",\"dailyRate\":0.2,\"description\":\"0\",\"endTime\":\"2025-06-19 00:00:00\",\"feeStructure\":\"0\",\"fundType\":\"0\",\"investmentArea\":0,\"investmentPeriod\":90,\"investmentStrategy\":\"0\",\"investmentUnit\":10,\"isHot\":0,\"issuerId\":\"0\",\"managerId\":\"0\",\"maxInvestmentUnits\":100,\"minInvestment\":100,\"params\":{},\"productCode\":\"cd3091\",\"productId\":\"437ae3b3-61d5-440f-a760-03dc5cb3afa5\",\"productName\":\"三级分成\",\"productStatus\":0,\"productType\":5,\"regionId\":\"\",\"remainingScale\":100000,\"repaymentType\":1,\"riskDisclosure\":\"0\",\"riskLevel\":1,\"soldUnits\":0,\"startTime\":\"2025-05-15 15:53:24\",\"summary\":\"0\",\"totalScale\":1000000,\"totalUnits\":10000,\"updatedAt\":\"2025-05-15 15:54:03\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 15:54:03',50),(423,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/5ac5055f-0fa1-431c-8ce6-e0be4c272c2a','***********','内网IP','\"5ac5055f-0fa1-431c-8ce6-e0be4c272c2a\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 16:18:56',91),(424,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/7d879ba5-740a-4d82-9a2c-cf32f4922cf5','***********','内网IP','\"7d879ba5-740a-4d82-9a2c-cf32f4922cf5\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 16:18:58',97),(425,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/620edac8-27b4-415e-9e0d-1972d8faef6e','***********','内网IP','\"620edac8-27b4-415e-9e0d-1972d8faef6e\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 16:18:58',114),(426,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/448d27b0-0160-4b5b-9727-0b55c086c128','***********','内网IP','\"448d27b0-0160-4b5b-9727-0b55c086c128\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 16:18:59',101),(427,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/814ca18a-0795-4940-b731-26c3aa4b6d16','***********','内网IP','\"814ca18a-0795-4940-b731-26c3aa4b6d16\"','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 16:18:59',62),(428,'充值记录-手动归集',0,'com.ruoyi.web.controller.business.DepositOrderController.manualCollection()','POST',1,'admin','研发部门','/business/depositOrder/manualCollection','*************','XX XX','{\"address\":\"TWgeiuwqi5KSAr7JpsyHrh93ZQkUJtGwjw\"}','{\"msg\":\"归集服务暂时不可用，请稍后再试\",\"code\":500}',0,NULL,'2025-05-15 17:03:00',10064),(429,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','***************','XX XX','{\"authStatus\":4,\"kycId\":\"26c4a93b-f73d-488d-8877-00b0f093ffb8\",\"params\":{},\"rejectReason\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:21:55',65),(430,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','**************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.7,\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.2,\"commissionRateLevel3\":0.3,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-15 15:53:44\",\"currency\":\"USDT\",\"custodian\":\"0\",\"custodianId\":\"0\",\"dailyRate\":0.2,\"description\":\"0\",\"endTime\":\"2025-06-19 00:00:00\",\"feeStructure\":\"0\",\"fundType\":\"0\",\"investmentArea\":0,\"investmentPeriod\":90,\"investmentStrategy\":\"0\",\"investmentUnit\":10,\"isHot\":0,\"issuerId\":\"0\",\"managerId\":\"0\",\"maxInvestmentUnits\":0,\"minInvestment\":100,\"params\":{},\"productCode\":\"cd3091\",\"productId\":\"437ae3b3-61d5-440f-a760-03dc5cb3afa5\",\"productName\":\"三级分成\",\"productStatus\":0,\"productType\":5,\"regionId\":\"\",\"remainingScale\":100000,\"repaymentType\":1,\"riskDisclosure\":\"0\",\"riskLevel\":1,\"soldUnits\":0,\"startTime\":\"2025-05-15 15:53:24\",\"summary\":\"0\",\"totalScale\":1000000,\"totalUnits\":10000,\"updatedAt\":\"2025-05-15 21:41:10\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:41:10',69),(431,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','*************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"期货\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-20 16:29:11\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.03,\"description\":\"1\",\"endTime\":\"2025-04-25 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":1,\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1000,\"isHot\":1,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":0,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"13ebfbac-a972-4a7f-87d6-67942f886e94\",\"productName\":\"WTI纽约原油\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"regionName\":\"美国\",\"remainingScale\":446000,\"repaymentType\":1,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"soldUnits\":5,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"1\",\"totalScale\":500000,\"totalUnits\":0,\"updatedAt\":\"2025-05-15 21:41:45\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:41:45',75),(432,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','**************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"期货\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-20 16:29:11\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.03,\"description\":\"1\",\"endTime\":\"2025-04-25 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":1,\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1000,\"isHot\":1,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":3,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"13ebfbac-a972-4a7f-87d6-67942f886e94\",\"productName\":\"WTI纽约原油\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"regionName\":\"美国\",\"remainingScale\":446000,\"repaymentType\":1,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"soldUnits\":5,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"1\",\"totalScale\":500000,\"totalUnits\":100,\"updatedAt\":\"2025-05-15 21:42:19\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:42:19',57),(433,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','**************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.2,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"categoryName\":\"期货\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-20 16:29:11\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.03,\"description\":\"1\",\"endTime\":\"2025-04-25 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":1,\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1000,\"isHot\":1,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":0,\"minInvestment\":5000,\"params\":{},\"productCode\":\"CL\",\"productId\":\"13ebfbac-a972-4a7f-87d6-67942f886e94\",\"productName\":\"WTI纽约原油\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"regionName\":\"美国\",\"remainingScale\":446000,\"repaymentType\":1,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"soldUnits\":5,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"1\",\"totalScale\":500000,\"totalUnits\":100,\"updatedAt\":\"2025-05-15 21:43:05\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:43:05',61),(434,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','***************','XX XX','{\"authStatus\":3,\"authTime\":\"2025-05-15 21:46:49\",\"kycId\":\"26c4a93b-f73d-488d-8877-00b0f093ffb8\",\"params\":{},\"rejectReason\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:46:49',155),(435,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','**************','XX XX','{\"authStatus\":3,\"authTime\":\"2025-05-15 21:47:17\",\"kycId\":\"032cc259-c7e3-4324-9765-88e784bfe2dd\",\"params\":{},\"rejectReason\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:47:17',56),(436,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','***************','XX XX','{\"authStatus\":3,\"authTime\":\"2025-05-15 21:47:27\",\"kycId\":\"032cc259-c7e3-4324-9765-88e784bfe2dd\",\"params\":{},\"rejectReason\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-15 21:47:27',54),(437,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','***********','内网IP','{\"authStatus\":4,\"kycId\":\"ca4ef8e4-43da-49d0-97f9-1221405187ad\",\"params\":{},\"rejectReason\":\"123\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-16 15:01:55',74),(438,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.reject()','PUT',1,'admin','研发部门','/business/withdrawal/audit/reject/4e3d1658-84c1-4310-adbe-596f5e7d1c14','************','XX XX','{\"reason\":\"您好，请你完成流水\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,             currency,             direction,             status,                                                    business_id,             business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-16 17:24:39',523),(439,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.reject()','PUT',1,'admin','研发部门','/business/withdrawal/audit/reject/4e3d1658-84c1-4310-adbe-596f5e7d1c14','************','XX XX','{\"reason\":\"不能提现\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,             currency,             direction,             status,                                                    business_id,             business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-16 17:26:41',51),(440,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.reject()','PUT',1,'admin','研发部门','/business/withdrawal/audit/reject/4e3d1658-84c1-4310-adbe-596f5e7d1c14','************','XX XX','{\"reason\":\"您好\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,             currency,             direction,             status,                                                    business_id,             business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-16 17:27:24',43),(441,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.approve()','PUT',1,'admin','研发部门','/business/withdrawal/audit/approve/4e3d1658-84c1-4310-adbe-596f5e7d1c14','************','XX XX','\"4e3d1658-84c1-4310-adbe-596f5e7d1c14\"',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,             currency,             direction,             status,                                                    business_id,             business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-16 17:27:28',38),(442,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','************','XX XX','{\"authStatus\":4,\"kycId\":\"26c4a93b-f73d-488d-8877-00b0f093ffb8\",\"params\":{},\"rejectReason\":\"没有人脸\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-16 18:10:49',66),(443,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.reject()','PUT',1,'admin','研发部门','/business/withdrawal/audit/reject/4e3d1658-84c1-4310-adbe-596f5e7d1c14','************','XX XX','{\"reason\":\"1\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,             currency,             direction,             status,                                                    business_id,             business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-16 18:33:53',48),(444,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','************','XX XX','{\"authStatus\":3,\"authTime\":\"2025-05-16 18:49:25\",\"kycId\":\"26c4a93b-f73d-488d-8877-00b0f093ffb8\",\"params\":{},\"rejectReason\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-16 18:49:26',54),(445,'用户实名认证',2,'com.ruoyi.web.controller.business.UserKycController.changeAuthStatus()','PUT',1,'admin','研发部门','/system/kyc/changeAuthStatus','************','XX XX','{\"authStatus\":3,\"authTime\":\"2025-05-16 18:49:48\",\"kycId\":\"b3c8f339-b6f5-4d0a-a6c1-a3e452f349b7\",\"params\":{},\"rejectReason\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-16 18:49:48',65),(446,'用户管理',5,'com.ruoyi.web.controller.system.SysUserController.export()','POST',1,'admin','研发部门','/system/user/export','**************','XX XX','{\"pageSize\":\"10\",\"pageNum\":\"1\"}',NULL,0,NULL,'2025-05-16 21:51:19',995),(447,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"remarkNickname\":\"测试\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 19:45:54',120),(448,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":0,\"params\":{},\"remarkNickname\":\"测试备注昵称\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 19:47:26',57),(449,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"remarkNickname\":\"测试备注\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 19:50:03',115),(450,'用户钱包充值',1,'com.ruoyi.web.controller.business.WalletRechargeController.rechargeWallet()','POST',1,'admin','研发部门','/h/wallet/recharge/add','*************','XX XX','{\"amount\":10000,\"currency\":\"USDT\",\"remark\":\"测试充值\",\"userIdentifier\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,             currency,             direction,             status,                                                                 business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                                 ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-18 19:50:24',208),(451,'用户钱包充值',1,'com.ruoyi.web.controller.business.WalletRechargeController.rechargeWallet()','POST',1,'admin','研发部门','/h/wallet/recharge/add','*************','XX XX','{\"amount\":10000,\"currency\":\"USDT\",\"remark\":\"测试充值\",\"userIdentifier\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,             currency,             direction,             status,                                                                 business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                                 ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-18 19:51:29',70),(452,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:01:32',68),(453,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:01:53',64),(454,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"remarkNickname\":\"123\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:06:37',133),(455,'用户钱包充值',1,'com.ruoyi.web.controller.business.WalletRechargeController.rechargeWallet()','POST',1,'admin','研发部门','/h/wallet/recharge/add','*************','XX XX','{\"amount\":10000,\"currency\":\"USDT\",\"remark\":\"123123\",\"userIdentifier\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:08:44',126),(456,'用户钱包充值',1,'com.ruoyi.web.controller.business.WalletRechargeController.rechargeWallet()','POST',1,'admin','研发部门','/h/wallet/recharge/add','*************','XX XX','{\"amount\":200,\"currency\":\"USDT\",\"remark\":\"*********\",\"userIdentifier\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:13:09',97),(457,'用户积分充值',1,'com.ruoyi.web.controller.business.PointsRechargeController.rechargePoints()','POST',1,'admin','研发部门','/h/points/recharge/add','*************','XX XX','{\"amount\":200,\"remark\":\"*********123\",\"userIdentifier\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\"}','{\"msg\":\"\\n### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table \'wealth_wise.users\' doesn\'t exist\\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/BusinessUserMapper.xml]\\n### The error may involve com.ruoyi.system.mapper.BusinessUserMapper.selectBusinessUserById-Inline\\n### The error occurred while setting parameters\\n### SQL: select u.id, u.phone, u.password, u.nickname, u.name, u.email, u.email_verified, u.avatar_url, u.provider,          u.provider_id, u.social_id, u.enabled, u.user_level, u.points, u.invitation_code, u.inviter_id, u.member_type,         (select IFNULL(sum(amount), 0) from wealth_wise.deposit_record where user_id = u.id and status = 3) as total_deposit,         (select IFNULL(sum(amount), 0) from wealth_wise.withdrawal_record where user_id = u.id and status = 4) as total_withdrawal,         (select nickname from users where id = u.inviter_id) as affiliation_nickname,         (select id_name from wealth_wise.user_kyc where user_id = u.id and auth_status = 3 limit 1) as real_name,         u.name as remark_nickname,         (select IFNULL(balance, 0) from wealth_wise.user_wallet where user_id = u.id and currency = \'USDT\' limit 1) as wallet_balance,         (select phone from users where id = u.inviter_id) as superior_phone,         (select level_name from wealth_wise.team_level_config where level_id = u.current_team_level_id) as team_level_name,         \'\' as join_ip,         (select count(*) from wealth_wise.user_relationships where ancestor_id = u.id and depth > 0) as fission_user_count,         u.current_team_level_id,         u.created_at, u.updated_at         from users u               where id = ?\\n### Cause: java.sql.SQLSyntaxErrorException: Table \'wealth_wise.users\' doesn\'t exist\\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table \'wealth_wise.users\' doesn\'t exist\",\"code\":500}',0,NULL,'2025-05-18 20:14:40',118),(458,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"remarkNickname\":\"123\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:23:24',113),(459,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"remarkNickname\":\"123\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:24:37',55),(460,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"enabled\":0,\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"memberType\":1,\"params\":{},\"remarkNickname\":\"123\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:26:36',59),(461,'用户积分充值',1,'com.ruoyi.web.controller.business.PointsRechargeController.rechargePoints()','POST',1,'admin','研发部门','/h/points/recharge/add','*************','XX XX','{\"amount\":200,\"remark\":\"*********123\",\"userIdentifier\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:26:53',199),(462,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','*************','XX XX','{\"id\":\"dcbefbeb-3c15-43f0-aa8e-a41bad588288\",\"invitationCode\":\"BFf\",\"params\":{},\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-18 20:32:50',60),(463,'用户钱包充值',1,'com.ruoyi.web.controller.business.WalletRechargeController.rechargeWallet()','POST',1,'admin','研发部门','/h/wallet/recharge/add','************','XX XX','{\"amount\":1000,\"currency\":\"USDT\",\"remark\":\"充值\",\"userIdentifier\":\"0d4d0bf7-615d-41f8-b46e-1d43a2373c88\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 08:46:18',91),(464,'用户钱包充值',1,'com.ruoyi.web.controller.business.WalletRechargeController.rechargeWallet()','POST',1,'admin','研发部门','/h/wallet/recharge/add','************','XX XX','{\"amount\":1000,\"currency\":\"USDT\",\"remark\":\"充值\",\"userIdentifier\":\"0d4d0bf7-615d-41f8-b46e-1d43a2373c88\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 08:47:23',65),(465,'用户提现地址',2,'com.ruoyi.web.controller.business.UserWithdrawalAddressController.edit()','PUT',1,'admin','研发部门','/business/address','************','XX XX','{\"address\":\"\",\"addressId\":\"c84a414a-9a45-41ad-8709-7df66ac8d851\",\"addressLabel\":\"\",\"createdAt\":\"2025-04-22 17:02:20\",\"idName\":\"郭富城规划局\",\"isDefault\":1,\"networkType\":\"TRC20\",\"params\":{},\"status\":1,\"updateTime\":\"2025-05-19 08:52:36\",\"updatedAt\":\"2025-05-16 09:24:03\",\"userId\":\"0d4d0bf7-615d-41f8-b46e-1d43a2373c88\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 08:52:36',107),(466,'用户提现地址',2,'com.ruoyi.web.controller.business.UserWithdrawalAddressController.edit()','PUT',1,'admin','研发部门','/business/address','************','XX XX','{\"address\":\"\",\"addressId\":\"c84a414a-9a45-41ad-8709-7df66ac8d851\",\"addressLabel\":\"\",\"createdAt\":\"2025-04-22 17:02:20\",\"idName\":\"郭富城规划局\",\"isDefault\":1,\"networkType\":\"TRC20\",\"params\":{},\"status\":1,\"updateTime\":\"2025-05-19 08:52:59\",\"updatedAt\":\"2025-05-19 08:52:36\",\"userId\":\"0d4d0bf7-615d-41f8-b46e-1d43a2373c88\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 08:52:59',180),(467,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','************','XX XX','{\"enabled\":1,\"id\":\"0d4d0bf7-615d-41f8-b46e-1d43a2373c88\",\"memberType\":1,\"params\":{},\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 08:56:39',62),(468,'业务用户管理',2,'com.ruoyi.web.controller.business.BusinessUserController.edit()','PUT',1,'admin','研发部门','/business/user','************','XX XX','{\"enabled\":1,\"id\":\"0d4d0bf7-615d-41f8-b46e-1d43a2373c88\",\"memberType\":1,\"params\":{},\"remarkNickname\":\"骚\",\"totalDeposit\":null,\"totalWithdrawal\":null,\"updateBy\":\"admin\",\"walletBalance\":null}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 08:56:54',55),(469,'产品分类',3,'com.ruoyi.web.controller.business.ProductCategoryController.remove()','DELETE',1,'admin','研发部门','/system/productCategory/87c82a6f-5b29-4f66-82eb-16014ee4ec2c','**************','XX XX','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:23:39',62),(470,'产品分类',3,'com.ruoyi.web.controller.business.ProductCategoryController.remove()','DELETE',1,'admin','研发部门','/system/productCategory/50df8e3f-e4e4-4b85-a332-18b063e1fdb1','**************','XX XX','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:23:44',67),(471,'产品分类',3,'com.ruoyi.web.controller.business.ProductCategoryController.remove()','DELETE',1,'admin','研发部门','/system/productCategory/4f8d1d9d-25f5-4f4f-9666-9e13e3e1c16c','**************','XX XX','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:23:47',41),(472,'产品分类',3,'com.ruoyi.web.controller.business.ProductCategoryController.remove()','DELETE',1,'admin','研发部门','/system/productCategory/accec33c-9f4f-4654-8760-6da061dcb7cf','**************','XX XX','{}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:23:50',65),(473,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"jj\",\"categoryId\":\"jj\",\"categoryName\":\"进阶理财\",\"createdAt\":\"2025-05-13 16:37:57\",\"params\":{},\"sortOrder\":6,\"status\":0,\"updatedAt\":\"2025-05-19 09:24:04\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:24:04',65),(474,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"wj\",\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"createdAt\":\"2025-05-13 16:37:42\",\"params\":{},\"sortOrder\":5,\"status\":0,\"updatedAt\":\"2025-05-19 09:24:09\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:24:09',53),(475,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','**************','XX XX','{\"allowFollowInvestment\":0,\"categoryId\":\"\",\"categoryName\":\"\",\"commissionRateLevel1\":3,\"commissionRateLevel2\":2,\"commissionRateLevel3\":1,\"contractTemplateId\":\"\",\"createdAt\":\"2025-05-19 09:28:16\",\"currency\":\"USDT\",\"custodian\":\"\",\"custodianId\":\"\",\"dailyRate\":0.25,\"description\":\"\",\"feeStructure\":\"\",\"fundType\":\"\",\"investmentArea\":0,\"investmentPeriod\":30,\"investmentStrategy\":\"\",\"isHot\":0,\"issuerId\":\"\",\"managerId\":\"\",\"maxInvestmentUnits\":0,\"params\":{},\"productCode\":\"TSLA\",\"productId\":\"8d85a1df-f8a0-47c8-a686-38bceade8f4a\",\"productName\":\"特斯拉\",\"productStatus\":1,\"productType\":4,\"regionId\":\"\",\"repaymentType\":1,\"riskDisclosure\":\"\",\"riskLevel\":1,\"summary\":\"\",\"totalScale\":100000000,\"updatedAt\":\"2025-05-19 09:28:16\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,                          product_type,             investment_area,             region_id,             currency,                                       total_scale,                                       daily_rate,             investment_period,             max_investment_units,                                       risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             contract_template_id,                                       description,             summary,                          custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot,             commission_rate_level1,             commission_rate_level2,             commission_rate_level3 )           values ( ?,             ?,             ?,                          ?,             ?,             ?,             ?,                                       ?,                                       ?,             ?,             ?,                                       ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ? )\n### Cause: java.sql','2025-05-19 09:28:16',24),(476,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','**************','XX XX','{\"allowFollowInvestment\":0,\"categoryId\":\"\",\"categoryName\":\"\",\"commissionRateLevel1\":3,\"commissionRateLevel2\":2,\"commissionRateLevel3\":1,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-19 09:32:02\",\"currency\":\"USDT\",\"custodian\":\"1\",\"custodianId\":\"1\",\"dailyRate\":0.25,\"description\":\"1\",\"endTime\":\"2026-05-19 00:00:00\",\"feeStructure\":\"1\",\"fundType\":\"1\",\"investmentArea\":0,\"investmentPeriod\":60,\"investmentStrategy\":\"1\",\"investmentUnit\":1,\"isHot\":0,\"issuerId\":\"1\",\"managerId\":\"1\",\"maxInvestmentUnits\":0,\"minInvestment\":10000,\"params\":{},\"productCode\":\"TSLA\",\"productId\":\"e47dbe96-86cc-476e-aae8-e101c4ad11b3\",\"productName\":\"特斯拉\",\"productStatus\":1,\"productType\":1,\"regionId\":\"\",\"repaymentType\":2,\"riskDisclosure\":\"1\",\"riskLevel\":1,\"startTime\":\"2025-05-19 09:30:19\",\"summary\":\"1\",\"totalScale\":100000000,\"totalUnits\":5000000,\"updatedAt\":\"2025-05-19 09:32:02\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,                          product_type,             investment_area,             region_id,             currency,             min_investment,             investment_unit,             total_scale,                                       daily_rate,             investment_period,             max_investment_units,             total_units,                          risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             contract_template_id,             start_time,             end_time,             description,             summary,                          custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot,             commission_rate_level1,             commission_rate_level2,             commission_rate_level3 )           values ( ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,        ','2025-05-19 09:32:02',6),(477,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','**************','XX XX','{\"configKey\":\"withdrawal.default.min_fee\",\"configValue\":\"0.1\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:38:06',64),(478,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','**************','XX XX','{\"configKey\":\"bonus.realname.amount\",\"configValue\":\"0\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:38:37',51),(479,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','**************','XX XX','{\"configKey\":\"recharge.countdown_minutes\",\"configValue\":\"15\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:39:21',47),(480,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','**************','XX XX','{\"configKey\":\"withdrawal.default.end_time\",\"configValue\":\"22:30:00\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:40:00',148),(481,'Banner管理',2,'com.ruoyi.web.controller.business.BannerInfoController.edit()','PUT',1,'admin','研发部门','/business/banner','************','XX XX','{\"bannerId\":\"b21486e1-4757-4769-abb3-2a668f228f48\",\"createTime\":\"2025-04-10 09:39:01\",\"endTime\":\"2025-05-10 23:59:59\",\"imageUrl\":\"https://picsum.photos/id/15/1200/400\",\"params\":{},\"position\":1,\"priority\":5,\"redirectUrl\":\"\",\"startTime\":\"2025-03-11 00:00:00\",\"status\":1,\"title\":\"首页Banner 5\",\"updateBy\":\"admin\",\"updateTime\":\"2025-04-10 09:39:01\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:40:36',37),(482,'全局系统配置',2,'com.ruoyi.web.controller.business.SystemConfigController.edit()','PUT',1,'admin','研发部门','/business/system/config','**************','XX XX','{\"configKey\":\"invite.reward_amount\",\"configValue\":\"0\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:40:52',146),(483,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:15:00\",\"isDisplay\":1,\"params\":{},\"tagId\":\"016f0f8f-3f2f-9f1f-4a8f-f0f3f2f7a0ff\",\"tagName\":\"中国市场\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:43:58',58),(484,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:14:00\",\"isDisplay\":1,\"params\":{},\"tagId\":\"f15f9f7f-2f1f-8f0f-3a7f-f9f2f1f6affe\",\"tagName\":\"环球市场\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:07',58),(485,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:13:00\",\"isDisplay\":0,\"params\":{},\"tagId\":\"e14f8f6f-1f0f-7f9f-2a6f-f8f1f0f5aefd\",\"tagName\":\"热门产品\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:15',51),(486,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:12:00\",\"isDisplay\":0,\"params\":{},\"tagId\":\"d13f7f5f-0f9f-6f8f-1a5f-f7f0f9f4adfc\",\"tagName\":\"蓝筹股\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:21',65),(487,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:11:00\",\"isDisplay\":0,\"params\":{},\"tagId\":\"c12f6f4f-9f8f-5f7f-0a4f-f6f9f8f3acfb\",\"tagName\":\"科技行业\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:26',63),(488,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:10:00\",\"isDisplay\":0,\"params\":{},\"tagId\":\"b11f5f3f-8f7f-4f6f-9a3f-f5f8f7f2abfa\",\"tagName\":\"国际市场\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:36',46),(489,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:09:00\",\"isDisplay\":0,\"params\":{},\"tagId\":\"a10f4f2f-7f6f-3f5f-8a2f-f4f7f6f1aaf9\",\"tagName\":\"政府债券\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:41',82),(490,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:08:00\",\"isDisplay\":0,\"params\":{},\"tagId\":\"909f3f1f-6f5f-2f4f-7a1f-f3f6f5f0a9f8\",\"tagName\":\"定期收益\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:47',47),(491,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:07:00\",\"isDisplay\":0,\"params\":{},\"tagId\":\"808f2f0f-5f4f-1f3f-6a0f-f2f5f4f9a8f7\",\"tagName\":\"稳健收益\",\"tagType\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:44:51',151),(492,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','**************','XX XX','{\"allowFollowInvestment\":0,\"categoryId\":\"\",\"categoryName\":\"\",\"commissionRateLevel1\":3,\"commissionRateLevel2\":2,\"commissionRateLevel3\":1,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-19 09:49:12\",\"currency\":\"USDT\",\"custodian\":\"0\",\"custodianId\":\"0\",\"dailyRate\":0.25,\"description\":\"0\",\"endTime\":\"2025-05-19 09:49:09\",\"feeStructure\":\"0\",\"fundType\":\"0\",\"investmentArea\":0,\"investmentPeriod\":60,\"investmentStrategy\":\"0\",\"investmentUnit\":1,\"isHot\":0,\"issuerId\":\"0\",\"managerId\":\"0\",\"maxInvestmentUnits\":0,\"minInvestment\":10000,\"params\":{},\"productCode\":\"TSLA\",\"productId\":\"52bbb97d-aa1e-4bfe-945f-e7b667c96ac8\",\"productName\":\"特斯拉\",\"productStatus\":0,\"productType\":4,\"remainingScale\":100000000,\"repaymentType\":1,\"riskDisclosure\":\"0\",\"riskLevel\":1,\"soldUnits\":0,\"startTime\":\"2025-05-19 09:49:00\",\"summary\":\"0\",\"totalScale\":100000000,\"totalUnits\":50000,\"updatedAt\":\"2025-05-19 09:49:12\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,                          product_type,             investment_area,                          currency,             min_investment,             investment_unit,             total_scale,             remaining_scale,                          daily_rate,             investment_period,             max_investment_units,             total_units,             sold_units,             risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             contract_template_id,             start_time,             end_time,             description,             summary,                          custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot,             commission_rate_level1,             commission_rate_level2,             commission_rate_level3 )           values ( ?,             ?,             ?,                          ?,             ?,                          ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,    ','2025-05-19 09:49:12',6),(493,'产品标签',2,'com.ruoyi.web.controller.business.ProductTagController.edit()','PUT',1,'admin','研发部门','/system/productTag','**************','XX XX','{\"createdAt\":\"2025-04-17 10:13:00\",\"isDisplay\":1,\"params\":{},\"tagId\":\"e14f8f6f-1f0f-7f9f-2a6f-f8f1f0f5aefd\",\"tagName\":\"热门产品\",\"tagType\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 09:55:41',55),(494,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"jj\",\"categoryId\":\"jj\",\"categoryName\":\"进阶理财\",\"createdAt\":\"2025-05-13 16:37:57\",\"params\":{},\"sortOrder\":6,\"status\":1,\"updatedAt\":\"2025-05-19 10:08:45\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:08:46',88),(495,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"wj\",\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"createdAt\":\"2025-05-13 16:37:42\",\"params\":{},\"sortOrder\":5,\"status\":1,\"updatedAt\":\"2025-05-19 10:08:49\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:08:49',64),(496,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"jj\",\"categoryId\":\"jj\",\"categoryName\":\"进阶理财\",\"createdAt\":\"2025-05-13 16:37:57\",\"params\":{},\"sortOrder\":6,\"status\":0,\"updatedAt\":\"2025-05-19 10:10:28\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:10:29',51),(497,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"wj\",\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"createdAt\":\"2025-05-13 16:37:42\",\"params\":{},\"sortOrder\":5,\"status\":0,\"updatedAt\":\"2025-05-19 10:10:33\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:10:33',45),(498,'提现审核',2,'com.ruoyi.web.controller.business.WithdrawalAuditController.reject()','PUT',1,'admin','研发部门','/business/withdrawal/audit/reject/4e3d1658-84c1-4310-adbe-596f5e7d1c14','************','XX XX','{\"reason\":\"未完成\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/WalletTransactionMapper.xml]\n### The error may involve com.ruoyi.system.mapper.WalletTransactionMapper.insertWalletTransaction-Inline\n### The error occurred while setting parameters\n### SQL: insert into wallet_transaction          ( transaction_id,             wallet_id,             user_id,             transaction_type,             amount,                                       currency,             direction,             status,                                                    business_id,             business_type,             remark )           values ( ?,             ?,             ?,             ?,             ?,                                       ?,             ?,             ?,                                                    ?,             ?,             ? )\n### Cause: java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value\n; Field \'balance_before_change\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'balance_before_change\' doesn\'t have a default value','2025-05-19 10:32:55',39),(499,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','**************','XX XX','{\"currency\":\"EUR\",\"marketCloseTime\":\"16:30:00\",\"marketOpenTime\":\"08:00:00\",\"params\":{},\"regionCode\":\"EU\",\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"regionName\":\"欧洲\",\"sortOrder\":3,\"status\":2,\"timezone\":\"Europe/London\",\"tradingDays\":\"1,2,3,4,5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:35:24',71),(500,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','**************','XX XX','{\"currency\":\"HKD\",\"marketCloseTime\":\"16:00:00\",\"marketOpenTime\":\"09:30:00\",\"params\":{},\"regionCode\":\"HK\",\"regionId\":\"b6d45e54-6b25-453f-a983-c0688c4e51c4\",\"regionName\":\"中国\",\"sortOrder\":2,\"status\":1,\"timezone\":\"Asia/Hong_Kong\",\"tradingDays\":\"1,2,3,4,5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:35:54',44),(501,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','**************','XX XX','{\"currency\":\"JPY\",\"marketCloseTime\":\"15:00:00\",\"marketOpenTime\":\"09:00:00\",\"params\":{},\"regionCode\":\"JP\",\"regionId\":\"953e3b07-ce60-4acd-adca-96a682f84746\",\"regionName\":\"日本\",\"sortOrder\":4,\"status\":2,\"timezone\":\"Asia/Tokyo\",\"tradingDays\":\"1,2,3,4,5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:36:04',56),(502,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','**************','XX XX','{\"currency\":\"GBP\",\"marketCloseTime\":\"16:30:00\",\"marketOpenTime\":\"08:00:00\",\"params\":{},\"regionCode\":\"UK\",\"regionId\":\"1c13ca66-2a58-4000-9fc0-fe42db3e8373\",\"regionName\":\"英国\",\"sortOrder\":5,\"status\":2,\"timezone\":\"Europe/London\",\"tradingDays\":\"1,2,3,4,5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:36:08',52),(503,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','**************','XX XX','{\"currency\":\"AUD\",\"marketCloseTime\":\"16:00:00\",\"marketOpenTime\":\"10:00:00\",\"params\":{},\"regionCode\":\"AU\",\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"regionName\":\"澳大利亚\",\"sortOrder\":6,\"status\":2,\"timezone\":\"Australia/Sydney\",\"tradingDays\":\"1,2,3,4,5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:36:11',50),(504,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','**************','XX XX','{\"currency\":\"SGD\",\"marketCloseTime\":\"17:00:00\",\"marketOpenTime\":\"09:00:00\",\"params\":{},\"regionCode\":\"SG\",\"regionId\":\"33c6818f-7206-4597-9460-6b0c595ad4c8\",\"regionName\":\"新加坡\",\"sortOrder\":7,\"status\":2,\"timezone\":\"Asia/Singapore\",\"tradingDays\":\"1,2,3,4,5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:36:14',49),(505,'全球市场区域',2,'com.ruoyi.web.controller.business.MarketRegionController.edit()','PUT',1,'admin','研发部门','/system/market/region','**************','XX XX','{\"currency\":\"EUR\",\"marketCloseTime\":\"16:30:00\",\"marketOpenTime\":\"08:00:00\",\"params\":{},\"regionCode\":\"EU\",\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"regionName\":\"环球\",\"sortOrder\":3,\"status\":1,\"timezone\":\"Europe/London\",\"tradingDays\":\"1,2,3,4,5\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:36:25',68),(506,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.08,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-21 10:23:11\",\"currency\":\"USDT\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":0.08,\"description\":\"12\",\"endTime\":\"2025-04-24 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":1,\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":1,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":10,\"params\":{},\"productCode\":\"CBD12321\",\"productId\":\"1bfb788f-7692-4e26-a6f6-e498ea6e31a6\",\"productName\":\"国贸期货理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"15d704f2-a812-4d21-8c58-ac61e43d5e0c\",\"regionName\":\"澳大利亚\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":8,\"startTime\":\"2025-04-21 10:23:01\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-05-19 10:37:30\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:37:30',63),(507,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.08,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-21 10:23:11\",\"currency\":\"USDT\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":0.08,\"description\":\"12\",\"endTime\":\"2025-04-24 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":1,\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":1,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":10,\"params\":{},\"productCode\":\"CBD12321\",\"productId\":\"1bfb788f-7692-4e26-a6f6-e498ea6e31a6\",\"productName\":\"国贸期货理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"b6d45e54-6b25-453f-a983-c0688c4e51c4\",\"regionName\":\"澳大利亚\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":8,\"startTime\":\"2025-04-21 10:23:01\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-05-19 10:38:48\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:38:48',40),(508,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.1464,\"categoryId\":\"00535fba-a5f4-485d-8ff4-233013863b9e\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:13\",\"currency\":\"USDT\",\"custodian\":\"花旗银行\",\"custodianId\":\"52abb3c9-ab17-457a-82be-928f029fd665\",\"dailyRate\":0.08,\"description\":\"欧洲大盘股指数基金(EU)是由道富环球投资管理发行的一款欧洲市场投资产品，投资期限180天，预期年化收益率14.64%。\",\"endTime\":\"2025-04-30 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"fundType\":\"混合型\",\"investmentArea\":1,\"investmentPeriod\":180,\"investmentStrategy\":\"本产品主要投资于欧洲市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":374.8182,\"isHot\":0,\"issuerId\":\"e4602226-9115-4c76-a760-f98811cac471\",\"managerId\":\"6493d272-8591-419d-a5be-37c93da0518c\",\"maxInvestmentUnits\":0,\"minInvestment\":4013.925,\"params\":{},\"productCode\":\"GPEU017\",\"productId\":\"3064d1dd-688f-4aa4-819e-14eaf2f0d024\",\"productName\":\"欧洲大盘股指数基金(EU)\",\"productStatus\":1,\"productType\":4,\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"regionName\":\"环球\",\"remainingScale\":12,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为2级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":2,\"soldUnits\":1,\"startTime\":\"2025-03-30 10:53:18\",\"summary\":\"投资欧洲市场，期限180天，预期年化收益14.64%。\",\"totalScale\":12,\"totalUnits\":10000,\"updatedAt\":\"2025-05-19 10:40:03\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 10:40:03',53),(509,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.08,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-21 10:23:11\",\"currency\":\"USDT\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":0.08,\"description\":\"12\",\"endTime\":\"2025-04-24 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":1,\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":1,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":10,\"params\":{},\"productCode\":\"CBD12321\",\"productId\":\"1bfb788f-7692-4e26-a6f6-e498ea6e31a6\",\"productName\":\"国贸期货理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"b6d45e54-6b25-453f-a983-c0688c4e51c4\",\"regionName\":\"中国\",\"remainingScale\":10,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":8,\"startTime\":\"2025-04-21 10:23:01\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-05-19 11:16:31\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 11:16:31',89),(510,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.08,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-21 10:23:11\",\"currency\":\"USDT\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":0.08,\"description\":\"12\",\"endTime\":\"2025-04-24 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":1,\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":1,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":10,\"params\":{},\"productCode\":\"CBD12321\",\"productId\":\"1bfb788f-7692-4e26-a6f6-e498ea6e31a6\",\"productName\":\"国贸期货理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"b6d45e54-6b25-453f-a983-c0688c4e51c4\",\"regionName\":\"中国\",\"remainingScale\":0,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":8,\"startTime\":\"2025-04-21 10:23:01\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-05-19 11:16:49\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 11:16:49',48),(511,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.08,\"categoryId\":\"50df8e3f-e4e4-4b85-a332-18b063e1fdb1\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-21 10:23:11\",\"currency\":\"USDT\",\"custodian\":\"12\",\"custodianId\":\"12\",\"dailyRate\":0.08,\"description\":\"12\",\"endTime\":\"2025-04-24 00:00:00\",\"feeStructure\":\"12\",\"fundType\":\"12\",\"investmentArea\":1,\"investmentPeriod\":12,\"investmentStrategy\":\"12\",\"investmentUnit\":12,\"isHot\":1,\"issuerId\":\"12\",\"managerId\":\"12\",\"maxInvestmentUnits\":12,\"minInvestment\":10,\"params\":{},\"productCode\":\"CBD12321\",\"productId\":\"1bfb788f-7692-4e26-a6f6-e498ea6e31a6\",\"productName\":\"国贸期货理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"b6d45e54-6b25-453f-a983-c0688c4e51c4\",\"regionName\":\"中国\",\"remainingScale\":-82,\"repaymentType\":1,\"riskDisclosure\":\"12\",\"riskLevel\":1,\"soldUnits\":8,\"startTime\":\"2025-04-21 10:23:01\",\"summary\":\"12\",\"totalScale\":12,\"totalUnits\":12,\"updatedAt\":\"2025-05-19 11:17:09\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 11:17:09',40),(512,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/testPage\",\"createTime\":\"2025-04-29 17:36:07\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2023,\"menuName\":\"充值记录\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2030,\"path\":\"deposit\",\"perms\":\"system\",\"routeName\":\"deposit\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 14:33:38',68),(513,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/user/chongzhi\",\"createTime\":\"2025-04-29 17:36:07\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2023,\"menuName\":\"充值记录\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2030,\"path\":\"deposit\",\"perms\":\"system\",\"routeName\":\"deposit\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 14:34:27',56),(514,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','***********','内网IP','{\"children\":[],\"component\":\"pages/testPage\",\"createTime\":\"2025-05-01 14:03:11\",\"icon\":\"404\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2031,\"menuName\":\"积分明细\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2030,\"path\":\"pointsdetail\",\"perms\":\"system\",\"routeName\":\"pointsdetail\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 14:34:40',61),(515,'用户钱包充值',1,'com.ruoyi.web.controller.business.WalletRechargeController.rechargeWallet()','POST',1,'admin','研发部门','/h/wallet/recharge/add','************','XX XX','{\"amount\":100000,\"currency\":\"USDT\",\"remark\":\"充值\",\"userIdentifier\":\"0d4d0bf7-615d-41f8-b46e-1d43a2373c88\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 14:44:09',67),(516,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.1081,\"categoryId\":\"00535fba-a5f4-485d-8ff4-233013863b9e\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:12\",\"currency\":\"USDT\",\"custodian\":\"瑞银集团\",\"custodianId\":\"b1cd752b-f7cc-40bc-b4a8-8babce7bea70\",\"dailyRate\":0.08,\"description\":\"欧洲科技股ETF(EU)是由贝莱德基金管理发行的一款欧洲市场投资产品，投资期限180天，预期年化收益率10.81%。\",\"endTime\":\"2025-05-23 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"fundType\":\"QDII\",\"investmentArea\":1,\"investmentPeriod\":180,\"investmentStrategy\":\"本产品主要投资于欧洲市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":974.1878,\"isHot\":0,\"issuerId\":\"402409cd-c6f2-4ac7-86e8-4b3331a32f93\",\"managerId\":\"f6c259d9-042a-4df0-95af-fd1bcacfd045\",\"maxInvestmentUnits\":50,\"minInvestment\":769.1213,\"params\":{},\"productCode\":\"GPEU001\",\"productId\":\"04b3e3ae-5911-43c3-83b2-c7941f06a177\",\"productName\":\"欧洲科技股ETF(EU)\",\"productStatus\":1,\"productType\":3,\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"regionName\":\"环球\",\"remainingScale\":600239.4065,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为2级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":2,\"soldUnits\":7546,\"startTime\":\"2025-04-10 10:53:18\",\"summary\":\"投资欧洲市场，期限180天，预期年化收益10.81%。\",\"totalScale\":2455569.8306,\"totalUnits\":10000,\"updatedAt\":\"2025-05-19 14:48:49\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 14:48:49',65),(517,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":0.1081,\"categoryId\":\"00535fba-a5f4-485d-8ff4-233013863b9e\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-17 10:54:12\",\"currency\":\"USDT\",\"custodian\":\"瑞银集团\",\"custodianId\":\"b1cd752b-f7cc-40bc-b4a8-8babce7bea70\",\"dailyRate\":0.08,\"description\":\"欧洲科技股ETF(EU)是由贝莱德基金管理发行的一款欧洲市场投资产品，投资期限180天，预期年化收益率10.81%。\",\"endTime\":\"2025-05-23 10:53:18\",\"feeStructure\":\"管理费：0.5%/年；托管费：0.1%/年；认购费：0%；赎回费：0%。\",\"fundType\":\"QDII\",\"investmentArea\":1,\"investmentPeriod\":180,\"investmentStrategy\":\"本产品主要投资于欧洲市场的优质资产，通过专业的投资团队和严格的风险控制，为投资者提供稳定的收益。\",\"investmentUnit\":974.1878,\"isHot\":0,\"issuerId\":\"402409cd-c6f2-4ac7-86e8-4b3331a32f93\",\"managerId\":\"f6c259d9-042a-4df0-95af-fd1bcacfd045\",\"maxInvestmentUnits\":50,\"minInvestment\":769.1213,\"params\":{},\"productCode\":\"GPEU001\",\"productId\":\"04b3e3ae-5911-43c3-83b2-c7941f06a177\",\"productName\":\"欧洲科技股ETF(EU)\",\"productStatus\":1,\"productType\":3,\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"regionName\":\"环球\",\"remainingScale\":0,\"repaymentType\":1,\"riskDisclosure\":\"投资有风险，本产品风险等级为2级，投资者应充分了解产品特性和风险，根据自身风险承受能力谨慎投资。\",\"riskLevel\":2,\"soldUnits\":7549,\"startTime\":\"2025-04-10 10:53:18\",\"summary\":\"投资欧洲市场，期限180天，预期年化收益10.81%。\",\"totalScale\":2455569.8306,\"totalUnits\":10000,\"updatedAt\":\"2025-05-19 14:50:49\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 14:50:49',65),(518,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.0,\"categoryId\":\"\",\"categoryName\":\"\",\"commissionRateLevel1\":3,\"commissionRateLevel2\":2,\"commissionRateLevel3\":1,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-19 14:55:50\",\"currency\":\"USDT\",\"custodian\":\"不好看\",\"custodianId\":\"\",\"dailyRate\":1.0,\"description\":\"11\",\"endTime\":\"2025-05-19 00:00:00\",\"feeStructure\":\"\",\"fundType\":\"点击卡斯柯\",\"investmentArea\":0,\"investmentPeriod\":1,\"investmentStrategy\":\"\",\"isHot\":1,\"issuerId\":\"\",\"managerId\":\"\",\"maxInvestmentUnits\":0,\"minInvestment\":1000,\"params\":{},\"productCode\":\"SOL\",\"productId\":\"8877a9d5-4722-479c-a798-02f8547f259f\",\"productName\":\"测试\",\"productStatus\":1,\"productType\":1,\"regionId\":\"\",\"remainingScale\":60000,\"repaymentType\":2,\"riskDisclosure\":\"\",\"riskLevel\":2,\"soldUnits\":0,\"startTime\":\"2025-05-19 00:00:00\",\"summary\":\"111\",\"totalScale\":60000,\"totalUnits\":0,\"updatedAt\":\"2025-05-19 14:55:50\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,                          product_type,             investment_area,             region_id,             currency,             min_investment,                          total_scale,             remaining_scale,             annual_rate,             daily_rate,             investment_period,             max_investment_units,             total_units,             sold_units,             risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             contract_template_id,             start_time,             end_time,             description,             summary,                          custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot,             commission_rate_level1,             commission_rate_level2,             commission_rate_level3 )           values ( ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,           ','2025-05-19 14:55:50',5),(519,'产品信息',1,'com.ruoyi.web.controller.business.ProductInfoController.add()','POST',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":0,\"annualRate\":1.0,\"categoryId\":\"\",\"categoryName\":\"\",\"commissionRateLevel1\":3,\"commissionRateLevel2\":2,\"commissionRateLevel3\":1,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-05-19 15:01:34\",\"currency\":\"USDT\",\"custodian\":\"哈哈比较\",\"custodianId\":\"51651\",\"dailyRate\":1.0,\"description\":\"4653\",\"endTime\":\"2025-05-20 00:00:00\",\"feeStructure\":\"达到\",\"fundType\":\"阿金大\",\"investmentArea\":0,\"investmentPeriod\":1,\"investmentStrategy\":\"阿达客家话\",\"isHot\":0,\"issuerId\":\"321313\",\"managerId\":\"51313\",\"maxInvestmentUnits\":0,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CN1YT\",\"productId\":\"17e0e50d-a488-4b1f-9c5f-d23fa060dabc\",\"productName\":\"中国1年国债\",\"productStatus\":1,\"productType\":2,\"regionId\":\"\",\"remainingScale\":60000,\"repaymentType\":2,\"riskDisclosure\":\"大\",\"riskLevel\":1,\"soldUnits\":0,\"startTime\":\"2025-05-19 15:01:29\",\"summary\":\"1565153\",\"totalScale\":60000,\"totalUnits\":20000,\"updatedAt\":\"2025-05-19 15:01:34\"}',NULL,1,'\n### Error updating database.  Cause: java.sql.SQLException: Field \'category_id\' doesn\'t have a default value\n### The error may exist in URL [jar:file:/home/<USER>/ruoyi-admin/ruoyi-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.8.jar!/mapper/system/ProductInfoMapper.xml]\n### The error may involve com.ruoyi.system.mapper.ProductInfoMapper.insertProductInfo-Inline\n### The error occurred while setting parameters\n### SQL: insert into product_info          ( product_id,             product_code,             product_name,                          product_type,             investment_area,             region_id,             currency,             min_investment,                          total_scale,             remaining_scale,             annual_rate,             daily_rate,             investment_period,             max_investment_units,             total_units,             sold_units,             risk_level,             repayment_type,             product_status,             allow_follow_investment,             issuer_id,             manager_id,             custodian_id,             contract_template_id,             start_time,             end_time,             description,             summary,                          custodian,             fund_type,             investment_strategy,             risk_disclosure,             fee_structure,             is_hot,             commission_rate_level1,             commission_rate_level2,             commission_rate_level3 )           values ( ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,             ?,                          ?,             ?,             ?,           ','2025-05-19 15:01:34',4),(520,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":1,\"annualRate\":1.2,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-20 16:02:35\",\"currency\":\"USDT\",\"custodian\":\"123\",\"custodianId\":\"123\",\"dailyRate\":0.08,\"description\":\"123\",\"endTime\":\"2025-04-22 00:00:00\",\"feeStructure\":\"123\",\"fundType\":\"123\",\"investmentArea\":0,\"investmentPeriod\":1,\"investmentStrategy\":\"123\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"123\",\"managerId\":\"123\",\"maxInvestmentUnits\":12,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CQ110\",\"productId\":\"fb7356cd-2d37-4ca5-a0bb-b57c19e2dddf\",\"productName\":\"测试理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"1c13ca66-2a58-4000-9fc0-fe42db3e8373\",\"remainingScale\":1000000,\"repaymentType\":1,\"riskDisclosure\":\"123\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"123\",\"totalScale\":10000000,\"totalUnits\":431231,\"updatedAt\":\"2025-05-19 15:03:50\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 15:03:50',58),(521,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":1,\"annualRate\":1.2,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-20 16:02:35\",\"currency\":\"USDT\",\"custodian\":\"123\",\"custodianId\":\"123\",\"dailyRate\":0.08,\"description\":\"123\",\"endTime\":\"2025-04-22 00:00:00\",\"feeStructure\":\"123\",\"fundType\":\"123\",\"investmentArea\":1,\"investmentPeriod\":1,\"investmentStrategy\":\"123\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"123\",\"managerId\":\"123\",\"maxInvestmentUnits\":12,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CQ110\",\"productId\":\"fb7356cd-2d37-4ca5-a0bb-b57c19e2dddf\",\"productName\":\"测试理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"a64b1a26-babc-4938-9cfc-562a0d846bb3\",\"remainingScale\":1000000,\"repaymentType\":1,\"riskDisclosure\":\"123\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"123\",\"totalScale\":10000000,\"totalUnits\":431231,\"updatedAt\":\"2025-05-19 15:04:50\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 15:04:50',61),(522,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":1,\"annualRate\":1.2,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-20 16:02:35\",\"currency\":\"USDT\",\"custodian\":\"123\",\"custodianId\":\"123\",\"dailyRate\":0.08,\"description\":\"123\",\"endTime\":\"2025-04-22 00:00:00\",\"feeStructure\":\"123\",\"fundType\":\"123\",\"investmentArea\":1,\"investmentPeriod\":1,\"investmentStrategy\":\"123\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"123\",\"managerId\":\"123\",\"maxInvestmentUnits\":12,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CQ110\",\"productId\":\"fb7356cd-2d37-4ca5-a0bb-b57c19e2dddf\",\"productName\":\"测试理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"b6d45e54-6b25-453f-a983-c0688c4e51c4\",\"regionName\":\"美国\",\"remainingScale\":1000000,\"repaymentType\":1,\"riskDisclosure\":\"123\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"123\",\"totalScale\":10000000,\"totalUnits\":431231,\"updatedAt\":\"2025-05-19 15:06:37\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 15:06:38',67),(523,'产品信息',2,'com.ruoyi.web.controller.business.ProductInfoController.edit()','PUT',1,'admin','研发部门','/system/productInfo','************','XX XX','{\"allowFollowInvestment\":1,\"annualRate\":1.2,\"categoryId\":\"87c82a6f-5b29-4f66-82eb-16014ee4ec2c\",\"commissionRateLevel1\":0.1,\"commissionRateLevel2\":0.05,\"commissionRateLevel3\":0.01,\"contractTemplateId\":\"tmpl-bond-std-v1-uuid-12345\",\"createdAt\":\"2025-04-20 16:02:35\",\"currency\":\"USDT\",\"custodian\":\"123\",\"custodianId\":\"123\",\"dailyRate\":0.08,\"description\":\"123\",\"endTime\":\"2025-04-22 00:00:00\",\"feeStructure\":\"123\",\"fundType\":\"123\",\"investmentArea\":1,\"investmentPeriod\":1,\"investmentStrategy\":\"123\",\"investmentUnit\":1000,\"isHot\":0,\"issuerId\":\"123\",\"managerId\":\"123\",\"maxInvestmentUnits\":12,\"minInvestment\":1000,\"params\":{},\"productCode\":\"CQ110\",\"productId\":\"fb7356cd-2d37-4ca5-a0bb-b57c19e2dddf\",\"productName\":\"测试理财产品\",\"productStatus\":1,\"productType\":1,\"regionId\":\"4fd9638e-cee2-40af-b3a3-f15b1b9ffb29\",\"regionName\":\"中国\",\"remainingScale\":1000000,\"repaymentType\":1,\"riskDisclosure\":\"123\",\"riskLevel\":1,\"soldUnits\":1231,\"startTime\":\"2025-04-20 00:00:00\",\"summary\":\"123\",\"totalScale\":10000000,\"totalUnits\":431231,\"updatedAt\":\"2025-05-19 15:07:03\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 15:07:03',57),(524,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"jj\",\"categoryId\":\"jj\",\"categoryName\":\"进阶理财\",\"createdAt\":\"2025-05-13 16:37:57\",\"params\":{},\"sortOrder\":6,\"status\":1,\"updatedAt\":\"2025-05-19 17:14:05\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 17:14:05',48),(525,'产品分类',2,'com.ruoyi.web.controller.business.ProductCategoryController.edit()','PUT',1,'admin','研发部门','/system/productCategory','**************','XX XX','{\"categoryCode\":\"wj\",\"categoryId\":\"wj\",\"categoryName\":\"稳健理财\",\"createdAt\":\"2025-05-13 16:37:42\",\"params\":{},\"sortOrder\":5,\"status\":1,\"updatedAt\":\"2025-05-19 17:14:10\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-19 17:14:10',53);
/*!40000 ALTER TABLE `sys_oper_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_post`
--

DROP TABLE IF EXISTS `sys_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_post` (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='岗位信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_post`
--

LOCK TABLES `sys_post` WRITE;
/*!40000 ALTER TABLE `sys_post` DISABLE KEYS */;
INSERT INTO `sys_post` VALUES (1,'ceo','董事长',1,'0','admin','2025-04-09 14:22:16','',NULL,''),(2,'se','项目经理',2,'0','admin','2025-04-09 14:22:16','',NULL,''),(3,'hr','人力资源',3,'0','admin','2025-04-09 14:22:16','',NULL,''),(4,'user','普通员工',4,'0','admin','2025-04-09 14:22:16','',NULL,'');
/*!40000 ALTER TABLE `sys_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_role` (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'超级管理员','admin',1,'1',1,1,'0','0','admin','2025-04-09 14:22:17','',NULL,'超级管理员'),(2,'普通角色','common',2,'2',1,1,'0','2','admin','2025-04-09 14:22:17','',NULL,'普通角色'),(100,'系统管理员','system',0,'1',1,1,'0','0','admin','2025-04-13 13:56:19','','2025-04-16 17:43:43',NULL);
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_dept`
--

DROP TABLE IF EXISTS `sys_role_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_role_dept` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和部门关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_dept`
--

LOCK TABLES `sys_role_dept` WRITE;
/*!40000 ALTER TABLE `sys_role_dept` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (100,1),(100,2),(100,3),(100,100),(100,101),(100,102),(100,103),(100,104),(100,105),(100,106),(100,107),(100,108),(100,109),(100,110),(100,111),(100,112),(100,113),(100,114),(100,115),(100,116),(100,117),(100,500),(100,501),(100,1000),(100,1001),(100,1002),(100,1003),(100,1004),(100,1005),(100,1006),(100,1007),(100,1008),(100,1009),(100,1010),(100,1011),(100,1012),(100,1013),(100,1014),(100,1015),(100,1016),(100,1017),(100,1018),(100,1019),(100,1020),(100,1021),(100,1022),(100,1023),(100,1024),(100,1025),(100,1026),(100,1027),(100,1028),(100,1029),(100,1030),(100,1031),(100,1032),(100,1033),(100,1034),(100,1035),(100,1036),(100,1037),(100,1038),(100,1039),(100,1040),(100,1041),(100,1042),(100,1043),(100,1044),(100,1045),(100,1046),(100,1047),(100,1048),(100,1049),(100,1050),(100,1051),(100,1052),(100,1053),(100,1054),(100,1055),(100,1056),(100,1057),(100,1058),(100,1059),(100,1060),(100,2000),(100,2001),(100,2002),(100,2003),(100,2004),(100,2005),(100,2006),(100,2008),(100,2010),(100,2011),(100,2012),(100,2013),(100,2014),(100,2015),(100,2016);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,103,'admin','若依','00','<EMAIL>','15888888888','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','***********','2025-05-19 17:36:00','admin','2025-04-09 14:22:16','','2025-05-19 17:35:59','管理员'),(2,105,'ry','若依','00','<EMAIL>','15666666666','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','***********','2025-04-16 17:42:23','admin','2025-04-09 14:22:16','','2025-04-16 17:42:23','测试员'),(100,100,'lisi','李四','00','','','0','','$2a$10$FO7a0rn3lbrJgBvgFmBXVe8UYwK3QrE9Rf0ie0pyc3TDhu0eL57kC','0','0','***********','2025-04-16 17:47:08','admin','2025-04-16 17:44:18','','2025-04-16 17:47:08',NULL);
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_post`
--

DROP TABLE IF EXISTS `sys_user_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user_post` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户与岗位关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_post`
--

LOCK TABLES `sys_user_post` WRITE;
/*!40000 ALTER TABLE `sys_user_post` DISABLE KEYS */;
INSERT INTO `sys_user_post` VALUES (1,1);
/*!40000 ALTER TABLE `sys_user_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sys_user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户和角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,1),(100,100);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'wise_wealth_ry'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-19 18:50:33
