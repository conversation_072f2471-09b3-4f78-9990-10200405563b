# MySQL 数据库备份、传输与恢复脚本使用说明

本项目包含三个独立的Shell脚本，用于自动化MySQL数据库的备份、将备份文件传输到远程服务器，以及在远程服务器上恢复数据库。

## 脚本文件

1.  `backup_db.sh`:
    *   **功能**: 备份指定的源MySQL数据库。
    *   **输出**: 成功后，在标准输出打印本地备份文件的完整路径。
2.  `transfer_backup.sh`:
    *   **功能**: 将本地的数据库备份文件通过SCP传输到指定的目标服务器。
    *   **输入**: 需要一个参数，即本地备份文件的完整路径。
    *   **输出**: 成功后，在标准输出打印备份文件在目标服务器上的完整路径。
3.  `restore_db.sh`:
    *   **功能**: 在目标服务器上使用指定的备份文件恢复MySQL数据库。
    *   **输入**: 需要一个参数，即备份文件在目标服务器上的完整路径。
    *   **输出**: 操作过程中的信息和最终结果。

## 先决条件

1.  **工具安装**:
    *   **运行脚本的机器**: 需要安装 `mysqldump`, `scp`, `ssh`。
    *   **目标服务器**: 需要安装 `mysql` 客户端。
2.  **SSH密钥认证 (推荐)**:
    *   `transfer_backup.sh` 和 `restore_db.sh` 脚本假定已经配置了从运行脚本的机器到目标服务器 (`************`，用户 `root`) 的SSH免密登录（基于密钥的认证）。
    *   如果未配置，您可能需要在脚本中修改 `scp` 和 `ssh` 命令以包含密码（不推荐）或手动输入密码（如果脚本未配置为完全非交互式）。当前脚本设计为通过SSH密钥认证进行非交互式操作。
3.  **网络连接**:
    *   运行脚本的机器必须能够访问源数据库服务器 (`*************`) 的MySQL端口 (默认为3306)。
    *   运行脚本的机器必须能够通过SSH连接到目标服务器 (`************`)。
    *   目标服务器必须能够访问其本地或指定的目标数据库主机 (`************`) 的MySQL端口。

## 配置

脚本内部包含以下预设配置，**通常情况下，这些值已根据您的环境预先设定好。如果环境变化，您可能需要直接修改脚本文件中的这些变量**：

*   **`backup_db.sh`**:
    *   `SOURCE_DB_HOST`: "*************"
    *   `SOURCE_DB_USER`: "root"
    *   `SOURCE_DB_PASS`: "123456" (警告：密码硬编码不安全)
    *   `SOURCE_DB_NAME`: "wise_wealth_ry"
    *   `BACKUP_DIR_LOCAL`: 脚本将备份文件保存在其自身所在的目录 (例如 `/Users/<USER>/Documents/work/stock/admin/huatimesnews-admin/service/publish/`)

*   **`transfer_backup.sh`**:
    *   `TARGET_SERVER_IP`: "************"
    *   `TARGET_SERVER_USER`: "root"
    *   `TARGET_REMOTE_PATH_BASE`: "/tmp" (目标服务器上存放备份的基础路径)

*   **`restore_db.sh`**:
    *   `TARGET_SERVER_IP`: "************" (用于SSH连接)
    *   `TARGET_SERVER_USER`: "root" (用于SSH连接)
    *   `TARGET_DB_HOST`: "************" (目标数据库主机)
    *   `TARGET_DB_USER`: "root" (目标数据库用户)
    *   `TARGET_DB_PASS`: "mysql123456" (警告：密码硬编码不安全)
    *   `TARGET_DB_NAME`: "wise_wealth_ry"

**安全警告**:
*   脚本中的数据库密码 (`SOURCE_DB_PASS`, `TARGET_DB_PASS`) 是硬编码的。这是一种不安全的做法。在生产环境中，请考虑使用更安全的方法，如MySQL的 `~/.my.cnf` 配置文件。
*   `scp` 和 `ssh` 命令使用了 `-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null` 选项以避免主机密钥检查提示，这在自动化脚本中很常见，但会带来中间人攻击的安全风险。在生产环境中，建议提前将目标服务器的公钥添加到运行脚本机器的 `known_hosts` 文件中。

## 使用步骤

1.  **导航到脚本目录**:
    ```bash
    cd /Users/<USER>/Documents/work/stock/admin/huatimesnews-admin/service/publish/
    ```

2.  **赋予脚本执行权限** (仅需执行一次):
    ```bash
    chmod +x backup_db.sh
    chmod +x transfer_backup.sh
    chmod +x restore_db.sh
    ```

3.  **执行备份 (`backup_db.sh`)**:
    此脚本会备份源数据库，并将本地备份文件的路径打印到标准输出。
    ```bash
    LOCAL_BACKUP_FILE=$(./backup_db.sh)
    ```
    检查是否成功：
    ```bash
    echo $LOCAL_BACKUP_FILE
    ```
    如果 `$LOCAL_BACKUP_FILE` 为空或脚本报错，请检查错误信息。

4.  **执行传输 (`transfer_backup.sh`)**:
    此脚本将上一步生成的本地备份文件传输到目标服务器，并将文件在目标服务器上的路径打印到标准输出。
    ```bash
    # 确保 LOCAL_BACKUP_FILE 变量已正确设置
    if [ -z "$LOCAL_BACKUP_FILE" ]; then
        echo "错误：本地备份文件路径未设置或备份失败。"
    else
        REMOTE_BACKUP_FILE=$(./transfer_backup.sh "${LOCAL_BACKUP_FILE}")
        echo $REMOTE_BACKUP_FILE
    fi
    ```
    如果 `$REMOTE_BACKUP_FILE` 为空或脚本报错，请检查错误信息和SSH连接。

5.  **执行恢复 (`restore_db.sh`)**:
    此脚本使用上一步传输到目标服务器的备份文件来恢复数据库。
    ```bash
    # 确保 REMOTE_BACKUP_FILE 变量已正确设置
    if [ -z "$REMOTE_BACKUP_FILE" ]; then
        echo "错误：远程备份文件路径未设置或传输失败。"
    else
        ./restore_db.sh "${REMOTE_BACKUP_FILE}"
    fi
    ```
    脚本执行完毕后，会输出恢复过程的信息。检查是否有错误报告。

## 注意事项

*   这些脚本会覆盖目标数据库 `wise_wealth_ry`（如果已存在）。在执行恢复操作前，请确保您了解这一点，并已备份目标数据库（如果需要）。
*   `backup_db.sh` 会在脚本所在的目录创建备份文件。 `restore_db.sh` 脚本在成功恢复后会尝试删除目标服务器上的备份文件 (`/home/<USER>/ruoyi-admin/` 目录下)。本地的备份文件 (`publish/` 目录下) 需要您在确认整个流程成功后手动清理，或者您可以根据需要自行在调用流程中添加清理逻辑。
*   如果任何步骤失败，请仔细阅读脚本输出的错误信息进行排查。
