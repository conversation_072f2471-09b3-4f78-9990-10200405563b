#!/bin/bash

# 脚本3：在目标服务器上直接恢复MySQL数据库
# 此脚本应直接在目标数据库服务器上运行。

# --- 配置 - 目标数据库 ---
# 这些是目标MySQL服务器的连接详细信息。
# 如果脚本与MySQL服务器在同一台机器上运行，TARGET_DB_HOST 通常是 "localhost" 或 "127.0.0.1"。
TARGET_DB_HOST="************" # 或 "localhost" 如果MySQL与此脚本在同一服务器上
TARGET_DB_USER="root"
TARGET_DB_PASS="mysql123456" # 警告：在脚本中存储密码不安全。
TARGET_DB_NAME="wise_wealth_ry"

# --- 辅助函数 ---
log_error() {
    echo "[错误] $(date +"%Y-%m-%d %H:%M:%S") - $1" >&2
}

log_info() {
    echo "[信息] $(date +"%Y-%m-%d %H:%M:%S") - $1"
}

# --- 主脚本 ---

# 检查输入参数
if [ -z "$1" ]; then
    log_error "用法: $0 <path_to_sql_backup_file_on_this_server>"
    log_error "例如: $0 /home/<USER>/ruoyi-admin/wise_wealth_ry_backup_YYYYMMDD_HHMMSS.sql"
    exit 1
fi

SQL_BACKUP_FILE_PATH="$1"

# 检查必要的工具
command -v mysql >/dev/null 2>&1 || { log_error "mysql 客户端未安装。请先安装。"; exit 1; }

# 检查备份文件是否存在
if [ ! -f "${SQL_BACKUP_FILE_PATH}" ]; then
    log_error "SQL备份文件 '${SQL_BACKUP_FILE_PATH}' 未在本服务器上找到。"
    exit 1
fi

log_info "准备从文件 '${SQL_BACKUP_FILE_PATH}' 恢复数据库 '${TARGET_DB_NAME}'..."

# 1. 创建数据库（如果不存在）
log_info "正在检查并创建数据库 '${TARGET_DB_NAME}' (如果不存在)..."
# 注意mysql命令中对反引号的转义
mysql -h "${TARGET_DB_HOST}" -u "${TARGET_DB_USER}" -p"${TARGET_DB_PASS}" -e "CREATE DATABASE IF NOT EXISTS \`${TARGET_DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if [ $? -ne 0 ]; then
    log_error "创建数据库 '${TARGET_DB_NAME}' 失败。"
    exit 1
fi
log_info "数据库 '${TARGET_DB_NAME}' 检查/创建完毕。"

# 2. 从备份文件恢复数据库
log_info "正在将数据从 '${SQL_BACKUP_FILE_PATH}' 导入到数据库 '${TARGET_DB_NAME}'..."
mysql -h "${TARGET_DB_HOST}" -u "${TARGET_DB_USER}" -p"${TARGET_DB_PASS}" "${TARGET_DB_NAME}" < "${SQL_BACKUP_FILE_PATH}"

if [ $? -ne 0 ]; then
    log_error "从 '${SQL_BACKUP_FILE_PATH}' 恢复数据库 '${TARGET_DB_NAME}' 失败。"
    # 此时不应删除备份文件，以便手动检查
    exit 1
fi
log_info "数据库 '${TARGET_DB_NAME}' 成功恢复。"

# 3. 清理本地备份文件 (可选，根据需要决定是否保留)
log_info "正在清理备份文件: ${SQL_BACKUP_FILE_PATH}..."
rm -f "${SQL_BACKUP_FILE_PATH}"
if [ $? -eq 0 ]; then
    log_info "备份文件 '${SQL_BACKUP_FILE_PATH}' 清理成功。"
else
    log_error "未能清理备份文件 '${SQL_BACKUP_FILE_PATH}'。请手动删除。"
    # 即使清理失败，恢复也已成功，所以不退出脚本
fi

log_info "数据库恢复过程成功完成！"
exit 0