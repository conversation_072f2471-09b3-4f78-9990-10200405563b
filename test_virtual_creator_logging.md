# 虚拟上级创建和同步测试

## 测试目的
1. 验证 `autoGenerateRelationships` 方法的详细日志输出
2. 验证 `createVirtualParentForGroup` 方法的ruoyi系统同步功能

## 测试前准备

### 1. 确保有孤儿组数据
```sql
-- 查看当前孤儿组
SELECT DISTINCT mp.group_name 
FROM monthly_performance mp
LEFT JOIN group_creator_relation gcr ON mp.group_name = gcr.group_name
WHERE gcr.creator_id IS NULL OR gcr.creator_id NOT IN (SELECT id FROM creators);
```

### 2. 查看现有虚拟上级
```sql
-- 查看现有虚拟creator
SELECT id, nickname, handle, parent_id, remark, created_at
FROM creators 
WHERE id LIKE '888%'
ORDER BY created_at DESC;
```

## 测试步骤

### 测试1：自动生成关系详细日志
```bash
# 调用自动生成关系接口
POST /h/historical/auto-generate-relationships
Content-Type: application/json
Authorization: Bearer [your-token]

{}
```

**期望日志输出：**
```
=== 开始自动为孤儿组创建虚拟上级并建立层级关系 ===
操作人：admin
第一步：开始识别孤儿组...
识别到孤儿组数量：X，组名列表：[组名1, 组名2, ...]
第二步：开始为孤儿组创建虚拟上级...
正在处理孤儿组：组名1
准备为组 组名1 创建虚拟上级，昵称：组名1-虚拟
开始为组 组名1 创建新的虚拟上级...
✅ 成功为组 组名1 创建虚拟上级，ID：888XXXXXXXXX，昵称：组名1-虚拟
第二步完成，创建的虚拟上级数量：X，跳过的组数量：X
新创建的虚拟上级ID列表：[888XXXXXXXXX, ...]
第三步：开始建立虚拟上级层级关系...
开始建立虚拟上级层级关系，虚拟creator数量：X
...
第三步完成：虚拟上级层级关系建立完成
第四步：开始自动重建关系...
第四步完成：关系重建完成，结果：{...}
=== 自动生成关系完成 ===
```

### 测试2：单独创建虚拟上级并验证ruoyi同步
```bash
# 调用创建虚拟上级接口
POST /h/historical/virtual-parent
Content-Type: application/json
Authorization: Bearer [your-token]

{
  "groupName": "TestGroup"
}
```

**期望日志输出：**
```
开始为组 TestGroup 创建虚拟上级，昵称：TestGroup-虚拟，备注：虚拟上级
生成虚拟上级ID：888XXXXXXXXX
准备插入虚拟creator到数据库：ID=888XXXXXXXXX, 昵称=TestGroup-虚拟, Handle=virtual_888XXXXXXXXX
✅ 虚拟creator插入数据库成功
准备建立组关系：组名=TestGroup, CreatorID=888XXXXXXXXX
✅ 组关系建立成功
开始同步虚拟上级到ruoyi用户系统...
调用UserSyncService同步虚拟上级，creator数量：1
=== 开始独立用户同步服务 ===
独立服务的数据源注解: @DataSource(DataSourceType.MASTER)
开始同步 1 个主播到ruoyi用户系统...
准备同步主播: virtual_888XXXXXXXXX (ID: 888XXXXXXXXX)
✅ 新增用户同步成功：virtual_888XXXXXXXXX
Ruoyi用户系统同步完成：新增 1 个，更新 0 个，跳过 0 个。
=== 独立用户同步服务结束 ===
✅ 虚拟上级同步到ruoyi系统成功
✅ 虚拟上级创建完成，返回ID：888XXXXXXXXX
```

### 测试3：验证ruoyi系统中的用户
```sql
-- 查看ruoyi系统中的虚拟用户
SELECT u.user_id, u.user_name, u.nick_name, u.status, u.create_time
FROM sys_user u
WHERE u.user_name LIKE '888%'
ORDER BY u.create_time DESC;
```

### 测试4：清理重复虚拟上级
```bash
# 调用清理接口
POST /h/historical/cleanup-duplicate-virtual-creators
Content-Type: application/json
Authorization: Bearer [your-token]

{}
```

**期望响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "cleanedCreators": X,
    "cleanedCreatorIds": ["888XXXXXXXXX", ...],
    "message": "清理完成，删除了 X 个重复的虚拟上级"
  }
}
```

## 验证点

### 1. 日志完整性
- [ ] 自动生成关系的每个步骤都有详细日志
- [ ] 能区分为哪个组创建了虚拟上级
- [ ] 层级关系建立过程有详细记录
- [ ] 错误情况有详细日志

### 2. ruoyi系统同步
- [ ] 虚拟上级成功同步到ruoyi系统
- [ ] 用户名格式正确（虚拟creator的ID）
- [ ] 昵称正确（组名-虚拟）
- [ ] 用户状态正常

### 3. 数据一致性
- [ ] creators表中有虚拟上级记录
- [ ] group_creator_relation表中有组关系
- [ ] sys_user表中有对应用户记录
- [ ] 没有重复的虚拟上级

## 故障排查

### 如果ruoyi同步失败
1. 检查日志中的具体错误信息
2. 确认ISysUserService是否正常注入
3. 检查数据源配置是否正确
4. 验证用户权限是否足够

### 如果出现重复虚拟上级
1. 调用清理接口：`POST /h/historical/cleanup-duplicate-virtual-creators`
2. 检查数据库约束
3. 验证修复后的代码是否生效

## 性能监控
- 关注创建虚拟上级的响应时间
- 监控ruoyi系统同步的成功率
- 观察大批量处理时的内存使用情况 