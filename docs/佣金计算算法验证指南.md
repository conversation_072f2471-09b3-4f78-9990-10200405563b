# 佣金计算算法验证指南

## 🎯 验证目标

通过详细记录**计算条件 + 算法规则 + 计算过程 + 输出结果**，确保每个主播的每一步计算都符合预期，算法执行与业务规则完全匹配。

## 📊 增强的算法上下文记录系统

### 核心理念
**每个计算步骤都记录：输入条件 + 算法描述 + 计算规则 + 执行过程 + 输出结果**

### 记录内容结构
```json
{
  "step": "计算步骤名称",
  "algorithm": "算法描述和公式",
  "inputData": "输入数据",
  "conditions": "计算条件和规则",
  "result": "计算结果",
  "timestamp": 1640995200000
}
```

## 🔍 具体验证场景

### 1. 动态门槛计算验证

**算法记录示例：**
```json
{
  "step": "动态门槛计算",
  "algorithm": {
    "formula": "max(上月收入 * (1 + 上浮比例), 基础门槛)",
    "step1": "上月收入 * (1 + 上浮比例) = 45000 * (1 + 0.1) = 49500",
    "step2": "max(49500, 50000) = 50000"
  },
  "inputData": {
    "lastMonthDiamonds": 45000,
    "thresholdIncreaseRate": 0.1,
    "baseDiamondThreshold": 50000
  },
  "conditions": {
    "rule": "动态门槛不能低于基础门槛",
    "baseThreshold": 50000
  },
  "result": {
    "calculatedByFormula": 49500,
    "finalThreshold": 50000,
    "appliedRule": "使用基础门槛"
  }
}
```

**验证要点：**
- ✅ 输入数据是否正确（上月收入、上浮比例、基础门槛）
- ✅ 计算公式是否按预期执行
- ✅ 规则应用是否正确（取较大值）
- ✅ 最终结果是否符合预期

### 2. 等级分成计算验证

**算法记录示例：**
```json
{
  "step": "等级分成计算",
  "algorithm": {
    "formula": "等级分成 = (个人钻石 + 团队钻石) * 等级分成比例",
    "calculation": "(50000 + 80000) * 0.15 = 19500"
  },
  "inputData": {
    "personalDiamonds": 50000,
    "teamDiamonds": 80000,
    "totalDiamonds": 130000,
    "achievedLevel": "金牌"
  },
  "conditions": {
    "levelRules": {
      "银牌": {"minDirectDownlines": 5, "commissionRate": 0.1},
      "金牌": {"minDirectDownlines": 10, "commissionRate": 0.15},
      "钻石": {"minDirectDownlines": 20, "commissionRate": 0.2}
    },
    "appliedRule": "等级金牌的分成比例为0.15"
  },
  "result": {
    "commissionRate": 0.15,
    "levelCommissionDiamonds": 19500.0000
  }
}
```

**验证要点：**
- ✅ 个人和团队钻石数据是否准确
- ✅ 等级评定是否正确
- ✅ 分成比例是否与配置规则匹配
- ✅ 计算公式执行是否正确

### 3. 多级提成计算验证

**算法记录示例：**
```json
{
  "step": "多级提成计算",
  "algorithm": {
    "formula": "多级提成 = ∑(各层级下级收入 * 对应层级提成比例)",
    "calculation": "L1(120000 * 0.05 = 6000) + L2(80000 * 0.03 = 2400) + L3(40000 * 0.02 = 800) = 9200"
  },
  "inputData": {
    "depthIncomes": {
      "1": 120000,
      "2": 80000,
      "3": 40000
    }
  },
  "conditions": {
    "multilevelRules": {
      "L1": 0.05,
      "L2": 0.03,
      "L3": 0.02
    }
  },
  "result": {
    "depthCommissions": {
      "1": 6000.0000,
      "2": 2400.0000,
      "3": 800.0000
    },
    "totalMultilevelCommission": 9200.0000
  }
}
```

**验证要点：**
- ✅ 各层级下级收入统计是否准确
- ✅ 多级提成比例是否与配置匹配
- ✅ 每层级计算是否正确
- ✅ 总提成汇总是否准确

### 4. 拉新奖励计算验证

**算法记录示例：**
```json
{
  "step": "拉新奖励计算",
  "algorithm": {
    "formula": "根据新招募人数匹配最高档位奖励"
  },
  "inputData": {
    "newRecruitsCount": 8,
    "calculationPeriod": "2025-06"
  },
  "conditions": {
    "recruitmentRules": [
      {"minNewRecruits": 15, "bonusUsd": 500},
      {"minNewRecruits": 10, "bonusUsd": 300},
      {"minNewRecruits": 5, "bonusUsd": 150}
    ],
    "appliedRule": "新招募8人 >= 5人，获得奖励150USD"
  },
  "result": {
    "bonusUsd": 150,
    "qualified": true
  }
}
```

**验证要点：**
- ✅ 新招募人数统计是否准确
- ✅ 规则匹配是否正确（按人数从高到低匹配）
- ✅ 奖励金额是否与规则配置一致

## 🛠️ 验证工具和API

### 1. 单个分销员算法验证
```http
GET /business/commission/validation/distributor/{creatorId}/{dataMonth}
```

**返回示例：**
```json
{
  "success": true,
  "creatorId": 12345,
  "dataMonth": "2025-06",
  "calculatedResult": {
    "personalDiamonds": 50000,
    "dynamicThreshold": 50000,
    "thresholdMet": true,
    "achievedLevel": "金牌",
    "levelCommissionDiamonds": 19500,
    "multilevelCommissionDiamonds": 9200,
    "recruitmentBonusUsd": 150,
    "finalPayoutUsd": 1631.50
  },
  "algorithmLogs": [
    {
      "step": "动态门槛计算",
      "algorithm": "...",
      "inputData": "...",
      "conditions": "...",
      "result": "..."
    }
  ],
  "validation": {
    "isValid": true,
    "differences": []
  }
}
```

### 2. 算法日志查询
```http
GET /business/commission/validation/logs/{creatorId}/{dataMonth}
```

### 3. 月度计算验证报告
```http
GET /business/commission/validation/report/{dataMonth}
```

## 📋 验证检查清单

### 前置条件验证
- [ ] 计算配置是否正确加载（预算、汇率、门槛等）
- [ ] 业务规则是否正确应用（等级规则、多级规则、拉新规则）
- [ ] 数据完整性检查（业绩数据、主播关系数据）

### 算法执行验证
- [ ] 每个计算步骤的输入数据是否准确
- [ ] 算法公式是否按预期执行
- [ ] 业务规则是否正确应用
- [ ] 计算结果是否符合预期

### 数据一致性验证
- [ ] 计算结果与数据库记录是否一致
- [ ] 收入构成明细是否匹配
- [ ] 汇总数据是否正确

### 可重现性验证
- [ ] 使用相同输入条件能否得到相同结果
- [ ] 算法日志是否完整记录计算过程
- [ ] 能否基于日志重现计算过程

## 🎯 最佳实践

### 1. 定期验证
- 每次月度计算后，抽查10-20个分销员进行详细验证
- 对收入异常变化的分销员进行重点验证
- 定期检查算法日志的完整性和准确性

### 2. 异常处理
- 当验证发现差异时，立即检查算法日志
- 对比不同计算环境的结果一致性
- 记录并修复发现的算法或数据问题

### 3. 持续改进
- 根据验证发现的问题优化算法记录
- 增加更详细的中间步骤记录
- 完善验证工具和报告功能

## 🔗 相关文档
- [佣金计算验证使用指南](./佣金计算验证使用指南.md)
- [API接口文档](./API接口文档.md)
- [故障排查指南](./故障排查指南.md)

---

通过这套算法上下文记录系统，您可以：
1. **完全重现**每个分销员的计算过程
2. **精确验证**每个算法步骤的正确性
3. **快速定位**计算结果与预期不符的原因
4. **持续改进**算法的准确性和可靠性 