# 佣金计算历史日志标记实现方案

## 概述

当进行重新计算时，需要标记之前的计算日志为"历史状态"，以便区分新旧计算结果，确保审计追踪的完整性。

## 实现方案对比

### 方案一：不修改表结构（推荐 ✅）

**原理**: 通过日志类型和批次ID来标记历史日志

**优点**:
- 不需要修改现有表结构
- 实现简单，兼容性好
- 保持完整的审计追踪
- 支持多次重新计算的历史追溯

**实现方式**:

#### 1. 标记流程
```java
// 1. 查询当月所有现有日志
List<CommissionCalculationLogs> existingLogs = getLogsByMonth(dataMonth);

// 2. 生成批次ID
String batchId = "RECALC_" + System.currentTimeMillis();

// 3. 记录重新计算开始事件
logCalculationEvent(dataMonth, "RECALCULATION_START", null, "INFO", 
    "开始第X次重新计算，标记N条历史日志", 
    "{\"batchId\":\"RECALC_123456\",\"historicalLogsCount\":500}");

// 4. 为每个历史日志创建标记记录
for (CommissionCalculationLogs log : existingLogs) {
    logCalculationEvent(dataMonth, "HISTORICAL_LOG_MARK", log.getCreatorId(), "INFO",
        "标记历史日志: " + log.getCalculationType(),
        "{\"originalLogId\":123,\"batchId\":\"RECALC_123456\"}");
}
```

#### 2. 查询逻辑

**查询最新有效日志**:
```sql
SELECT * FROM commission_calculation_logs 
WHERE data_month = '2024-01-01'
  AND created_at >= (
    SELECT MAX(created_at) FROM commission_calculation_logs 
    WHERE data_month = '2024-01-01' 
      AND calculation_type = 'RECALCULATION_START'
  )
  AND calculation_type != 'HISTORICAL_LOG_MARK'
ORDER BY created_at;
```

**查询历史日志**:
```sql
SELECT * FROM commission_calculation_logs 
WHERE data_month = '2024-01-01'
  AND calculation_type = 'HISTORICAL_LOG_MARK'
ORDER BY created_at;
```

#### 3. 日志类型定义

| 日志类型 | 用途 | 示例 |
|---------|------|------|
| `RECALCULATION_START` | 标记重新计算开始 | 开始第2次重新计算 |
| `HISTORICAL_LOG_MARK` | 标记某个日志为历史 | 标记历史日志: DISTRIBUTOR_CALCULATION |
| `DATA_BACKUP` | 记录数据备份 | 备份现有计算数据 |
| `DATA_CLEANUP` | 记录数据清理 | 清理现有计算数据完成 |

### 方案二：添加状态字段

**需要修改表结构**:
```sql
ALTER TABLE commission_calculation_logs 
ADD COLUMN log_status ENUM('ACTIVE', 'HISTORICAL', 'ARCHIVED') DEFAULT 'ACTIVE';

ALTER TABLE commission_calculation_logs 
ADD COLUMN recalculation_batch_id VARCHAR(50) NULL;

ADD INDEX idx_status_batch (data_month, log_status, recalculation_batch_id);
```

**实现方式**:
```java
// 标记历史日志
UPDATE commission_calculation_logs 
SET log_status = 'HISTORICAL', 
    recalculation_batch_id = 'RECALC_123456'
WHERE data_month = '2024-01-01' 
  AND log_status = 'ACTIVE';
```

**优点**:
- 查询效率更高
- 数据结构更清晰

**缺点**:
- 需要修改表结构
- 可能影响现有功能
- 增加维护复杂度

### 方案三：日志分表

**需要创建历史表**:
```sql
CREATE TABLE commission_calculation_logs_history 
LIKE commission_calculation_logs;

ALTER TABLE commission_calculation_logs_history 
ADD COLUMN archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN recalculation_batch_id VARCHAR(50);
```

**实现方式**:
```java
// 1. 将当前日志移动到历史表
INSERT INTO commission_calculation_logs_history 
SELECT *, NOW(), 'RECALC_123456' FROM commission_calculation_logs 
WHERE data_month = '2024-01-01';

// 2. 删除当前表中的日志
DELETE FROM commission_calculation_logs WHERE data_month = '2024-01-01';
```

## 推荐实现：方案一详细说明

### 核心设计思路

1. **批次概念**: 每次重新计算分配一个唯一的批次ID
2. **标记机制**: 为每个被废弃的日志创建一个"历史标记"记录
3. **时间基准**: 用最后一次 `RECALCULATION_START` 的时间作为分界线
4. **完整审计**: 保留所有历史操作的完整记录

### 实际使用示例

#### 首次计算
```
2024-01-15 10:00:00 | CALCULATION_START | 开始执行月度佣金计算
2024-01-15 10:01:00 | DISTRIBUTOR_CALCULATION | 计算分销员123收入
2024-01-15 10:05:00 | CALCULATION_COMPLETE | 月度佣金计算完成
```

#### 第一次重新计算
```
2024-01-16 14:00:00 | RECALCULATION_START | 开始第1次重新计算，标记3条历史日志 {batchId:"RECALC_1642334400000"}
2024-01-16 14:00:01 | HISTORICAL_LOG_MARK | 标记历史日志: CALCULATION_START {originalLogId:1, batchId:"RECALC_1642334400000"}
2024-01-16 14:00:02 | HISTORICAL_LOG_MARK | 标记历史日志: DISTRIBUTOR_CALCULATION {originalLogId:2, batchId:"RECALC_1642334400000"}
2024-01-16 14:00:03 | HISTORICAL_LOG_MARK | 标记历史日志: CALCULATION_COMPLETE {originalLogId:3, batchId:"RECALC_1642334400000"}
2024-01-16 14:00:05 | DATA_BACKUP | 备份现有计算数据
2024-01-16 14:00:10 | DATA_CLEANUP | 清理现有计算数据完成
2024-01-16 14:01:00 | CALCULATION_START | 开始执行月度佣金计算
2024-01-16 14:02:00 | DISTRIBUTOR_CALCULATION | 计算分销员123收入
2024-01-16 14:06:00 | CALCULATION_COMPLETE | 月度佣金计算完成
```

### 查询功能实现

#### 1. 查询最新有效日志
```java
@Override
public List<CommissionCalculationLogs> getLatestValidCalculationLogs(Date dataMonth) {
    List<CommissionCalculationLogs> allLogs = getAllLogs(dataMonth);
    
    // 找到最后一次重新计算的时间
    Date lastRecalcTime = allLogs.stream()
        .filter(log -> "RECALCULATION_START".equals(log.getCalculationType()))
        .map(CommissionCalculationLogs::getCreatedAt)
        .max(Date::compareTo)
        .orElse(new Date(0));
    
    // 返回最后一次重新计算之后的日志（排除历史标记）
    return allLogs.stream()
        .filter(log -> log.getCreatedAt().compareTo(lastRecalcTime) >= 0)
        .filter(log -> !"HISTORICAL_LOG_MARK".equals(log.getCalculationType()))
        .collect(Collectors.toList());
}
```

#### 2. 查询历史日志
```java
@Override
public Map<String, Object> getHistoricalCalculationLogs(Date dataMonth, String batchId) {
    List<CommissionCalculationLogs> historicalMarks = getAllLogs(dataMonth).stream()
        .filter(log -> "HISTORICAL_LOG_MARK".equals(log.getCalculationType()))
        .filter(log -> batchId == null || log.getDetails().contains(batchId))
        .collect(Collectors.toList());
    
    // 按批次分组
    Map<String, List<CommissionCalculationLogs>> logsByBatch = historicalMarks.stream()
        .collect(Collectors.groupingBy(log -> extractBatchId(log.getDetails())));
    
    return Map.of(
        "historicalLogs", historicalMarks,
        "logsByBatch", logsByBatch
    );
}
```

#### 3. 查询重新计算历史
```java
@Override
public Map<String, Object> getRecalculationHistory(Date dataMonth) {
    List<CommissionCalculationLogs> recalcStarts = getAllLogs(dataMonth).stream()
        .filter(log -> "RECALCULATION_START".equals(log.getCalculationType()))
        .sorted(Comparator.comparing(CommissionCalculationLogs::getCreatedAt))
        .collect(Collectors.toList());
    
    List<Map<String, Object>> history = new ArrayList<>();
    for (int i = 0; i < recalcStarts.size(); i++) {
        CommissionCalculationLogs log = recalcStarts.get(i);
        Map<String, Object> info = new HashMap<>();
        info.put("recalculationNumber", i + 1);
        info.put("startTime", log.getCreatedAt());
        info.put("batchId", extractBatchId(log.getDetails()));
        info.put("message", log.getMessage());
        history.add(info);
    }
    
    return Map.of(
        "recalculationHistory", history,
        "totalRecalculations", recalcStarts.size()
    );
}
```

### API接口说明

#### 1. 查询最新计算日志
```http
GET /business/commission/validation/logs-latest/{dataMonth}
```

**响应示例**:
```json
{
  "success": true,
  "dataMonth": "2024-01",
  "logs": [
    {
      "id": 15,
      "calculationType": "CALCULATION_START",
      "message": "开始执行月度佣金计算",
      "createdAt": "2024-01-16 14:01:00"
    }
  ]
}
```

#### 2. 查询历史日志
```http
GET /business/commission/validation/logs-historical/{dataMonth}?batchId=RECALC_1642334400000
```

#### 3. 查询重新计算历史
```http
GET /business/commission/validation/recalculation-history/{dataMonth}
```

### 优势总结

1. **无需表结构变更**: 基于现有表结构实现
2. **完整审计追踪**: 保留所有操作的完整历史
3. **清晰的时间线**: 通过批次ID和时间戳清晰区分不同版本
4. **灵活查询**: 支持按批次、按时间、按类型等多种查询方式
5. **向后兼容**: 不影响现有功能

### 注意事项

1. **性能考虑**: 随着重新计算次数增加，日志量会快速增长，建议定期归档历史日志
2. **存储成本**: 每次重新计算都会产生额外的标记日志，需要考虑存储成本
3. **查询优化**: 建议在 `data_month + calculation_type + created_at` 上创建复合索引

### 数据库索引建议

```sql
-- 优化按月份和类型查询
CREATE INDEX idx_month_type_time ON commission_calculation_logs 
(data_month, calculation_type, created_at);

-- 优化按创建时间查询
CREATE INDEX idx_created_at ON commission_calculation_logs (created_at);
```

## 总结

**推荐使用方案一**，因为它在不修改表结构的前提下，提供了完整的历史追踪功能，实现简单且易于维护。通过批次ID和时间基准的设计，可以清晰地区分不同版本的计算结果，满足审计和调试需求。 