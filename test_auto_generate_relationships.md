# 自动生成关系功能测试

## 接口信息
- **接口地址**: `POST /h/historical/auto-generate-relationships`
- **权限**: `h:historical`
- **功能**: 自动为孤儿组创建虚拟上级并建立层级关系

## 测试场景

### 1. 正常场景测试
**前提条件**:
- 数据库中存在一些组数据（monthly_performance表）
- 这些组在group_creator_relation表中没有对应的creator_id

**请求示例**:
```http
POST /h/historical/auto-generate-relationships
Content-Type: application/json
Authorization: Bearer [token]

{}
```

**预期响应**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "orphanGroups": 5,
    "createdVirtualCreators": 5,
    "skippedGroups": 0,
    "relationshipLevels": 3,
    "rebuildResult": {
      "success": true,
      "updatedCreators": 15,
      "rebuiltRelationships": 45
    },
    "virtualCreatorIds": [
      88812345678901234,
      88812345678901235,
      88812345678901236,
      88812345678901237,
      88812345678901238
    ]
  }
}
```

### 2. 无孤儿组场景
**前提条件**:
- 所有组都已经有对应的creator关系

**预期响应**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "orphanGroups": 0,
    "createdVirtualCreators": 0,
    "skippedGroups": 0,
    "message": "没有发现孤儿组，无需处理"
  }
}
```

### 3. 部分组已有关系场景
**前提条件**:
- 5个组中有2个已有creator关系，3个没有

**预期响应**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "orphanGroups": 3,
    "createdVirtualCreators": 3,
    "skippedGroups": 2,
    "relationshipLevels": 3,
    "rebuildResult": {
      "success": true,
      "updatedCreators": 12,
      "rebuiltRelationships": 36
    },
    "virtualCreatorIds": [...]
  }
}
```

## 数据验证

### 1. 虚拟Creator验证
执行接口后，检查 `creators` 表：
```sql
SELECT * FROM creators WHERE id LIKE '888%';
```

应该能看到新创建的虚拟creator：
- ID以888开头
- nickname包含"虚拟上级"
- remark为"系统自动生成的虚拟上级"
- handle格式为"virtual_[ID]"

### 2. 组关系验证
检查 `group_creator_relation` 表：
```sql
SELECT gcr.*, c.nickname 
FROM group_creator_relation gcr
LEFT JOIN creators c ON gcr.creator_id = c.id
WHERE c.id LIKE '888%';
```

应该能看到孤儿组现在都有了对应的虚拟creator。

### 3. 层级关系验证
检查虚拟creator之间的层级关系：
```sql
SELECT c1.id as child_id, c1.nickname as child_name,
       c2.id as parent_id, c2.nickname as parent_name
FROM creators c1
LEFT JOIN creators c2 ON c1.parent_id = c2.id
WHERE c1.id LIKE '888%'
ORDER BY c1.id;
```

应该能看到虚拟creator形成了树状层级结构。

### 4. 闭包表验证
检查 `creator_relationships` 表是否正确重建：
```sql
SELECT COUNT(*) as total_relationships 
FROM creator_relationships;

SELECT depth, COUNT(*) as count_per_depth
FROM creator_relationships 
GROUP BY depth 
ORDER BY depth;
```

## 错误场景测试

### 1. 权限不足
**请求**: 使用没有 `h:historical` 权限的token

**预期响应**:
```json
{
  "code": 401,
  "msg": "没有权限访问"
}
```

### 2. 系统异常
可以通过暂时断开数据库连接等方式模拟系统异常。

**预期响应**:
```json
{
  "code": 500,
  "msg": "自动生成关系失败: [具体错误信息]"
}
```

## 性能测试

### 大数据量测试
- 准备1000个孤儿组
- 执行接口，观察处理时间
- 预期在合理时间内完成（<30秒）

### 幂等性测试
- 连续多次调用同一接口
- 第二次及以后调用应该返回"没有发现孤儿组"
- 不应该重复创建虚拟creator

## 回滚测试

如果需要清理测试数据：
```sql
-- 删除虚拟creator的关系
DELETE FROM creator_relationships 
WHERE ancestor_id LIKE '888%' OR descendant_id LIKE '888%';

-- 删除虚拟creator
DELETE FROM creators WHERE id LIKE '888%';

-- 删除虚拟creator的组关系
DELETE FROM group_creator_relation WHERE creator_id LIKE '888%';
``` 