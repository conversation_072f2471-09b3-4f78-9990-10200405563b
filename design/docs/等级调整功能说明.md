# 分销员等级调整功能说明

## 功能概述

本功能实现了"防守"体系的月度业绩考核与等级调整机制，确保分销员等级的"含金量"。分销员在通过"拉新"（进攻）获得高等级后，必须通过持续的、健康的团队业绩表现来维持（"防守"）该等级。

## 核心特性

### 1. 月度业绩考核算法
- **考核指标**: 个人钻石收入 + 直属一级（depth=1）团队钻石总收入
- **考核周期**: 滚动窗口，基于过去三个月的业绩表现
- **核心算法**: 月度环比增长率的平均值
- **保级公式**: (近1月MGR + 近2月MGR + 近3月MGR) / 3 ≥ 15%

### 2. 风险控制规则
- **除以零处理**: 上月业绩为0，本月业绩>0时，按100%增长计算
- **增长率上限**: 单月环比增长率上限为300%，防止数据泡沫
- **参数化配置**: 所有数值均可通过commission_settings表配置

### 3. 保护期机制
- **新手保护期**: 新注册用户前4个月完全豁免"防守"考核
- **升级保护期**: 通过"进攻"升级的用户获得2个月等级保护期

### 4. 降级机制
- **触发条件**: 未处于保护期且业绩考核未达标
- **执行操作**: 基础等级降低一级
- **降级下限**: 已处于最低等级则不再降级

## 技术实现

### 1. 核心类文件

#### Service接口
- `IDistributorLevelAdjustmentService`: 等级调整核心业务接口

#### Service实现
- `DistributorLevelAdjustmentServiceImpl`: 等级调整业务逻辑实现

#### Controller
- `DistributorLevelAdjustmentController`: 等级调整管理控制器

### 2. 数据库变更

#### Creator表新增字段
```sql
ALTER TABLE `creators`
    ADD COLUMN `current_distributor_level_id` INT(10) UNSIGNED DEFAULT NULL 
        COMMENT '当前基础等级ID, 关联commission_level_rules.id, 由"防守"体系更新',
    ADD COLUMN `level_updated_at` TIMESTAMP NULL DEFAULT NULL 
        COMMENT '基础等级最后更新时间, 用于判断升级保护期',
    ADD COLUMN `first_active_month` DATE DEFAULT NULL 
        COMMENT '首次活跃月份，用于新人识别',
    ADD COLUMN `recruited_by` BIGINT(20) UNSIGNED DEFAULT NULL 
        COMMENT '招募人ID';
```

#### 配置参数
```sql
INSERT INTO `commission_settings` (`setting_key`, `setting_value`, `description`) VALUES
('retention_target_avg_growth_rate', '0.15', '业绩保级-目标平均增长率(15%)'),
('retention_growth_from_zero', '1.00', '业绩保级-从零增长的定义值(100%)'),
('retention_growth_rate_cap', '3.00', '业绩保级-单月增长率上限(300%)'),
('new_user_grace_period_months', '4', '新手保护期月数'),
('upgrade_protection_period_months', '2', '升级保护期月数');
```

### 3. 集成到计算流程

等级调整作为"前置任务二"集成到月度佣金计算流程中：

```java
// 前置任务一：执行拉新资格判定
executeNewRecruitQualificationTask(dataMonth);

// 前置任务二：执行等级调整（防守体系）
executeDistributorLevelAdjustmentTask(dataMonth);
```

## API接口

### 1. 执行月度等级调整
- **URL**: `POST /business/level/adjustment/execute/{dataMonth}`
- **权限**: `h:level_adjustment`
- **说明**: 执行指定月份的等级调整任务

### 2. 获取月度调整报告
- **URL**: `GET /business/level/adjustment/report/{dataMonth}`
- **权限**: `h:level_adjustment`
- **说明**: 获取等级调整统计报告

### 3. 验证等级调整计算
- **URL**: `GET /business/level/adjustment/validate/{creatorId}/{dataMonth}`
- **权限**: `h:level_adjustment`
- **说明**: 验证特定分销员的等级调整计算过程

### 4. 检查保护期状态
- **URL**: `GET /business/level/adjustment/protection/{creatorId}/{dataMonth}`
- **权限**: `h:level_adjustment`
- **说明**: 检查分销员的保护期状态

### 5. 获取配置参数
- **URL**: `GET /business/level/adjustment/config`
- **权限**: `h:level_adjustment`
- **说明**: 获取等级调整相关配置参数

## 配置参数说明

| 参数键 | 默认值 | 说明 |
|--------|--------|------|
| retention_target_avg_growth_rate | 0.15 | 目标平均增长率(15%) |
| retention_growth_from_zero | 1.00 | 从零增长的定义值(100%) |
| retention_growth_rate_cap | 3.00 | 单月增长率上限(300%) |
| new_user_grace_period_months | 4 | 新手保护期月数 |
| upgrade_protection_period_months | 2 | 升级保护期月数 |

## 使用说明

1. **初始化配置**: 执行`insert_level_adjustment_config.sql`脚本插入配置参数
2. **数据库更新**: 确保creators表包含新增字段
3. **权限配置**: 为相关用户分配`h:level_adjustment`权限
4. **集成测试**: 通过API接口验证功能正常运行

## 注意事项

1. 等级调整会在每月的佣金计算流程中自动执行
2. 保护期机制确保新用户和新晋升用户不会立即面临降级风险
3. 所有配置参数都可以通过commission_settings表动态调整
4. 建议在生产环境使用前进行充分测试
