<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<data_structure>
@database.sql
</data_structure>

<requirements>
@PRD.md
</requirements>



# 问题

- 现在要导入的EXCEL表的数据结构和 monthly_performance 表结构还有不对应的情况， 需要根据EXCEL表的数据来修改 monthly_performance 表的结构。 
<EXCEL>
Data Month	Creator ID	Creator nickname	Handle	Creator Network manager	Group	Group manager	Is violative creators	The creator was Rookie at the time of first joining	Diamonds	Valid days(d)	LIVE duration(h)	Estimated bonus	Estimated bonus - Revenue scale task task	Estimated bonus - New Creator Network task task	Estimated bonus - Rookie milestone 1 retention bonus task	Estimated bonus - Rookie milestone 2 bonus task	Estimated bonus - Rookie half-milestone bonus task	Estimated bonus - Rookie milestone 1 bonus task	Estimated bonus - Activeness task task
202505	7505039326170988545	Camilo Ruiz	camiloruiz916	Alen	Jason	Bruce	FALSE	Yes	0	0	0.516666666666667	0	0	0	Did not participate	0	0	0	0
202505	7493835287303405584	🦁𝑮𝑹𝑰𝑪𝑬𝑳𝑫𝑨♡🍒	griceldagalindoo	Salvador	Jason	Bruce	FALSE	Yes	28	4	5.66305555555556	0.0362	0.0279	0.0083	Did not participate	0	0	0	0
202505	7507973721085444112	Garcia_Rhlm👹	garcia_rhlm	Salvador	Jason	Bruce	FALSE	Yes	0	0	0.302777777777778	0	0	0	Did not participate	0	0	0	0
</EXCEL>

# 需求
- 现在要做的是把历史数据导入到现有的系统中，但老数据没有对应的上下级关系，或上级的用户数据， 所以需要添加对应的API来手动指定group和上级的关系， creator和上级的关系。 最后通过重建关系的方式来生成整个的上下级关系。 
- 要导入的EXCEL表的数据中， 无法确定上下级关系，所以使用 group_creator_relation 来手工指定关系， 导入时需要处理已经有的重复的相同的老数据的问题， 要保证这条数据的唯一性
- API: 新建导入历史真实数据接口，因为历史数据中没能约定的上下级关系，  只导入到， monthly_performance， raw_imports， creators。 
- API: 列表显示 组和creators 的关系，能显示出未指定creatorID的组, SQL先从monthly_performance里分组后，查出所有的组， 再列表显示组及对应的关系。 
- API： 指定组对应的上级
- API： 指定虚拟上级。 有时组对应的上级是不存在的， 需要建立一个虚拟的creators，ID 可以是88800000000000000000 前3位固定成888， 后面的可以随机或生成一个唯一的值。 nickname 和 remark 都需要描述这是一个虚拟的上级， 然后指定组对应这个上级为这个虚拟上及的ID。 
- API： 指定creators对应的上级，手动指定 creators 的parent_id 为指定的主播
- API: 根据指定导入的数据表monthly_performance中对应的group_name 及 group_creator_relation 的对应关系， 重新生成 creators 的对应的关系 parent_id 的字段。 再根据对应的parent_id生成， 对应的关系表creator_relationships。 
- 根据我的描述， 先了解需求，逐步思考如何完成相关的实现API工作， 根据思考后的结果， 列出需要实现的API接口， 及具体实施细节
- 逐一实现对应的接口


关联的数据库结构 
<TABLE>
CREATE TABLE `group_creator_relation` (
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '组名',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建者ID, 关联creators表的主键',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`group_name`),
  UNIQUE KEY `uk_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组名与CreatorID关系表';

CREATE TABLE `creators` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主播的唯一ID (Creator ID)',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '直接上级主播的ID, 顶级为NULL',
  `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主播昵称',
  `handle` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主播的Handle',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `first_active_month` date DEFAULT NULL COMMENT '首次活跃月份，用于新人识别',
  `recruited_by` bigint(20) unsigned DEFAULT NULL COMMENT '招募人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_handle` (`handle`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_recruited_by` (`recruited_by`),
  KEY `idx_first_active` (`first_active_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播信息与直接关系表';

CREATE TABLE `creator_relationships` (
  `ancestor_id` bigint(20) unsigned NOT NULL COMMENT '祖先用户ID (关联 creators.id)',
  `descendant_id` bigint(20) unsigned NOT NULL COMMENT '后代用户ID (关联 creators.id)',
  `depth` int(11) NOT NULL COMMENT '层级深度 (0表示用户自身)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ancestor_id`,`descendant_id`),
  KEY `idx_cr_descendant_depth` (`descendant_id`,`depth`),
  KEY `idx_cr_ancestor_depth` (`ancestor_id`,`depth`),
  CONSTRAINT `fk_rel_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rel_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主播层级关系闭包表';

</TABLE>

---

# 详细方案设计

## 数据字段对应关系

### Excel到数据库字段映射
```
Excel字段 -> 数据库字段 -> 目标表
Data Month -> data_month -> monthly_performance  
Creator ID -> creator_id -> monthly_performance + creators.id
Creator nickname -> nickname -> creators
Handle -> handle -> creators
Creator Network manager -> creator_network_manager -> monthly_performance (需新增字段)
Group -> group_name -> monthly_performance
Group manager -> group_manager -> monthly_performance
Is violative creators -> is_violative -> monthly_performance
The creator was Rookie -> is_rookie -> monthly_performance
Diamonds -> diamonds -> monthly_performance
Valid days(d) -> valid_days -> monthly_performance
LIVE duration(h) -> live_duration_hours -> monthly_performance
Estimated bonus -> bonus_estimated -> monthly_performance
Estimated bonus - Revenue scale task -> bonus_revenue_scale -> monthly_performance
Estimated bonus - New Creator Network task -> bonus_new_creator_network -> monthly_performance
Estimated bonus - Rookie milestone 1 retention bonus -> bonus_rookie_m1_retention -> monthly_performance
Estimated bonus - Rookie milestone 2 bonus -> bonus_rookie_m2 -> monthly_performance
Estimated bonus - Rookie half-milestone bonus -> bonus_rookie_half_milestone -> monthly_performance
Estimated bonus - Rookie milestone 1 bonus -> bonus_rookie_m1 -> monthly_performance
Estimated bonus - Activeness task -> bonus_activeness -> monthly_performance
```

## 需要补充的数据库字段

### monthly_performance表需新增字段
```sql
ALTER TABLE `monthly_performance` 
ADD COLUMN `creator_network_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创作者网络管理员';
```

## API接口设计

### 1. 历史数据导入模块 - HistoricalDataController

#### 1.1 导入Excel历史数据
```
POST /api/h/historical/import
功能：上传Excel文件，导入历史数据到raw_imports、creators、monthly_performance
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
参数：MultipartFile file
处理逻辑：
- 解析Excel文件
- 数据格式验证
- 去重处理（基于creator_id + data_month）
- 分别插入三个目标表
- 返回导入结果统计
```

#### 1.2 查看导入历史记录
```
GET /api/h/historical/imports
功能：分页查询导入记录
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
参数：分页参数 + 时间范围筛选
```

### 2. 组关系管理模块 - GroupRelationController

#### 2.1 组关系列表
```
GET /api/h/group/relations
功能：显示所有组与creators的关系，包含未指定creatorID的组
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
SQL逻辑：
SELECT DISTINCT 
    mp.group_name,
    gcr.creator_id,
    c.nickname,
    c.handle,
    gcr.created_at
FROM monthly_performance mp
LEFT JOIN group_creator_relation gcr ON mp.group_name = gcr.group_name
LEFT JOIN creators c ON gcr.creator_id = c.id
ORDER BY mp.group_name, gcr.created_at DESC
```

#### 2.2 指定组对应的上级
```
POST /api/h/group/assign-parent
功能：为指定组分配上级creator
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
参数：groupName, creatorId
处理逻辑：
- 验证creator存在
- 插入或更新group_creator_relation表
- 确保唯一性约束
```

#### 2.3 为组创建虚拟上级
```
POST /api/h/group/create-virtual-parent
功能：创建虚拟上级并分配给组
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
参数：groupName, virtualNickname, remark
处理逻辑：
- 生成虚拟ID：888 + 13位唯一数字
- 创建虚拟creator记录
- 建立组关系
```

### 3. 创建者关系管理模块 - CreatorRelationController

#### 3.1 指定创建者的上级
```
POST /api/h/creator/assign-parent
功能：手动指定creator的parent_id
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
参数：creatorId, parentId
处理逻辑：
- 验证双方存在
- 检查循环关系
- 更新creators.parent_id
```

#### 3.2 批量重建关系
```
POST /api/h/creator/rebuild-relationships
功能：根据group_creator_relation重建整个关系树
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
处理逻辑：
1. 清理现有creator_relationships数据
2. 根据group_creator_relation更新creators.parent_id
3. 重新生成creator_relationships闭包表
4. 返回重建统计
```

#### 3.3 查看关系树
```
GET /api/h/creator/relationship-tree
功能：查看完整的创建者关系树
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
参数：可选的根节点creatorId
```

### 4. 虚拟上级管理模块

#### 4.1 虚拟上级列表
```
GET /api/h/virtual/creators
功能：查看所有虚拟创建者
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
筛选条件：id LIKE '888%'
```

#### 4.2 编辑虚拟上级
```
PUT /api/h/virtual/creators/{id}
功能：修改虚拟创建者信息
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
参数：nickname, remark
```

## 实施细节

### 数据导入策略
1. **三阶段导入**：
   - 阶段1：原始数据 -> raw_imports（保留所有原始信息）
   - 阶段2：清洗数据 -> creators（去重，补充信息）
   - 阶段3：业务数据 -> monthly_performance（关联creator_id）

2. **去重逻辑**：
   - creators表：按handle去重
   - monthly_performance表：按(creator_id, data_month)去重

3. **数据验证**：
   - Creator ID格式验证
   - 日期格式验证  
   - 数值字段范围验证

### 关系重建算法
```
1. 遍历所有group_creator_relation记录
2. 获取该组下所有creators（从monthly_performance）
3. 将这些creators的parent_id设置为组对应的creator_id
4. 递归构建creator_relationships闭包表：
   - 插入自身关系（depth=0）
   - 插入直接关系（depth=1）
   - 递归插入间接关系（depth>1）
```

### 虚拟ID生成规则
```
格式：888 + 17位数字
生成逻辑：888 + 时间戳(13位) + 随机数(4位)
确保全局唯一性
```

### 错误处理机制
1. **导入错误**：记录错误行，继续处理其他数据
2. **关系冲突**：检测并报告循环依赖
3. **数据完整性**：外键约束验证
4. **回滚机制**：关键操作支持事务回滚

### 性能优化
1. **批量插入**：使用MyBatis批量操作
2. **索引利用**：充分利用现有索引
3. **分页处理**：大数据量分页加载
4. **缓存策略**：关系树结果缓存

## 控制器权限设计
- 统一权限标识：`h:historical`
- 简化权限管理，不区分增删改查
- 符合要求的二级权限设计

## 数据源配置
- Service层使用：`@DataSource(value = DataSourceType.SLAVE)`
- 确保使用正确的逻辑数据库：streamer_distribution_system

---

## 自动生成关系功能设计

### 背景需求
导入历史数据后，由于历史数据缺失关系信息，需要自动生成虚拟上级并建立合理的层级关系结构。

### 功能说明
为了避免手动为每个孤儿组创建虚拟上级的繁琐工作，系统需要提供自动生成关系的功能。

### 实现逻辑

#### 1. 自动创建虚拟上级API
```
POST /api/h/historical/auto-generate-relationships
功能：自动为孤儿组创建虚拟上级并建立层级关系
权限：@PreAuthorize("@ss.hasPermi('h:historical')")
```

#### 2. 处理步骤

**第一步：识别孤儿组**
- 查询`group_creator_relation`表，找出没有对应`creator_id`的组
- SQL逻辑：
```sql
SELECT DISTINCT mp.group_name 
FROM monthly_performance mp
LEFT JOIN group_creator_relation gcr ON mp.group_name = gcr.group_name
WHERE gcr.creator_id IS NULL OR gcr.creator_id NOT IN (SELECT id FROM creators)
```

**第二步：创建虚拟上级**
- 为每个孤儿组检查是否已存在虚拟上级
- 如果不存在，创建新的虚拟creator（888开头ID）
- 更新`group_creator_relation`表建立关系

**第三步：建立虚拟上级层级关系**
- 收集所有新创建的虚拟creator
- 将它们组织成3级层级关系，避免平级结构
- 层级组织规则：
  - 按组名字母顺序排序
  - 每2-3个虚拟creator为一组，形成树状结构
  - 示例：如果创建了A、B、C、D、E五个虚拟上级
    ```
    A (顶级)
    ├── B (2级)
    │   └── C (3级)
    └── D (2级)
        └── E (3级)
    ```

**第四步：自动重建关系**
- 创建完虚拟上级层级后，自动调用重建关系功能
- 调用 `POST /api/h/creator/rebuild-relationships`中的service接口
- 完成整个creator关系树的重建

### 分组策略
1. **虚拟上级分组**：将虚拟creator按照一定规则分组建立层级
2. **层级深度**：最多3层深度，避免过深的层级结构
3. **负载均衡**：尽量让每个顶级虚拟creator下的子级数量均衡

### 完整流程
1. **数据导入** → 历史数据进入系统
2. **手动关系建立** → 为部分组指定已存在的creator
3. **自动生成关系** → 为剩余孤儿组创建虚拟上级并建立层级
4. **重建关系** → 自动调用重建功能，完成整个关系树构建
5. **验证结果** → 检查关系树的完整性和正确性

### 返回结果
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "orphanGroups": 15,           // 处理的孤儿组数量
    "createdVirtualCreators": 8,  // 创建的虚拟上级数量
    "skippedGroups": 2,           // 已有关系跳过的组数量
    "relationshipLevels": 3,      // 建立的层级深度
    "rebuildResult": {            // 重建关系结果
      "totalCreators": 156,
      "totalRelationships": 892,
      "executionTime": "2.3s"
    }
  }
}
```

### 注意事项
1. **幂等性**：多次调用不会重复创建虚拟上级
2. **事务性**：整个过程在事务中执行，确保数据一致性
3. **可回滚**：提供回滚机制，出错时可以恢复到执行前状态
4. **日志记录**：记录详细的操作日志，便于问题排查