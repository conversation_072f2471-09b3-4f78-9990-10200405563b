<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<requirements>
# 需求： 

二、 任务背景与目标 (Task Background & Objective)
我需要你为一个“分销员业绩系统”开发一个支持团队结构下钻分析的后端API接口。这个接口的核心功能是，当给定一个团队领导的ID (creatorId) 时，返回其直属下级（L1）团队的聚合信息，以及L1中每个成员的详细数据摘要。

这个API将被前端重复调用以实现层层下钻的导航效果。例如，前端首先调用此接口获取根用户(Root)的L1团队；当用户点击L1中的某个成员时，前端会再次调用同一个接口，但传入被点击成员的ID，从而展示其L2团队的情况。

三、 API接口定义 (API Endpoint Definition)
HTTP方法: GET

URL: /api/team-structure/{creatorId}

路径参数:

{creatorId}: Long - 需要查询其团队的领导的用户ID。

查询参数:

month: String - 可选的查询月份，格式为 YYYY-MM (例如 2024-12)。用于计算月度相关的指标，如环比增长。

功能描述: 获取指定分销员 (creatorId) 的直属团队（L1）的聚合信息，以及L1中每个成员的详细摘要。

四、 核心业务逻辑与数据来源 (Core Business Logic & Data Sources)
你需要整合来自多个数据表的信息，计算出以下JSON结构中定义的每一个字段。

1. viewedLeader (被查看的领导信息)
creatorId, name: 被查看者的ID和昵称。

来源: 从 creators 表中，根据 :creatorId 查询。

2. levelSummary (L1团队整体聚合数据)
memberCount: L1团队的成员总数。

来源: creator_relationships 表。

SQL逻辑: SELECT COUNT(descendant_id) FROM creator_relationships WHERE ancestor_id = :creatorId AND depth = 1;

totalIncomeUSD: L1团队所有成员及其各自团队的累计总收入之和。

来源: 依赖于下面 members 列表中每个成员的 teamIncomeUSD 的计算结果，然后进行加总。

avgMomGrowth: L1团队所有成员的月收入环比增长率的平均值。

来源: 依赖于下面 members 列表中每个成员的 momGrowth 的计算结果，然后求平均值。

totalAnchorCount: L1团队所有成员及其各自团队的总人数之和。

来源: creator_relationships 表。

逻辑: SELECT COUNT(descendant_id) FROM creator_relationships WHERE ancestor_id = :creatorId AND depth > 0; (这是被查看者整个团队的人数，与UI中的“71 Anchors”对应)

3. members (L1团队成员列表)
逻辑: 首先，获取L1成员列表 (SELECT descendant_id FROM creator_relationships WHERE ancestor_id = :creatorId AND depth = 1)。然后，遍历这个列表，为每一个member计算以下字段：

creatorId, name: 成员的基本信息，从 creators 表查询。

level: 成员在 :month 的等级，从 commission_distributor_qualifications 表查询。

momGrowth: 该成员的月收入环比增长。查询 commission_payouts 中该成员在 :month 和 :month - 1 的收入，然后计算。

subordinateCount: 他自己的直属下级人数。需要为他再次查询 creator_relationships 表：SELECT COUNT(*) WHERE ancestor_id = member.creatorId AND depth = 1;

teamIncomeUSD: 他和他整个团队的累计总收入。这是一个计算量巨大的递归求和操作。

teamAnchorCount: 他和他整个团队的总人数。这是一个递归计数操作。

diamondMetric: 一个业务待定的钻石指标。请暂时返回0或null。

五、 数据库表结构参考 (Database Schema Reference)
SQL

-- 用户核心信息与层级关系表
CREATE TABLE `creators` ( ... );
-- 闭包表，此API的核心
CREATE TABLE `creator_relationships` (
  `ancestor_id` bigint(20) unsigned NOT NULL,
  `descendant_id` bigint(20) unsigned NOT NULL,
  `depth` int(11) NOT NULL,
   PRIMARY KEY (`ancestor_id`,`descendant_id`)
) COMMENT='主播层级关系闭包表';

-- 月度业绩与汇总表
CREATE TABLE `commission_payouts` ( ... );
CREATE TABLE `commission_distributor_qualifications` ( ... );
(注：此处省略了详细的字段，实际使用时应将完整的CREATE TABLE语句提供给AI)

六、 性能与实现建议 (Performance & Implementation Recommendations)
！！！极其重要: 此API中的 teamIncomeUSD 和 teamAnchorCount 等指标，需要对每个L1成员进行递归式的聚合计算。在生产环境中，实时执行这种查询是绝对不可接受的，会导致数据库超时和API性能雪崩。

强制性要求: 你的代码实现必须基于一个**预计算好的月度汇总表（数据快照）**来获取这些复杂指标。你可以假定存在一张名为 distributor_monthly_summary 的表，其中已经存有每个用户在每个月的 total_team_income_usd 和 total_team_size。你的查询逻辑应该直接 SELECT 这张表，而不是去实时递归计算。

七、 响应格式与示例 (Response Format & Example)
严格要求: 所有成功的API响应，都必须封装在标准的“若依 (ruoyi)”风格的JSON结构中。

JSON

{
    "code": 200,
    "msg": "查询成功",
    "data": {
      "viewedLeader": {
        "creatorId": 1,
        "name": "Zhang Xiaoming (Root)"
      },
      "levelSummary": {
        "memberCount": 3,
        "totalIncomeUSD": 360100.00,
        "avgMomGrowth": 0.112,
        "totalAnchorCount": 71
      },
      "members": [
        {
          "creatorId": 124,
          "name": "Li Xiaohong",
          "level": "Silver",
          "momGrowth": 0.125,
          "subordinateCount": 1,
          "teamIncomeUSD": 45600.00,
          "teamAnchorCount": 8,
          "diamondMetric": null
        },
        {
          "creatorId": 125,
          "name": "Wang Daming",
          "level": "Gold",
          "momGrowth": 0.183,
          "subordinateCount": 3,
          "teamIncomeUSD": 128900.00,
          "teamAnchorCount": 25,
          "diamondMetric": null
        },
        {
          "creatorId": 126,
          "name": "Zhao Xiaohua",
          "level": "Diamond",
          "momGrowth": 0.027,
          "subordinateCount": 2,
          "teamIncomeUSD": 185600.00,
          "teamAnchorCount": 38,
          "diamondMetric": null
        }
      ]
    }
}
八、 任务要求总结 (Summary of Task Requirements)
生成代码: 请为这个API生成完整的后端代码，包括Controller, Service, ServiceImpl, Mapper接口和对应的XML/Mapper实现。

性能优先: 严格遵循第六部分的性能建议，查询逻辑应面向一个预先聚合好的汇总表来获取teamIncomeUSD和teamAnchorCount等复杂指标。

边界处理: 代码需要能处理一个领导没有任何直属下级的情况（此时levelSummary中的聚合值为0，members数组为空）。

严格遵循格式: Controller的最终返回结果，必须严格遵守第七部分中定义的“若依”JSON封装格式。
</requirements>



<data_structure>
@database.sql
</data_structure>


- 先读需求内容requirements， 了解后. 读数据库结构 data_structure 
- 逐步思考, 本次的修改内容， 及修改方案， 列出修改计划
- 实现修改现有代码逻辑
- 所有的代码实现后， 为其它大模型生成一个review这次修改内容的提示词，生成到本地的/design/test/ 目录中。 提示词要求： 提供本次修改的文件及对应的行数范围， 修改的内容，需求， 及要求review 的内容。 

 

