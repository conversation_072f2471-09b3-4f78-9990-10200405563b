# 分销员等级规则修改测试指南

## 修改内容总结

本次修改实现了以下功能：

### 1. 数据库结构调整
- 将 `commission_level_rules` 表的 `min_direct_downlines` 字段改为 `min_qualified_recruits`
- 添加了 `payout_depth` 字段用于控制收益深度

### 2. 实体类更新
- 更新了 `CommissionLevelRules` 实体类的字段名称和注解
- 添加了 `payoutDepth` 字段及其 getter/setter 方法

### 3. 等级评定逻辑调整
- 从基于"直属下级人数"改为基于"合格拉新人数"
- 使用 `countQualifiedNewRecruits()` 方法统计合格拉新

### 4. 多级佣金计算优化
- 引入 `payout_depth` 限制收益层级
- 不同等级享受不同深度的收益（金牌3级、银牌2级、铜牌1级）

### 5. 初始化数据更新
- 修改了默认等级规则的初始化逻辑

## 测试步骤

### 步骤1：执行数据库结构调整
```sql
-- 执行 design/SQL/level_rules_structure_update.sql 中的SQL语句
```

### 步骤2：验证实体类映射
1. 启动应用
2. 访问等级规则管理页面
3. 检查字段是否正确显示

### 步骤3：测试等级评定逻辑
1. 准备测试数据：创建用户并设置合格拉新记录
2. 执行佣金计算
3. 验证等级评定是否基于合格拉新人数

### 步骤4：测试多级佣金限制
1. 创建多层级用户关系（A->B->C->D）
2. 设置不同等级的用户A（金牌、银牌、铜牌）
3. 让用户D产生业绩
4. 验证用户A的收益是否受到 `payout_depth` 限制

### 步骤5：测试初始化功能
1. 清空等级规则表
2. 调用初始化接口
3. 验证新的规则是否正确创建
4. 重复调用初始化接口，验证幂等性

### 步骤6：性能测试
#### 6.1 大数据量测试
1. **测试场景**：模拟大规模分销网络
   - 创建10,000个用户的多层级分销网络
   - 每个用户有3-5个直属下级
   - 最深层级达到5级

2. **测试数据准备**：
   - 创建10,000个creators记录
   - 创建50,000条monthly_performance记录
   - 创建5,000条合格拉新记录

3. **性能指标**：
   - 单个分销员佣金计算时间 < 500ms
   - 批量计算1000个分销员 < 30秒
   - 数据库查询次数应为O(1)而非O(n)

#### 6.2 并发测试
1. **测试场景**：模拟多用户同时计算佣金
   - 50个并发用户同时触发佣金计算
   - 每个用户计算不同月份的数据

2. **性能指标**：
   - 系统响应时间不超过2秒
   - 无数据库死锁或连接超时
   - 计算结果准确性100%

#### 6.3 内存使用测试
1. **测试场景**：长时间运行佣金计算
   - 连续计算12个月的佣金数据
   - 监控内存使用情况

2. **性能指标**：
   - 内存使用稳定，无内存泄漏
   - GC频率在合理范围内

#### 6.4 数据库性能测试
1. **SQL执行计划分析**：
   - 验证所有查询都使用了正确的索引
   - 确认没有全表扫描
   - 批量查询的执行时间线性增长

2. **连接池测试**：
   - 验证数据库连接池配置合理
   - 高并发下连接不会耗尽

## 预期结果

### 等级评定
- 金牌：需要10个合格拉新，享受3级收益
- 银牌：需要5个合格拉新，享受2级收益  
- 铜牌：需要2个合格拉新，享受1级收益

### 多级佣金限制
- 金牌用户：可以获得L1、L2、L3的佣金
- 银牌用户：只能获得L1、L2的佣金
- 铜牌用户：只能获得L1的佣金

## 性能测试工具和方法

### 测试工具
1. **JMeter**：用于并发测试和压力测试
2. **JProfiler**：用于内存和CPU性能分析
3. **MySQL Workbench**：用于SQL执行计划分析
4. **Arthas**：用于线上性能诊断

### 测试数据生成脚本
```sql
-- 生成测试用户数据
INSERT INTO creators (nickname, handle, parent_id, created_at)
SELECT
    CONCAT('test_user_', id),
    CONCAT('handle_', id),
    CASE WHEN id > 1000 THEN FLOOR(RAND() * 1000) + 1 ELSE NULL END,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 365) DAY)
FROM
    (SELECT @row := @row + 1 as id FROM
     (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) t1,
     (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) t2,
     (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) t3,
     (SELECT 0 UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3) t4,
     (SELECT @row := 0) r) numbers
WHERE id <= 10000;

-- 生成测试业绩数据
INSERT INTO monthly_performance (creator_id, data_month, diamonds, valid_days)
SELECT
    c.id,
    DATE_FORMAT(DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 12) MONTH), '%Y-%m-01'),
    FLOOR(RAND() * 10000) + 100,
    FLOOR(RAND() * 30) + 1
FROM creators c
WHERE c.id <= 10000;
```

### 性能基准测试
```bash
# JMeter并发测试命令
jmeter -n -t commission_calculation_test.jmx -l results.jtl -e -o report/

# 内存使用监控
jstat -gc -t [PID] 5s

# SQL慢查询分析
SET long_query_time = 0.1;
SET slow_query_log = ON;
```

## 注意事项

1. 确保在执行数据库结构调整前备份数据
2. 新的等级评定依赖于合格拉新资格判定功能
3. 多级佣金计算现在会记录更详细的日志信息
4. 配置参数已从硬编码改为数据库配置
5. **性能测试必须在类生产环境中进行**
6. **测试前确保数据库索引已正确创建**

## 相关配置参数

以下参数需要在 `commission_settings` 表中配置：
- `new_recruit_tenure_days`: 新人资格-账号最大创建天数（默认60天）
- `new_recruit_min_valid_days`: 新人资格-月度最低有效直播天数（默认24天）

## 性能优化建议

### 数据库索引优化
```sql
-- 确保以下索引存在
CREATE INDEX idx_creators_parent_id ON creators(parent_id);
CREATE INDEX idx_monthly_performance_creator_month ON monthly_performance(creator_id, data_month);
CREATE INDEX idx_creators_qualified_recruits ON creators(qualified_by_recruiter_id, first_qualified_month);
CREATE INDEX idx_commission_level_rules_recruits ON commission_level_rules(min_qualified_recruits DESC);
```

### 应用层优化
1. **缓存策略**：对等级规则进行缓存，减少数据库查询
2. **批量处理**：使用批量查询替代循环查询
3. **异步处理**：大批量计算考虑使用异步任务
4. **连接池配置**：合理配置数据库连接池大小
