# 系统配置管理接口调用文档

## 概述
本文档说明系统配置界面各功能模块对应的后端接口调用方法，包括基础参数配置、汇率管理、分销员规则配置等所有功能。

## 基础信息

**Base URL**: `/business/commission/`

**认证方式**: <PERSON><PERSON> (需要登录后获取)

**内容类型**: `application/json`

---

## 1. 基础参数配置

### 1.1 查询全局佣金配置

**接口**: `GET /business/commission/config/settings/list`

**说明**: 获取基础钻石门槛、钻石门槛月度上浮比例等全局配置

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "settingKey": "" // 可选，用于搜索特定配置项
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "settingKey": "base_diamond_threshold",
      "settingValue": "5000",
      "description": "基础分成钻石门槛",
      "updateTime": "2024-01-01 00:00:00"
    },
    {
      "settingKey": "threshold_increase_rate",
      "settingValue": "0.025",
      "description": "动态钻石门槛月度上浮比例 (2.5%)",
      "updateTime": "2024-01-01 00:00:00"
    }
  ],
  "total": 2
}
```

### 1.2 修改全局配置

**接口**: `PUT /business/commission/config/settings`

**说明**: 修改基础钻石门槛或门槛上浮比例

**请求体**:
```json
{
  "settingKey": "base_diamond_threshold",
  "settingValue": "5000",
  "description": "基础分成钻石门槛"
}
```

### 1.3 查询月度配置列表

**接口**: `GET /business/commission/config/monthly/list`

**说明**: 获取每月的预算、支出上限比例等配置

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "dataMonth": "2024-01-01" // 可选，筛选特定月份
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "dataMonth": "2024-01-01",
      "manualIncomeUsd": 100000.0000,
      "payoutCapRate": 0.0900,
      "diamondToUsdRate": 0.005000,
      "createTime": "2024-01-01 00:00:00"
    }
  ],
  "total": 1
}
```

### 1.4 新增月度配置

**接口**: `POST /business/commission/config/monthly`

**说明**: 为特定月份新增预算和汇率配置

**请求体**:
```json
{
  "dataMonth": "2024-02-01",
  "manualIncomeUsd": 100000.0000,
  "payoutCapRate": 0.0900,
  "diamondToUsdRate": 0.005000
}
```

### 1.5 修改月度配置

**接口**: `PUT /business/commission/config/monthly`

**说明**: 修改已有月份的配置

**请求体**:
```json
{
  "dataMonth": "2024-01-01",
  "manualIncomeUsd": 120000.0000,
  "payoutCapRate": 0.0800,
  "diamondToUsdRate": 0.0055
}
```

---

## 2. 钻石兑美元汇率管理

### 2.1 汇率配置说明

汇率配置实际存储在月度配置中，每个月可以设置不同的汇率。

### 2.2 添加新汇率

使用月度配置接口添加新汇率：

**接口**: `POST /business/commission/config/monthly`

**请求体**:
```json
{
  "dataMonth": "2024-03-01",
  "manualIncomeUsd": 100000.0000,
  "payoutCapRate": 0.0900,
  "diamondToUsdRate": 0.0048
}
```

### 2.3 修改汇率

**接口**: `PUT /business/commission/config/monthly`

**请求体**:
```json
{
  "dataMonth": "2024-01-01",
  "manualIncomeUsd": 100000.0000,
  "payoutCapRate": 0.0900,
  "diamondToUsdRate": 0.0050
}
```

---

## 3. 分销员等级规则配置

### 3.1 查询等级规则列表

**接口**: `GET /business/commission/rules/level/list`

**说明**: 获取金牌、银牌、铜牌等级规则配置

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "levelName": "", // 可选，搜索特定等级
  "isActive": 1    // 可选，1=启用，0=禁用
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "levelName": "金牌",
      "minDirectDownlines": 10,
      "commissionRate": 0.2500,
      "displayOrder": 1,
      "isActive": 1
    },
    {
      "id": 2,
      "levelName": "银牌",
      "minDirectDownlines": 5,
      "commissionRate": 0.2000,
      "displayOrder": 2,
      "isActive": 1
    },
    {
      "id": 3,
      "levelName": "铜牌",
      "minDirectDownlines": 2,
      "commissionRate": 0.1500,
      "displayOrder": 3,
      "isActive": 1
    }
  ],
  "total": 3
}
```

### 3.2 新增等级规则

**接口**: `POST /business/commission/rules/level`

**请求体**:
```json
{
  "levelName": "钻石",
  "minDirectDownlines": 50,
  "commissionRate": 0.3000,
  "displayOrder": 0,
  "isActive": 1
}
```

### 3.3 修改等级规则

**接口**: `PUT /business/commission/rules/level`

**请求体**:
```json
{
  "id": 1,
  "levelName": "金牌",
  "minDirectDownlines": 15,
  "commissionRate": 0.2800,
  "displayOrder": 1,
  "isActive": 1
}
```

### 3.4 删除等级规则

**接口**: `DELETE /business/commission/rules/level/{id}`

**说明**: 删除指定ID的等级规则

---

## 4. 多级提成比例设置

### 4.1 查询多级提成规则

**接口**: `GET /business/commission/rules/multilevel/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "depth": null,    // 可选，1=L1, 2=L2, 3=L3
  "isActive": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "depth": 1,
      "commissionRate": 0.4000,
      "isActive": 1
    },
    {
      "id": 2,
      "depth": 2,
      "commissionRate": 0.3000,
      "isActive": 1
    },
    {
      "id": 3,
      "depth": 3,
      "commissionRate": 0.2000,
      "isActive": 1
    }
  ],
  "total": 3
}
```

### 4.2 修改多级提成比例

**接口**: `PUT /business/commission/rules/multilevel`

**请求体**:
```json
{
  "id": 1,
  "depth": 1,
  "commissionRate": 0.4200,
  "isActive": 1
}
```

### 4.3 新增多级提成规则

**接口**: `POST /business/commission/rules/multilevel`

**请求体**:
```json
{
  "depth": 4,
  "commissionRate": 0.1000,
  "isActive": 1
}
```

---

## 5. 拉新奖励阶梯配置

### 5.1 查询拉新奖励规则

**接口**: `GET /business/commission/rules/recruitment/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "minNewRecruits": null, // 可选，筛选特定人数要求
  "isActive": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "minNewRecruits": 1,
      "bonusUsd": 100.0000,
      "isActive": 1
    },
    {
      "id": 2,
      "minNewRecruits": 6,
      "bonusUsd": 250.0000,
      "isActive": 1
    },
    {
      "id": 3,
      "minNewRecruits": 11,
      "bonusUsd": 500.0000,
      "isActive": 1
    }
  ],
  "total": 3
}
```

### 5.2 新增拉新奖励规则

**接口**: `POST /business/commission/rules/recruitment`

**请求体**:
```json
{
  "minNewRecruits": 21,
  "bonusUsd": 800.0000,
  "isActive": 1
}
```

### 5.3 修改拉新奖励规则

**接口**: `PUT /business/commission/rules/recruitment`

**请求体**:
```json
{
  "id": 1,
  "minNewRecruits": 1,
  "bonusUsd": 120.0000,
  "isActive": 1
}
```

### 5.4 删除拉新奖励规则

**接口**: `DELETE /business/commission/rules/recruitment/{id}`

---

## 6. 批量操作和初始化

### 6.1 初始化系统基础配置

**接口**: `POST /business/commission/config/init`

**说明**: 一键初始化基础配置项

**请求体**: 无

**响应示例**:
```json
{
  "code": 200,
  "msg": "配置初始化成功"
}
```

### 6.2 初始化所有规则

**接口**: `POST /business/commission/rules/init`

**说明**: 一键初始化等级规则、多级提成规则、拉新奖励规则

**请求体**: 无

**响应示例**:
```json
{
  "code": 200,
  "msg": "规则初始化成功"
}
```

---

## 7. 界面实现建议

### 7.1 基础参数配置界面

1. **公会月总收入预算**: 调用月度配置接口设置 `manualIncomeUsd`
2. **公会支出上限比例**: 调用月度配置接口设置 `payoutCapRate`
3. **基础钻石门槛**: 调用全局配置接口设置 `base_diamond_threshold`
4. **钻石门槛月度上浮比例**: 调用全局配置接口设置 `threshold_increase_rate`

### 7.2 汇率管理界面

1. **查询历史汇率**: 调用月度配置列表接口
2. **添加新汇率**: 调用新增月度配置接口
3. **修改汇率**: 调用修改月度配置接口

### 7.3 等级规则配置界面

1. **显示三个等级卡片**: 调用等级规则列表接口获取数据
2. **修改等级要求**: 调用修改等级规则接口
3. **实时计算比例**: 前端计算并显示

### 7.4 多级提成配置界面

1. **显示L1/L2/L3比例**: 调用多级提成规则接口
2. **实时计算总比例**: 前端计算 L1+L2+L3 的总和
3. **提成计算示例**: 前端基于输入金额和比例计算预览

### 7.5 拉新奖励配置界面

1. **显示阶梯列表**: 调用拉新奖励规则接口
2. **添加新阶梯**: 调用新增拉新奖励规则接口
3. **修改现有阶梯**: 调用修改拉新奖励规则接口

---

## 8. 错误处理

### 8.1 常见错误码

- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `500`: 服务器内部错误

### 8.2 错误响应格式

```json
{
  "code": 400,
  "msg": "参数验证失败：分成比例不能大于1"
}
```

---

## 9. 注意事项

1. **数据精度**: 比例类字段需要使用小数形式（如 0.25 表示 25%）
2. **月份格式**: 日期需要使用 `YYYY-MM-DD` 格式，月度配置建议使用每月1日
3. **权限验证**: 所有接口都需要相应的权限，确保用户有 `h:commission_config` 和 `h:commission_rules` 权限
4. **并发控制**: 修改配置时建议实现前端锁定机制，避免并发修改冲突
5. **数据验证**: 前端应实现必要的数据验证，如比例范围检查、人数非负验证等

---

## 10. 接口调用示例代码

### JavaScript 示例

```javascript
// 获取等级规则列表
async function getLevelRules() {
  const response = await fetch('/business/commission/rules/level/list?pageNum=1&pageSize=10', {
    method: 'GET',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    }
  });
  const data = await response.json();
  return data.rows;
}

// 修改等级规则
async function updateLevelRule(rule) {
  const response = await fetch('/business/commission/rules/level', {
    method: 'PUT',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(rule)
  });
  return await response.json();
}

// 新增月度配置
async function addMonthlyConfig(config) {
  const response = await fetch('/business/commission/config/monthly', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(config)
  });
  return await response.json();
}
``` 