# 拉新资格判定配置使用说明

## 概述

拉新资格判定功能现在支持从数据库的`commission_settings`表中读取配置参数，不再使用硬编码的常量。这使得系统管理员可以动态调整拉新资格的判定标准，无需修改代码。

## 配置参数

### 1. 账号创建天数阈值 (new_recruit_tenure_days)
- **配置键**: `new_recruit_tenure_days`
- **默认值**: 60
- **说明**: 用户账号创建时间必须在此天数内才符合拉新资格
- **示例**: 设置为60表示账号创建时间必须在60天内

### 2. 最小有效直播天数 (new_recruit_min_valid_days)
- **配置键**: `new_recruit_min_valid_days`
- **默认值**: 24
- **说明**: 用户在指定天数内的总有效直播天数必须达到此值才符合拉新资格
- **示例**: 设置为24表示60天内总有效直播天数必须达到24天

## 数据库配置

### 插入配置数据
```sql
INSERT INTO `commission_settings` (`setting_key`, `setting_value`, `description`, `updated_at`)
VALUES
    ('new_recruit_tenure_days', '60', '新人资格-账号最大创建天数', NOW()),
    ('new_recruit_min_valid_days', '24', '新人资格-月度最低有效直播天数', NOW());
```

### 更新配置数据
```sql
-- 修改账号创建天数阈值为90天
UPDATE `commission_settings` 
SET `setting_value` = '90', `updated_at` = NOW() 
WHERE `setting_key` = 'new_recruit_tenure_days';

-- 修改最小有效直播天数为30天
UPDATE `commission_settings` 
SET `setting_value` = '30', `updated_at` = NOW() 
WHERE `setting_key` = 'new_recruit_min_valid_days';
```

## API接口使用

### 1. 获取当前配置
```http
GET /business/new-recruit-qualification/config
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "tenureDays": 60,
    "minValidDays": 24,
    "description": "拉新用户资格判定配置参数",
    "configKeys": {
      "tenureDays": "new_recruit_tenure_days",
      "minValidDays": "new_recruit_min_valid_days"
    }
  }
}
```

### 2. 更新配置
```http
POST /business/new-recruit-qualification/config
Content-Type: application/x-www-form-urlencoded

tenureDays=90&minValidDays=30
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "配置更新成功",
    "updatedCount": 2,
    "updatedItems": [
      "账号创建天数阈值: 90天",
      "最小有效直播天数: 30天"
    ]
  }
}
```

### 3. 测试配置效果
```http
GET /business/new-recruit-qualification/check/{creatorId}/{dataMonth}
```

此接口会使用最新的配置参数来检查用户资格，响应中会包含配置信息：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "qualified": true,
    "tenureQualified": true,
    "validDaysQualified": true,
    "tenureDaysConfig": 90,
    "requiredValidDays": 30,
    "validDaysIn60Days": 35,
    "qualificationReason": "符合拉新资格：账号创建时间符合要求且90天内有效直播天数达标"
  }
}
```

## 配置生效机制

1. **实时生效**: 配置更新后立即生效，无需重启服务
2. **缓存机制**: 每次资格判定时都会从数据库读取最新配置
3. **容错机制**: 如果配置读取失败，会使用默认值并记录警告日志
4. **参数验证**: 更新配置时会验证参数的有效性（必须为正整数）

## 使用建议

1. **测试环境验证**: 在生产环境修改配置前，建议先在测试环境验证效果
2. **逐步调整**: 建议逐步调整参数，避免一次性大幅度修改
3. **监控影响**: 修改配置后，建议监控拉新资格判定的结果变化
4. **备份配置**: 修改前建议备份当前配置值

## 注意事项

1. 配置参数必须为正整数
2. 账号创建天数阈值建议不要设置过小（建议不少于30天）
3. 最小有效直播天数建议根据业务实际情况设置
4. 配置修改会影响所有后续的拉新资格判定，请谨慎操作

## 故障排除

### 配置读取失败
如果遇到配置读取失败的情况，系统会：
1. 使用默认配置值继续执行
2. 在日志中记录警告信息
3. 在API响应中标明使用了默认值

### 配置更新失败
常见原因：
1. 参数值不是有效的整数
2. 数据库连接问题
3. 权限不足

解决方法：
1. 检查参数格式
2. 检查数据库连接状态
3. 确认用户权限
