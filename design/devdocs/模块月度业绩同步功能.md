# Ruoyi 后端 RawImport 模块月度业绩同步功能增强文档

## 1. 功能概述

本次开发旨在增强 Ruoyi 后端的 `RawImport` 模块，使其在处理从 Excel 导入的原始数据的同时，能够全面支持主播月度业绩（`MonthlyPerformance`）数据的同步。核心需求是实现月度业绩数据的“存在则更新，不存在则插入”逻辑，确保数据基于 `creator_id`（主播ID）和 `data_month`（数据月份）的唯一性，并与业务需求保持一致。此外，还包括了对相关实体、Mapper、Service接口及实现的修改和新增，以及确保审计字段的正确填充。

## 2. 主要修改文件及内容

以下是本次功能增强所涉及的核心文件及其主要修改点：

### 2.1. `MonthlyPerformance.java` (实体类)
   - **路径**: `com.ruoyi.system.domain.business.MonthlyPerformance`
   - **修改**: 
     - 继承 `com.ruoyi.common.core.domain.BaseEntity` 以包含标准的审计字段（如 `createBy`, `createTime`, `updateBy`, `updateTime`, `remark`）。
     - 移除了因继承 `BaseEntity` 而冗余的 `java.io.Serializable` 接口声明。
     - 更新了 `toString()` 方法以包含 `BaseEntity` 中的字段。
     - 确认了实体字段与数据库表 `monthly_performance` 的映射关系，特别是业绩相关字段（如 `diamonds`, `bonusEstimated`, `bonusRookieM1Retention` 等）的类型和命名。

### 2.2. `MonthlyPerformanceMapper.java` (Mapper 接口)
   - **路径**: `com.ruoyi.system.mapper.business.MonthlyPerformanceMapper`
   - **修改**: 
     - 新增 `MonthlyPerformance selectMonthlyPerformanceByCreatorAndMonth(@Param("creatorId") Long creatorId, @Param("dataMonth") Date dataMonth);` 方法，用于根据主播ID和数据月份查询已存在的月度业绩记录。
     - 添加了Swagger注解，增强了接口的可读性。
     - 修正了之前可能存在的重复包声明和导入问题。

### 2.3. `MonthlyPerformanceMapper.xml` (MyBatis 映射文件)
   - **路径**: `mapper/business/MonthlyPerformanceMapper.xml`
   - **修改**: 
     - 为 `selectMonthlyPerformanceByCreatorAndMonth` 方法添加了对应的 `<select>` SQL 映射。该查询基于 `creator_id` 和 `data_month` 来精确查找记录。
     - 确保了 `insertMonthlyPerformance` 和 `updateMonthlyPerformance` SQL语句能够正确处理 `BaseEntity` 中的审计字段。

### 2.4. `IMonthlyPerformanceService.java` (Service 接口)
   - **路径**: `com.ruoyi.system.service.business.IMonthlyPerformanceService`
   - **修改**: 
     - 新增 `int saveOrUpdateMonthlyPerformance(MonthlyPerformance monthlyPerformance);` 方法声明，用于实现月度业绩的新增或更新逻辑。
     - 添加了Swagger注解。

### 2.5. `MonthlyPerformanceServiceImpl.java` (Service 实现)
   - **路径**: `com.ruoyi.system.service.impl.business.MonthlyPerformanceServiceImpl`
   - **修改**: 
     - 实现了 `saveOrUpdateMonthlyPerformance` 方法：
       - 首先调用 `monthlyPerformanceMapper.selectMonthlyPerformanceByCreatorAndMonth()` 检查记录是否存在。
       - 若存在，则设置传入对象的ID，并调用 `monthlyPerformanceMapper.updateMonthlyPerformance()` 更新记录。
       - 若不存在，则调用 `monthlyPerformanceMapper.insertMonthlyPerformance()` 新增记录。

### 2.6. `RawImport.java` (实体类)
   - **路径**: `com.ruoyi.system.domain.business.RawImport`
   - **修改**: 
     - 此文件经过审阅，确认已包含必要的字段（如 `dataMonth` (String类型), `diamonds`, `estimatedBonus` 等）来承载从Excel中读取的原始业绩数据。本次开发未对其进行结构性修改。

### 2.7. `RawImportServiceImpl.java` (Service 实现)
   - **路径**: `com.ruoyi.system.service.impl.business.RawImportServiceImpl`
   - **修改**: 
     - **依赖注入**: 注入了 `IMonthlyPerformanceService`。
     - **新增核心逻辑方法**: `private void synchronizeMonthlyPerformanceData(List<RawImport> rawImportList, String operName)`
       - 此方法在 `importRawDataFromExcel` 流程中，于主播信息同步之后、层级关系重建之前被调用。
       - 遍历从Excel解析得到的 `RawImport` 对象列表。
       - 对每个 `RawImport` 对象：
         - 通过 `rawImport.getHandle()` 查询对应的 `Creator` 实体，获取其数据库 `id`。
         - 解析 `rawImport.getDataMonth()` (String, 格式如 "yyyy-MM") 转换为 `java.util.Date` 对象（通常处理为该月的第一天）。
         - 创建一个新的 `MonthlyPerformance` 实例。
         - **数据映射与转换**: 将 `RawImport` 中的业绩相关字段（如 `groupName`, `groupManager`, `isViolativeCreators`, `diamonds`, `liveDurationH`, 各项奖金等）映射到 `MonthlyPerformance` 对象的相应字段。此过程包含：
           - 字符串到 `BigDecimal` (例如奖金金额)。
           - 字符串到 `Long` (例如钻石数)。
           - 字符串到 `Integer` (例如有效天数)。
           - 中文字符串（如“是”/“否”）到 `Integer` (1/0) 的转换。
           - 为此实现了 `parseLong`, `parseBigDecimal`, `parseInteger`, `parseBooleanString` 等辅助解析方法，增强了数据转换的健壮性。
         - **审计字段填充**: 显式设置 `MonthlyPerformance` 对象的 `createBy`, `createTime`, `updateBy`, `updateTime` 字段。
         - 调用 `monthlyPerformanceService.saveOrUpdateMonthlyPerformance(mp)` 将数据持久化。
     - **主导入流程修改**: `importRawDataFromExcel` 方法中集成了对 `synchronizeMonthlyPerformanceData` 的调用。
     - **确保事务性**: 整个导入操作（包括原始数据保存、主播同步、月度业绩同步、层级关系重建）处于同一个事务管理下。

## 3. 核心实现逻辑

功能的核心在于 `RawImportServiceImpl` 如何协调和处理从Excel导入的数据，并将其同步到 `monthly_performance` 表。

1.  **数据入口**: 用户通过前端上传包含主播业绩数据的Excel文件。
2.  **Excel解析**: `RawImportServiceImpl` 中的 `importRawDataFromExcel` 方法使用 `ExcelUtil` 工具类将Excel文件内容解析为一个 `List<RawImport>` 对象列表。
3.  **原始数据存储**: 解析出的 `RawImport` 列表中的每条记录首先被批量插入到 `raw_imports` 表中，作为原始数据的备份和记录。
4.  **主播信息同步**: 系统接着会根据 `RawImport` 数据同步 `creators` 表中的主播信息，包括新增主播或更新现有主播信息，并处理其 `parentId` 用于后续层级关系构建。
5.  **月度业绩同步 (关键步骤)**:
    a.  调用新增的 `synchronizeMonthlyPerformanceData` 方法，传入 `rawImportList` 和操作人 `operName`。
    b.  方法内部遍历 `rawImportList`：
        i.  **获取主播ID**: 根据 `rawImport.getHandle()` 从 `creators` 表获取对应的 `creator.getId()`。
        ii. **处理数据月份**: 将 `rawImport.getDataMonth()` (例如 "2023-10") 字符串通过 `SimpleDateFormat` 解析为 `java.util.Date` 对象（通常处理为每月的第一天，如 `2023-10-01`）。
        iii. **构建MonthlyPerformance对象**: 创建 `MonthlyPerformance` 实例，并从 `RawImport` 对象中提取数据填充到此实例中。这包括：
            - 直接映射的字段（如 `groupName`）。
            - 需要类型转换的字段：
                - `diamonds`: `String` -> `Long`
                - `validDays`: `String` -> `Integer`
                - `liveDurationH`: `String` -> `BigDecimal`
                - 各种奖金字段 (`estimatedBonus`, `estBonusRookieM1`, etc.): `String` -> `BigDecimal` (注意：`bonusRookieM1Retention` 在 `MonthlyPerformance` 中为 `String` 类型，直接传递)。
                - `isViolativeCreators`, `theCreatorWasRookie`: `String` ("是"/"否") -> `Integer` (1/0)。
        iv. **设置审计字段**: 为 `MonthlyPerformance` 对象设置 `createBy`, `createTime`, `updateBy`, `updateTime`。
        v.  **调用Service层进行保存/更新**: 调用 `iMonthlyPerformanceService.saveOrUpdateMonthlyPerformance(monthlyPerformance)`。
            - `MonthlyPerformanceServiceImpl` 中的该方法会先使用 `creatorId` 和 `dataMonth` 查询数据库。
            - 如果记录已存在，则执行更新操作（`updateMonthlyPerformance`）。
            - 如果记录不存在，则执行插入操作（`insertMonthlyPerformance`）。
6.  **层级关系重建**: 在所有数据同步完成后，系统会重建主播的层级关系表 `creator_relationships`。
7.  **事务保证**: 整个导入和同步过程由Spring的 `@Transactional` 注解管理，确保数据的一致性。若中途发生错误，所有已执行的数据库操作将回滚。

## 4. 数据流和关键点

```mermaid
sequenceDiagram
    participant User
    participant RawImportController
    participant RawImportServiceImpl
    participant CreatorService
    participant MonthlyPerformanceService
    participant RawImportMapper
    participant CreatorMapper
    participant MonthlyPerformanceMapper
    participant Database

    User->>RawImportController: Upload Excel File
    RawImportController->>RawImportServiceImpl: importRawDataFromExcel(file, operName)
    RawImportServiceImpl->>ExcelUtil: Parse Excel to List<RawImport>
    ExcelUtil-->>RawImportServiceImpl: rawImportList
    
    RawImportServiceImpl->>RawImportMapper: batchInsertRawImport(rawImportList)
    RawImportMapper->>Database: INSERT INTO raw_imports
    Database-->>RawImportMapper: Success/Failure
    RawImportMapper-->>RawImportServiceImpl: Result

    RawImportServiceImpl->>RawImportServiceImpl: synchronizeCreatorsAndUpdateHierarchy(rawImportList, operName)
    loop For each rawImport in rawImportList
        RawImportServiceImpl->>CreatorService: selectCreatorByHandle(handle)
        CreatorService->>CreatorMapper: SELECT FROM creators
        CreatorMapper-->>CreatorService: Creator_existing
        RawImportServiceImpl->>CreatorService: saveOrUpdateCreator(creator_new_or_updated)
        CreatorService->>CreatorMapper: INSERT/UPDATE creators
    end

    RawImportServiceImpl->>RawImportServiceImpl: synchronizeMonthlyPerformanceData(rawImportList, operName)
    loop For each rawImport in rawImportList
        RawImportServiceImpl->>CreatorService: selectCreatorByHandle(handle)
        CreatorService-->>RawImportServiceImpl: Creator (with ID)
        Note over RawImportServiceImpl: Parse dataMonth (String to Date)
        Note over RawImportServiceImpl: Map RawImport fields to MonthlyPerformance (with type conversions)
        Note over RawImportServiceImpl: Set audit fields (createBy, createTime, etc.)
        RawImportServiceImpl->>MonthlyPerformanceService: saveOrUpdateMonthlyPerformance(monthlyPerformance)
        MonthlyPerformanceService->>MonthlyPerformanceMapper: selectMonthlyPerformanceByCreatorAndMonth(creatorId, dataMonth)
        MonthlyPerformanceMapper-->>MonthlyPerformanceService: existingPerformance / null
        alt Record Exists
            MonthlyPerformanceService->>MonthlyPerformanceMapper: updateMonthlyPerformance(monthlyPerformance)
            MonthlyPerformanceMapper->>Database: UPDATE monthly_performance
        else Record Not Exists
            MonthlyPerformanceService->>MonthlyPerformanceMapper: insertMonthlyPerformance(monthlyPerformance)
            MonthlyPerformanceMapper->>Database: INSERT INTO monthly_performance
        end
        Database-->>MonthlyPerformanceMapper: Success/Failure
        MonthlyPerformanceMapper-->>MonthlyPerformanceService: Result
        MonthlyPerformanceService-->>RawImportServiceImpl: Result
    end

    RawImportServiceImpl->>ICreatorRelationshipService: rebuildCreatorRelationships()
    ICreatorRelationshipService->>Database: Rebuild creator_relationships table

    RawImportServiceImpl-->>RawImportController: Success/Failure Message
    RawImportController-->>User: Response
```

**关键技术点**: 
- **原子性**: 利用 `@Transactional` 保证整个导入过程的原子性。
- **幂等性**: `saveOrUpdateMonthlyPerformance` 的设计使得重复导入相同月份的数据（对于同一主播）不会产生重复记录，而是更新现有记录。
- **数据转换**: 实现了一系列健壮的 `parseXXX` 方法来处理从Excel（通常为文本格式）到Java实体所需类型的转换，并包含日志记录潜在的解析错误。
- **模块化**: 将月度业绩同步逻辑封装在 `synchronizeMonthlyPerformanceData` 方法中，并由 `MonthlyPerformanceService` 提供核心的数据库操作服务，保持了代码的清晰和可维护性。
- **审计**: 通过继承 `BaseEntity` 和在Service层显式设置，确保了 `monthly_performance` 表记录的审计信息完整性。

## 5. 后续工作建议
- **全面测试**: 包括单元测试（特别是Service层逻辑和解析工具类）和集成测试（模拟Excel导入全流程）。
- **性能优化**: 对于超大Excel文件的导入，可能需要考虑异步处理或进一步的批量优化。
- **错误反馈**: 增强对用户导入失败时的错误提示，例如具体到哪一行数据、哪个字段出现问题。
