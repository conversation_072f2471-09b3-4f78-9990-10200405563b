# 分销员等级评定机制调整 - 代码审查提示词

## 审查背景

本次开发实现了分销体系的核心激励机制调整：将分销员等级评定标准从"直属下级人数"改为"合格拉新人数"，并引入了基于等级的收益深度控制机制。这是一个涉及业务逻辑重构、数据库结构调整和算法优化的重要功能。

## 需求概述

### 核心需求
1. **等级评定机制调整**：分销员等级（金/银/铜）的评定依据从"直属下级人数"改为"合格拉新人数"
2. **收益深度控制**：不同等级享受不同层级的佣金收益（金牌3级、银牌2级、铜牌1级）
3. **配置参数化**：将拉新资格判定参数从硬编码改为数据库配置
4. **规则数据化**：所有等级规则存储在数据库中，支持运营人员动态调整

### 业务价值
- 建立清晰的等级晋升路径，激励分销员提升"拉新"能力
- 实现差异化收益机制，等级越高收益网络越广
- 提供灵活的配置机制，支持业务规则的快速调整

## 代码审查要点

### 1. 数据库设计审查

**审查文件**: `design/SQL/level_rules_structure_update.sql`

**审查要点**:
- [ ] 字段重命名是否正确：`min_direct_downlines` → `min_qualified_recruits`
- [ ] 新增字段设计是否合理：`payout_depth` 的数据类型和默认值
- [ ] 数据迁移脚本是否安全：是否包含数据备份和回滚方案
- [ ] 示例数据是否符合业务逻辑：金牌10个拉新、银牌5个、铜牌2个

**对应需求**: 
- 等级评定标准从"直属下级"改为"合格拉新"
- 引入收益深度控制机制

### 2. 实体类设计审查

**审查文件**: `ruoyi-system/src/main/java/com/ruoyi/system/domain/commission/CommissionLevelRules.java`

**审查要点**:
- [ ] 字段命名是否符合Java规范：`minQualifiedRecruits`, `payoutDepth`
- [ ] 验证注解是否完整：`@NotNull`, `@Min`, `@DecimalMin`等
- [ ] Swagger注解是否准确：API文档描述是否清晰
- [ ] getter/setter方法是否完整
- [ ] toString方法是否包含所有关键字段

**对应需求**:
- 实体类字段调整以支持新的业务逻辑
- 保持代码的可维护性和文档完整性

### 3. 数据访问层审查

**审查文件**: `ruoyi-system/src/main/resources/mapper/commission/CommissionLevelRulesMapper.xml`

**审查要点**:
- [ ] ResultMap映射是否正确：所有字段都有对应的映射
- [ ] SQL语句是否更新：select、insert、update语句中的字段名
- [ ] 查询条件是否调整：where条件中的字段名更新
- [ ] 排序规则是否合理：按display_order排序用于等级评定

**对应需求**:
- 支持新的数据库结构
- 确保数据访问的正确性

### 4. 核心业务逻辑审查

**审查文件**: `ruoyi-system/src/main/java/com/ruoyi/system/service/commission/impl/CommissionCalculationServiceImpl.java`

#### 4.1 等级评定逻辑审查

**关键方法**: `evaluateDistributorLevel()`

**审查要点**:
- [ ] 参数变更是否正确：从`directDownlinesCount`改为`qualifiedRecruitsCount`
- [ ] 数据来源是否正确：使用`countQualifiedNewRecruits()`方法
- [ ] 等级匹配逻辑是否正确：使用`getMinQualifiedRecruits()`进行比较
- [ ] 日志记录是否完整：记录合格拉新人数和评定结果
- [ ] 异常处理是否充分：空值和边界情况处理

**对应需求**:
- 等级评定标准从"直属下级人数"改为"合格拉新人数"

#### 4.2 多级佣金计算审查

**关键方法**: `calculateMultilevelCommission()`

**审查要点**:
- [ ] 收益深度限制是否正确实现：根据`payout_depth`限制层级
- [ ] 等级查询逻辑是否正确：`getPayoutDepthByLevel()`方法实现
- [ ] 佣金计算是否准确：超出深度限制的层级应为0
- [ ] 性能考虑是否充分：避免重复查询和计算
- [ ] 日志记录是否详细：包含等级、深度限制和计算过程

**对应需求**:
- 不同等级享受不同层级的佣金收益
- 金牌3级、银牌2级、铜牌1级的收益深度控制

#### 4.3 辅助方法审查

**关键方法**: `getPayoutDepthByLevel()`, `countQualifiedNewRecruits()`

**审查要点**:
- [ ] 方法职责是否单一：每个方法只做一件事
- [ ] 参数验证是否充分：空值检查和边界条件
- [ ] 返回值处理是否正确：null值的含义和处理
- [ ] 方法命名是否清晰：见名知意
- [ ] 代码复用是否合理：避免重复逻辑

### 5. 配置和初始化审查

**审查文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/business/CommissionRulesController.java`

**审查要点**:
- [ ] 初始化数据是否合理：金牌10个拉新、银牌5个、铜牌2个
- [ ] 收益深度设置是否正确：金牌3级、银牌2级、铜牌1级
- [ ] 分成比例是否保持：原有的佣金比例不变
- [ ] 异常处理是否完整：初始化失败的处理机制
- [ ] 权限控制是否正确：`@PreAuthorize`注解

**对应需求**:
- 提供合理的默认配置
- 支持系统的快速部署和测试

### 6. 代码质量审查

#### 6.1 代码规范审查
- [ ] 命名规范：类名、方法名、变量名是否符合Java规范
- [ ] 注释完整性：中文注释是否清晰，Javadoc是否完整
- [ ] 代码格式：缩进、换行、空格是否统一
- [ ] 魔法数字：是否将硬编码数字提取为常量

#### 6.2 架构设计审查
- [ ] 分层架构：Controller、Service、Mapper职责是否清晰
- [ ] 依赖注入：Spring注解使用是否正确
- [ ] 事务管理：数据库操作是否有适当的事务控制
- [ ] 异常处理：是否有统一的异常处理机制

#### 6.3 性能和安全审查
- [ ] 数据库查询：是否存在N+1查询问题
- [ ] 内存使用：大数据量处理是否考虑内存优化
- [ ] 并发安全：多线程环境下的数据一致性
- [ ] 输入验证：用户输入是否有充分的验证

### 7. 测试覆盖审查

**审查文件**: `design/test_level_rules_changes.md`

**审查要点**:
- [ ] 测试场景是否完整：正常流程、边界条件、异常情况
- [ ] 测试数据是否充分：不同等级、不同拉新数量的测试
- [ ] 集成测试是否考虑：端到端的业务流程测试
- [ ] 性能测试是否规划：大数据量下的性能表现

## 重点关注领域

### 🔴 高风险区域
1. **数据库结构变更**：字段重命名可能影响现有功能
2. **等级评定逻辑**：算法变更可能影响用户收益
3. **多级佣金计算**：收益深度限制的正确性至关重要

### 🟡 中等风险区域
1. **配置参数化**：从硬编码到数据库配置的迁移
2. **初始化数据**：默认规则的合理性
3. **日志记录**：新增日志的格式和内容

### 🟢 低风险区域
1. **实体类调整**：主要是字段名称变更
2. **Mapper XML更新**：SQL语句的字段名调整
3. **注释和文档**：代码可读性改进

## 审查输出要求

请按以下格式提供审查结果：

### 1. 总体评价
- 代码质量评分（1-10分）
- 需求实现完整性评价
- 架构设计合理性评价

### 2. 发现的问题
- 🔴 严重问题：可能导致系统故障或数据错误
- 🟡 一般问题：影响代码质量或维护性
- 🟢 建议改进：优化建议和最佳实践

### 3. 改进建议
- 代码优化建议
- 性能改进建议
- 安全加固建议
- 测试补充建议

### 4. 部署风险评估
- 数据库变更风险
- 业务逻辑变更风险
- 回滚方案建议

## 相关文档

- 需求文档：详见代码中的注释和设计文档
- 数据库设计：`design/SQL/database.sql`
- 测试指南：`design/test_level_rules_changes.md`
- 配置说明：`design/拉新资格判定配置使用说明.md`
