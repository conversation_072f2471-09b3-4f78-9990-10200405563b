# 佣金汇总功能使用示例

## 功能概述

修改后的佣金汇总功能现在会在月度佣金计算过程中，自动计算并填充当月所有主播的 `bonus_estimated` 累计值到 `commission_monthly_summary.total_bonus_estimated` 字段中。

## 使用流程

### 1. 准备数据

确保当月的 `monthly_performance` 数据已经导入：

```sql
-- 检查当月是否有主播业绩数据
SELECT COUNT(*) FROM monthly_performance 
WHERE DATE_FORMAT(data_month, '%Y-%m') = '2025-01';

-- 查看当月主播奖金估计值
SELECT 
    creator_id,
    bonus_estimated,
    data_month
FROM monthly_performance 
WHERE DATE_FORMAT(data_month, '%Y-%m') = '2025-01'
ORDER BY bonus_estimated DESC;
```

### 2. 执行佣金计算

通过 API 接口触发佣金计算：

#### 方式一：执行月度佣金计算
```bash
curl -X POST "http://localhost:8080/business/commission/calculation/execute/2025-01" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### 方式二：重新计算月度佣金
```bash
curl -X POST "http://localhost:8080/business/commission/calculation/recalculate/2025-01?force=true" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. 验证计算结果

#### 3.1 查看计算状态
```bash
curl -X GET "http://localhost:8080/business/commission/calculation/status/2025-01" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.2 获取月度计算总览
```bash
curl -X GET "http://localhost:8080/business/commission/calculation/summary/2025-01" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

返回示例：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "dataMonth": "2025-01-01",
    "budgetUsd": 50000.0000,
    "payoutCapUsd": 15000.0000,
    "actualPayoutUsd": 12500.0000,
    "payoutToBudgetRatio": 0.2500,
    "exceededAmountUsd": 0.0000,
    "totalBonusEstimated": 8750.5000,
    "calculationStatus": "completed",
    "calculatedAt": "2025-01-17 15:30:45"
  }
}
```

### 4. 查询汇总列表

#### 4.1 查询所有月份汇总
```bash
curl -X GET "http://localhost:8080/business/commission/report/summary/list" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 4.2 查询特定月份汇总
```bash
curl -X GET "http://localhost:8080/business/commission/report/summary/list?dataMonth=2025-01-01" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

返回示例：
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "dataMonth": "2025-01-01",
      "budgetUsd": 50000.0000,
      "payoutCapUsd": 15000.0000,
      "actualPayoutUsd": 12500.0000,
      "payoutToBudgetRatio": 0.2500,
      "exceededAmountUsd": 0.0000,
      "totalBonusEstimated": 8750.5000,
      "calculationStatus": "completed",
      "calculatedAt": "2025-01-17 15:30:45"
    }
  ],
  "total": 1
}
```

## 数据验证

### 验证计算准确性

可以通过以下 SQL 查询验证 `total_bonus_estimated` 的计算是否正确：

```sql
-- 手动计算当月主播奖金估计总额
SELECT 
    DATE_FORMAT(data_month, '%Y-%m') AS month,
    SUM(bonus_estimated) AS manual_total
FROM monthly_performance 
WHERE DATE_FORMAT(data_month, '%Y-%m') = '2025-01'
GROUP BY DATE_FORMAT(data_month, '%Y-%m');

-- 查看存储在汇总表中的值
SELECT 
    DATE_FORMAT(data_month, '%Y-%m') AS month,
    total_bonus_estimated AS stored_total
FROM commission_monthly_summary 
WHERE DATE_FORMAT(data_month, '%Y-%m') = '2025-01';
```

两个查询的结果应该一致。

## 常见问题

### Q1: 为什么 `totalBonusEstimated` 为 0？

**可能原因：**
1. 该月份还没有执行佣金计算
2. 该月份没有 `monthly_performance` 数据
3. 所有主播的 `bonus_estimated` 值都为 0

**解决方案：**
```bash
# 1. 检查是否有业绩数据
curl -X GET "http://localhost:8080/business/monthly-performance/list?dataMonth=2025-01-01"

# 2. 执行佣金计算
curl -X POST "http://localhost:8080/business/commission/calculation/execute/2025-01"
```

### Q2: 如何重新计算历史月份的数据？

```bash
# 使用强制重新计算
curl -X POST "http://localhost:8080/business/commission/calculation/recalculate/2024-12?force=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Q3: 计算状态一直是 "processing" 怎么办？

```bash
# 检查计算日志
curl -X GET "http://localhost:8080/business/commission/calculation/status/2025-01" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 如果确认卡住，可以强制重新计算
curl -X POST "http://localhost:8080/business/commission/calculation/recalculate/2025-01?force=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 性能优化建议

1. **批量处理**：如果需要计算多个月份，建议逐月执行，避免并发计算
2. **监控资源**：计算过程可能消耗较多数据库资源，建议在低峰期执行
3. **数据预检**：计算前确认 `monthly_performance` 数据完整性

## 注意事项

1. `total_bonus_estimated` 字段只有在执行佣金计算后才会有值
2. 修改 `monthly_performance` 数据后，需要重新执行计算以更新汇总值
3. 该字段反映的是主播的奖金估计，不是分销员佣金
4. 计算过程是事务性的，确保数据一致性 