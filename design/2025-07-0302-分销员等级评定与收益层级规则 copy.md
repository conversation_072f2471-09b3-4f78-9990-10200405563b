<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<data_structure>
@database.sql
</data_structure>

<data_changes>


-- 使用 INSERT ... ON DUPLICATE KEY UPDATE 确保数据可重复执行插入或更新
INSERT INTO `commission_settings` (`setting_key`, `setting_value`, `description`) VALUES
('new_recruit_tenure_days', '60', '新人资格-账号最大创建天数'),
('new_recruit_min_valid_days', '24', '新人资格-月度最低有效直播天数'),
('retention_target_avg_growth_rate', '0.15', '业绩保级-目标平均增长率(15%)'),
('retention_growth_from_zero', '1.00', '业绩保级-从零增长的定义值(100%)'),
('retention_growth_rate_cap', '3.00', '业绩保级-单月增长率上限(300%)'),
('new_user_grace_period_months', '4', '新手保护期月数'),
('upgrade_protection_period_months', '2', '升级保护期月数')
ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`), `description` = VALUES(`description`);
 
<data_changes>

<requirements>

# 需求

## 设计详案 

关联需求:
“分销员等级（金/银/铜）的评定，依据其成功招募的‘合格拉新’数量。”
“金牌分配下3级， 银牌收益下2级， 铜牌收益下1级。”

1. 需求背景

本需求定义了分销体系的核心激励机制：如何获得尊贵的身份等级，以及该等级能带来什么样的核心权益。方案的设计目标是建立一个清晰、灵活、可配置的等级与权益绑定体系。
等级评定 (如何获得): 明确了分销员的等级晋升路径完全依赖于其“拉新”能力，即“进攻”能力。
收益层级 (获得什么): 明确了不同等级直接对应不同的佣金收益深度，等级越高，能从团队网络中获得的收益范围越广。

2. 核心挑战与设计原则

挑战一：灵活性与可维护性 (Flexibility & Maintainability)
问题: 金、银、铜牌的晋升门槛（需要多少拉新人数）和对应的收益深度（3/2/1级）可能会随着业务发展而调整。将这些规则硬编码在程序中，会导致每次调整都需要修改代码，效率低下且风险高。
设计原则: “规则数据化”。必须将所有等级的定义、晋升门槛和权益，全部存储在数据库的规则表中，使之成为可由管理员配置的数据，而非写死的代码逻辑。
挑战二：逻辑集成 (Integration)
问题: 等级评定逻辑在整个“一键计算”流程中处于什么位置？
设计原则: “承上启下，流程清晰”。等级评定必须在“合格拉新”资格判定之后（承上），并在“佣金计算”之前（启下）完成。它是连接“拉新事实”和“佣金权益”的关键桥梁。

3. 最终解决方案：规则驱动的等级体系

我们采用完全由数据库驱动的规则引擎来解决此需求。系统的所有行为都将基于数据库中配置的规则，而非写死的程序逻辑。

3.1. 等级评定：基于“进攻”的月度快照

评定依据: 分销员在某个考核月（例如7月）的等级，完全由其在该月成功招募的“合格拉新”数量决定。
数据来源: 此步骤的计算，直接依赖于我们在上一份方案中详述的**“前置任务一：全量‘合格拉新’资格判定”**的产出结果。
评定性质: 这是一个月度快照。用户在7月获得的“进攻等级”，是基于他7月份的拉新表现，这构成了他参与当月最终等级评定（MAX(进攻, 防守)）的基础。

3.2. 收益层级：与等级绑定的核心权益

权益绑定: “享受下N级佣金”这一核心权益，被设计为每个等级的一项固有属性。
实现机制: 在等级的规则定义中，我们直接包含一个字段（payout_depth）来明确其收益深度。例如，金牌的记录中，payout_depth 的值就是 3。

4. 数据库设计详述

等级体系的核心，由 commission_level_rules 表来定义和驱动。
表名: commission_level_rules
作用: 存储所有分销员等级的定义、晋升条件和核心权益。
关键字段详述:
id (INT): 主键。
level_name (VARCHAR): 等级名称，如 '金牌', '银牌', '铜牌'。
min_qualified_recruits (INT): 【晋升门槛】 达到此等级所需要的最低月度合格拉新人数。
payout_depth (TINYINT): 【核心权益】 该等级对应的佣金收益深度。
is_active (TINYINT): 规则是否启用（1=是, 0=否）。
display_order (TINYINT): 用于在界面排序显示。
示例数据:
| id | level_name | min_qualified_recruits | payout_depth | is_active |
|:---|:---|:---:|:---:|:---:|
| 1 | 金牌 | 10 | 3 | 1 |
| 2 | 银牌 | 5 | 2 | 1 |
| 3 | 铜牌 | 2 | 1 |
关联表 commission_multilevel_rules:
payout_depth 定义了**“能拿几层”**，而每一层的具体提成比例（例如L1提2%，L2提1%），则由 commission_multilevel_rules 表来定义。两者协同工作，完整地构成了佣金计算规则。

5. 应用与执行流程

本需求的逻辑，在“一键计算”流程中作为**“前置任务二”**的核心部分被执行。
输入: “前置任务一”已经完成，所有当月“合格拉新”及其归属已在数据库中标记。
执行逻辑: 系统遍历所有分销员，为每个人执行以下操作：
A. 统计拉新数: COUNT 该用户在当月名下的“合格拉新”总数。
B. 匹配等级: 使用上一步得到的拉新数，查询 commission_level_rules 表，找出他能达到的最高等级。
SQL伪代码: SELECT * FROM commission_level_rules WHERE min_qualified_recruits <= [拉新总数] ORDER BY min_qualified_recruits DESC LIMIT 1;
C. 记录结果: 将查询到的等级（如“金牌”）及其对应的 payout_depth (如 3)，作为该用户的“进攻等级”结果，记录在月度资格表中，用于后续的最终等级评定和佣金计算。
输出: 所有分销员的“进攻等级”和对应的收益深度计算完毕。

## 字段作用说明：payout_depth (佣金收益深度)

一、 核心定义

payout_depth 字段定义了一个分销员有资格获取其团队网络中多少层级（深度）下线所产生业绩的佣金。
简单来说，这个数字决定了你的“管道”有多长，能从多远的下游成员那里获益。

二、 场景设置

为了说明问题，我们先设定好两个关键要素：
1. 团队层级结构
假设我们有一个四层的团队结构：
用户A → 用户B → 用户C → 用户D
B 是 A 的 L1 (下1级)
C 是 A 的 L2 (下2级)
D 是 A 的 L3 (下3级)
2. 等级规则表 (commission_level_rules)
level_name
payout_depth
金牌
3
银牌
2
铜牌
1

3. 触发事件
现在，处在最底层的 用户D 产生了一笔可以带来 1000钻石 佣金的业绩。系统需要判断，作为团队顶层的 用户A，是否有资格拿到这笔佣金。

三、 不同场景下的作用分析

用户A 能否拿到这笔由 用户D 产生的佣金，完全取决于 用户A 当月的等级及其对应的 payout_depth。

场景一：用户A是“金牌”分销员

获取A的规则: 系统查询到 用户A 的等级是金牌。
查找A的权益: 从规则表中得知，金牌的 payout_depth 是 3。这意味着A最多可以拿到他L3下线产生的佣金。
判定资格:
产生业绩的 用户D，是 用户A 的 L3。
A的收益深度是3 (payout_depth = 3)。
因为 3 <= 3 (事件发生的层级 ≤ 用户享受的层级)，条件满足。
结论: 用户A可以 拿到这笔由用户D产生的佣金。

场景二：用户A是“银牌”分销员

获取A的规则: 系统查询到 用户A 的等级是银牌。
查找A的权益: 从规则表中得知，银牌的 payout_depth 是 2。这意味着A最多可以拿到他L2下线产生的佣金。
判定资格:
产生业绩的 用户D，是 用户A 的 L3。
A的收益深度是2 (payout_depth = 2)。
因为 3 > 2 (事件发生的层级 > 用户享受的层级)，条件不满足。
结论: 用户A不可以 拿到这笔由用户D产生的佣金。佣金流到 用户B（D是B的L2）后，对于A来说，就因为深度不够而中断了。

场景三：用户A是“铜牌”分销员

获取A的规则: 系统查询到 用户A 的等级是铜牌。
查找A的权益: 从规则表中得知，铜牌的 payout_depth 是 1。这意味着A只能拿到他L1（即用户B）产生的佣金。
判定资格:
产生业绩的 用户D，是 用户A 的 L3。
A的收益深度是1 (payout_depth = 1)。
因为 3 > 1，条件不满足。
结论: 用户A更不可能 拿到这笔佣金。

四、 总结

payout_depth 字段就像一个“权限开关”，它在佣金计算的每一步都进行检查，直接决定了：
激励的差异化: 它将“高等级”和“高收益”直接挂钩，等级越高，收益网络越广，这是激励用户冲击更高等级的最核心动力。
系统的灵活性: 未来如果业务需要调整，比如想让“金牌”能拿到5层收益，让“银牌”也能拿到3层，运营人员只需要修改规则表里的 payout_depth 数值即可，完全无需改动程序代码，实现了业务规则的“可配置”。


## 已有系统中的收入分配机制  

### 2.2 收入来源分类

#### 2.2.1 分销员等级分成 (LEVEL_COMMISSION)
- **计算基础**: 个人钻石收入 + 直属下级(L1)钻石收入总和
- **前置条件**: 
  1. 达到动态钻石门槛
  2. 满足等级要求的直属下级人数
- **计算公式**: `等级分成 = 团队总钻石 × 等级分成比例`

#### 2.2.2 下三级提成 (MULTI_LEVEL_COMMISSION)
- **计算基础**: L1、L2、L3各层级的钻石收入
- **前置条件**: 获得任一分销员等级资格
- **计算公式**: `下三级提成 = ∑(Li收入 × Li提成比例)` (i=1,2,3)

INSERT INTO `commission_multilevel_rules` (`id`, `depth`, `commission_rate`, `is_active`, `created_at`, `updated_at`)
VALUES
	(1, 1, 0.0000, 1, '2025-06-20 11:25:07', '2025-06-23 16:03:45'),
	(2, 2, 0.0100, 1, '2025-06-20 11:25:07', '2025-06-23 16:03:45'),
	(3, 3, 0.0050, 1, '2025-06-20 11:25:07', '2025-06-23 16:03:45');

#### 2.2.3 拉新奖励 (RECRUITMENT_BONUS)
- **计算基础**: 当月成功招募的新人数量
- **前置条件**: 无
- **计算公式**: 按拉新人数阶梯匹配USD奖励

</requirements>

<db_config>
可配置的系统参数列表


参数项
内部标识/Key (建议)
当前建议值
说明与配置原因
“进攻”体系参数


1. 新人资格-账号时长
new_recruit_tenure_days
60
定义: 判断一个用户是否为“新人”的最大账号创建天数。
原因: 未来可能会调整对“新人”的定义，例如放宽到90天或缩紧到30天。
2. 新人资格-活跃天数
new_recruit_min_valid_days
24
定义: 新人在考核月需要达到的最低有效直播天数。
原因: 这是衡量“高活跃”的关键指标，可能会根据整体用户活跃情况进行调整。
“防守”体系参数

3. 业绩保级-目标增长率
retention_target_avg_growth_rate
0.15
定义: 月度环比增长率的平均值需要达到的目标，0.15代表15%。
原因: 这是整个保级体系的核心目标，是未来最有可能被微调的业务参数。
4. “从零增长”的定义值
retention_growth_from_zero
1.00
定义: 当上月业绩为0，本月不为0时，记为100%增长。
原因: 这个特殊定义值直接影响考核结果，需要根据实际效果进行调整，我们刚刚就把它从500%调整到了100%。
5. 单月增长率上限
retention_growth_rate_cap
3.00
定义: 为防止数据泡沫，计入平均值的单月增长率上限，3.00代表300%。
原因: 这个“风控”数值，需要根据观察到的用户行为来调整，以防止规则被滥用。
保护期与特殊规则参数

6. 新手保护期
new_user_grace_period_months
4
定义: 新用户豁免“防守”考核的月数。
原因: 业务方可能会根据新人成长速度，决定延长或缩短这个保护期。
7. 升级保护期
upgrade_protection_period_months
2
定义: 通过“进攻”升级后，用户豁免“防守”考核的月数。
原因: 同上，这个保护期也需要根据运营策略灵活调整。

通过数据表管理的业务规则（非系统参数）

除了上述简单的键值对参数，我们方案中的另一些核心规则，已经通过设计良好的数据表来实现“配置化”了。这些通常不放在 commission_settings 中，因为它们是结构化的规则集。
等级评定标准 (金/银/铜)
配置方式: 通过 commission_level_rules 表进行管理。
可配置项:
level_name: 等级名称。
min_qualified_recruits: 晋升到该等级所需要的月度合格拉新人数。
payout_depth: 该等级对应的佣金收益深度。
灵活性: 运营人员可以直接修改这张表的数据，来调整每个等级的晋升门槛和权益，甚至可以新增“钻石级”等。
多层级佣金费率
配置方式: 通过 commission_multilevel_rules 表进行管理。
可配置项:
depth: 下级深度（1=L1, 2=L2...）。
commission_rate: 对应层级的提成比例。
灵活性: 可以灵活调整不同层级的具体提成比例。

</db_config>

<source_files>
@CommissionCalculationController.java
</source_files>

- 先读需求内容requirements， 了解后. 读数据库结构 data_structure,  了解相关的数据修改data_changes， 及配置参数db_config
- 逐步思考, 本次的修改内容， 及修改方案， 列出修改计划
- 实现修改现有代码逻辑
- 所有的代码实现后， 为其它大模型生成一个review这次修改内容的提示词，生成到本地的/design/test/ 目录中。 提示词要求： 提供本次修改的文件及对应的行数范围， 修改的内容，需求， 及要求review 的内容。 

