
<配置项>
配置需求	数据库表	管理接口
基础分成门槛	commission_settings	/config/settings
动态门槛上浮比例	commission_settings	/config/settings
月度预算收入	commission_monthly_settings	/config/monthly
钻石兑美元汇率	commission_monthly_settings	/config/monthly
支出上限比例	commission_monthly_settings	/config/monthly
分销员等级规则	commission_level_rules	/rules/level
下三级提成比例	commission_multilevel_rules	/rules/multilevel
拉新奖励阶梯	commission_recruitment_bonus_rules	/rules/recruitment
</配置项> 

<配置表>
配置表	服务接口	服务实现	Mapper接口	XML映射
commission_settings	✅	✅	✅	✅
commission_monthly_settings	✅	✅	✅	✅
commission_level_rules	✅	✅	✅	✅
commission_multilevel_rules	✅	✅	✅	✅
commission_recruitment_bonus_rules	✅	✅	✅	✅
</配置表>

<数据库结构> 
@database.sql 
</数据库结构> 



- 根据配置项及数据结构， 逐步思考 如何检查 生成的代码， 是否符合数据库的结构及相关的字段 
- 按思考结果，检查每个接口， 实体， 及对应的mapper 文件 中是否符合每个配置项对应的数据表的字段及类型。 
- 预期是检查完成后， 程序不会出现任何数据库字段 等不对应的问题。 
- 重点检查 mapper 及 实体与数据库结构的对应 

