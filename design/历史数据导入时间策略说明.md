# 历史数据导入时间策略说明

## 问题背景

在导入历史数据时，如果导入的是3月的数据，creators表中的创建时间也是当月时间（3月），但新人计算逻辑是基于考核月份往前推60天来判定的。这会导致时间对不上的问题：

### 新人资格判定逻辑
1. **基准点**：考核月份的最后一天（比如7月31日 23:59:59）
2. **门槛日期**：从基准点往前推60天（比如6月1日左右）
3. **判定条件**：用户创建时间必须 >= 门槛日期

### 问题场景
- 导入3月的历史数据，creators的创建时间设为3月
- 计算7月的佣金时，门槛日期是6月1日左右
- 3月创建的用户早于6月1日，不符合"新人"标准

## 解决方案

### 1. 默认策略（推荐）
**新creator的创建时间设为当前时间**

- **优点**：不影响新人资格判定逻辑，保持业务逻辑一致性
- **适用场景**：大部分历史数据导入场景
- **API接口**：`POST /h/historical/import`

```java
// 默认使用当前时间作为创建时间
Map<String, Object> result = historicalDataService.importHistoricalData(file, updateBy, false);
```

### 2. 历史时间策略
**新creator的创建时间设为数据月份**

- **优点**：保持历史数据的时间准确性
- **缺点**：可能影响新人资格判定，需要谨慎使用
- **适用场景**：需要保持历史时间准确性的特殊场景
- **API接口**：`POST /h/historical/import-with-historical-time`

```java
// 使用历史数据月份作为创建时间
Map<String, Object> result = historicalDataService.importHistoricalData(file, updateBy, true);
```

## API接口说明

### 1. 默认导入接口
```http
POST /h/historical/import
Content-Type: multipart/form-data

file: [Excel文件]
```

**特点**：
- 新creator的创建时间设为当前时间
- 不影响新人资格判定
- 推荐使用

### 2. 历史时间导入接口
```http
POST /h/historical/import-with-historical-time
Content-Type: multipart/form-data

file: [Excel文件]
```

**特点**：
- 新creator的创建时间设为数据月份
- 可能影响新人资格判定
- 谨慎使用

## 实现细节

### 代码逻辑
```java
private Creator insertOrUpdateCreator(Map<String, Object> rowData, String updateBy, boolean useHistoricalCreateTime) {
    // ... 其他逻辑
    
    if (creator == null) { // 新增creator
        creator = new Creator();
        // ... 设置基本信息
        
        if (useHistoricalCreateTime) {
            // 策略1：使用历史数据月份作为创建时间
            String dataMonthStr = (String) rowData.get("dataMonth");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            Date historicalDate = sdf.parse(dataMonthStr);
            creator.setCreateTime(historicalDate);
        } else {
            // 策略2：使用当前时间作为创建时间（默认）
            creator.setCreateTime(DateUtils.getNowDate());
        }
    } else {
        // 更新现有creator，不修改创建时间
        // ... 更新逻辑
    }
}
```

### 日志记录
系统会记录使用的时间策略：
- 默认策略：`"历史数据导入：为creator {} 设置创建时间为当前时间，避免影响新人资格判定"`
- 历史时间策略：`"历史数据导入：为creator {} 设置创建时间为历史月份 {}"`

## 使用建议

### 1. 一般情况
**推荐使用默认策略**（当前时间）：
- 保持新人资格判定逻辑的一致性
- 避免历史数据影响当前业务逻辑
- 适用于大部分历史数据导入场景

### 2. 特殊情况
**谨慎使用历史时间策略**：
- 仅在需要保持历史时间准确性时使用
- 使用前需要评估对新人资格判定的影响
- 可能需要调整新人资格判定的配置参数

### 3. 配置调整
如果使用历史时间策略，可能需要调整新人资格判定配置：

```sql
-- 调整账号创建天数阈值（比如从60天调整到更大的值）
UPDATE commission_settings 
SET setting_value = '120' 
WHERE setting_key = 'new_recruit_tenure_days';
```

## 注意事项

1. **数据一致性**：同一批历史数据应使用相同的时间策略
2. **业务影响**：使用历史时间策略前需要评估对新人资格判定的影响
3. **配置同步**：如果使用历史时间策略，可能需要同步调整相关配置参数
4. **测试验证**：导入后建议测试新人资格判定功能是否正常

## 相关配置

### 新人资格判定配置
- `new_recruit_tenure_days`：账号创建天数阈值（默认60天）
- `new_recruit_min_valid_days`：最小有效直播天数（默认24天）

### 配置查询接口
```http
GET /business/new-recruit-qualification/config
```

### 配置更新接口
```http
POST /business/new-recruit-qualification/config
Content-Type: application/x-www-form-urlencoded

tenureDays=120&minValidDays=24
```
