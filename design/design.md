# 主播分销系统 

## 1. 系统概述

本系统旨在管理主播的分销层级关系及月度业绩数据。其核心是实现对第三方Excel数据的自动化导入、层级关系解析、数据持久化存储以及高效的层级查询。

最终设计采用**“邻接表 + 闭包表”**的混合数据库模式，以兼顾数据写入的简明性与层级查询的高性能，完美适配MySQL 5.7环境。

## 2. 数据库设计 (Database Design)

### 2.1 设计模式
-   **邻接表 (Adjacency List)**: 在`creators`表中通过`parent_id`字段存储每个主播的**直接上级**。这作为层级关系的“源头真相(Source of Truth)”，使得数据导入和单点关系维护逻辑简单清晰。
-   **闭包表 (Closure Table)**: 创建一张独立的`creator_relationships`表，预先计算并存储**所有主播之间的所有上下级路径关系**（包括跨级）。这张表专门用于加速复杂的层级查询，如获取全部分支、指定深度的下级等。

### 2.2 表结构详情

#### 表1: `creators` - 主播信息与直接关系表
存储主播的静态信息和直接父子关系。

| 字段名 | 数据类型 | 主键/索引 | 注释 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | **PK** | 主播的唯一ID (Creator ID)。 |
| `parent_id` | `BIGINT UNSIGNED` | **INDEX** | 直接上级主播的ID。顶级主播此字段为`NULL`。 |
| `nickname` | `VARCHAR(255)` | | 主播昵称 (支持emoji)。 |
| `handle` | `VARCHAR(255)` | **UNIQUE** | 主播的Handle，具有唯一性。 |
| `created_at` | `TIMESTAMP` | | 记录创建时间。 |
| `updated_at` | `TIMESTAMP` | | 记录最后更新时间。 |

#### 表2: `creator_relationships` - 层级关系加速表 (闭包表)
用于高性能的层级查询。

| 字段名 | 数据类型 | 主键/索引 | 注释 |
| :--- | :--- | :--- | :--- |
| `ancestor_id` | `BIGINT UNSIGNED`| **PK (复合)** | 祖先用户ID。 |
| `descendant_id` | `BIGINT UNSIGNED`| **PK (复合)**, INDEX | 后代用户ID。 |
| `depth` | `INT` | INDEX | 层级深度 (0表示自己, 1表示直接下级)。 |

#### 表3: `monthly_performance` - 主播月度业绩表
存储主播每月动态的业绩数据。

| 字段名 | 数据类型 | 主键/索引 | 注释 |
| :--- | :--- | :--- | :--- |
| `id` | `INT UNSIGNED` | **PK** | 自增主键。 |
| `creator_id` | `BIGINT UNSIGNED`| **FK, INDEX** | 关联`creators.id`。 |
| `data_month` | `DATE` | **INDEX** | 数据月份 (如 '2025-04-01')。 |
| ... | ... | | ...所有业绩和奖金字段。 |
| `UNIQUE KEY` | `uk_creator_month`(`creator_id`, `data_month`) | | 防止同一主播同月数据重复。 |

#### 表4: `raw_imports` - 原始数据导入记录表
完整备份每次导入的Excel源数据，用于审计和追溯。

| 字段名 | 数据类型 | 主键/索引 | 注释 |
| :--- | :--- | :--- | :--- |
| `id` | `INT UNSIGNED` | **PK** | 自增主键。 |
| `file_name` | `VARCHAR(255)` | | 导入的源文件名。 |
| `imported_at` | `TIMESTAMP` | | 导入操作的时间戳。 |
| ... | `TEXT` / `VARCHAR`| | ...所有Excel中的原始字段。 |

---
---

### **db/final_schema.sql**

```sql
-- -------------------------------------------------------------
-- 主播分销系统数据库结构 (最终优化版)
-- 数据库: streamer_distribution_system
-- 目标服务器: MySQL 5.7
-- -------------------------------------------------------------

CREATE DATABASE IF NOT EXISTS `streamer_distribution_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `streamer_distribution_system`;

--
-- 表1: `creators` - 主播信息与直接关系表
--
DROP TABLE IF EXISTS `creators`;
CREATE TABLE `creators` (
  `id` BIGINT UNSIGNED NOT NULL COMMENT '主播的唯一ID (Creator ID)',
  `parent_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '直接上级主播的ID, 顶级为NULL',
  `nickname` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主播昵称',
  `handle` VARCHAR(255) NOT NULL COMMENT '主播的Handle',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_handle` (`handle`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播信息与直接关系表';

--
-- 表2: `creator_relationships` - 层级关系加速表 (闭包表)
--
DROP TABLE IF EXISTS `creator_relationships`;
CREATE TABLE `creator_relationships` (
  `ancestor_id` BIGINT UNSIGNED NOT NULL COMMENT '祖先用户ID (关联 creators.id)',
  `descendant_id` BIGINT UNSIGNED NOT NULL COMMENT '后代用户ID (关联 creators.id)',
  `depth` INT NOT NULL COMMENT '层级深度 (0表示用户自身)',
  PRIMARY KEY (`ancestor_id`, `descendant_id`),
  KEY `idx_cr_descendant_depth` (`descendant_id`, `depth`),
  KEY `idx_cr_ancestor_depth` (`ancestor_id`, `depth`),
  CONSTRAINT `fk_rel_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rel_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主播层级关系闭包表';

--
-- 表3: `monthly_performance` - 主播月度业绩表
--
DROP TABLE IF EXISTS `monthly_performance`;
CREATE TABLE `monthly_performance` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `creator_id` BIGINT UNSIGNED NOT NULL COMMENT '关联到creators.id',
  `data_month` DATE NOT NULL COMMENT '数据月份 (存储为每月第一天)',
  `group_name` VARCHAR(255) DEFAULT NULL COMMENT 'Group',
  `group_manager` VARCHAR(255) DEFAULT NULL COMMENT 'Group manager',
  `is_violative` BOOLEAN DEFAULT FALSE COMMENT '是否违规主播',
  `is_rookie` BOOLEAN DEFAULT FALSE COMMENT '是否新人主播',
  `diamonds` BIGINT DEFAULT 0 COMMENT '钻石数',
  `valid_days` INT DEFAULT 0 COMMENT '有效天数(d)',
  `live_duration_hours` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '直播时长(h)',
  `bonus_estimated` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '估计的奖金',
  `bonus_rookie_m1_retention` VARCHAR(50) DEFAULT NULL COMMENT '新人里程碑1保留任务奖金',
  `bonus_rookie_m2` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '新人里程碑2任务奖金',
  `bonus_rookie_half_milestone` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '新人半里程碑任务奖金',
  `bonus_rookie_m1` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '新人里程碑1任务奖金',
  `bonus_activeness` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '活跃任务奖金',
  `bonus_revenue_scale` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '收入规模任务奖金',
  `bonus_new_creator_network` DECIMAL(10, 4) DEFAULT 0.0000 COMMENT '新创作者网络任务奖金',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_creator_month` (`creator_id`, `data_month`),
  CONSTRAINT `fk_performance_creator` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播月度业绩数据表';

--
-- 表4: `raw_imports` - 原始数据导入记录表
--
DROP TABLE IF EXISTS `raw_imports`;
CREATE TABLE `raw_imports` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(255) DEFAULT NULL COMMENT '源文件名',
  `imported_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `data_month` VARCHAR(255) DEFAULT NULL,
  `creator_id` VARCHAR(255) DEFAULT NULL,
  `creator_nickname` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `handle` VARCHAR(255) DEFAULT NULL,
  `creator_network_manager` VARCHAR(255) DEFAULT NULL,
  `group_name` VARCHAR(255) DEFAULT NULL,
  `group_manager` VARCHAR(255) DEFAULT NULL,
  `is_violative_creators` VARCHAR(255) DEFAULT NULL,
  `the_creator_was_rookie` VARCHAR(255) DEFAULT NULL,
  `diamonds` VARCHAR(255) DEFAULT NULL,
  `valid_days` VARCHAR(255) DEFAULT NULL,
  `live_duration_h` VARCHAR(255) DEFAULT NULL,
  `estimated_bonus` VARCHAR(255) DEFAULT NULL,
  `est_bonus_rookie_m1_retention` VARCHAR(255) DEFAULT NULL,
  `est_bonus_rookie_m2` VARCHAR(255) DEFAULT NULL,
  `est_bonus_rookie_half_milestone` VARCHAR(255) DEFAULT NULL,
  `est_bonus_rookie_m1` VARCHAR(255) DEFAULT NULL,
  `est_bonus_activeness_task` VARCHAR(255) DEFAULT NULL,
  `est_bonus_revenue_scale_task` VARCHAR(255) DEFAULT NULL,
  `est_bonus_new_creator_network_task` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原始Excel数据导入记录表';

```
---
---

### **doc/最终系统实现方案.md**

# 主播分销系统 - 最终系统实现方案

## 1. 数据导入与处理流程 (ETL)

此流程由后台应用程序（如Python, Java, Node.js脚本）执行，确保数据的一致性和完整性。

1.  **上传并存储源数据**: 用户上传Excel文件。程序读取文件，将每一行作为文本原样插入到 `raw_imports` 表中，并记录文件名和时间戳。

2.  **更新主播基础信息**: 遍历本次导入的数据。对每一行，提取`Creator ID`, `Creator nickname`, `Handle`等信息。使用`INSERT ... ON DUPLICATE KEY UPDATE ...`语句，将这些信息更新到 `creators` 表中。

3.  **更新直接层级关系**: 再次遍历导入的数据。解析 `Creator Network manager` 字段，提取出上级的ID。执行 `UPDATE creators SET parent_id = ? WHERE id = ?`，在`creators`表中建立起直接的父子链接。

4.  **核心步骤：重建关系闭包表**: 这是保证查询性能的关键。此步骤在`parent_id`全部更新完毕后执行。
    * **清空旧数据**: `TRUNCATE TABLE creator_relationships;`
    * **插入自身关系**: 为`creators`表中的每个主播，都在`creator_relationships`表中插入一条自己到自己的记录。
        ```sql
        INSERT INTO creator_relationships (ancestor_id, descendant_id, depth)
        SELECT id, id, 0 FROM creators;
        ```
    * **计算并插入所有路径**: 在应用程序中，通过循环或递归遍历`creators`表中的`parent_id`关系，计算出所有路径并插入。
        **基本逻辑**: 如果A是B的上级，那么A也是B所有下级的上级。
        ```sql
        -- 这个SQL需要在循环中执行，直到没有新行被插入。
        -- level 代表当前已计算的深度
        INSERT INTO creator_relationships (ancestor_id, descendant_id, depth)
        SELECT
            sup.ancestor_id,         -- 上级的上级
            sub.descendant_id,       -- 下级的下级
            sup.depth + sub.depth + 1 -- 深度相加
        FROM
            creator_relationships sup
        JOIN
            creator_relationships sub ON sup.descendant_id = sub.ancestor_id
        WHERE
            sup.depth + sub.depth + 1 > (SELECT MAX(depth) FROM creator_relationships WHERE ancestor_id = sup.ancestor_id AND descendant_id = sub.descendant_id); -- 伪代码，表示避免重复
        ```
        更稳妥的方式是在应用层代码中进行图遍历来填充这张表。

5.  **导入月度业绩**: 最后，遍历源数据，将业绩指标（钻石、奖金等）进行类型转换后，使用`INSERT ... ON DUPLICATE KEY UPDATE ...`语句更新到`monthly_performance`表中。

## 2. 核心功能实现方案

所有层级查询都将通过`creator_relationships`表进行，以获得最佳性能。

### 2.1 识别主播角色
-   **网络经济人 (Manager)**: `SELECT DISTINCT ancestor_id FROM creator_relationships WHERE depth > 0;`
-   **网络主播/普通主播 (Subordinate)**: `SELECT DISTINCT descendant_id FROM creator_relationships WHERE depth > 0;`
-   **顶级主播**: `SELECT id FROM creators WHERE parent_id IS NULL;`

### 2.2 按树形显示（获取某用户所有下级）
```sql
-- 获取ID为 7498598837318123537 的所有下级，用于生成层级树
SELECT
    c.id,
    c.nickname,
    c.handle,
    cr.depth,
    c.parent_id -- 同时提供直接上级ID，便于在前端构建树
FROM
    creator_relationships cr
JOIN
    creators c ON cr.descendant_id = c.id
WHERE
    cr.ancestor_id = 7498598837318123537 AND cr.ancestor_id != cr.descendant_id
ORDER BY
    cr.depth;
```

### 2.3 查询指定用户下3级的内容
```sql
-- 查询ID为 7498598837318123537 的下1至3级的主播
SELECT
    c.id,
    c.nickname,
    c.handle,
    cr.depth AS `level`
FROM
    creator_relationships cr
JOIN
    creators c ON cr.descendant_id = c.id
WHERE
    cr.ancestor_id = 7498598837318123537
    AND cr.depth BETWEEN 1 AND 3
ORDER BY
    `level`, c.id;
```
 