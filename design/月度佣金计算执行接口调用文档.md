# 月度佣金计算执行接口调用文档

## 概述
本文档说明月度佣金计算执行界面各功能模块对应的后端接口调用方法，包括计算执行、状态查询、历史记录查看等功能。

## 基础信息

**Base URL**: `/business/commission/calculation`

**认证方式**: Bearer <PERSON> (需要登录后获取)

**内容类型**: `application/json`

**权限要求**: `h:commission_calculation`

---

## 1. 计算执行功能

### 1.1 检查计算前置条件

**接口**: `GET /business/commission/calculation/check-preconditions/{dataMonth}`

**说明**: 在执行计算前，检查所有必要的配置和数据是否就绪

**请求参数**:
- `dataMonth`: 数据月份，格式 `yyyy-MM`，如 `2024-01`

**请求示例**:
```
GET /business/commission/calculation/check-preconditions/2024-01
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "canCalculate": true,
    "message": "找到 150 个主播的业绩数据，其中 120 个有钻石收入；共有 200 个主播，其中 180 个配置了上级关系；所有前置条件检查通过，可以开始计算。",
    "dataMonth": "2024-01",
    "errors": [],
    "warnings": [
      "拉新奖励规则未配置，将无法计算拉新奖励"
    ]
  }
}
```

### 1.2 执行月度佣金计算

**接口**: `POST /business/commission/calculation/execute/{dataMonth}`

**说明**: 执行指定月份的佣金计算，这是正常计算模式

**请求参数**:
- `dataMonth`: 数据月份，格式 `yyyy-MM`

**请求示例**:
```
POST /business/commission/calculation/execute/2024-01
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "月度佣金计算执行成功",
    "dataMonth": "2024-01",
    "summary": {
      "dataMonth": "2024-01-01",
      "budgetUsd": 150000.0000,
      "payoutCapUsd": 37500.0000,
      "actualPayoutUsd": 32450.7500,
      "payoutToBudgetRatio": 0.2163,
      "exceededAmountUsd": 0.0000,
      "calculationStatus": "completed",
      "calculatedAt": "2024-02-01 17:15:30"
    }
  }
}
```

### 1.3 预览计算结果

**接口**: `POST /business/commission/calculation/preview/{dataMonth}`

**说明**: 预览计算结果，不保存到数据库，用于验证计算逻辑

**请求示例**:
```
POST /business/commission/calculation/preview/2024-01
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "预览计算完成",
    "dataMonth": "2024-01",
    "previewData": {
      "totalDistributors": 85,
      "totalCommissionAmount": 32450.75,
      "totalLevelCommission": 18500.50,
      "totalMultilevelCommission": 11200.25,
      "totalRecruitmentBonus": 2750.00
    }
  }
}
```

### 1.4 重新计算月度佣金

**接口**: `POST /business/commission/calculation/recalculate/{dataMonth}?force=true`

**说明**: 重新计算已完成的月份佣金，可强制覆盖

**请求参数**:
- `dataMonth`: 数据月份，格式 `yyyy-MM`
- `force`: 是否强制重新计算，默认 `false`

**请求示例**:
```
POST /business/commission/calculation/recalculate/2024-01?force=true
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "月度佣金计算执行成功",
    "dataMonth": "2024-01"
  }
}
```

---

## 2. 状态查询功能

### 2.1 获取计算状态

**接口**: `GET /business/commission/calculation/status/{dataMonth}`

**说明**: 实时查询指定月份的计算进度和状态

**请求示例**:
```
GET /business/commission/calculation/status/2024-01
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "status": "completed",
    "calculatedAt": "2024-02-01 17:15:30",
    "actualPayoutUsd": 32450.7500,
    "budgetUsd": 150000.0000,
    "message": "计算已完成",
    "dataMonth": "2024-01"
  }
}
```

**状态值说明**:
- `not_started`: 该月份尚未开始计算
- `processing`: 计算进行中
- `completed`: 计算已完成
- `failed`: 计算失败

### 2.2 获取月度计算总览

**接口**: `GET /business/commission/calculation/summary/{dataMonth}`

**说明**: 获取指定月份的详细计算总览信息

**请求示例**:
```
GET /business/commission/calculation/summary/2024-01
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "dataMonth": "2024-01-01",
    "budgetUsd": 150000.0000,
    "payoutCapUsd": 37500.0000,
    "actualPayoutUsd": 32450.7500,
    "payoutToBudgetRatio": 0.2163,
    "exceededAmountUsd": 0.0000,
    "calculationStatus": "completed",
    "calculatedAt": "2024-02-01 17:15:30"
  }
}
```

---

## 3. 历史记录查询

### 3.1 查询历史计算记录列表

**接口**: `GET /business/commission/summary/list`

**说明**: 获取所有月份的计算记录，用于历史记录表格显示

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "dataMonth": "2024-01-01", // 可选，筛选特定月份
  "calculationStatus": "completed" // 可选，筛选特定状态
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "dataMonth": "2024-01-01",
      "budgetUsd": 150000.0000,
      "payoutCapUsd": 37500.0000,
      "actualPayoutUsd": 32450.7500,
      "payoutToBudgetRatio": 0.2163,
      "exceededAmountUsd": 0.0000,
      "calculationStatus": "completed",
      "calculatedAt": "2024-02-01 17:15:30"
    },
    {
      "dataMonth": "2023-12-01",
      "budgetUsd": 140000.0000,
      "payoutCapUsd": 35000.0000,
      "actualPayoutUsd": 38250.5000,
      "payoutToBudgetRatio": 0.2732,
      "exceededAmountUsd": 3250.5000,
      "calculationStatus": "completed",
      "calculatedAt": "2024-01-01 17:20:00"
    }
  ],
  "total": 12
}
```

---

## 4. 界面实现建议

### 4.1 计算参数设置区域

1. **计算月份选择**:
   ```javascript
   // 监听月份变化，实时检查前置条件
   function onMonthChange(selectedMonth) {
     checkPreconditions(selectedMonth);
   }
   ```

2. **计算模式选择**:
   - 正常计算: 调用 `/execute/` 接口
   - 预览计算: 调用 `/preview/` 接口
   - 重新计算: 调用 `/recalculate/` 接口

3. **开始计算按钮**:
   ```javascript
   async function startCalculation() {
     // 1. 先检查前置条件
     const preCheck = await checkPreconditions(selectedMonth);
     if (!preCheck.canCalculate) {
       showErrorDialog(preCheck.message);
       return;
     }
     
     // 2. 执行计算
     const result = await executeCalculation(selectedMonth);
     if (result.success) {
       refreshHistoryTable();
       showSuccessMessage();
     }
   }
   ```

### 4.2 历史计算记录表格

1. **数据加载**:
   ```javascript
   // 页面加载时获取历史记录
   async function loadHistoryRecords() {
     const response = await fetch('/business/commission/summary/list?pageNum=1&pageSize=10');
     const data = await response.json();
     renderTable(data.rows);
   }
   ```

2. **状态显示**:
   ```javascript
   function renderStatus(status) {
     const statusMap = {
       'completed': { text: '已完成', color: 'green' },
       'processing': { text: '计算中', color: 'blue' },
       'failed': { text: '失败', color: 'red' }
     };
     return statusMap[status];
   }
   ```

3. **支出比例和超出金额**:
   ```javascript
   function renderPayoutRatio(ratio, exceededAmount) {
     const percentage = (ratio * 100).toFixed(1) + '%';
     const isExceeded = exceededAmount > 0;
     return {
       percentage,
       exceeded: isExceeded ? `$${exceededAmount.toFixed(2)}` : '-',
       color: isExceeded ? 'red' : 'blue'
     };
   }
   ```

### 4.3 实时状态轮询

```javascript
// 计算进行中时的状态轮询
function startStatusPolling(dataMonth) {
  const pollInterval = setInterval(async () => {
    const status = await getCalculationStatus(dataMonth);
    updateStatusDisplay(status);
    
    if (status.status === 'completed' || status.status === 'failed') {
      clearInterval(pollInterval);
      refreshHistoryTable();
    }
  }, 3000); // 每3秒查询一次
}
```

---

## 5. 错误处理

### 5.1 前置条件检查失败

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "canCalculate": false,
    "message": "发现以下错误：该月份未配置月度设置（预算、汇率等）；基础钻石门槛未配置。",
    "dataMonth": "2024-01",
    "errors": [
      "该月份未配置月度设置（预算、汇率等）",
      "基础钻石门槛未配置"
    ],
    "warnings": []
  }
}
```

### 5.2 计算执行失败

```json
{
  "code": 500,
  "msg": "计算执行失败：数据异常",
  "data": {
    "success": false,
    "message": "计算执行失败：数据异常"
  }
}
```

### 5.3 重复计算防护

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": false,
    "message": "该月份已完成计算，如需重新计算请使用重新计算接口"
  }
}
```

---

## 6. 接口调用示例代码

### 6.1 完整的计算流程

```javascript
class CommissionCalculationManager {
  constructor() {
    this.baseUrl = '/business/commission/calculation';
    this.token = localStorage.getItem('token');
  }

  // 检查前置条件
  async checkPreconditions(dataMonth) {
    const response = await fetch(`${this.baseUrl}/check-preconditions/${dataMonth}`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    return await response.json();
  }

  // 执行计算
  async executeCalculation(dataMonth) {
    const response = await fetch(`${this.baseUrl}/execute/${dataMonth}`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    return await response.json();
  }

  // 获取计算状态
  async getCalculationStatus(dataMonth) {
    const response = await fetch(`${this.baseUrl}/status/${dataMonth}`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    return await response.json();
  }

  // 完整的计算流程
  async performCalculation(dataMonth, mode = 'execute') {
    try {
      // 1. 检查前置条件
      const preCheck = await this.checkPreconditions(dataMonth);
      if (!preCheck.data.canCalculate) {
        throw new Error(preCheck.data.message);
      }

      // 2. 执行计算
      let result;
      if (mode === 'preview') {
        result = await this.previewCalculation(dataMonth);
      } else {
        result = await this.executeCalculation(dataMonth);
      }

      if (!result.data.success) {
        throw new Error(result.data.message);
      }

      // 3. 如果是正式计算，开始轮询状态
      if (mode === 'execute') {
        this.startStatusPolling(dataMonth);
      }

      return result;

    } catch (error) {
      console.error('计算失败:', error);
      throw error;
    }
  }

  // 状态轮询
  startStatusPolling(dataMonth) {
    const pollInterval = setInterval(async () => {
      try {
        const status = await this.getCalculationStatus(dataMonth);
        this.updateStatusDisplay(status.data);

        if (status.data.status === 'completed' || status.data.status === 'failed') {
          clearInterval(pollInterval);
          this.onCalculationComplete(status.data);
        }
      } catch (error) {
        console.error('状态查询失败:', error);
        clearInterval(pollInterval);
      }
    }, 3000);
  }

  // 计算完成回调
  onCalculationComplete(statusData) {
    console.log('计算完成:', statusData);
    // 刷新历史记录表格
    this.refreshHistoryTable();
    // 显示完成提示
    this.showCompletionNotification(statusData);
  }
}
```

### 6.2 界面交互示例

```javascript
// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
  const calculator = new CommissionCalculationManager();
  
  // 开始计算按钮
  document.getElementById('startCalculation').addEventListener('click', async function() {
    const selectedMonth = document.getElementById('calculationMonth').value;
    const calculationMode = document.getElementById('calculationMode').value;
    
    try {
      await calculator.performCalculation(selectedMonth, calculationMode);
    } catch (error) {
      alert('计算失败: ' + error.message);
    }
  });
  
  // 月份变化时检查前置条件
  document.getElementById('calculationMonth').addEventListener('change', async function() {
    const selectedMonth = this.value;
    if (selectedMonth) {
      const preCheck = await calculator.checkPreconditions(selectedMonth);
      displayPreconditionResult(preCheck.data);
    }
  });
});
```

---

## 7. 注意事项

1. **日期格式**: 所有月份参数使用 `yyyy-MM` 格式
2. **权限验证**: 确保用户有 `h:commission_calculation` 权限
3. **并发控制**: 同一月份不能同时进行多次计算
4. **状态轮询**: 计算进行中时建议3秒轮询一次状态
5. **错误处理**: 实现完善的错误提示和重试机制
6. **数据刷新**: 计算完成后及时刷新历史记录表格
7. **用户体验**: 计算过程中禁用相关按钮，显示进度指示器 