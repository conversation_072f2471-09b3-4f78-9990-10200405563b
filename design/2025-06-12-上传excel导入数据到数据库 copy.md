你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 

# 要求
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service 时可以参考下面的实现， 要注意数据源问题，  @DataSource(value = DataSourceType.SLAVE)
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")

```

@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 
如： 
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 

# 项目架构

## 模块结构
系统采用Maven多模块架构，主要包含以下核心模块：

### ruoyi-admin：系统启动模块
包含应用程序入口类RuoYiApplication
包含Web控制器层，负责处理HTTP请求
按功能领域划分控制器（system、monitor、tool等）

### ruoyi-framework：核心框架模块
包含系统框架配置
安全框架实现（基于Spring Security）
数据源配置
AOP切面实现
拦截器配置
Web相关配置

### ruoyi-system：系统功能模块
遵循经典三层架构：
domain：领域模型层
mapper：数据访问层
service：业务逻辑层

### ruoyi-common：通用工具模块
包含注解、常量定义
核心基础类
工具类库
异常处理
XSS防护
枚举定义

### ruoyi-quartz：定时任务模块
基于Quartz实现的任务调度功能
### ruoyi-generator：代码生成模块
用于自动生成代码，提高开发效率

## 技术架构
### 基础框架：
Spring Boot 2.5.15
Spring Framework 5.3.33
Spring Security 5.7.12

### 数据访问：
MyBatis（通过PageHelper实现分页）
Druid数据库连接池

### 安全框架：
Spring Security
JWT令牌认证

### API文档：
Swagger 3.0

### 其他技术组件：
Kaptcha（验证码）
POI（Excel处理）
Velocity（模板引擎，用于代码生成）
Fastjson（JSON处理）
Lombok（简化代码）

## 架构设计特点
### 分层架构：
表现层（Controller）
业务层（Service）
数据访问层（Mapper）
领域模型层（Domain）

### 模块化设计：
功能模块清晰分离
依赖关系明确

### 安全性设计：
基于Spring Security的认证授权
XSS防护机制
数据过滤

### 扩展性：
通过模块化设计支持功能扩展
代码生成器支持快速开发

### 可维护性：
统一的异常处理
规范的代码结构
通用工具类封装
开发规范

 
# 数据结构
```
-- Create syntax for TABLE 'creator_relationships'
CREATE TABLE `creator_relationships` (
  `ancestor_id` bigint(20) unsigned NOT NULL COMMENT '祖先用户ID (关联 creators.id)',
  `descendant_id` bigint(20) unsigned NOT NULL COMMENT '后代用户ID (关联 creators.id)',
  `depth` int(11) NOT NULL COMMENT '层级深度 (0表示用户自身)',
  PRIMARY KEY (`ancestor_id`,`descendant_id`),
  KEY `idx_cr_descendant_depth` (`descendant_id`,`depth`),
  KEY `idx_cr_ancestor_depth` (`ancestor_id`,`depth`),
  CONSTRAINT `fk_rel_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rel_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主播层级关系闭包表';

-- Create syntax for TABLE 'creators'
CREATE TABLE `creators` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主播的唯一ID (Creator ID)',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '直接上级主播的ID, 顶级为NULL',
  `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主播昵称',
  `handle` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主播的Handle',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_handle` (`handle`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播信息与直接关系表';

-- Create syntax for TABLE 'monthly_performance'
CREATE TABLE `monthly_performance` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '关联到creators.id',
  `data_month` date NOT NULL COMMENT '数据月份 (存储为每月第一天)',
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Group',
  `group_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Group manager',
  `is_violative` tinyint(1) DEFAULT '0' COMMENT '是否违规主播',
  `is_rookie` tinyint(1) DEFAULT '0' COMMENT '是否新人主播',
  `diamonds` bigint(20) DEFAULT '0' COMMENT '钻石数',
  `valid_days` int(11) DEFAULT '0' COMMENT '有效天数(d)',
  `live_duration_hours` decimal(10,4) DEFAULT '0.0000' COMMENT '直播时长(h)',
  `bonus_estimated` decimal(10,4) DEFAULT '0.0000' COMMENT '估计的奖金',
  `bonus_rookie_m1_retention` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新人里程碑1保留任务奖金',
  `bonus_rookie_m2` decimal(10,4) DEFAULT '0.0000' COMMENT '新人里程碑2任务奖金',
  `bonus_rookie_half_milestone` decimal(10,4) DEFAULT '0.0000' COMMENT '新人半里程碑任务奖金',
  `bonus_rookie_m1` decimal(10,4) DEFAULT '0.0000' COMMENT '新人里程碑1任务奖金',
  `bonus_activeness` decimal(10,4) DEFAULT '0.0000' COMMENT '活跃任务奖金',
  `bonus_revenue_scale` decimal(10,4) DEFAULT '0.0000' COMMENT '收入规模任务奖金',
  `bonus_new_creator_network` decimal(10,4) DEFAULT '0.0000' COMMENT '新创作者网络任务奖金',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_creator_month` (`creator_id`,`data_month`),
  CONSTRAINT `fk_performance_creator` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播月度业绩数据表';

-- Create syntax for TABLE 'raw_imports'
CREATE TABLE `raw_imports` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '源文件名',
  `imported_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `data_month` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator_nickname` text COLLATE utf8mb4_unicode_ci,
  `handle` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator_network_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `group_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_violative_creators` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `the_creator_was_rookie` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `diamonds` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `valid_days` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `live_duration_h` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `estimated_bonus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_m1_retention` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_m2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_half_milestone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_m1` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_activeness_task` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_revenue_scale_task` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_new_creator_network_task` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原始Excel数据导入记录表';
```

# 需求
- Excel 导入
接口：POST /distribution/import
工具：Apache POI、EasyExcel 二选一（RuoYi 常用 EasyExcel）。
流程：
• 保存文件到本地/minio -> 解析 -> 行转实体 Dto -> 批量插入 raw_imports
• 同步 creators & monthly_performance（ON DUPLICATE KEY UPDATE）
• 解析 Creator Network manager 字段，更新 parent_id
- 闭包表重建 • 方案 1：纯 SQL 递归插入（MySQL 8 的 CTE 更简洁，可惜 5.7 无递归 CTE，需循环）

- 查询接口 • GET /distribution/tree/{creatorId} → 全部下级
• GET /distribution/tree/{creatorId}/{minDepth}/{maxDepth} → 指定等级
• Mapper 直接查询 creator_relationships，或调用存储过程/视图。

# 参考： 

@design.md
