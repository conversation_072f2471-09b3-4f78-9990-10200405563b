你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 

# 要求
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service 时可以参考下面的实现， 要注意数据源问题，  @DataSource(value = DataSourceType.SLAVE)
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")

```

@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 
如： 
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 

# 项目架构

## 模块结构
系统采用Maven多模块架构，主要包含以下核心模块：

### ruoyi-admin：系统启动模块
包含应用程序入口类RuoYiApplication
包含Web控制器层，负责处理HTTP请求
按功能领域划分控制器（system、monitor、tool等）

### ruoyi-framework：核心框架模块
包含系统框架配置
安全框架实现（基于Spring Security）
数据源配置
AOP切面实现
拦截器配置
Web相关配置

### ruoyi-system：系统功能模块
遵循经典三层架构：
domain：领域模型层
mapper：数据访问层
service：业务逻辑层

### ruoyi-common：通用工具模块
包含注解、常量定义
核心基础类
工具类库
异常处理
XSS防护
枚举定义

### ruoyi-quartz：定时任务模块
基于Quartz实现的任务调度功能
### ruoyi-generator：代码生成模块
用于自动生成代码，提高开发效率

## 技术架构
### 基础框架：
Spring Boot 2.5.15
Spring Framework 5.3.33
Spring Security 5.7.12

### 数据访问：
MyBatis（通过PageHelper实现分页）
Druid数据库连接池

### 安全框架：
Spring Security
JWT令牌认证

### API文档：
Swagger 3.0

### 其他技术组件：
Kaptcha（验证码）
POI（Excel处理）
Velocity（模板引擎，用于代码生成）
Fastjson（JSON处理）
Lombok（简化代码）

## 架构设计特点
### 分层架构：
表现层（Controller）
业务层（Service）
数据访问层（Mapper）
领域模型层（Domain）

### 模块化设计：
功能模块清晰分离
依赖关系明确

### 安全性设计：
基于Spring Security的认证授权
XSS防护机制
数据过滤

### 扩展性：
通过模块化设计支持功能扩展
代码生成器支持快速开发

### 可维护性：
统一的异常处理
规范的代码结构
通用工具类封装
开发规范

 
# 数据结构
```


```

# 需求

- 修改导入功能, 导入主播数据时需要把数据同时导入到ruoyi 的系统中， 让这个用户可以登录到ruoyi系统 

-- 导入主播相关的代码  
RawImportServiceImpl.java

```

    /**
     * 同步Creator表并更新其层级关系中的parentId
     * @param rawImportList 从Excel解析的原始数据列表
     * @param operName 操作人
     */
    private void synchronizeCreatorsAndUpdateHierarchy(List<RawImport> rawImportList, String operName) {
        log.info("开始同步 Creators 表并更新 Parent ID...");
        int newCreatorsCount = 0;
        int updatedCreatorsCount = 0;
        int skippedCount = 0;

        for (RawImport rawImport : rawImportList) {
            log.info("正在处理 Excel 行数据: {}", rawImport.toString());

            if (StringUtils.isBlank(rawImport.getHandle())) {
                log.warn("跳过记录：Handle 为空。数据: {}", rawImport);
                skippedCount++;
                continue;
            }

            if (StringUtils.isBlank(rawImport.getCreatorId())) {
                log.error("跳过记录：Creator ID 为空。Handle: {}, 数据: {}", rawImport.getHandle(), rawImport);
                skippedCount++;
                continue;
            }

            Long creatorIdFromExcel;
            try {
                creatorIdFromExcel = parseLong(rawImport.getCreatorId());
                if (creatorIdFromExcel == null) throw new NumberFormatException("Parsed to null");
            } catch (NumberFormatException e) {
                log.error("跳过记录：Creator ID '{}' 格式错误。Handle: {}, 数据: {}", rawImport.getCreatorId(), rawImport.getHandle(), rawImport, e);
                skippedCount++;
                continue;
            }

            Creator existingCreatorById = creatorService.selectCreatorById(creatorIdFromExcel);
            Creator existingCreatorByHandle = creatorService.selectCreatorByHandle(rawImport.getHandle());

            Creator creatorToSave;
            boolean isNewCreator = false;

            if (existingCreatorById != null) { // ID exists
                creatorToSave = existingCreatorById;
                if (existingCreatorByHandle != null && !existingCreatorById.getId().equals(existingCreatorByHandle.getId())) {
                    log.error("跳过记录：数据冲突，ID '{}' 和 Handle '{}' 指向不同用户。", creatorIdFromExcel, rawImport.getHandle());
                    skippedCount++;
                    continue;
                }
                if (!creatorToSave.getHandle().equals(rawImport.getHandle())) {
                    log.warn("数据警告：ID '{}' 的 Handle 将从 '{}' 更新为 '{}'。", creatorIdFromExcel, creatorToSave.getHandle(), rawImport.getHandle());
                    creatorToSave.setHandle(rawImport.getHandle());
                }
            } else if (existingCreatorByHandle != null) { // Handle exists but ID does not
                log.error("跳过记录：数据冲突，Handle '{}' 已存在，但其 ID 与 Excel 中的 ID '{}' 不符。", rawImport.getHandle(), creatorIdFromExcel);
                skippedCount++;
                continue;
            } else { // Entirely new user
                isNewCreator = true;
                creatorToSave = new Creator();
                creatorToSave.setId(creatorIdFromExcel);
                creatorToSave.setHandle(rawImport.getHandle());
            }

            if (StringUtils.isNotBlank(rawImport.getCreatorNickname())) {
                creatorToSave.setNickname(rawImport.getCreatorNickname());
            } else if (isNewCreator) {
                creatorToSave.setNickname(rawImport.getHandle());
            }

            if (StringUtils.isNotBlank(rawImport.getCreatorNetworkManager())) {
                // 解析 "上级名称-上级ID" 格式的字段，例如 "Manager_L0-7500000000000000000"
                String managerInfo = rawImport.getCreatorNetworkManager().trim();
                Long parentId = parseParentIdFromManagerInfo(managerInfo);
                
                if (parentId != null) {
                    // 验证上级ID是否存在
                    Creator manager = creatorService.selectCreatorById(parentId);
                    if (manager != null) {
                        creatorToSave.setParentId(parentId);
                        log.debug("为主播 '{}' 设置上级ID: {}", rawImport.getHandle(), parentId);
                    } else {
                        log.warn("上级ID '{}' 在数据库中不存在，主播 '{}' 的 ParentID 将设为 0。管理器信息: '{}'", 
                                parentId, rawImport.getHandle(), managerInfo);
                        if (isNewCreator) creatorToSave.setParentId(0L);
                    }
                } else {
                    log.warn("无法从管理器信息 '{}' 中解析出上级ID，主播 '{}' 的 ParentID 将设为 0。", 
                            managerInfo, rawImport.getHandle());
                    if (isNewCreator) creatorToSave.setParentId(0L);
                }
            } else if (isNewCreator) {
                creatorToSave.setParentId(0L);
            }

            try {
                if (isNewCreator) {
                    creatorToSave.setCreateBy(operName);
                    creatorService.insertCreator(creatorToSave);
                    newCreatorsCount++;
                } else {
                    creatorToSave.setUpdateBy(operName);
                    creatorService.updateCreator(creatorToSave);
                    updatedCreatorsCount++;
                }
            } catch (Exception e) {
                log.error("保存主播 '{}' (ID: {}) 到数据库时失败: {}", creatorToSave.getHandle(), creatorToSave.getId(), e.getMessage(), e);
                skippedCount++;
            }
        }
        log.info("Creators 表同步完成：新增 {} 个，更新 {} 个，跳过 {} 个。", newCreatorsCount, updatedCreatorsCount, skippedCount);
    }
```


-- 导入ruoyi相关的代码 

@SysUserServiceImpl.java

```
/**
     * 导入用户数据
     * 
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysUser user : userList)
        {
            try
            {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u))
                {
                    BeanValidators.validateWithException(validator, user);
                    deptService.checkDeptDataScope(user.getDeptId());
                    String password = configService.selectConfigByKey("sys.user.initPassword");
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    userMapper.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getUserId());
                    deptService.checkDeptDataScope(user.getDeptId());
                    user.setUserId(u.getUserId());
                    user.setUpdateBy(operName);
                    userMapper.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
```

- 对应的关系

SysUser.userName = Creator.id
SysUser.nickName = Creator.nickname
SysUser.userName = Creator.id

SysUser 其它的字段按默认值, 保证用户能正常导入到系统中， 又能正常登录即可

