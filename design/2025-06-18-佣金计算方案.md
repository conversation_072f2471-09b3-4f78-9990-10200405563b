# TikTok公会主播奖励分成计算方案

**文档版本**: 1.0  
**创建日期**: 2025年6月18日  
**状态**: 设计阶段  

---

## 1. 系统概述

TikTok公会主播奖励分成计算与报表系统是一个精细化的多维度激励计算引擎，主要功能包括：

- **多层级分成计算**：支持分销员等级分成、下三级提成、拉新奖励三种收入来源
- **动态门槛机制**：基于上月业绩的自适应门槛调整
- **精确财务控制**：提供预算对比和支出分析
- **完整审计追踪**：记录每笔收入的详细构成

## 2. 核心业务规则

### 2.1 角色定义
- **分销员 (Distributor)**: 有资格发展下级并参与团队分成的特殊主播
- **下级网络**: 以分销员为根的树状结构
  - **L1**: 直属下级（parent_id = 分销员ID）
  - **L2**: L1的下级（通过关系闭包表查询depth=2）
  - **L3**: L2的下级（通过关系闭包表查询depth=3）

### 2.2 收入来源分类

#### 2.2.1 分销员等级分成 (LEVEL_COMMISSION)
- **计算基础**: 个人钻石收入 + 直属下级(L1)钻石收入总和
- **前置条件**: 
  1. 达到动态钻石门槛
  2. 满足等级要求的直属下级人数
- **计算公式**: `等级分成 = 团队总钻石 × 等级分成比例`

#### 2.2.2 下三级提成 (MULTI_LEVEL_COMMISSION)
- **计算基础**: L1、L2、L3各层级的钻石收入
- **前置条件**: 获得任一分销员等级资格
- **计算公式**: `下三级提成 = ∑(Li收入 × Li提成比例)` (i=1,2,3)

#### 2.2.3 拉新奖励 (RECRUITMENT_BONUS)
- **计算基础**: 当月成功招募的新人数量
- **前置条件**: 无
- **计算公式**: 按拉新人数阶梯匹配USD奖励

### 2.3 动态门槛机制

```
current_dynamic_threshold = MAX(
    last_month_diamonds × (1 + threshold_increase_rate),
    base_diamond_threshold
)
```

## 3. 详细计算流程

### 3.1 计算准备阶段

#### 步骤1: 配置加载
```sql
-- 加载月度配置
SELECT manual_income_usd, payout_cap_rate, diamond_to_usd_rate 
FROM commission_monthly_settings 
WHERE data_month = ?

-- 加载全局配置
SELECT setting_key, setting_value 
FROM commission_settings 
WHERE setting_key IN ('base_diamond_threshold', 'threshold_increase_rate')

-- 加载分销员等级规则
SELECT level_name, min_direct_downlines, commission_rate 
FROM commission_level_rules 
WHERE is_active = 1 
ORDER BY display_order

-- 加载多级提成规则
SELECT depth, commission_rate 
FROM commission_multilevel_rules 
WHERE is_active = 1

-- 加载拉新奖励规则
SELECT min_new_recruits, bonus_usd 
FROM commission_recruitment_bonus_rules 
WHERE is_active = 1 
ORDER BY min_new_recruits DESC
```

#### 步骤2: 数据预处理
```sql
-- 获取当月所有主播业绩数据
SELECT creator_id, diamonds 
FROM monthly_performance 
WHERE data_month = ?

-- 获取所有分销员及其关系网络
SELECT DISTINCT ancestor_id as distributor_id
FROM creator_relationships cr
JOIN monthly_performance mp ON mp.creator_id = cr.ancestor_id
WHERE mp.data_month = ? AND cr.depth = 0
```

### 3.2 分销员资格计算阶段

#### 步骤3: 动态门槛计算
对每个分销员：
```java
// 获取上月钻石收入
long lastMonthDiamonds = getLastMonthDiamonds(creatorId, dataMonth);

// 计算动态门槛
long dynamicThreshold = Math.max(
    Math.round(lastMonthDiamonds * (1 + thresholdIncreaseRate)),
    baseDiamondThreshold
);

// 获取个人当月钻石收入
long personalDiamonds = getCurrentMonthDiamonds(creatorId, dataMonth);

// 判断是否达到门槛
boolean thresholdMet = personalDiamonds >= dynamicThreshold;
```

#### 步骤4: 团队数据统计
```java
// 计算直属下级数量和收入
List<Long> directDownlines = getDirectDownlines(creatorId);
int directDownlinesCount = directDownlines.size();
long teamDiamonds = directDownlines.stream()
    .mapToLong(id -> getCurrentMonthDiamonds(id, dataMonth))
    .sum();

// 计算各层级下级收入
long l1Income = getDownlineIncomeByDepth(creatorId, dataMonth, 1);
long l2Income = getDownlineIncomeByDepth(creatorId, dataMonth, 2);
long l3Income = getDownlineIncomeByDepth(creatorId, dataMonth, 3);
```

#### 步骤5: 等级评定
```java
String achievedLevel = null;
BigDecimal levelCommissionRate = BigDecimal.ZERO;

if (thresholdMet) {
    // 按等级要求从高到低检查
    for (CommissionLevelRule rule : levelRules) {
        if (directDownlinesCount >= rule.getMinDirectDownlines()) {
            achievedLevel = rule.getLevelName();
            levelCommissionRate = rule.getCommissionRate();
            break;
        }
    }
}
```

### 3.3 收入计算阶段

#### 步骤6: 分销员等级分成计算
```java
BigDecimal levelCommissionDiamonds = BigDecimal.ZERO;

if (achievedLevel != null) {
    long totalTeamDiamonds = personalDiamonds + teamDiamonds;
    levelCommissionDiamonds = BigDecimal.valueOf(totalTeamDiamonds)
        .multiply(levelCommissionRate)
        .setScale(4, RoundingMode.HALF_UP);
}
```

#### 步骤7: 下三级提成计算
```java
BigDecimal multilevelCommissionDiamonds = BigDecimal.ZERO;

if (achievedLevel != null) { // 有等级资格才能获得多级提成
    for (CommissionMultilevelRule rule : multilevelRules) {
        long depthIncome = getDownlineIncomeByDepth(creatorId, dataMonth, rule.getDepth());
        BigDecimal depthCommission = BigDecimal.valueOf(depthIncome)
            .multiply(rule.getCommissionRate())
            .setScale(4, RoundingMode.HALF_UP);
        multilevelCommissionDiamonds = multilevelCommissionDiamonds.add(depthCommission);
    }
}
```

#### 步骤8: 拉新奖励计算
```java
// 统计当月新招募人数
int newRecruitsCount = countNewRecruits(creatorId, dataMonth);

BigDecimal recruitmentBonusUsd = BigDecimal.ZERO;
for (CommissionRecruitmentBonusRule rule : recruitmentBonusRules) {
    if (newRecruitsCount >= rule.getMinNewRecruits()) {
        recruitmentBonusUsd = rule.getBonusUsd();
        break; // 匹配最高档奖励
    }
}
```

#### 步骤9: 最终收入计算
```java
// 钻石收入转换为USD
BigDecimal totalDiamonds = levelCommissionDiamonds.add(multilevelCommissionDiamonds);
BigDecimal diamondsToUsd = totalDiamonds.multiply(diamondToUsdRate)
    .setScale(4, RoundingMode.HALF_UP);

// 最终收入
BigDecimal finalPayoutUsd = diamondsToUsd.add(recruitmentBonusUsd)
    .setScale(2, RoundingMode.HALF_UP);
```

### 3.4 数据保存阶段

#### 步骤10: 保存分销员收入记录
```java
// 保存主表记录
CommissionPayouts payout = new CommissionPayouts();
payout.setCreatorId(creatorId);
payout.setDataMonth(dataMonth);
payout.setDistributorLevel(achievedLevel);
payout.setFinalPayoutUsd(finalPayoutUsd);

// 保存收入构成明细
if (levelCommissionDiamonds.compareTo(BigDecimal.ZERO) > 0) {
    saveBreakdown(payout.getId(), "LEVEL_COMMISSION", 
        totalTeamDiamonds, levelCommissionDiamonds, diamondsToUsd);
}

if (multilevelCommissionDiamonds.compareTo(BigDecimal.ZERO) > 0) {
    saveBreakdown(payout.getId(), "MULTI_LEVEL_COMMISSION", 
        null, multilevelCommissionDiamonds, multilevelToUsd);
}

if (recruitmentBonusUsd.compareTo(BigDecimal.ZERO) > 0) {
    saveBreakdown(payout.getId(), "RECRUITMENT_BONUS", 
        null, null, recruitmentBonusUsd);
}
```

### 3.5 汇总统计阶段

#### 步骤11: 生成月度总览
```java
// 统计所有分销员总收入
BigDecimal totalActualPayoutUsd = allPayouts.stream()
    .map(CommissionPayouts::getFinalPayoutUsd)
    .reduce(BigDecimal.ZERO, BigDecimal::add);

// 计算财务指标
BigDecimal payoutToBudgetRatio = totalActualPayoutUsd
    .divide(budgetUsd, 4, RoundingMode.HALF_UP);

BigDecimal exceededAmountUsd = totalActualPayoutUsd
    .subtract(payoutCapUsd)
    .max(BigDecimal.ZERO);

// 保存月度总览
CommissionMonthlySummary summary = new CommissionMonthlySummary();
summary.setDataMonth(dataMonth);
summary.setBudgetUsd(budgetUsd);
summary.setPayoutCapUsd(payoutCapUsd);
summary.setActualPayoutUsd(totalActualPayoutUsd);
summary.setPayoutToBudgetRatio(payoutToBudgetRatio);
summary.setExceededAmountUsd(exceededAmountUsd);
```

## 4. 数据查询策略

### 4.1 获取下级网络收入
```sql
-- 获取指定层级下级收入
SELECT SUM(mp.diamonds) as total_income
FROM creator_relationships cr
JOIN monthly_performance mp ON mp.creator_id = cr.descendant_id
WHERE cr.ancestor_id = ? 
  AND cr.depth = ? 
  AND mp.data_month = ?
```

### 4.2 统计新招募人数
```sql
-- 基于first_active_month字段统计
SELECT COUNT(*) as new_recruits_count
FROM creators c
WHERE c.recruited_by = ? 
  AND c.first_active_month = ?
```

### 4.3 获取上月业绩
```sql
-- 获取上月钻石收入
SELECT COALESCE(mp.diamonds, 0) as last_month_diamonds
FROM monthly_performance mp
WHERE mp.creator_id = ? 
  AND mp.data_month = DATE_SUB(?, INTERVAL 1 MONTH)
```

## 5. 异常处理策略

### 5.1 数据完整性检查
- 检查月度配置是否存在
- 检查主播业绩数据是否完整
- 检查关系闭包表数据一致性

### 5.2 计算异常处理
- 除零保护
- 数值溢出检查
- 舍入精度控制

### 5.3 业务规则验证
- 门槛计算合理性验证
- 等级评定逻辑验证
- 收入构成数据验证

## 6. 性能优化建议

### 6.1 数据库优化
- 为关键查询字段添加复合索引
- 使用批量操作减少数据库交互
- 考虑使用临时表存储中间计算结果

### 6.2 计算优化
- 缓存配置数据避免重复查询
- 并行处理独立的分销员计算
- 预计算常用的关系网络数据

### 6.3 内存优化
- 分批处理大量分销员数据
- 及时释放不再需要的中间结果
- 使用流式处理减少内存占用

## 7. 测试验证方案

### 7.1 单元测试
- 动态门槛计算逻辑测试
- 各项收入计算公式测试
- 数据精度和舍入测试

### 7.2 集成测试
- 完整计算流程测试
- 数据库事务一致性测试
- 大数据量性能测试

### 7.3 业务场景测试
- 边界条件测试（新人、无下级等）
- 多等级分销员混合场景测试
- 配置变更影响测试

---

**备注**: 本计算方案严格遵循PRD文档中的业务规则，确保计算结果的准确性和可追溯性。 