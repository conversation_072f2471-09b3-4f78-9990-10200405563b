<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<data_structure>
@database.sql
</data_structure>

<data_changes>
数据库最终变更方案

版本: 1.0
日期: 2025年7月1日

1. 文档概述

本方案旨在提供一份完整、最终的数据库结构（Schema）变更清单。所有变更均源于“分销员等级与佣金系统V2.0”的最终设计方案，旨在为新业务逻辑提供必要的数据支持。

2. creators 表的变更

目标表: creators
变更描述: 此表是分销员信息的核心，我们将增加四个新字段，用于存储用户的“基础等级”状态，以及实现“合格拉新”的唯一性标记。
SQL语句:

SQL


ALTER TABLE `creators`
    ADD COLUMN `current_distributor_level_id` INT(10) UNSIGNED DEFAULT NULL 
        COMMENT '当前基础等级ID, 关联commission_level_rules.id, 由“防守”体系更新' AFTER `recruited_by`,
    
    ADD COLUMN `level_updated_at` TIMESTAMP NULL DEFAULT NULL 
        COMMENT '基础等级最后更新时间, 用于判断升级保护期' AFTER `current_distributor_level_id`,
        
    ADD COLUMN `first_qualified_month` DATE DEFAULT NULL 
        COMMENT '首次被确认为合格拉新的月份 (YYYY-MM-01), 用于确保唯一性' AFTER `level_updated_at`,
    
    ADD COLUMN `qualified_by_recruiter_id` BIGINT(20) UNSIGNED DEFAULT NULL 
        COMMENT '确认其合格时的直接上级(parent_id)快照, 用于永久锁定业绩归属' AFTER `first_qualified_month`;

-- 为新字段增加索引以优化查询
ALTER TABLE `creators`
    ADD INDEX `idx_creators_current_level` (`current_distributor_level_id`),
    ADD INDEX `idx_creators_qualified_by_month` (`qualified_by_recruiter_id`, `first_qualified_month`);


解决的问题:
current_distributor_level_id: 解决了“防守”体系的状态存储问题。系统需要一个地方来记录用户通过业绩考核所维持的“基础等级”，此字段即为该状态的载体。
level_updated_at: 解决了“升级保护期”的判断依据问题。通过记录等级变更的时间，系统可以准确判断一个刚刚晋升的用户是否应进入保护期，豁免“防守”考核。
first_qualified_month: 解决了“拉新业绩唯一性”问题。通过标记用户首次合格的月份，从根本上杜绝了同一个新用户在不同月份被重复计算为拉新业绩的可能。
qualified_by_recruiter_id: 解决了“历史业绩归属准确性”问题。通过在用户合格的瞬间，将其当时的parent_id“快照”并永久保存，确保了即使未来团队架构调整，这份历史功绩也不会丢失或错乱。

3. commission_level_rules 表的变更

目标表: commission_level_rules
变更描述: 此表是等级规则的核心，我们需要调整其字段以匹配新的业务逻辑：“等级由拉新人数决定”以及“不同等级对应不同收益深度”。
SQL语句:

SQL


-- 1. 修改现有字段的名称和注释，使其含义更清晰
ALTER TABLE `commission_level_rules`
    CHANGE COLUMN `min_direct_downlines` `min_qualified_recruits` INT(11) NOT NULL 
        COMMENT '该等级要求的最低月度合格拉新人数';

-- 2. 增加新字段以定义收益深度
ALTER TABLE `commission_level_rules`
    ADD COLUMN `payout_depth` TINYINT(4) NOT NULL DEFAULT 1 
        COMMENT '佣金收益深度 (1=下1级, 2=下2级, 3=下3级)' AFTER `commission_rate`;


解决的问题:
min_direct_downlines -> min_qualified_recruits: 解决了字段名与业务逻辑不符的问题。明确地体现出等级的评定标准是“合格拉新人数”，而非团队总人数。
payout_depth: 解决了收益规则“数据化”和“可配置”的问题。将金(3)/银(2)/铜(1)牌的收益深度作为数据存储在规则表中，替代了硬编码在程序里的逻辑，极大地提升了业务的灵活性。

4. commission_settings 表的数据插入（非结构变更）

目标表: commission_settings
变更描述: 将新的业务规则中的所有可变数值，作为参数插入到全局配置表中，以便于未来由运营人员进行调整，而无需修改代码。
SQL语句:

SQL


-- 使用 INSERT ... ON DUPLICATE KEY UPDATE 确保数据可重复执行插入或更新
INSERT INTO `commission_settings` (`setting_key`, `setting_value`, `description`) VALUES
('new_recruit_tenure_days', '60', '新人资格-账号最大创建天数'),
('new_recruit_min_valid_days', '24', '新人资格-月度最低有效直播天数'),
('retention_target_avg_growth_rate', '0.15', '业绩保级-目标平均增长率(15%)'),
('retention_growth_from_zero', '1.00', '业绩保级-从零增长的定义值(100%)'),
('retention_growth_rate_cap', '3.00', '业绩保级-单月增长率上限(300%)'),
('new_user_grace_period_months', '4', '新手保护期月数'),
('upgrade_protection_period_months', '2', '升级保护期月数')
ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`), `description` = VALUES(`description`);


解决的问题:
解决了系统的灵活性和可维护性问题。将所有关键业务数字参数化后，运营可以根据市场和业务变化，快速调整激励策略和风控阈值，大大降低了对开发资源的依赖和响应周期。
<data_changes>

<requirements>

# 需求


## 设计详案 

1. 需求背景

本需求旨在建立一套动态的等级调整机制，以确保分销员等级的“含金量”。它要求分销员在通过“拉新”（进攻）获得高等级后，必须通过持续的、健康的团队业绩表现来维持（“防守”）该等级。这套机制是维持整个分销体系健康、防止“躺平”现象、激励头部玩家持续贡献的关键。

2. 核心挑战与设计原则

挑战一：算法的稳定性与公平性
问题: 业务方选择的“月度环比增长率的平均值”算法，在数学上具有天然的波动性。如何设计风险控制规则，以确保考核结果尽可能公平，并避免极端情况下的计算错误？
设计原则: “算法稳健，风险可控”。在实施核心算法的同时，必须配套设计并实施“零值处理”、“增长率封顶”等风控规则，以保证系统的健壮性。
挑战二：用户生命周期的差异化处理
问题: “一刀切”的考核规则对所有用户是不公平的。一个刚注册的新手和一个刚刚晋升的“新贵”，都不应立刻面临严苛的业绩考核。
设计原则: “规则明确，例外清晰”。必须为处于特殊生命周期的用户建立明确的“保护期”或“豁免”机制，以体现人性化管理。

3. 最终解决方案：月度考核的“防守”体系

本需求将作为“进攻与防守”模型中的“防守”体系进行落地。它是一个在“一键计算”流程中，每月执行一次的月度考核任务。

3.1. 考核指标与周期

考核指标:
个人钻石收入 + 直属一级（depth=1）团队钻石总收入。
考核周期:
滚动窗口: 考核基于过去三个月的业绩表现。
执行频率: 每月执行一次。例如，10月1日执行的考核，将评估7月、8月、9月这三个月的业绩增长趋势。

3.2. 核心保级算法与风险控制

核心算法:
月度环比增长率的平均值 (Average of Month-over-Month Growth Rates)。
保级公式:
(近1月MGR + 近2月MGR + 近3月MGR) / 3 ≥ 15%
MGR = Monthly Growth Rate
风险控制规则 (算法的重要组成部分):
“除以零”处理:
若上月业绩为 0，本月业绩 > 0，则本月增长率（MGR）在计算时按固定的 100% 计。
若上月和本月业绩均为 0，则本月增长率（MGR）按 0% 计。
增长率上限 (Cap):
为防止数据泡沫和恶意刷数据，任何单月的环比增长率在代入公式计算前，其上限为 300%。超过此值的按300%计算。
参数化: 以上所有数值 (15%, 100%, 300%) 均作为系统参数进行配置，方便未来调整。

3.3. 降级惩罚机制

触发条件: 分销员未处于任何保护期内，且其业绩考核结果未能满足上述保级公式。
执行操作: 该用户的“基础等级”（存储于 creators.current_distributor_level_id）将被降低一级。例如，从“金牌”降为“银牌”。
降级下限: 如果用户已处于最低等级或无等级，则不执行任何操作。

4. 特殊场景处理：豁免与保护机制

为保证公平性，系统在对任何用户执行“防守”考核前，必须先检查其是否属于以下豁免情况：
新手保护期 (New User Grace Period):
规则: 新注册用户在前4个月内，完全豁免“防守”考核。
判定: 系统通过用户的账号创建时间 creators.created_at 进行判断。
升级保护期 (Upgrade Protection Period):
规则: 通过“进攻”（拉新）方式获得等级提升的用户，将自动获得为期2个月的等级保护期。
判定: 系统需要记录用户每次等级变动的时间和原因，以判断是否触发此保护期。

5. 应用与执行流程

本需求的逻辑，在“一键计算”流程中作为**“前置任务二”**的一部分被执行。
输入: 系统已完成“合格拉新”的判定。
执行逻辑: 在为每个分销员评定当月最终等级时，计算其“防守等级”的子流程如下：
A. 检查豁免: 判断用户是否处于“新手保护期”或“升级保护期”。
是: 则该用户的“防守等级”直接等于其当前的“基础等级”，流程结束。
否: 继续下一步。
B. 计算业绩: 获取该用户过去4个月的业绩数据。
C. 应用算法: 执行3.2中定义的“核心保级算法与风险控制”，得出一个“通过”或“不通过”的结果。
D. 得出“防守等级”:
若结果为“通过”，则“防守等级”= 其当前的“基础等级”。
若结果为“不通过”，则“防守等级”= 其当前的“基础等级”降一级。
输出: 计算出的“防守等级”，将与“进攻等级”进行比较，得出当月的最终等级。

6. 数据库设计影响

creators 表:
current_distributor_level_id 字段是本逻辑的核心，既是考核的输入（需要防守的等级），也是降级结果的输出。
created_at 字段用于判断“新手保护期”。
level_updated_at 字段（建议增加）可用于判断“升级保护期”的起始。
monthly_performance 表:
是计算业绩指标的唯一数据源。
commission_settings 表:
用于存储本方案涉及的所有可配置参数（目标增长率、从零增长定义值、增长率上限、保护期月数等）。
此方案详尽地定义了等级调整的全部逻辑，通过严谨的算法、必要的风险控制和人性化的保护机制，确保了整个分销体系的长期健康与活力。

</requirements>

<db_config>
可配置的系统参数列表


参数项
内部标识/Key (建议)
当前建议值
说明与配置原因
“进攻”体系参数






1. 新人资格-账号时长
new_recruit_tenure_days
60
定义: 判断一个用户是否为“新人”的最大账号创建天数。
原因: 未来可能会调整对“新人”的定义，例如放宽到90天或缩紧到30天。
2. 新人资格-活跃天数
new_recruit_min_valid_days
24
定义: 新人在考核月需要达到的最低有效直播天数。
原因: 这是衡量“高活跃”的关键指标，可能会根据整体用户活跃情况进行调整。
“防守”体系参数






3. 业绩保级-目标增长率
retention_target_avg_growth_rate
0.15
定义: 月度环比增长率的平均值需要达到的目标，0.15代表15%。
原因: 这是整个保级体系的核心目标，是未来最有可能被微调的业务参数。
4. “从零增长”的定义值
retention_growth_from_zero
1.00
定义: 当上月业绩为0，本月不为0时，记为100%增长。
原因: 这个特殊定义值直接影响考核结果，需要根据实际效果进行调整，我们刚刚就把它从500%调整到了100%。
5. 单月增长率上限
retention_growth_rate_cap
3.00
定义: 为防止数据泡沫，计入平均值的单月增长率上限，3.00代表300%。
原因: 这个“风控”数值，需要根据观察到的用户行为来调整，以防止规则被滥用。
保护期与特殊规则参数






6. 新手保护期
new_user_grace_period_months
4
定义: 新用户豁免“防守”考核的月数。
原因: 业务方可能会根据新人成长速度，决定延长或缩短这个保护期。
7. 升级保护期
upgrade_protection_period_months
2
定义: 通过“进攻”升级后，用户豁免“防守”考核的月数。
原因: 同上，这个保护期也需要根据运营策略灵活调整。


通过数据表管理的业务规则（非系统参数）

除了上述简单的键值对参数，我们方案中的另一些核心规则，已经通过设计良好的数据表来实现“配置化”了。这些通常不放在 commission_settings 中，因为它们是结构化的规则集。
等级评定标准 (金/银/铜)
配置方式: 通过 commission_level_rules 表进行管理。
可配置项:
level_name: 等级名称。
min_qualified_recruits: 晋升到该等级所需要的月度合格拉新人数。
payout_depth: 该等级对应的佣金收益深度。
灵活性: 运营人员可以直接修改这张表的数据，来调整每个等级的晋升门槛和权益，甚至可以新增“钻石级”等。
多层级佣金费率
配置方式: 通过 commission_multilevel_rules 表进行管理。
可配置项:
depth: 下级深度（1=L1, 2=L2...）。
commission_rate: 对应层级的提成比例。
灵活性: 可以灵活调整不同层级的具体提成比例。
</db_config>


<source_files>
@CommissionCalculationController.java
</source_files>


- 先读需求内容requirements， 了解后. 读数据库结构 data_structure,  了解相关的数据修改data_changes， 及配置参数db_config
- 逐步思考, 本次的修改内容， 及修改方案
- 实现修改现有代码逻辑

