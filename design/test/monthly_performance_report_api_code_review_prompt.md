# 分销员月度业绩分析报告API代码审查提示词

## 审查目标
请对本次实现的分销员月度业绩分析报告API进行全面的代码审查，确保代码质量、业务逻辑正确性和系统架构合理性。

## 需求背景
实现一个"分销员业绩系统"的业绩分析报告后端API接口。当用户选择一个历史月份后，系统能展示以该月为终点的、过去6个月的收入趋势分析图，并提供一些自动化的数据洞察，如最佳表现月份和整体增长趋势。

### API接口定义
- **HTTP方法**: GET
- **URL**: `/api/reports/monthly-performance/{creatorId}/{month}`
- **路径参数**:
  - `{creatorId}`: Long - 需要查询的分销员的用户ID
  - `{month}`: String - 需要查询的基准月份，格式为 YYYY-MM (例如 2024-12)
- **功能描述**: 获取指定分销员在指定月份的完整业绩分析报告，包含过去6个月的趋势数据和洞察

## 本次修改的文件及内容

### 1. 新增文件：响应DTO类
**文件**: `ruoyi-system/src/main/java/com/ruoyi/system/domain/dto/MonthlyPerformanceReportResponse.java`
**行数范围**: 1-210
**修改内容**:
- 创建了完整的响应数据结构，包含：
  - `Summary`: 顶部摘要信息（年度累计总收入、环比增长率）
  - `TrendData`: 6个月趋势数据点（月份、收入、增长率）
  - `Insights`: 数据洞察（最佳表现月份、增长趋势）
- 使用了Swagger注解进行API文档化
- 使用了Jackson注解进行JSON序列化控制

### 2. 扩展文件：Mapper接口
**文件**: `ruoyi-system/src/main/java/com/ruoyi/system/mapper/commission/CommissionPayoutsMapper.java`
**行数范围**: 1-7 (import), 104-124 (新增方法)
**修改内容**:
- 添加了`BigDecimal`的import
- 新增了`selectCommissionPayoutsByCreatorAndMonthRange`方法：查询指定分销员在指定月份范围内的收入数据
- 新增了`selectTotalIncomeByCreatorAndYear`方法：查询指定分销员在指定年份的累计收入

### 3. 扩展文件：Mapper XML
**文件**: `ruoyi-system/src/main/resources/mapper/commission/CommissionPayoutsMapper.xml`
**行数范围**: 177-191
**修改内容**:
- 新增了`selectCommissionPayoutsByCreatorAndMonthRange`的SQL实现：按时间范围查询收入数据，按月份升序排序
- 新增了`selectTotalIncomeByCreatorAndYear`的SQL实现：计算年度累计收入，使用COALESCE处理NULL值

### 4. 新增文件：Service接口
**文件**: `ruoyi-system/src/main/java/com/ruoyi/system/service/business/IMonthlyPerformanceReportService.java`
**行数范围**: 1-20
**修改内容**:
- 定义了`getMonthlyPerformanceReport`方法接口
- 包含完整的JavaDoc注释说明

### 5. 新增文件：Service实现类
**文件**: `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/business/MonthlyPerformanceReportServiceImpl.java`
**行数范围**: 1-300+
**修改内容**:
- 实现了完整的业务逻辑，包括：
  - 数据查询和处理
  - 顶部摘要计算（年度累计收入、环比增长率）
  - 趋势数据生成（6个月数据点，环比增长率计算）
  - 数据洞察生成（最佳表现月份、增长趋势分析）
- 使用了`@DataSource(SLAVE)`注解确保数据源正确
- 包含完整的异常处理和日志记录
- 使用线程安全的SimpleDateFormat作为实例变量

### 6. 新增文件：Controller控制器
**文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/business/MonthlyPerformanceReportController.java`
**行数范围**: 1-80
**修改内容**:
- 实现了GET接口，路径为`/api/reports/monthly-performance/{creatorId}/{month}`
- 包含完整的参数验证（格式验证、范围验证）
- 使用了`@PreAuthorize("@ss.hasPermi('h:reports')")`权限控制
- 遵循若依框架的Controller规范，继承BaseController
- 包含完整的Swagger API文档注解
- 实现了标准的若依JSON响应格式

## 需要重点审查的内容

### 1. 业务逻辑正确性
- **环比增长率计算**: 验证`(当月收入 / 上月收入) - 1`的计算逻辑是否正确
- **趋势数据生成**: 确认6个月数据点的生成逻辑，特别是第一个数据点的growth为null的处理
- **数据洞察算法**: 检查最佳表现月份查找和增长趋势判断的算法是否合理
- **边界情况处理**: 验证用户历史数据不足7个月时的处理逻辑

### 2. 数据源配置
- **@DataSource注解**: 确认Service层和Mapper层都正确使用了`@DataSource(SLAVE)`
- **数据库查询**: 验证SQL查询是否正确访问`streamer_distribution_system`数据库

### 3. 性能考虑
- **查询效率**: 检查是否需要在`commission_payouts`表上添加`(creator_id, data_month)`联合索引
- **内存使用**: 评估大量数据处理时的内存占用情况

### 4. 代码规范
- **若依框架规范**: 确认是否遵循了若依框架的开发规范
- **权限控制**: 验证`h:reports`权限的使用是否正确
- **异常处理**: 检查异常处理是否完整和合理
- **日志记录**: 评估日志记录的详细程度和有用性

### 5. API设计
- **响应格式**: 确认是否严格遵循若依的JSON响应格式`{code, msg, data}`
- **参数验证**: 检查路径参数的验证是否充分
- **错误处理**: 验证各种错误情况的处理和响应

### 6. 线程安全
- **SimpleDateFormat**: 确认SimpleDateFormat的使用是否线程安全
- **共享状态**: 检查是否存在线程安全问题

### 7. 数据精度
- **BigDecimal使用**: 验证金额计算中BigDecimal的使用是否正确
- **舍入模式**: 检查除法运算中的舍入模式设置

## 预期响应格式示例
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "selectedMonth": "2024-12",
        "summary": {
            "totalIncomeToDate": 456800.00,
            "selectedMonthGrowth": 0.152
        },
        "trendData": [
            { "month": "2024-07", "income": 12500, "growth": null },
            { "month": "2024-08", "income": 18900, "growth": 0.512 },
            { "month": "2024-09", "income": 25400, "growth": 0.343 },
            { "month": "2024-10", "income": 32100, "growth": 0.263 },
            { "month": "2024-11", "income": 28900, "growth": -0.099 },
            { "month": "2024-12", "income": 38600, "growth": 0.335 }
        ],
        "insights": {
            "bestMonth": {
                "month": "2024-12",
                "income": 38600
            },
            "growthTrend": {
                "type": "UPWARD",
                "message": "持续上升趋势，表现优秀"
            }
        }
    }
}
```

## 审查要求
请从以上各个方面对代码进行详细审查，特别关注：
1. 业务逻辑的正确性和完整性
2. 代码的健壮性和异常处理
3. 性能优化的可能性
4. 是否符合若依框架的开发规范
5. API设计的合理性和易用性
