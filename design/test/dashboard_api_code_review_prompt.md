# 个人主仪表盘API代码审查提示词

## 审查背景
本次开发实现了一个"分销员业绩系统"的核心后端API接口，为前端的"个人主仪表盘"提供所有需要展示的数据。请对以下新增和修改的代码进行全面审查。

## 需求概述
- **API接口**: GET /api/dashboard/{creatorId}/{month}
- **功能**: 获取指定分销员在指定月份的完整仪表盘聚合数据
- **数据源**: 基于历史月份数据进行聚合和分析，不涉及实时计算
- **响应格式**: 严格遵循若依框架的JSON封装格式 (code/msg/data结构)

## 新增文件清单

### 1. DTO数据传输对象 (ruoyi-system/src/main/java/com/ruoyi/system/domain/dto/)
- **DashboardResponse.java** (1-95行) - 主响应对象
- **UserInfo.java** (1-25行) - 用户信息对象  
- **LevelMaintenance.java** (1-35行) - 保级状态对象
- **PerformanceAnalytics.java** (1-45行) - 绩效分析对象
- **IncomeComponents.java** (1-30行) - 收入构成对象
- **PerformanceKPIs.java** (1-30行) - 关键绩效指标对象

### 2. 数据访问层
- **DashboardMapper.java** (1-85行) - Mapper接口，包含8个查询方法
- **DashboardMapper.xml** (1-85行) - MyBatis映射文件，实现复杂SQL查询

### 3. 业务逻辑层
- **IDashboardService.java** (1-20行) - Service接口
- **DashboardServiceImpl.java** (1-273行) - Service实现类，包含核心业务逻辑

### 4. 控制器层
- **DashboardController.java** (1-65行) - REST控制器，实现API端点

## 核心业务逻辑审查要点

### 1. 数据计算逻辑
- **年度累计总收入**: 截至所选月份末的年度累计总收入计算
- **月度收入环比增长率**: (本月收入 / 上月收入) - 1，需处理上月收入为0的边界情况
- **保级状态计算**: 基于"防守算法"计算实际月均环比增长率，与目标增长率(15%)比较
- **收入分类统计**: 个人收入(LEVEL_COMMISSION) vs 团队收入(MULTI_LEVEL_COMMISSION, RECRUITMENT_BONUS)

### 2. 数据源和SQL查询
- **数据库**: 使用@DataSource(SLAVE)访问streamer_distribution_system数据库
- **主要表**: commission_payouts, commission_payout_breakdowns, commission_distributor_qualifications, commission_settings, creator_relationships, creators
- **复杂查询**: 年度收入聚合、过去4个月收入趋势、团队层级关系统计

### 3. 异常处理和数据验证
- **参数验证**: creatorId > 0, month格式为YYYY-MM
- **边界情况**: 用户不存在、数据为空、收入为负数、除零操作
- **数据安全**: 确保所有金额字段不为负数，占比在0-1之间

## 技术规范审查要点

### 1. 若依框架规范
- **权限控制**: @PreAuthorize("@ss.hasPermi('h:dashboard')")
- **数据源配置**: @DataSource(value = DataSourceType.SLAVE)
- **响应格式**: AjaxResult.success(data) 封装
- **Controller注释**: "后台-个人主仪表盘控制器 h:dashboard"

### 2. 代码质量
- **日志记录**: 使用SLF4J记录关键操作和异常
- **异常处理**: ServiceException统一异常处理
- **数据精度**: BigDecimal处理金额，RoundingMode.HALF_UP舍入
- **空值处理**: 所有查询结果进行null检查

### 3. 性能考虑
- **查询优化**: 避免N+1查询，使用合适的索引
- **数据缓存**: 考虑是否需要缓存配置数据
- **批量操作**: 团队统计使用单次查询而非循环

## 审查检查清单

### 功能完整性
- [ ] 是否实现了需求中的所有数据字段
- [ ] 保级状态计算逻辑是否正确
- [ ] 收入分类是否符合业务规则
- [ ] 团队统计逻辑是否准确

### 代码质量
- [ ] 是否遵循若依框架开发规范
- [ ] 异常处理是否完善
- [ ] 日志记录是否充分
- [ ] 代码注释是否清晰

### 数据安全
- [ ] SQL注入防护是否到位
- [ ] 参数验证是否充分
- [ ] 数据权限控制是否正确
- [ ] 敏感数据处理是否安全

### 性能优化
- [ ] SQL查询是否高效
- [ ] 是否存在性能瓶颈
- [ ] 内存使用是否合理
- [ ] 是否需要添加缓存

## 特别关注点

1. **防守算法实现**: DashboardServiceImpl.calculateActualGrowthRate()方法的计算逻辑是否正确
2. **数据一致性**: 个人收入+团队收入是否等于总收入
3. **边界情况**: 新用户、无历史数据、数据异常等情况的处理
4. **API响应格式**: 是否严格遵循需求中定义的JSON结构

请基于以上要点对代码进行全面审查，重点关注业务逻辑正确性、代码质量和潜在风险。
