<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<requirements>
# 需求： 


二、 任务背景与目标 (Task Background & Objective)
我需要你为一个“分销员业绩系统”开发一个业绩分析报告的后端API接口。这个接口的核心功能是，当用户选择一个历史月份后，系统能展示以该月为终点的、过去6个月的收入趋势分析图，并提供一些自动化的数据洞察，如最佳表现月份和整体增长趋势。

三、 API接口定义 (API Endpoint Definition)
HTTP方法: GET

URL: /api/reports/monthly-performance/{creatorId}/{month}

路径参数:

{creatorId}: Long - 需要查询的分销员的用户ID。

{month}: String - 需要查询的基准月份，格式为 YYYY-MM (例如 2024-12)。

功能描述: 获取指定分销员 (creatorId) 在指定月份 (month) 的完整业绩分析报告，包含过去6个月的趋势数据和洞察。

四、 核心业务逻辑与数据来源 (Core Business Logic & Data Sources)
你需要整合来自数据表的信息，计算出以下JSON结构中定义的每一个字段。

1. summary (顶部摘要)
totalIncomeToDate: 截至所选月份末的年度累计总收入。

来源: commission_payouts 表。

SQL逻辑: SELECT SUM(final_payout_usd) FROM commission_payouts WHERE creator_id = :creatorId AND YEAR(data_month) = YEAR(:month) AND data_month <= :month;

selectedMonthGrowth: 所选月份的收入环比增长率（对比上一个月）。

来源: commission_payouts 表。

逻辑:

获取所选月份的收入。

获取上一个月的收入。

计算 (所选月份收入 / 上月收入) - 1。注意处理上月收入为0的边界情况。

2. trendData (6个月趋势数据点数组)
这是此API的核心逻辑。你需要返回一个包含6个对象的数组，每个对象代表一个月的数据。

来源: commission_payouts 表。

逻辑:

为了计算6个月的环比增长率，你需要一次性获取过去7个月的收入数据。

SQL逻辑: SELECT data_month, final_payout_usd FROM commission_payouts WHERE creator_id = :creatorId AND data_month BETWEEN DATE_SUB(CONCAT(:month, '-01'), INTERVAL 6 MONTH) AND CONCAT(:month, '-01') ORDER BY data_month ASC;

在Java Service层，遍历这7个月的数据结果，生成一个包含6个数据点的 trendData 数组。对于每个数据点（例如8月），它的growth值由 (8月收入 / 7月收入) - 1 计算得出。第一个数据点（7月）的growth为null。

3. insights (数据洞察)
bestMonth: 过去6个月中收入最高的一个月。

逻辑: 在生成 trendData 数组后，在Service层直接遍历此数组，找出 income 最高的那个对象，并返回其 month 和 income。

growthTrend: 对过去6个月增长趋势的定性判断。

逻辑:

在生成 trendData 数组时，你已经计算出了5个环比增长率 (growth值)。

在Service层统计这5个growth值中大于0的个数（positive_months）。

应用业务规则：

if (positive_months >= 4)，则type为 "UPWARD"。

else if (positive_months <= 1)，则type为 "DOWNWARD"。

else，则type为 "STABLE"。

根据type，从预设的文案库中返回对应的message。

五、 数据库表结构参考 (Database Schema Reference)
SQL

-- 佣金月度实收表，此API的核心数据源
CREATE TABLE `commission_payouts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `creator_id` bigint(20) unsigned NOT NULL,
  `data_month` date NOT NULL COMMENT '数据月份 (YYYY-MM-01)',
  `distributor_level` varchar(50) DEFAULT NULL,
  `final_payout_usd` decimal(15,2) NOT NULL COMMENT '最终实收总额(USD)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_creator_month` (`creator_id`,`data_month`)
) COMMENT='分销员月度实收金额表';

-- 用户信息表
CREATE TABLE `creators` ( ... );
(注：此处省略了详细的字段，实际使用时应将完整的CREATE TABLE语句提供给AI)

六、 性能与实现建议 (Performance & Implementation Recommendations)
此API的核心查询是基于单个用户的、按时间范围的查询。为了保证性能，务必确保 commission_payouts 表上存在 (creator_id, data_month) 的联合索引。

所有的业务逻辑，如计算环比、寻找最大值、判断趋势等，都应该在Java的Service层完成，而不是在数据库中用复杂的SQL实现。

七、 响应格式与示例 (Response Format & Example)
严格要求: 所有成功的API响应，都必须封装在标准的“若依 (ruoyi)”风格的JSON结构中。

JSON

{
    "code": 200,
    "msg": "查询成功",
    "data": {
      "selectedMonth": "2024-12",
      "summary": {
        "totalIncomeToDate": 456800.00,
        "selectedMonthGrowth": 0.152
      },
      "trendData": [
        { "month": "2024-07", "income": 12500, "growth": null },
        { "month": "2024-08", "income": 18900, "growth": 0.512 },
        { "month": "2024-09", "income": 25400, "growth": 0.343 },
        { "month": "2024-10", "income": 32100, "growth": 0.263 },
        { "month": "2024-11", "income": 28900, "growth": -0.099 },
        { "month": "2024-12", "income": 38600, "growth": 0.335 }
      ],
      "insights": {
        "bestMonth": {
          "month": "2024-12",
          "income": 38600
        },
        "growthTrend": {
          "type": "UPWARD",
          "message": "Continuous upward trend, excellent performance"
        }
      }
    }
}
八、 任务要求总结 (Summary of Task Requirements)
生成代码: 请为这个API生成完整的后端代码，包括Controller, Service, ServiceImpl, Mapper接口和对应的XML/Mapper实现。

业务逻辑: 核心的数据处理和洞察生成逻辑，应清晰地在Service层实现。

边界处理: 代码需要能优雅地处理用户历史数据不足7个月的情况（例如，一个新用户只有3个月的收入数据，那么trendData数组就只包含3个对象，并且只有2个对象有growth值）。

严格遵循格式: Controller的最终返回结果，必须严格遵守第七部分中定义的“若依”JSON封装格式。
</requirements>



<data_structure>
@database.sql
</data_structure>


- 先读需求内容requirements， 了解后. 读数据库结构 data_structure 
- 逐步思考, 本次的修改内容， 及修改方案， 列出修改计划
- 实现修改现有代码逻辑
- 所有的代码实现后， 为其它大模型生成一个review这次修改内容的提示词，生成到本地的/design/test/ 目录中。 提示词要求： 提供本次修改的文件及对应的行数范围， 修改的内容，需求， 及要求review 的内容。 

 

