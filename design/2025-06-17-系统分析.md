# 角色
你是一名优秀的产品需求分析师， 并精通程序开发。 根据我的需求及产品描述，进一步理清产品的需求。 

# 需求： 
当前要做一个公会给主播的奖励分成系统。

# 系统中的角色
- 公会，就是系统管理者
- 主播，TIKTOK的主播
- 分销员， 同时是主播， 但可以拉其它主播进入公会， 与被拉者成为上下级关系 
    - 示例： 分销员-》下级主播。
    - 分销员可以有多个下级， 下级可以是主播， 也可以是分销员。
    - 每个下级也可以成为分销员，也可以有自己的下级。
    - 这样理论上每个分销员可以有无限多的下级， 这是一个树状的结构。 
    - 分销员可以按比例分成 自己+直属下级（1级下级）总收入。 详细看配置项。
    - 分销员还可以按比例分成 自己下3级 总收入， 每一级有不同的分成比例， 详细可以看配置项。
    - 分销员拉新奖励， 分销员拉了新的主播成为自己的下级， 按规则获取拉新奖励。


# 分销员的等级规则： 
- 分销员 可以按比例抽取 自己+直属下级收入之和的一个比例， 这个分成是系统发送的，  这个比例很重要， 这个比例是根据当前的主播的等级或完成的任务来决定的。 
- 开始分成所需钻石数|直属下级人数 都是必要条件
分销员等级|开始分成所需钻石数|直属下级人数|分成比例
金牌|5000|30|2% 
银牌|5000|20|1% 
铜牌|5000|10|0.5% 

# 配置项
- 公会当前在TIKTOK收入分成比例：  10%  
- 公会月总收入USD： 100000 美元

## 分销员配置
-- 分销员达到一定等级， 也就是达成了下面配置的要求 如： 开始分成所需钻石数|直属下级人数， 可以按配置分成比例， 分成自己+直属下级所有当月收入的钻石。 
-- 配置： 
分销员等级|开始分成所需钻石数|直属下级人数|分成比例
金牌|5000|30|2% 
银牌|5000|20|1% 
铜牌|5000|10|0.5% 

-- 这里的 分成比例， 针对的是钻石数，也就是分销员的自己直播获取的数量 + 直属下级获取的数量。 

## 开始分成所需钻石数 每月上浮逻辑 
开始分成所需钻石数： 这个数值（5000） 是一个开始分成的钻石数， 每个月会根据主播在当月完成的基础上进行向上提高， 在当前月的基础上提高1.2%。  这个1.2% 也是一个配置项。 
如果用户完成了 基础数值 5000 下个月在5000的基础上上浮 1.2% 
如果用户未完成 基础数值 5000， 只完成4800 ， 在4990 的基础上上浮1.2%， 但上浮后的值 必须 >= 5000 才能分成。 
如果用户未完成 基础数值 5000，只完成2000， 在2000的基础上上浮1.2%， 上浮后的值 没有超过5000 不能分成。 

## 分销员下3级提成比例配置， 只分成其下3级的内的收益
分销员还可以分成下3级内的收入， 这个收入也是按比例分成， 但这个分成是要先达到分销员的等级要求才能分成。 

分成类型 | 分成比例
——————————
分成 下级  | 2% 
分成 下下级 | 1% 
分成 下下下级 | 0.5% 

## 分销员拉新奖励， 这个奖励是额外奖励， 不需要其它规则 ， 只要他达到了月拉新人数就可获取奖励
月拉新人数|奖励金额
5|10
10|20
30|50 

# 钻石 换成 美元的比例
配置 钻石*比例 = 美元

## 约定
- 分销员拉新奖励， 这个配置的USD美元
- 分销员 配置最后获取的是 钻石  
- 系统中要添加配置 钻石 换成 美元的比例 


# 当前系统预期
- 系统管员配置对应的配置项
- 导入指定月的主播或分销员的关系及直播获取的钻石数等数据， 已经完成。 
- 根据这些数据生成指定月的统计信息， 如当前公会为分销员分成的总数，当前的分成比例， 每个主播的分成收入及分成收入的来源，及来源分成明细。 其中能看到收入的钻石， 及把钻石转换成USD后的值，   等级分成换成USD + 下三级提成换算成USD + 拉新奖励
A. 等级分成 (Level Commission): 计算结果为 钻石。
B. 下三级提成 (Multi-level Commission): 计算结果为 钻石。
C. 拉新奖励 (Recruitment Bonus): 计算结果为 美元。
- 系统的最终目的很明确， 系统分成的数量不得超过当前系统的总收入的30%。 控制住整体的系统的收入不会超出预期
- 系统最终想看到的 当月的总支出 USD 美元
- 系统还想 确定一个相对稳定的分成的配置, 至少能稳定6个月， 不会变化，来让主播更简单的了解当前系统的分成原则




 策略与稳定性建议 (新增)
此部分为非功能性需求，旨在指导公会如何使用本系统达成业务目标。

目标: 确保分成配置能稳定至少6个月，为主播提供清晰、可信的激励预期。
建议:
上线前建模: 在系统配置上线前，使用历史数据进行多次模拟测算。调整各项分成比例（distributor_levels, multilevel_commission_rates等），观察在不同业绩水平下，总支出是否会频繁触及30%的红线。
寻求平衡: 寻找一个既有吸引力又能保证财务安全的“甜点”配置。目标是让绝大多数正常月份的总支出落在总收入的20%-25%区间，为业绩爆发的月份留出缓冲空间。
透明沟通: 一旦配置确定，应向所有主播清晰、详细地公示规则，建立信任。
未来迭代 (可选): 未来可开发一个“沙盒”或“模拟器”功能，让管理员可以输入预估的业绩数据，系统能立即反馈出预估的总支出和各项指标，辅助决策。


# 问题： 

② 等级分成 VS. 三级提成 的“重复计量”风险
等级分成公式把「自己 + 全部 L1」的钻石收入做一次乘法。随后三级提成又对 L1～L3 再分配一次。L1 部分被“双分配”；如未做冲抵会导致“二次分成”。