-- 分销员月度汇总表，用于存储预计算的团队数据
-- 这个表是为了优化团队结构下钻API的性能而设计的
-- 避免实时递归计算团队收入和团队人数

CREATE TABLE `distributor_monthly_summary` (
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '分销员ID (关联creators.id)',
  `data_month` date NOT NULL COMMENT '数据月份 (YYYY-MM-01)',
  `total_team_income_usd` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '团队总收入(USD) - 包含自己和所有下级的收入总和',
  `total_team_size` int(11) NOT NULL DEFAULT '0' COMMENT '团队总人数 - 包含自己和所有下级的人数总和',
  `direct_subordinates_count` int(11) NOT NULL DEFAULT '0' COMMENT '直属下级人数 - L1层级的人数',
  `personal_income_usd` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '个人收入(USD) - 仅自己的收入',
  `team_income_usd` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '团队收入(USD) - 不包含自己，仅下级团队的收入总和',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`creator_id`,`data_month`),
  KEY `idx_month_income` (`data_month`,`total_team_income_usd`),
  KEY `idx_month_size` (`data_month`,`total_team_size`),
  CONSTRAINT `fk_summary_creator` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员月度汇总表 - 预计算团队数据以优化API性能';

-- 创建索引以优化查询性能
CREATE INDEX `idx_creator_month_desc` ON `distributor_monthly_summary` (`creator_id`, `data_month` DESC);
CREATE INDEX `idx_month_team_income_desc` ON `distributor_monthly_summary` (`data_month`, `total_team_income_usd` DESC);
CREATE INDEX `idx_month_team_size_desc` ON `distributor_monthly_summary` (`data_month`, `total_team_size` DESC);

-- 插入示例数据（用于测试）
-- 注意：在生产环境中，这些数据应该通过定时任务或计算服务来生成
INSERT INTO `distributor_monthly_summary` (`creator_id`, `data_month`, `total_team_income_usd`, `total_team_size`, `direct_subordinates_count`, `personal_income_usd`, `team_income_usd`) VALUES
(1, '2024-12-01', 360100.00, 71, 3, 45600.00, 314500.00),
(124, '2024-12-01', 45600.00, 8, 1, 12000.00, 33600.00),
(125, '2024-12-01', 128900.00, 25, 3, 28900.00, 100000.00),
(126, '2024-12-01', 185600.00, 38, 2, 35600.00, 150000.00);