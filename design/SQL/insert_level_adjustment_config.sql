-- 插入等级调整相关的配置参数
-- 用于支持"防守"体系的月度业绩考核与等级调整机制

-- 使用 INSERT ... ON DUPLICATE KEY UPDATE 确保数据可重复执行插入或更新
INSERT INTO `commission_settings` (`setting_key`, `setting_value`, `description`) VALUES
('new_recruit_tenure_days', '60', '新人资格-账号最大创建天数'),
('new_recruit_min_valid_days', '24', '新人资格-月度最低有效直播天数'),
('retention_target_avg_growth_rate', '0.15', '业绩保级-目标平均增长率(15%)'),
('retention_growth_from_zero', '1.00', '业绩保级-从零增长的定义值(100%)'),
('retention_growth_rate_cap', '3.00', '业绩保级-单月增长率上限(300%)'),
('new_user_grace_period_months', '4', '新手保护期月数'),
('upgrade_protection_period_months', '2', '升级保护期月数')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`), 
    `description` = VALUES(`description`),
    `updated_at` = CURRENT_TIMESTAMP;

-- 验证插入结果
SELECT * FROM `commission_settings` 
WHERE `setting_key` IN (
    'new_recruit_tenure_days',
    'new_recruit_min_valid_days', 
    'retention_target_avg_growth_rate',
    'retention_growth_from_zero',
    'retention_growth_rate_cap',
    'new_user_grace_period_months',
    'upgrade_protection_period_months'
)
ORDER BY `setting_key`;
