-- 数据库迁移脚本：为 commission_monthly_summary 表添加 total_bonus_estimated 字段
-- 执行日期: 2025-01-17
-- 描述: 添加当月主播奖金估计总额字段，用于显示 monthly_performance.bonus_estimated 的累计值

-- 添加新字段
ALTER TABLE `commission_monthly_summary` 
ADD COLUMN `total_bonus_estimated` decimal(20,4) DEFAULT '0.0000' COMMENT '当月主播奖金估计总额 (来自monthly_performance.bonus_estimated累计)' AFTER `exceeded_amount_usd`;

-- 更新现有记录，计算并填充 total_bonus_estimated 字段
UPDATE commission_monthly_summary cms
SET total_bonus_estimated = (
    SELECT COALESCE(SUM(mp.bonus_estimated), 0)
    FROM monthly_performance mp
    WHERE DATE_FORMAT(mp.data_month, '%Y-%m') = DATE_FORMAT(cms.data_month, '%Y-%m')
)
WHERE cms.data_month IS NOT NULL;

-- 验证更新结果
SELECT 
    cms.data_month,
    cms.total_bonus_estimated,
    (SELECT COALESCE(SUM(mp.bonus_estimated), 0) 
     FROM monthly_performance mp 
     WHERE DATE_FORMAT(mp.data_month, '%Y-%m') = DATE_FORMAT(cms.data_month, '%Y-%m')) as calculated_total
FROM commission_monthly_summary cms
ORDER BY cms.data_month DESC; 