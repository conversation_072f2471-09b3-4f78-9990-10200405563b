-- Create syntax for TABLE 'commission_calculation_logs'
CREATE TABLE `commission_calculation_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `data_month` date NOT NULL,
  `calculation_type` varchar(50) NOT NULL COMMENT '计算类型',
  `creator_id` bigint(20) unsigned DEFAULT NULL,
  `log_level` enum('INFO','WARN','ERROR') NOT NULL DEFAULT 'INFO',
  `message` text NOT NULL,
  `details` json DEFAULT NULL COMMENT '详细信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_month_type` (`data_month`,`calculation_type`),
  KEY `idx_creator_month` (`creator_id`,`data_month`)
) ENGINE=InnoDB AUTO_INCREMENT=224126 DEFAULT CHARSET=utf8mb4 COMMENT='佣金计算日志表';

-- Create syntax for TABLE 'commission_config_snapshots'
CREATE TABLE `commission_config_snapshots` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `data_month` date NOT NULL COMMENT '配置生效月份',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型',
  `config_data` json NOT NULL COMMENT '配置数据快照',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_month_type` (`data_month`,`config_type`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='月度配置快照表，确保历史计算可重现';

-- Create syntax for TABLE 'commission_distributor_qualifications'
CREATE TABLE `commission_distributor_qualifications` (
  `creator_id` bigint(20) unsigned NOT NULL,
  `data_month` date NOT NULL,
  `is_qualified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否符合分销员资格',
  `achieved_level` varchar(50) DEFAULT NULL COMMENT '达成的等级',
  `personal_diamonds` bigint(20) NOT NULL DEFAULT '0' COMMENT '个人钻石收入',
  `team_diamonds` bigint(20) NOT NULL DEFAULT '0' COMMENT '团队钻石收入',
  `direct_downlines_count` int(11) NOT NULL DEFAULT '0' COMMENT '直属下级数量',
  `dynamic_threshold` bigint(20) NOT NULL DEFAULT '0' COMMENT '动态门槛',
  `threshold_met` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否达到门槛',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`creator_id`,`data_month`),
  KEY `idx_month_qualified` (`data_month`,`is_qualified`),
  CONSTRAINT `fk_qualification_creator` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员月度资格记录表';

-- Create syntax for TABLE 'commission_dynamic_thresholds'
CREATE TABLE `commission_dynamic_thresholds` (
  `creator_id` bigint(20) unsigned NOT NULL,
  `data_month` date NOT NULL,
  `last_month_diamonds` bigint(20) NOT NULL DEFAULT '0' COMMENT '上月钻石收入',
  `calculated_threshold` bigint(20) NOT NULL COMMENT '计算出的动态门槛',
  `base_threshold` bigint(20) NOT NULL COMMENT '基础门槛',
  `threshold_increase_rate` decimal(7,4) NOT NULL COMMENT '上浮比例',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`creator_id`,`data_month`),
  CONSTRAINT `fk_threshold_creator` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员动态门槛计算记录表';

-- Create syntax for TABLE 'commission_level_rules'
CREATE TABLE `commission_level_rules` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `level_name` varchar(50) NOT NULL COMMENT '分销员等级名称 (如: 金牌, 银牌, 铜牌)',
  `min_qualified_recruits` int(11) NOT NULL COMMENT '该等级要求的最低月度合格拉新人数',
  `commission_rate` decimal(7,4) NOT NULL COMMENT '该等级的分成比例 (例如 0.0200 代表 2%)',
  `payout_depth` tinyint(4) NOT NULL DEFAULT '1' COMMENT '佣金收益深度 (1=下1级, 2=下2级, 3=下3级)',
  `display_order` tinyint(4) DEFAULT '0' COMMENT '用于排序显示',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (1=是, 0=否)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_name` (`level_name`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='分销员等级分成规则表';

-- Create syntax for TABLE 'commission_monthly_settings'
CREATE TABLE `commission_monthly_settings` (
  `data_month` date NOT NULL COMMENT '数据月份 (YYYY-MM-01)',
  `manual_income_usd` decimal(20,4) NOT NULL COMMENT '管理员输入的当月预算收入(USD)',
  `payout_cap_rate` decimal(7,4) NOT NULL COMMENT '支出上限比例 (例如 0.3000 代表 30%)',
  `diamond_to_usd_rate` decimal(10,6) NOT NULL COMMENT '当月生效的钻石兑美元汇率',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`data_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='月度佣金配置表 (预算与汇率)';

-- Create syntax for TABLE 'commission_monthly_summary'
CREATE TABLE `commission_monthly_summary` (
  `data_month` date NOT NULL COMMENT '数据月份 (YYYY-MM-01)',
  `budget_usd` decimal(20,4) NOT NULL COMMENT '当月预算收入 (来自monthly_settings)',
  `payout_cap_usd` decimal(20,4) NOT NULL COMMENT '当月支出上限 (USD)',
  `actual_payout_usd` decimal(20,4) NOT NULL COMMENT '实际总支出 (USD)',
  `payout_to_budget_ratio` decimal(7,4) NOT NULL COMMENT '支出/收入比例',
  `exceeded_amount_usd` decimal(20,4) NOT NULL COMMENT '超出预算金额 (若未超出则为0)',
  `total_bonus_estimated` decimal(20,4) DEFAULT '0.0000' COMMENT '当月主播奖金估计总额 (来自monthly_performance.bonus_estimated累计)',
  `calculation_status` varchar(50) DEFAULT 'COMPLETED' COMMENT '计算状态',
  `calculated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计算执行时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`data_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金月度计算总览表';

-- Create syntax for TABLE 'commission_multilevel_rules'
CREATE TABLE `commission_multilevel_rules` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `depth` int(11) NOT NULL COMMENT '下级深度 (1=L1, 2=L2, 3=L3)',
  `commission_rate` decimal(7,4) NOT NULL COMMENT '该层级的提成比例 (例如 0.0100 代表 1%)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (1=是, 0=否)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_depth` (`depth`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='下三级提成规则表';

-- Create syntax for TABLE 'commission_payout_breakdowns'
CREATE TABLE `commission_payout_breakdowns` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `payout_id` bigint(20) unsigned NOT NULL COMMENT '关联到commission_payouts.id',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '分销员ID (冗余字段, 便于查询)',
  `data_month` date NOT NULL COMMENT '数据月份 (冗余字段, 便于查询)',
  `source_type` enum('LEVEL_COMMISSION','MULTI_LEVEL_COMMISSION','RECRUITMENT_BONUS') NOT NULL COMMENT '收入来源类型',
  `base_amount_diamonds` bigint(20) DEFAULT NULL COMMENT '计算基数 (钻石), 如团队总收入 | 类型为RECRUITMENT_BONUS 时为拉新人数',
  `calculated_amount_diamonds` bigint(20) DEFAULT NULL COMMENT '计算出的钻石金额',
  `calculated_amount_usd` decimal(15,4) NOT NULL COMMENT '计算出的USD金额 (拉新奖励直接记录, 其他为换算后)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_payout_id` (`payout_id`),
  KEY `idx_creator_month` (`creator_id`,`data_month`),
  CONSTRAINT `fk_breakdown_payout` FOREIGN KEY (`payout_id`) REFERENCES `commission_payouts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=147 DEFAULT CHARSET=utf8mb4 COMMENT='分销员收入构成明细表';

-- Create syntax for TABLE 'commission_payouts'
CREATE TABLE `commission_payouts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '分销员ID (关联creators.id)',
  `data_month` date NOT NULL COMMENT '数据月份 (YYYY-MM-01)',
  `distributor_level` varchar(50) DEFAULT NULL COMMENT '当月达成的分销员等级',
  `final_payout_usd` decimal(15,2) NOT NULL COMMENT '最终实收总额(USD, 保留2位小数)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_creator_month` (`creator_id`,`data_month`),
  KEY `idx_month_payout` (`data_month`,`final_payout_usd`),
  CONSTRAINT `fk_payout_creator` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=242 DEFAULT CHARSET=utf8mb4 COMMENT='分销员月度实收金额表';

-- Create syntax for TABLE 'commission_recruitment_bonus_rules'
CREATE TABLE `commission_recruitment_bonus_rules` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `min_new_recruits` int(11) NOT NULL COMMENT '月拉新人数要求 (阶梯下限)',
  `bonus_usd` decimal(15,4) NOT NULL COMMENT '对应的奖励金额 (USD)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (1=是, 0=否)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_min_recruits` (`min_new_recruits`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='分销员拉新奖励规则表';

-- Create syntax for TABLE 'commission_recruitment_stats'
CREATE TABLE `commission_recruitment_stats` (
  `recruiter_id` bigint(20) unsigned NOT NULL COMMENT '招募人ID',
  `data_month` date NOT NULL COMMENT '统计月份',
  `new_recruits_count` int(11) NOT NULL DEFAULT '0' COMMENT '当月新招募人数',
  `qualified_bonus_usd` decimal(15,4) NOT NULL DEFAULT '0.0000' COMMENT '符合条件的奖励金额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`recruiter_id`,`data_month`),
  CONSTRAINT `fk_recruitment_recruiter` FOREIGN KEY (`recruiter_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='招募统计表，优化拉新奖励计算';

-- Create syntax for TABLE 'commission_settings'
CREATE TABLE `commission_settings` (
  `setting_key` varchar(100) NOT NULL COMMENT '配置项的唯一键',
  `setting_value` varchar(255) NOT NULL COMMENT '配置项的值',
  `description` varchar(500) DEFAULT NULL COMMENT '配置项描述',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局佣金配置表 (如基础门槛, 上浮比例)';

-- Create syntax for TABLE 'creator_relationships'
CREATE TABLE `creator_relationships` (
  `ancestor_id` bigint(20) unsigned NOT NULL COMMENT '祖先用户ID (关联 creators.id)',
  `descendant_id` bigint(20) unsigned NOT NULL COMMENT '后代用户ID (关联 creators.id)',
  `depth` int(11) NOT NULL COMMENT '层级深度 (0表示用户自身)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ancestor_id`,`descendant_id`),
  KEY `idx_cr_descendant_depth` (`descendant_id`,`depth`),
  KEY `idx_cr_ancestor_depth` (`ancestor_id`,`depth`),
  CONSTRAINT `fk_rel_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rel_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主播层级关系闭包表';

-- Create syntax for TABLE 'creators'
CREATE TABLE `creators` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主播的唯一ID (Creator ID)',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '直接上级主播的ID, 顶级为NULL',
  `first_qualified_month` date DEFAULT NULL COMMENT '首次被确认为合格拉新的月份 (YYYY-MM-01), 用于确保唯一性',
  `qualified_by_recruiter_id` bigint(20) unsigned DEFAULT NULL COMMENT '确认其合格时的直接上级(parent_id)快照, 用于永久锁定业绩归属',
  `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主播昵称',
  `handle` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主播的Handle',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `first_active_month` date DEFAULT NULL COMMENT '首次活跃月份，用于新人识别',
  `recruited_by` bigint(20) unsigned DEFAULT NULL COMMENT '招募人ID',
  `current_distributor_level_id` int(10) unsigned DEFAULT NULL COMMENT '当前基础等级ID, 关联commission_level_rules.id, 由“防守”体系更新',
  `level_updated_at` timestamp NULL DEFAULT NULL COMMENT '基础等级最后更新时间, 用于判断升级保护期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_handle` (`handle`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_recruited_by` (`recruited_by`),
  KEY `idx_first_active` (`first_active_month`),
  KEY `idx_qualified_by_month` (`qualified_by_recruiter_id`,`first_qualified_month`),
  KEY `idx_creators_current_level` (`current_distributor_level_id`),
  KEY `idx_creators_qualified_by_month` (`qualified_by_recruiter_id`,`first_qualified_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播信息与直接关系表';

-- Create syntax for TABLE 'group_creator_relation'
CREATE TABLE `group_creator_relation` (
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '组名',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建者ID, 关联creators表的主键',
  `group_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '组类型：0-分销组，1-非分销组',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`group_name`),
  UNIQUE KEY `uk_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组名与CreatorID关系表';

-- Create syntax for TABLE 'monthly_performance'
CREATE TABLE `monthly_performance` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '关联到creators.id',
  `data_month` date NOT NULL COMMENT '数据月份 (存储为每月第一天)',
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Group',
  `group_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Group manager',
  `is_violative` tinyint(1) DEFAULT '0' COMMENT '是否违规主播',
  `is_rookie` tinyint(1) DEFAULT '0' COMMENT '是否新人主播',
  `diamonds` bigint(20) DEFAULT '0' COMMENT '钻石数',
  `valid_days` int(11) DEFAULT '0' COMMENT '有效天数(d)',
  `live_duration_hours` decimal(10,4) DEFAULT '0.0000' COMMENT '直播时长(h)',
  `bonus_estimated` decimal(10,4) DEFAULT '0.0000' COMMENT '估计的奖金',
  `bonus_rookie_m1_retention` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新人里程碑1保留任务奖金',
  `bonus_rookie_m2` decimal(10,4) DEFAULT '0.0000' COMMENT '新人里程碑2任务奖金',
  `bonus_rookie_half_milestone` decimal(10,4) DEFAULT '0.0000' COMMENT '新人半里程碑任务奖金',
  `bonus_rookie_m1` decimal(10,4) DEFAULT '0.0000' COMMENT '新人里程碑1任务奖金',
  `bonus_activeness` decimal(10,4) DEFAULT '0.0000' COMMENT '活跃任务奖金',
  `bonus_revenue_scale` decimal(10,4) DEFAULT '0.0000' COMMENT '收入规模任务奖金',
  `bonus_new_creator_network` decimal(10,4) DEFAULT '0.0000' COMMENT '新创作者网络任务奖金',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_network_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创作者网络管理员',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_creator_month` (`creator_id`,`data_month`),
  KEY `idx_creator_month_desc` (`creator_id`,`data_month`),
  KEY `idx_month_diamonds` (`data_month`,`diamonds`),
  CONSTRAINT `fk_performance_creator` FOREIGN KEY (`creator_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播月度业绩数据表';

-- Create syntax for TABLE 'raw_imports'
CREATE TABLE `raw_imports` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '源文件名',
  `imported_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
  `data_month` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator_nickname` text COLLATE utf8mb4_unicode_ci,
  `handle` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `creator_network_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `group_manager` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_violative_creators` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `the_creator_was_rookie` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `diamonds` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `valid_days` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `live_duration_h` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `estimated_bonus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_m1_retention` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_m2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_half_milestone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_rookie_m1` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_activeness_task` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_revenue_scale_task` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `est_bonus_new_creator_network_task` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原始Excel数据导入记录表';

-- Create syntax for TABLE 'team_events'
CREATE TABLE `team_events` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `actor_creator_id` bigint(20) unsigned NOT NULL COMMENT '事件关联人ID (如: 团队领导)',
  `target_creator_id` bigint(20) unsigned DEFAULT NULL COMMENT '事件目标者ID (如: 新人, 晋升者)',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型 (LEVEL_UP, NEW_RECRUIT, PERFORMANCE_MILESTONE, APPROACHING_MILESTONE)',
  `event_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '事件发生的确切时间',
  `details` json DEFAULT NULL COMMENT '事件详情 (如: {"new_level": "Gold", "current_value": 22, "target_value": 24})',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_actor_timestamp` (`actor_creator_id`,`event_timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=331 DEFAULT CHARSET=utf8mb4 COMMENT='团队动态与成就事件日志表';