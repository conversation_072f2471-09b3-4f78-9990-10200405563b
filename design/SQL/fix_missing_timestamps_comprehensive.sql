-- =================================================================
-- 全面修复所有表缺失的created_at和updated_at字段
-- 执行时间：需要在部署时运行此脚本
-- =================================================================

-- 1. 修复 commission_level_rules 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_level_rules'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_level_rules ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_level_rules时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 修复 commission_multilevel_rules 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_multilevel_rules'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_multilevel_rules ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_multilevel_rules时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 修复 commission_recruitment_bonus_rules 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_recruitment_bonus_rules'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_recruitment_bonus_rules ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_recruitment_bonus_rules时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 修复 creator_relationships 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'creator_relationships'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE creator_relationships ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''creator_relationships时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 修复 monthly_performance 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'monthly_performance'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE monthly_performance ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''monthly_performance时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 修复 commission_monthly_summary 表 (添加updated_at)
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_monthly_summary'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_monthly_summary ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_monthly_summary时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 修复 commission_dynamic_thresholds 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_dynamic_thresholds'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_dynamic_thresholds ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_dynamic_thresholds时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 修复 commission_recruitment_stats 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_recruitment_stats'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_recruitment_stats ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_recruitment_stats时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 9. 修复 commission_distributor_qualifications 表
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_distributor_qualifications'
    AND COLUMN_NAME = 'created_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_distributor_qualifications ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT ''创建时间'', ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_distributor_qualifications时间戳字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 10. 为 commission_payouts 表添加 updated_at 字段（已有created_at）
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'commission_payouts'
    AND COLUMN_NAME = 'updated_at'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE commission_payouts ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''更新时间''',
    'SELECT ''commission_payouts的updated_at字段已存在'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示所有表的修复结果
SELECT 'Tables structure after timestamp fields addition:' as status;

SELECT 
    TABLE_NAME as '表名',
    GROUP_CONCAT(
        CASE 
            WHEN COLUMN_NAME IN ('created_at', 'updated_at', 'calculated_at', 'imported_at', 'create_time', 'update_time') 
            THEN CONCAT(COLUMN_NAME, '(', DATA_TYPE, ')') 
        END 
        ORDER BY ORDINAL_POSITION
    ) as '时间戳字段'
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN (
        'creators', 'commission_settings', 'commission_monthly_settings',
        'commission_level_rules', 'commission_multilevel_rules', 'commission_recruitment_bonus_rules',
        'creator_relationships', 'monthly_performance', 'commission_monthly_summary',
        'commission_payouts', 'commission_payout_breakdowns', 'raw_imports',
        'commission_config_snapshots', 'commission_dynamic_thresholds', 
        'commission_recruitment_stats', 'commission_distributor_qualifications',
        'commission_calculation_logs'
    )
GROUP BY TABLE_NAME
ORDER BY TABLE_NAME; 