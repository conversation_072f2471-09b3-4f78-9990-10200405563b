-- 分销员等级规则表结构调整
-- 将等级评定标准从"直属下级人数"改为"合格拉新人数"，并添加收益深度控制

-- 1. 修改字段名称：min_direct_downlines -> min_qualified_recruits
ALTER TABLE `commission_level_rules`
    CHANGE COLUMN `min_direct_downlines` `min_qualified_recruits` INT(11) NOT NULL 
        COMMENT '该等级要求的最低月度合格拉新人数';

-- 2. 增加新字段以定义收益深度
ALTER TABLE `commission_level_rules`
    ADD COLUMN `payout_depth` TINYINT(4) NOT NULL DEFAULT 1 
        COMMENT '佣金收益深度 (1=下1级, 2=下2级, 3=下3级)' AFTER `commission_rate`;

-- 3. 更新现有数据，设置合理的等级门槛和收益深度
-- 注意：这里的数据需要根据实际业务需求调整

-- 金牌：需要10个合格拉新，享受3级收益
UPDATE `commission_level_rules` 
SET `min_qualified_recruits` = 10, `payout_depth` = 3 
WHERE `level_name` = '金牌';

-- 银牌：需要5个合格拉新，享受2级收益  
UPDATE `commission_level_rules` 
SET `min_qualified_recruits` = 5, `payout_depth` = 2 
WHERE `level_name` = '银牌';

-- 铜牌：需要2个合格拉新，享受1级收益
UPDATE `commission_level_rules` 
SET `min_qualified_recruits` = 2, `payout_depth` = 1 
WHERE `level_name` = '铜牌';

-- 4. 验证数据更新结果
SELECT 
    id,
    level_name,
    min_qualified_recruits,
    commission_rate,
    payout_depth,
    display_order,
    is_active
FROM `commission_level_rules` 
WHERE is_active = 1
ORDER BY display_order ASC;
