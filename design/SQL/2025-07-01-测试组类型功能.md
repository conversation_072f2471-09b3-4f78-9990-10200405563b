# 组类型功能测试文档

## 测试目的
验证添加组类型字段后，相关API功能是否正常工作。

## 测试前准备
1. 执行数据库升级脚本：`2025-07-01-添加组类型字段.md`
2. 重启应用服务

## API测试用例

### 1. 查询组关系列表 - 验证返回groupType字段
```bash
GET /h/group/relations
Authorization: Bearer {token}
```

**期望结果：**
- 返回的数据中包含 `groupType` 字段
- 现有数据的 `groupType` 默认为 0（分销组）

### 2. 修改组类型 - 设置为非分销组
```bash
POST /h/group/update-type
Content-Type: application/json
Authorization: Bearer {token}

{
    "groupName": "TestGroup",
    "groupType": 1
}
```

**期望结果：**
- 返回成功状态
- 数据库中对应组的 `group_type` 字段更新为 1

### 3. 修改组类型 - 设置为分销组
```bash
POST /h/group/update-type
Content-Type: application/json
Authorization: Bearer {token}

{
    "groupName": "TestGroup", 
    "groupType": 0
}
```

**期望结果：**
- 返回成功状态
- 数据库中对应组的 `group_type` 字段更新为 0

### 4. 参数验证测试
```bash
POST /h/group/update-type
Content-Type: application/json
Authorization: Bearer {token}

{
    "groupName": "",
    "groupType": 2
}
```

**期望结果：**
- 返回参数验证错误
- groupName 不能为空
- groupType 必须为 0 或 1

## 数据库验证

### 查询组类型数据
```sql
SELECT group_name, creator_id, group_type, created_at, updated_at 
FROM group_creator_relation 
ORDER BY updated_at DESC;
```

### 验证字段约束
```sql
-- 验证字段存在且有正确的默认值
DESCRIBE group_creator_relation;

-- 验证数据类型和约束
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'streamer_distribution_system' 
  AND TABLE_NAME = 'group_creator_relation' 
  AND COLUMN_NAME = 'group_type';
```

## 功能验证清单

- [ ] 数据库字段添加成功
- [ ] 实体类包含 groupType 属性
- [ ] Mapper XML 映射正确
- [ ] Service 层方法实现正确
- [ ] Controller API 接口可访问
- [ ] 查询接口返回 groupType 字段
- [ ] 修改组类型接口功能正常
- [ ] 参数验证正常工作
- [ ] 现有数据默认值正确

## 注意事项

1. 测试前确保有足够的权限（h:historical）
2. 测试用的组名应该是系统中实际存在的
3. 如果组关系不存在，修改组类型会创建新的关系记录
4. 所有操作都会记录操作日志
