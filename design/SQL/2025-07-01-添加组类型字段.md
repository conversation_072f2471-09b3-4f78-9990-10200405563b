# 数据库升级脚本 - 添加组类型字段

## 变更说明
- 为 `group_creator_relation` 表添加 `group_type` 字段
- 字段类型：TINYINT(1)，默认值为0（分销组）
- 0: 默认分销组，1: 非分销组

## 执行时间
2025-07-01

## 影响表
- group_creator_relation

## SQL脚本

```sql
-- 为group_creator_relation表添加group_type字段
ALTER TABLE `group_creator_relation` 
ADD COLUMN `group_type` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '组类型：0-分销组，1-非分销组' 
AFTER `creator_id`;

-- 验证字段添加成功
DESCRIBE `group_creator_relation`;

-- 查看表结构确认
SHOW CREATE TABLE `group_creator_relation`;
```

## 回滚脚本

```sql
-- 如需回滚，删除group_type字段
ALTER TABLE `group_creator_relation` DROP COLUMN `group_type`;
```

## 验证脚本

```sql
-- 验证字段是否正确添加
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'streamer_distribution_system' 
  AND TABLE_NAME = 'group_creator_relation' 
  AND COLUMN_NAME = 'group_type';

-- 测试插入数据
INSERT INTO `group_creator_relation` 
(`group_name`, `creator_id`, `group_type`, `create_by`, `update_by`) 
VALUES 
('TestGroup', 999999, 1, 'admin', 'admin');

-- 查询测试数据
SELECT * FROM `group_creator_relation` WHERE `group_name` = 'TestGroup';

-- 清理测试数据
DELETE FROM `group_creator_relation` WHERE `group_name` = 'TestGroup';
```

## 注意事项
1. 执行前请备份数据库
2. 建议在测试环境先执行验证
3. 该字段添加后，现有数据的group_type将默认为0（分销组）
4. 应用代码需要同步更新以支持新字段
