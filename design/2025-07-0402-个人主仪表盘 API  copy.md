<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<requirements>

# 需求
二、 任务背景与目标 (Task Background & Objective)
我需要你为一个“分销员业绩系统”开发一个核心的后端API接口。这个接口的功能是为前端的“个人主仪表盘”提供所有需要展示的数据。数据是基于用户选择的某个已经结束的历史月份进行聚合和分析，因此接口不涉及实时计算。

三、 API接口定义 (API Endpoint Definition)
HTTP方法: GET

URL: /api/dashboard/{creatorId}/{month}

路径参数:

{creatorId}: Long - 需要查询的分销员的用户ID。

{month}: String - 需要查询的月份，格式为 YYYY-MM (例如 2024-12)。

功能描述: 获取指定分销员 (creatorId) 在指定月份 (month) 的完整仪表盘聚合数据。

四、 核心业务逻辑与数据来源 (Core Business Logic & Data Sources)
你需要整合来自多个数据表的信息，计算出以下JSON结构中定义的每一个字段。

1. 顶层数据
user.name: 用户的昵称。

来源: 从 creators 表中，根据 :creatorId 查询 nickname 字段。

level: 用户在所选月份的最终等级。

来源: 从 commission_distributor_qualifications 表中，根据 :creatorId 和 :month 查询 achieved_level 字段。

selectedMonth: 返回用户请求的月份 YYYY-MM。

totalIncome: 截至所选月份末的年度累计总收入。

来源: 对 commission_payouts 表进行计算。

SQL逻辑: SELECT SUM(final_payout_usd) FROM commission_payouts WHERE creator_id = :creatorId AND YEAR(data_month) = YEAR(:month) AND data_month <= :month;

monthlyIncome: 所选月份的当月总收入。

来源: 从 commission_payouts 表中查询。

SQL逻辑: SELECT final_payout_usd FROM commission_payouts WHERE creator_id = :creatorId AND data_month = :month;

monthlyIncomeGrowth: 月度收入环比增长率。

来源: 对 commission_payouts 表进行计算。

逻辑:

获取 monthlyIncome (本月收入)。

查询上一个月的收入 (SELECT ... WHERE data_month = :month - 1 month)。

计算 (本月收入 / 上月收入) - 1。注意处理上月收入为0的边界情况（此时可返回null或0）。

2. levelMaintenance (保级状态)
progressPercentage: 保级任务完成度百分比。

来源: 这是基于“防守”算法的计算结果。

逻辑:

先计算出用户在当期考核的“实际月均环比增长率”（actual_growth_rate），这个算法我们在之前的需求中已经定义（需要查询过去4个月的业绩来计算）。

从 commission_settings 表获取保级目标（retention_target_avg_growth_rate，值为0.15）。

最终结果为 actual_growth_rate / target_growth_rate。例如，实际达成10.2%，目标15%，则返回 0.102 / 0.15 = 0.68。

targetGrowthRate: 保级任务的目标增长率。

来源: 从 commission_settings 表获取 retention_target_avg_growth_rate 的值。

3. performanceAnalytics (绩效分析)
components.personalIncomeUSD 和 components.teamIncomeUSD:

来源: commission_payout_breakdowns 表。

逻辑:

需要业务方提供一份source_type的分类清单，定义哪些属于“个人收入”，哪些属于“团队收入”。

基于此清单，对 commission_payout_breakdowns 表进行GROUP BY source_type 和 SUM(calculated_amount_usd) 的计算。

SQL伪代码: SELECT SUM(calculated_amount_usd) FROM commission_payout_breakdowns WHERE creator_id = :creatorId AND data_month <= :month AND source_type IN ([个人收入类型列表]); （注意：此处的统计周期应与totalIncome保持一致，例如年度）。

kpis.personalIncomeRatio: 个人收入占比。

逻辑: personalIncomeUSD / totalIncome。

kpis.totalTeamMembers: 团队总人数。

来源: creator_relationships 表。

SQL逻辑: SELECT COUNT(descendant_id) FROM creator_relationships WHERE ancestor_id = :creatorId AND depth > 0;

五、 数据库表结构参考 (Database Schema Reference)
SQL

-- 用户核心信息与层级关系表
CREATE TABLE `creators` ( ... );
CREATE TABLE `creator_relationships` ( ... );

-- 月度业绩与汇总表
CREATE TABLE `monthly_performance` ( ... );
CREATE TABLE `commission_payouts` ( ... );
CREATE TABLE `commission_payout_breakdowns` ( ... );
CREATE TABLE `commission_distributor_qualifications` ( ... );
CREATE TABLE `commission_recruitment_stats` ( ... );

-- 规则与配置表
CREATE TABLE `commission_level_rules` ( ... );
CREATE TABLE `commission_settings` ( ... );
(注：此处省略了详细的字段，实际使用时应将完整的CREATE TABLE语句提供给AI)

六、 性能与实现建议 (Performance & Implementation Recommendations)
此API涉及多次跨表和聚合查询，计算量较大。为了保证线上性能，强烈建议在“一键计算”的最终步骤，将此API所需的所有数据预先计算好，并存入一张月度数据快照表（例如 dashboard_monthly_summary）。API应优先从此快照表读取数据。

在Service层需要编写复杂的业务逻辑来整合所有数据源。

七、 响应格式与示例 (Response Format & Example)
严格要求: 所有成功的API响应，都必须封装在标准的“若依 (ruoyi)”风格的JSON结构中，code为200，msg为“查询成功”，实际业务数据包含在data字段内。

JSON

{
    "code": 200,
    "msg": "查询成功",
    "data": {
      "user": {
        "name": "Zhang Xiaoming"
      },
      "level": "金牌 Gold",
      "selectedMonth": "2024-12",
      "totalIncome": 456800.00,
      "monthlyIncome": 38900.00,
      "monthlyIncomeGrowth": 0.152,
      "levelMaintenance": {
        "progressPercentage": 0.68,
        "targetGrowthRate": 0.15
      },
      "performanceAnalytics": {
        "totalPerformanceUSD": 456800.00,
        "components": {
          "personalIncomeUSD": 89600.00,
          "teamIncomeUSD": 367200.00
        },
        "kpis": {
          "personalIncomeRatio": 0.196,
          "totalTeamMembers": 21
        }
      }
    }
}
八、 任务要求总结 (Summary of Task Requirements)
生成代码: 请为这个API生成完整的后端代码，包括Controller, Service, ServiceImpl, Mapper接口和对应的XML/Mapper实现。

代码质量: 代码风格需清晰、简洁，并添加必要的注释来解释复杂的业务逻辑。

错误处理: Service层需要处理各种可能出现的异常，如查询结果为空（例如新用户没有任何历史数据），并向上层返回统一的错误信息。

严格遵循格式: Controller的最终返回结果，必须严格遵守第七部分中定义的“若依”JSON封装格式。
</requirements>



<data_structure>
@database.sql
</data_structure>


- 先读需求内容requirements， 了解后. 读数据库结构 data_structure 
- 逐步思考, 本次的修改内容， 及修改方案， 列出修改计划
- 实现修改现有代码逻辑
- 所有的代码实现后， 为其它大模型生成一个review这次修改内容的提示词，生成到本地的/design/test/ 目录中。 提示词要求： 提供本次修改的文件及对应的行数范围， 修改的内容，需求， 及要求review 的内容。 

 

