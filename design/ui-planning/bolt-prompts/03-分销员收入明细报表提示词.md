# 分销员收入明细报表界面 - Bolt.new 提示词

## 核心提示词

请帮我创建一个公会主播奖励分成系统的**分销员收入明细报表界面**。这是一个数据密集型的报表页面，用于展示所有分销员的详细收入计算结果，支持多维度查询和分析。

### 界面要求

创建一个功能强大的数据报表界面，包含以下核心功能：

1. **筛选和搜索工具栏**
   - 分销员姓名/ID搜索框（支持模糊搜索）
   - 等级筛选下拉框（全部/金牌/银牌/铜牌）
   - 收入范围筛选（滑块组件，$0-$10000）
   - 拉新人数筛选（0-50人的范围选择）
   - 日期范围选择器（月份选择）
   - 清空筛选和应用筛选按钮

2. **数据表格主体**
   - 分页数据表格（每页20/50/100条记录可选）
   - 排序功能（点击列标题排序）
   - 列显示/隐藏配置
   - 行数据点击展开详情功能
   - 批量选择和操作功能

3. **表格列定义**
   - 分销员ID（固定列）
   - 姓名/昵称（固定列） 
   - 当月等级（带颜色标识）
   - 个人钻石收入（数值，可排序）
   - 团队总钻石收入（数值，可排序）
   - 拉新人数（数值，可排序）
   - 最终实收总额USD（突出显示，可排序）
   - 操作列（查看详情按钮）

4. **收入明细弹窗**
   - 分销员基本信息展示
   - 收入计算明细分解（等级分成、下三级提成、拉新奖励）
   - 钻石到USD的换算过程展示
   - 下级网络树状图展示
   - 历史收入趋势小图表

5. **数据导出功能**
   - 导出当前筛选结果（Excel/CSV/PDF）
   - 导出全部数据选项
   - 自定义导出字段选择
   - 导出进度显示

### 技术要求
- 使用React + TypeScript + Tailwind CSS
- 集成react-table或类似数据表格库
- 虚拟滚动支持大数据量渲染
- 响应式设计，支持桌面和平板
- 数据懒加载和分页加载
- 表格状态持久化（筛选条件、排序等）

### 设计风格
- 采用经典的后台管理系统表格布局
- 使用中性色调，突出数据可读性
- 重要数据使用颜色区分（等级标识、金额显示）
- 合理的行高和列宽，避免数据拥挤

<examples>
<example>
输入：筛选和搜索工具栏
输出：
```tsx
<div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
    <div className="lg:col-span-2">
      <label className="block text-sm font-medium text-gray-700 mb-2">搜索分销员</label>
      <div className="relative">
        <input
          type="text"
          placeholder="输入姓名或ID搜索..."
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
      </div>
    </div>
    
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">等级筛选</label>
      <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
        <option value="">全部等级</option>
        <option value="gold">金牌分销员</option>
        <option value="silver">银牌分销员</option>
        <option value="bronze">铜牌分销员</option>
      </select>
    </div>
    
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">月份选择</label>
      <input
        type="month"
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">收入范围</label>
      <div className="flex items-center space-x-2">
        <input
          type="number"
          placeholder="最小"
          className="w-full px-2 py-2 border border-gray-300 rounded-md text-sm"
        />
        <span className="text-gray-500">-</span>
        <input
          type="number"
          placeholder="最大"
          className="w-full px-2 py-2 border border-gray-300 rounded-md text-sm"
        />
      </div>
    </div>
    
    <div className="flex items-end space-x-2">
      <button className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
        应用筛选
      </button>
      <button className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
        清空
      </button>
    </div>
  </div>
</div>
```
</example>

<example>
输入：数据表格主体
输出：
```tsx
<div className="bg-white rounded-lg shadow-sm border">
  <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
    <div className="flex items-center space-x-4">
      <h3 className="text-lg font-medium text-gray-900">分销员收入明细</h3>
      <span className="text-sm text-gray-500">共 247 条记录</span>
    </div>
    <div className="flex items-center space-x-2">
      <button className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600">
        导出Excel
      </button>
      <button className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
        导出PDF
      </button>
    </div>
  </div>
  
  <div className="overflow-x-auto">
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <input type="checkbox" className="rounded border-gray-300" />
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
            分销员ID
            <ArrowUpDownIcon className="w-4 h-4 inline ml-1" />
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            姓名/昵称
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            当月等级
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
            个人钻石收入
            <ArrowUpDownIcon className="w-4 h-4 inline ml-1" />
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
            团队总钻石收入
            <ArrowUpDownIcon className="w-4 h-4 inline ml-1" />
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
            拉新人数
            <ArrowUpDownIcon className="w-4 h-4 inline ml-1" />
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
            最终实收总额(USD)
            <ArrowUpDownIcon className="w-4 h-4 inline ml-1" />
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            操作
          </th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        <tr className="hover:bg-gray-50 cursor-pointer">
          <td className="px-6 py-4 whitespace-nowrap">
            <input type="checkbox" className="rounded border-gray-300" />
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
            DS001
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="flex items-center">
              <div className="flex-shrink-0 h-8 w-8">
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">张</span>
                </div>
              </div>
              <div className="ml-3">
                <div className="text-sm font-medium text-gray-900">张小明</div>
                <div className="text-sm text-gray-500">@zhangxm</div>
              </div>
            </div>
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
              金牌分销员
            </span>
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            12,350 钻
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            45,720 钻
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            8人
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">
            $234.56
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <button className="text-blue-600 hover:text-blue-900">查看详情</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <div className="px-4 py-3 border-t border-gray-200 flex items-center justify-between">
    <div className="flex items-center">
      <span className="text-sm text-gray-700">显示</span>
      <select className="mx-2 px-2 py-1 border border-gray-300 rounded text-sm">
        <option>20</option>
        <option>50</option>
        <option>100</option>
      </select>
      <span className="text-sm text-gray-700">条记录</span>
    </div>
    <div className="flex items-center space-x-2">
      <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">上一页</button>
      <span className="px-3 py-1 bg-blue-500 text-white rounded text-sm">1</span>
      <span className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 cursor-pointer">2</span>
      <span className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 cursor-pointer">3</span>
      <button className="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">下一页</button>
    </div>
  </div>
</div>
```
</example>

<example>
输入：收入明细弹窗
输出：
```tsx
<div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
  <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
    <div className="flex items-center justify-between border-b pb-3 mb-4">
      <h3 className="text-lg font-semibold text-gray-900">分销员收入明细 - 张小明 (DS001)</h3>
      <button className="text-gray-400 hover:text-gray-600">
        <XMarkIcon className="w-6 h-6" />
      </button>
    </div>
    
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-4">收入计算明细</h4>
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">等级分成收入</span>
              <span className="text-lg font-bold text-blue-600">$95.40</span>
            </div>
            <div className="text-sm text-gray-600">
              <p>个人钻石: 12,350 钻</p>
              <p>团队钻石: 33,370 钻</p>
              <p>分成比例: 25% (金牌)</p>
              <p>计算: (12,350 + 33,370) × 25% × 0.0042 = $95.40</p>
            </div>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">下三级提成收入</span>
              <span className="text-lg font-bold text-green-600">$119.16</span>
            </div>
            <div className="text-sm text-gray-600">
              <p>L1级收入: 28,000 钻 × 8% = $9.41</p>
              <p>L2级收入: 15,500 钻 × 5% = $3.26</p>
              <p>L3级收入: 8,200 钻 × 3% = $1.03</p>
              <p>合计: $119.16</p>
            </div>
          </div>
          
          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">拉新奖励</span>
              <span className="text-lg font-bold text-orange-600">$20.00</span>
            </div>
            <div className="text-sm text-gray-600">
              <p>本月拉新人数: 8人</p>
              <p>奖励阶梯: 5-10人 → $20.00</p>
            </div>
          </div>
          
          <div className="bg-gray-100 p-4 rounded-lg border-2 border-gray-300">
            <div className="flex justify-between items-center">
              <span className="text-lg font-bold text-gray-800">最终收入合计</span>
              <span className="text-2xl font-bold text-gray-900">$234.56</span>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-md font-medium text-gray-800 mb-4">下级网络结构</h4>
        <div className="bg-gray-50 p-4 rounded-lg h-80 overflow-y-auto">
          <div className="space-y-2">
            <div className="flex items-center text-sm">
              <div className="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
              <span className="font-medium">L1直属下级 (12人)</span>
            </div>
            <div className="ml-6 space-y-1">
              <div className="flex justify-between text-xs text-gray-600">
                <span>• 李四 (DS025) - 2,500钻</span>
                <span>银牌</span>
              </div>
              <div className="flex justify-between text-xs text-gray-600">
                <span>• 王五 (DS031) - 3,200钻</span>
                <span>铜牌</span>
              </div>
              <div className="text-xs text-gray-500">... 还有10个下级</div>
            </div>
            
            <div className="flex items-center text-sm mt-4">
              <div className="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
              <span className="font-medium">L2间接下级 (28人)</span>
            </div>
            
            <div className="flex items-center text-sm mt-4">
              <div className="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
              <span className="font-medium">L3三级下级 (15人)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```
</example>
</examples>

请确保表格具有良好的性能和用户体验，支持大数据量的流畅操作，并提供直观的数据展示和交互功能。 