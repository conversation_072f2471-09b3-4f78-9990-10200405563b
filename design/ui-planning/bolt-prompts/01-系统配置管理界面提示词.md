# 系统配置管理界面 - Bolt.new 提示词

## 核心提示词

请帮我创建一个公会主播奖励分成系统的**系统配置管理界面**。这是一个管理员专用的配置页面，用于设置所有系统运行的核心参数。

### 界面要求

创建一个现代化的管理后台配置界面，包含以下功能模块：

1. **基础参数配置区块**
   - 公会月总收入预算输入（USD格式，支持小数点后2位）
   - 公会支出上限比例设置（百分比滑块，1%-50%）
   - 基础钻石门槛设置（整数输入，最小值1000）
   - 钻石门槛月度上浮比例（百分比输入，0.1%-5%）

2. **汇率管理区块**
   - 钻石兑美元汇率历史表格
   - 添加新汇率记录（月份选择器 + 汇率输入）
   - 汇率记录的编辑和删除功能
   - 当前生效汇率高亮显示

3. **分销员等级规则配置**
   - 金牌/银牌/铜牌等级的配置卡片
   - 每个等级包含：直属下级人数要求、分成比例设置

4. **多级提成比例设置**
   - L1/L2/L3级别的提成比例输入
   - 实时预览提成计算示例
   - 比例总和不超过100%的验证

5. **拉新奖励阶梯配置**
   - 动态添加/删除奖励阶梯
   - 每个阶梯：人数范围 + USD奖励金额
   - 阶梯重叠检查和排序

### 技术要求
- 使用React + TypeScript + Tailwind CSS
- 表单验证和错误提示
- 数据变更的保存/取消/重置功能
- 响应式设计，适配桌面端
- 配置变更日志记录

### 设计风格
- 采用卡片式布局，清晰分组
- 每个配置块都有明确的标题和说明文字
- 重要操作需要二次确认弹窗

<examples>
<example>
输入：基础参数配置区块
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <h3 className="text-lg font-semibold text-gray-800 mb-4">基础参数配置</h3>
  <div className="grid grid-cols-2 gap-4">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        公会月总收入预算 (USD)
      </label>
      <input 
        type="number" 
        step="0.01"
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        placeholder="请输入月预算金额"
      />
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        支出上限比例 ({percentage}%)
      </label>
      <input 
        type="range" 
        min="1" 
        max="50" 
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
      />
    </div>
  </div>
</div>
```
</example>

<example>
输入：汇率管理区块
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <div className="flex justify-between items-center mb-4">
    <h3 className="text-lg font-semibold text-gray-800">钻石兑美元汇率管理</h3>
    <button className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
      + 添加汇率
    </button>
  </div>
  <div className="overflow-x-auto">
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效月份</th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">汇率 (USD)</th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        <tr className="bg-blue-50">
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01</td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0.0050</td>
          <td className="px-6 py-4 whitespace-nowrap">
            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
              当前生效
            </span>
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
            <button className="text-indigo-600 hover:text-indigo-900 mr-4">编辑</button>
            <button className="text-red-600 hover:text-red-900">删除</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
```
</example>

<example>
输入：等级规则配置
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <h3 className="text-lg font-semibold text-gray-800 mb-4">分销员等级规则配置</h3>
  <div className="grid grid-cols-3 gap-4">
    <div className="border-2 border-yellow-200 rounded-lg p-4 bg-yellow-50">
      <div className="flex items-center mb-3">
        <div className="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
        <h4 className="font-semibold text-gray-800">金牌分销员</h4>
      </div>
      <div className="space-y-3">
        <div>
          <label className="block text-sm text-gray-600">直属下级人数要求</label>
          <input type="number" className="w-full px-2 py-1 border rounded text-sm" value="10" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">分成比例 (%)</label>
          <input type="number" step="0.1" className="w-full px-2 py-1 border rounded text-sm" value="25.0" />
        </div>
      </div>
    </div>
    
    <div className="border-2 border-gray-200 rounded-lg p-4 bg-gray-50">
      <div className="flex items-center mb-3">
        <div className="w-4 h-4 bg-gray-400 rounded-full mr-2"></div>
        <h4 className="font-semibold text-gray-800">银牌分销员</h4>
      </div>
      <div className="space-y-3">
        <div>
          <label className="block text-sm text-gray-600">直属下级人数要求</label>
          <input type="number" className="w-full px-2 py-1 border rounded text-sm" value="5" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">分成比例 (%)</label>
          <input type="number" step="0.1" className="w-full px-2 py-1 border rounded text-sm" value="20.0" />
        </div>
      </div>
    </div>
    
    <div className="border-2 border-orange-200 rounded-lg p-4 bg-orange-50">
      <div className="flex items-center mb-3">
        <div className="w-4 h-4 bg-orange-600 rounded-full mr-2"></div>
        <h4 className="font-semibold text-gray-800">铜牌分销员</h4>
      </div>
      <div className="space-y-3">
        <div>
          <label className="block text-sm text-gray-600">直属下级人数要求</label>
          <input type="number" className="w-full px-2 py-1 border rounded text-sm" value="2" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">分成比例 (%)</label>
          <input type="number" step="0.1" className="w-full px-2 py-1 border rounded text-sm" value="15.0" />
        </div>
      </div>
    </div>
  </div>
</div>
```
</example>
</examples>

请确保界面具有良好的用户体验，包括表单验证、加载状态、成功/错误提示等交互细节。 