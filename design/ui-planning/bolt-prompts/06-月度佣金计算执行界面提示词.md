# 月度佣金计算执行界面 - Bolt.new 提示词

## 核心提示词

请帮我创建一个公会主播奖励分成系统的**月度佣金计算执行界面**。这是一个用于执行月度佣金计算任务的核心操作页面，包含月份选择、计算执行、结果展示功能。

### 界面要求

创建一个专业的佣金计算执行界面，包含以下核心功能：

1. **月份选择和前置检查区域**
   - 月份选择器（yyyy-MM格式）
   - 前置条件检查按钮和结果显示
   - 检查项目列表（配置完整性、数据可用性、权限验证等）
   - 检查状态指示器（通过/警告/失败）

2. **计算操作控制区域**
   - 预览计算按钮（不保存结果）
   - 执行计算按钮（正式计算并保存）
   - 重新计算按钮（force参数选项）
   - 取消计算按钮

3. **计算结果总览区域**
   - 月度计算总览信息展示
   - 关键财务指标（总支出、分销员数量、平均收入等）
 

### API接口对应关系
- 前置条件检查：`GET /business/commission/calculation/check-preconditions/{dataMonth}`
- 预览计算：`POST /business/commission/calculation/preview/{dataMonth}`
- 执行计算：`POST /business/commission/calculation/execute/{dataMonth}`
- 获取状态：`GET /business/commission/calculation/status/{dataMonth}`
- 重新计算：`POST /business/commission/calculation/recalculate/{dataMonth}?force=true/false`
- 获取总览：`GET /business/commission/calculation/summary/{dataMonth}`

### 技术要求
- 使用React + TypeScript + Tailwind CSS
- WebSocket或轮询实现实时状态更新
- 表单验证和错误处理
- 响应式设计，适配桌面端
- 操作确认弹窗和加载状态

### 设计风格
- 采用卡片式分步骤布局
- 每个配置块都有明确的标题和说明文字
- 使用进度指示器和状态标识
- 重要操作需要二次确认弹窗

<examples>
<example>
输入：月份选择和前置检查区域
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <h3 className="text-lg font-semibold text-gray-900 mb-4">计算月份选择</h3>
  
  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        选择计算月份
      </label>
      <input
        type="month"
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        value="2025-06"
      />
      <p className="text-xs text-gray-500 mt-1">格式：yyyy-MM</p>
    </div>
    
    <div className="lg:col-span-2">
      <div className="flex items-center justify-between mb-3">
        <label className="block text-sm font-medium text-gray-700">
          前置条件检查
        </label>
        <button className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm">
          执行检查
        </button>
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="w-5 h-5 text-green-600" />
            <span className="text-sm text-gray-700">系统配置完整性</span>
          </div>
          <span className="text-xs text-green-600 font-medium">通过</span>
        </div>
        
        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="w-5 h-5 text-green-600" />
            <span className="text-sm text-gray-700">分销员数据可用性</span>
          </div>
          <span className="text-xs text-green-600 font-medium">通过</span>
        </div>
        
        <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />
            <span className="text-sm text-gray-700">汇率配置检查</span>
          </div>
          <span className="text-xs text-yellow-600 font-medium">警告</span>
        </div>
        
        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="w-5 h-5 text-green-600" />
            <span className="text-sm text-gray-700">操作权限验证</span>
          </div>
          <span className="text-xs text-green-600 font-medium">通过</span>
        </div>
      </div>
    </div>
  </div>
  
  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
    <div className="flex items-start space-x-2">
      <InformationCircleIcon className="w-5 h-5 text-blue-600 mt-0.5" />
      <div>
        <p className="text-sm text-blue-800 font-medium">检查结果</p>
        <p className="text-sm text-blue-700">
          前置条件检查基本通过，发现1个警告项。汇率配置可能需要更新，建议检查当月汇率设置。
        </p>
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：计算操作控制区域
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <h3 className="text-lg font-semibold text-gray-900 mb-4">计算操作</h3>
  
  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <button className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center space-x-2">
      <EyeIcon className="w-5 h-5" />
      <span>预览计算</span>
    </button>
    
    <button className="px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center space-x-2">
      <PlayIcon className="w-5 h-5" />
      <span>执行计算</span>
    </button>
    
    <button className="px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center space-x-2">
      <ArrowPathIcon className="w-5 h-5" />
      <span>重新计算</span>
    </button>
    
    <button className="px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center space-x-2">
      <StopIcon className="w-5 h-5" />
      <span>取消计算</span>
    </button>
  </div>
  
  <div className="border border-gray-200 rounded-lg p-4">
    <h4 className="text-sm font-medium text-gray-700 mb-3">操作选项</h4>
    <div className="space-y-3">
      <div className="flex items-center">
        <input
          id="force-recalculate"
          type="checkbox"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="force-recalculate" className="ml-2 block text-sm text-gray-700">
          强制重新计算（忽略已有结果）
        </label>
      </div>
      
      <div className="flex items-center">
        <input
          id="send-notification"
          type="checkbox"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          defaultChecked
        />
        <label htmlFor="send-notification" className="ml-2 block text-sm text-gray-700">
          计算完成后发送通知
        </label>
      </div>
      
      <div className="flex items-center">
        <input
          id="auto-export"
          type="checkbox"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="auto-export" className="ml-2 block text-sm text-gray-700">
          自动导出计算结果
        </label>
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：计算进度和状态监控区域
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <div className="flex items-center justify-between mb-4">
    <h3 className="text-lg font-semibold text-gray-900">计算进度</h3>
    <div className="flex items-center space-x-2">
      <div className="flex items-center space-x-1">
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        <span className="text-sm text-gray-600">计算中</span>
      </div>
      <span className="text-sm text-gray-500">任务ID: CALC_202506_001</span>
    </div>
  </div>
  
  <div className="mb-6">
    <div className="flex justify-between items-center mb-2">
      <span className="text-sm font-medium text-gray-700">整体进度</span>
      <span className="text-sm font-medium text-blue-600">75% 完成</span>
    </div>
    <div className="w-full bg-gray-200 rounded-full h-3">
      <div className="bg-blue-500 h-3 rounded-full transition-all duration-500" style={{width: '75%'}}></div>
    </div>
    <div className="flex justify-between text-xs text-gray-500 mt-1">
      <span>开始时间: 14:30:25</span>
      <span>预计剩余: 1分15秒</span>
    </div>
  </div>
  
  <div className="grid grid-cols-4 gap-4 mb-6">
    <div className="text-center">
      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
        <CheckIcon className="w-5 h-5 text-white" />
      </div>
      <div className="text-xs font-medium text-green-600">数据检查</div>
      <div className="text-xs text-gray-500">已完成</div>
    </div>
    
    <div className="text-center">
      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
        <CheckIcon className="w-5 h-5 text-white" />
      </div>
      <div className="text-xs font-medium text-green-600">分成计算</div>
      <div className="text-xs text-gray-500">已完成</div>
    </div>
    
    <div className="text-center">
      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
        <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
      </div>
      <div className="text-xs font-medium text-blue-600">汇率换算</div>
      <div className="text-xs text-gray-500">进行中</div>
    </div>
    
    <div className="text-center">
      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-2">
        <ClockIcon className="w-5 h-5 text-gray-500" />
      </div>
      <div className="text-xs font-medium text-gray-500">结果保存</div>
      <div className="text-xs text-gray-500">等待中</div>
    </div>
  </div>
  
  <div className="grid grid-cols-3 gap-4">
    <div className="text-center p-3 bg-blue-50 rounded-lg">
      <div className="text-lg font-bold text-blue-600">247</div>
      <div className="text-xs text-gray-600">分销员总数</div>
    </div>
    <div className="text-center p-3 bg-green-50 rounded-lg">
      <div className="text-lg font-bold text-green-600">185</div>
      <div className="text-xs text-gray-600">已计算完成</div>
    </div>
    <div className="text-center p-3 bg-orange-50 rounded-lg">
      <div className="text-lg font-bold text-orange-600">62</div>
      <div className="text-xs text-gray-600">等待处理</div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：计算结果总览区域
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <div className="flex items-center justify-between mb-4">
    <h3 className="text-lg font-semibold text-gray-900">计算结果总览</h3>
    <div className="flex items-center space-x-2">
      <button className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600">
        导出Excel
      </button>
      <button className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
        查看详情
      </button>
    </div>
  </div>
  
  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <div className="lg:col-span-2">
      <h4 className="text-md font-medium text-gray-800 mb-4">财务指标</h4>
      <div className="grid grid-cols-2 gap-4">
        <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-400">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">总支出金额</p>
              <p className="text-2xl font-bold text-green-600">$87,450.30</p>
            </div>
            <CurrencyDollarIcon className="w-8 h-8 text-green-600" />
          </div>
        </div>
        
        <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">参与分销员</p>
              <p className="text-2xl font-bold text-blue-600">247</p>
            </div>
            <UsersIcon className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        
        <div className="p-4 bg-purple-50 rounded-lg border-l-4 border-purple-400">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">平均收入</p>
              <p className="text-2xl font-bold text-purple-600">$354.25</p>
            </div>
            <ChartBarIcon className="w-8 h-8 text-purple-600" />
          </div>
        </div>
        
        <div className="p-4 bg-orange-50 rounded-lg border-l-4 border-orange-400">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">拉新总数</p>
              <p className="text-2xl font-bold text-orange-600">1,128</p>
            </div>
            <UserPlusIcon className="w-8 h-8 text-orange-600" />
          </div>
        </div>
      </div>
    </div>
    
    <div>
      <h4 className="text-md font-medium text-gray-800 mb-4">计算信息</h4>
      <div className="space-y-3">
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">计算月份</div>
          <div className="text-lg font-semibold text-gray-900">2025年6月</div>
        </div>
        
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">计算完成时间</div>
          <div className="text-sm font-medium text-gray-900">2025-06-18 14:35:42</div>
        </div>
        
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">计算耗时</div>
          <div className="text-sm font-medium text-gray-900">5分23秒</div>
        </div>
        
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">使用汇率</div>
          <div className="text-sm font-medium text-gray-900">1钻石 = $0.0042</div>
        </div>
        
        <div className="p-3 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="w-5 h-5 text-green-600" />
            <span className="text-sm font-medium text-green-800">计算成功完成</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：历史计算记录区域
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6">
  <div className="flex items-center justify-between mb-4">
    <h3 className="text-lg font-semibold text-gray-900">历史计算记录</h3>
    <button className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600">
      查看全部
    </button>
  </div>
  
  <div className="overflow-x-auto">
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            计算月份
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            执行时间
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            状态
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            总支出
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            分销员数
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            操作
          </th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        <tr className="bg-green-50">
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
            2025-06
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            2025-06-18 14:35:42
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
              成功
            </span>
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            $87,450.30
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            247
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
            <button className="text-blue-600 hover:text-blue-900">查看</button>
            <button className="text-orange-600 hover:text-orange-900">重算</button>
          </td>
        </tr>
        
        <tr>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
            2025-05
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            2025-05-18 15:20:15
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
              成功
            </span>
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            $92,125.80
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            238
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
            <button className="text-blue-600 hover:text-blue-900">查看</button>
            <button className="text-orange-600 hover:text-orange-900">重算</button>
          </td>
        </tr>
        
        <tr>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
            2025-04
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            2025-04-18 16:45:33
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
              失败
            </span>
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            --
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            --
          </td>
          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
            <button className="text-blue-600 hover:text-blue-900">查看</button>
            <button className="text-red-600 hover:text-red-900">重试</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
```
</example>
</examples>

请确保界面具有良好的交互体验，支持实时状态更新，并提供完善的错误处理和操作确认机制。重要操作需要二次确认，计算过程需要显示详细的进度信息。 