# 财务总览仪表板 - Bolt.new 提示词

## 核心提示词

请帮我创建一个公会主播奖励分成系统的**财务总览仪表板**。这是系统的核心展示页面，为公会管理者提供实时的财务状况概览和预算对比分析。

### 界面要求

创建一个现代化的财务仪表板，包含以下核心模块：

1. **关键指标卡片区域**
   - 公会月总收入（预算）- 大号显示，绿色主题
   - 实际总支出 - 大号显示，根据预算状态变色（绿色/橙色/红色）
   - 支出上限 - 中等显示，灰色主题  
   - 超出预算金额 - 仅在超预算时显示，红色警告

2. **预算对比可视化区域**
   - 环形进度图显示支出/收入比例
   - 颜色编码：绿色(健康) < 80%，橙色(警告) 80-100%，红色(超支) > 100%
   - 中心显示百分比数值和状态文字

3. **财务趋势图表区域**
   - 最近6个月的收入vs支出趋势线图
   - 双Y轴显示，左侧收入，右侧支出
   - 支持数据点悬停显示详细数值

4. **分销员分级统计区域**  
   - 饼图显示各等级分销员的收入占比
   - 金牌/银牌/铜牌的人数和收入统计表格
   - 平均收入和排名信息

5. **实时数据刷新控制**
   - 最后更新时间显示
   - 手动刷新按钮
   - 自动刷新开关（每30秒/1分钟/5分钟）

### 技术要求
- 使用React + TypeScript + Tailwind CSS
- 集成Chart.js或Recharts图表库
- 响应式设计，支持桌面和平板
- 数据加载状态和错误处理
- 支持数据导出（PDF报告）

### 设计风格
- 采用现代卡片式仪表板布局
- 使用财务级别的色彩系统（绿色=正常，橙色=警告，红色=危险）
- 大数字突出显示，小细节淡化处理
- 适当的空白和间距，避免信息过载

<examples>
<example>
输入：关键指标卡片区域
输出：
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">月预算收入</p>
        <p className="text-3xl font-bold text-gray-900">$120,000.00</p>
        <p className="text-sm text-green-600 mt-1">
          <span className="inline-flex items-center">
            <ArrowUpIcon className="w-4 h-4 mr-1" />
            本月目标
          </span>
        </p>
      </div>
      <div className="p-3 bg-green-100 rounded-full">
        <CurrencyDollarIcon className="w-8 h-8 text-green-600" />
      </div>
    </div>
  </div>

  <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">实际支出</p>
        <p className="text-3xl font-bold text-gray-900">$87,450.30</p>
        <p className="text-sm text-blue-600 mt-1">72.9% 预算使用</p>
      </div>
      <div className="p-3 bg-blue-100 rounded-full">
        <BanknotesIcon className="w-8 h-8 text-blue-600" />
      </div>
    </div>
  </div>

  <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-gray-500">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">支出上限</p>
        <p className="text-3xl font-bold text-gray-900">$36,000.00</p>
        <p className="text-sm text-gray-600 mt-1">30% 预算上限</p>
      </div>
      <div className="p-3 bg-gray-100 rounded-full">
        <ExclamationTriangleIcon className="w-8 h-8 text-gray-600" />
      </div>
    </div>
  </div>

  <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">超支金额</p>
        <p className="text-3xl font-bold text-red-600">$51,450.30</p>
        <p className="text-sm text-red-600 mt-1">
          <span className="inline-flex items-center">
            <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
            需要关注
          </span>
        </p>
      </div>
      <div className="p-3 bg-red-100 rounded-full">
        <ExclamationTriangleIcon className="w-8 h-8 text-red-600" />
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：预算对比可视化区域
输出：
```tsx
<div className="bg-white rounded-xl shadow-lg p-6 mb-8">
  <h3 className="text-lg font-semibold text-gray-800 mb-6">预算执行情况</h3>
  <div className="flex items-center justify-center">
    <div className="relative">
      <svg className="w-40 h-40" viewBox="0 0 160 160">
        <circle
          cx="80"
          cy="80"
          r="70"
          stroke="#e5e7eb"
          strokeWidth="12"
          fill="transparent"
        />
        <circle
          cx="80"
          cy="80"
          r="70"
          stroke="#ef4444"
          strokeWidth="12"
          fill="transparent"
          strokeDasharray={`${Math.PI * 2 * 70 * 0.729} ${Math.PI * 2 * 70}`}
          strokeDashoffset={`${Math.PI * 2 * 70 * 0.25}`}
          className="transition-all duration-1000 ease-in-out"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center flex-col">
        <span className="text-3xl font-bold text-red-600">143%</span>
        <span className="text-sm text-gray-600">预算使用率</span>
        <span className="text-xs text-red-600 font-medium">超出预算</span>
      </div>
    </div>
    <div className="ml-8 space-y-4">
      <div className="flex items-center">
        <div className="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
        <span className="text-sm text-gray-600">预算内 (0-80%)</span>
      </div>
      <div className="flex items-center">
        <div className="w-4 h-4 bg-orange-500 rounded-full mr-3"></div>
        <span className="text-sm text-gray-600">警告 (80-100%)</span>
      </div>
      <div className="flex items-center">
        <div className="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
        <span className="text-sm text-gray-600">超支 (>100%)</span>
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：分销员分级统计区域
输出：
```tsx
<div className="bg-white rounded-xl shadow-lg p-6">
  <h3 className="text-lg font-semibold text-gray-800 mb-6">分销员等级统计</h3>
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <div>
      <h4 className="text-md font-medium text-gray-700 mb-4">收入占比分布</h4>
      {/* 这里放置饼图组件 */}
      <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
        <span className="text-gray-500">饼图区域</span>
      </div>
    </div>
    <div>
      <h4 className="text-md font-medium text-gray-700 mb-4">等级详情统计</h4>
      <div className="space-y-4">
        <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
            <span className="font-medium text-gray-800">金牌分销员</span>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">15人</p>
            <p className="text-lg font-semibold text-gray-900">$45,230.50</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border-l-4 border-gray-400">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
            <span className="font-medium text-gray-800">银牌分销员</span>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">28人</p>
            <p className="text-lg font-semibold text-gray-900">$32,145.80</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border-l-4 border-orange-400">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
            <span className="font-medium text-gray-800">铜牌分销员</span>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">47人</p>
            <p className="text-lg font-semibold text-gray-900">$10,074.00</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```
</example>
</examples>

请确保仪表板具有专业的财务系统外观，数据更新流畅，并提供清晰的视觉反馈来帮助管理者快速理解财务状况。 