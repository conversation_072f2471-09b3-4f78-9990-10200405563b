# 数据处理状态监控界面 - Bolt.new 提示词

## 核心提示词

请帮我创建一个公会主播奖励分成系统的**数据处理状态监控界面**。这是一个实时监控Excel数据导入、解析和计算处理过程的状态页面，为管理员提供详细的处理进度和错误信息。

### 界面要求

创建一个专业的状态监控界面，包含以下核心功能：

1. **处理进度总览区域**
   - 当前处理任务的整体进度条
   - 处理阶段指示器（上传→解析→验证→计算→完成）
   - 预计剩余时间显示
   - 任务开始时间和当前耗时

2. **详细状态展示区域**
   - 文件上传状态（文件大小、上传进度、上传速度）
   - 数据解析状态（总行数、已解析行数、解析速度）
   - 数据验证状态（验证项目、通过率、错误统计）
   - 收入计算状态（计算进度、处理的分销员数量）

3. **错误日志和警告区域**
   - 实时错误日志显示
   - 错误分类统计（格式错误、数据缺失、逻辑错误）
   - 警告信息展示
   - 日志导出功能

4. **数据质量报告区域**
   - 数据完整性检查结果
   - 重复数据统计
   - 异常值检测报告
   - 数据质量评分

5. **操作控制区域**
   - 暂停/继续处理按钮
   - 取消任务按钮
   - 重新处理按钮
   - 查看处理结果按钮

### 技术要求
- 使用React + TypeScript + Tailwind CSS
- WebSocket实现实时状态更新
- 支持大文件处理进度显示
- 响应式设计，适配桌面端
- 错误处理和重试机制
- 处理历史记录存储

### 设计风格
- 采用现代化的监控界面风格
- 使用进度条和状态指示器
- 绿色表示成功，橙色表示警告，红色表示错误
- 清晰的层次结构和信息分组

<examples>
<example>
输入：处理进度总览区域
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6 mb-6">
  <div className="flex items-center justify-between mb-4">
    <h3 className="text-lg font-semibold text-gray-900">数据处理总览</h3>
    <div className="flex items-center space-x-4">
      <span className="text-sm text-gray-500">任务ID: TASK_20240120_001</span>
      <span className="text-sm text-gray-500">开始时间: 14:30:25</span>
      <span className="text-sm text-gray-600 font-medium">耗时: 00:05:23</span>
    </div>
  </div>
  
  <div className="mb-6">
    <div className="flex justify-between items-center mb-2">
      <span className="text-sm font-medium text-gray-700">整体进度</span>
      <span className="text-sm font-medium text-blue-600">68% 完成</span>
    </div>
    <div className="w-full bg-gray-200 rounded-full h-3">
      <div className="bg-blue-500 h-3 rounded-full transition-all duration-300" style={{width: '68%'}}></div>
    </div>
    <div className="flex justify-between text-xs text-gray-500 mt-1">
      <span>预计剩余时间: 2分30秒</span>
      <span>处理速度: 1,250 条/秒</span>
    </div>
  </div>
  
  <div className="grid grid-cols-5 gap-4">
    <div className="text-center">
      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
        <CheckIcon className="w-5 h-5 text-white" />
      </div>
      <div className="text-xs font-medium text-green-600">文件上传</div>
      <div className="text-xs text-gray-500">已完成</div>
    </div>
    
    <div className="text-center">
      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
        <CheckIcon className="w-5 h-5 text-white" />
      </div>
      <div className="text-xs font-medium text-green-600">数据解析</div>
      <div className="text-xs text-gray-500">已完成</div>
    </div>
    
    <div className="text-center">
      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
        <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
      </div>
      <div className="text-xs font-medium text-blue-600">数据验证</div>
      <div className="text-xs text-gray-500">进行中</div>
    </div>
    
    <div className="text-center">
      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-2">
        <ClockIcon className="w-5 h-5 text-gray-500" />
      </div>
      <div className="text-xs font-medium text-gray-500">收入计算</div>
      <div className="text-xs text-gray-500">等待中</div>
    </div>
    
    <div className="text-center">
      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-2">
        <ClockIcon className="w-5 h-5 text-gray-500" />
      </div>
      <div className="text-xs font-medium text-gray-500">处理完成</div>
      <div className="text-xs text-gray-500">等待中</div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：详细状态展示区域
输出：
```tsx
<div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
  <div className="bg-white rounded-lg shadow-md p-6">
    <h4 className="text-md font-semibold text-gray-900 mb-4">文件处理状态</h4>
    <div className="space-y-4">
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">文件上传</span>
          <span className="text-sm font-medium text-green-600">100% 完成</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-green-500 h-2 rounded-full" style={{width: '100%'}}></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>文件大小: 15.8 MB</span>
          <span>上传速度: 2.3 MB/s</span>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">数据解析</span>
          <span className="text-sm font-medium text-green-600">100% 完成</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-green-500 h-2 rounded-full" style={{width: '100%'}}></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>总行数: 25,430</span>
          <span>解析成功: 25,398</span>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">数据验证</span>
          <span className="text-sm font-medium text-blue-600">68% 进行中</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-blue-500 h-2 rounded-full transition-all duration-300" style={{width: '68%'}}></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>已验证: 17,270 条</span>
          <span>验证速度: 850 条/秒</span>
        </div>
      </div>
    </div>
  </div>
  
  <div className="bg-white rounded-lg shadow-md p-6">
    <h4 className="text-md font-semibold text-gray-900 mb-4">收入计算状态</h4>
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-lg font-bold text-blue-600">247</div>
          <div className="text-xs text-gray-600">分销员总数</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-lg font-bold text-green-600">189</div>
          <div className="text-xs text-gray-600">已计算完成</div>
        </div>
        <div className="text-center p-3 bg-orange-50 rounded-lg">
          <div className="text-lg font-bold text-orange-600">58</div>
          <div className="text-xs text-gray-600">等待计算</div>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">计算进度</span>
          <span className="text-sm font-medium text-gray-600">等待验证完成</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-gray-400 h-2 rounded-full" style={{width: '0%'}}></div>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          计算将在数据验证完成后自动开始
        </div>
      </div>
      
      <div className="p-3 bg-gray-50 rounded-lg">
        <div className="text-sm font-medium text-gray-700 mb-2">计算项目</div>
        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">• 等级分成计算</span>
            <span className="text-gray-500">等待中</span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">• 下三级提成计算</span>
            <span className="text-gray-500">等待中</span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">• 拉新奖励计算</span>
            <span className="text-gray-500">等待中</span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">• 汇率换算</span>
            <span className="text-gray-500">等待中</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：错误日志和警告区域
输出：
```tsx
<div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
  <div className="lg:col-span-2 bg-white rounded-lg shadow-md">
    <div className="p-4 border-b border-gray-200">
      <div className="flex items-center justify-between">
        <h4 className="text-md font-semibold text-gray-900">实时处理日志</h4>
        <div className="flex items-center space-x-2">
          <button className="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600">
            清空日志
          </button>
          <button className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600">
            导出日志
          </button>
        </div>
      </div>
    </div>
    <div className="h-64 overflow-y-auto p-4">
      <div className="space-y-2 font-mono text-sm">
        <div className="flex items-start space-x-2">
          <span className="text-xs text-gray-500 mt-0.5">14:35:48</span>
          <span className="text-green-600">[INFO]</span>
          <span className="text-gray-700">数据验证完成: 第17270行，分销员DS156验证通过</span>
        </div>
        <div className="flex items-start space-x-2">
          <span className="text-xs text-gray-500 mt-0.5">14:35:47</span>
          <span className="text-orange-600">[WARN]</span>
          <span className="text-gray-700">第17265行：分销员DS155的上级ID为空，已自动分配到根节点</span>
        </div>
        <div className="flex items-start space-x-2">
          <span className="text-xs text-gray-500 mt-0.5">14:35:46</span>
          <span className="text-red-600">[ERROR]</span>
          <span className="text-gray-700">第17260行：分销员DS154的钻石收入格式错误（包含非数字字符）</span>
        </div>
        <div className="flex items-start space-x-2">
          <span className="text-xs text-gray-500 mt-0.5">14:35:45</span>
          <span className="text-green-600">[INFO]</span>
          <span className="text-gray-700">批量验证完成: 第17000-17250行，共250条记录</span>
        </div>
        <div className="flex items-start space-x-2">
          <span className="text-xs text-gray-500 mt-0.5">14:35:44</span>
          <span className="text-orange-600">[WARN]</span>
          <span className="text-gray-700">检测到重复分销员ID: DS152，已跳过重复记录</span>
        </div>
      </div>
    </div>
  </div>
  
  <div className="bg-white rounded-lg shadow-md p-6">
    <h4 className="text-md font-semibold text-gray-900 mb-4">错误统计</h4>
    <div className="space-y-4">
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">格式错误</span>
          <span className="text-sm font-bold text-red-600">32条</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-red-500 h-2 rounded-full" style={{width: '12%'}}></div>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">数据缺失</span>
          <span className="text-sm font-bold text-orange-600">18条</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-orange-500 h-2 rounded-full" style={{width: '7%'}}></div>
        </div>
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-600">逻辑错误</span>
          <span className="text-sm font-bold text-yellow-600">8条</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-yellow-500 h-2 rounded-full" style={{width: '3%'}}></div>
        </div>
      </div>
      
      <div className="pt-4 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">数据通过率</span>
          <span className="text-lg font-bold text-green-600">97.7%</span>
        </div>
      </div>
      
      <div className="bg-gray-50 p-3 rounded-lg">
        <div className="text-sm font-medium text-gray-700 mb-2">处理建议</div>
        <div className="text-xs text-gray-600 space-y-1">
          <p>• 格式错误较多，建议检查Excel模板</p>
          <p>• 部分分销员上级信息缺失</p>
          <p>• 整体数据质量良好，可继续处理</p>
        </div>
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：操作控制区域
输出：
```tsx
<div className="bg-white rounded-lg shadow-md p-6">
  <div className="flex items-center justify-between mb-4">
    <h4 className="text-md font-semibold text-gray-900">操作控制</h4>
    <div className="flex items-center space-x-2">
      <div className="flex items-center space-x-1">
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        <span className="text-sm text-gray-600">处理中</span>
      </div>
    </div>
  </div>
  
  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
    <button className="px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center space-x-2">
      <PauseIcon className="w-5 h-5" />
      <span>暂停处理</span>
    </button>
    
    <button className="px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center space-x-2">
      <StopIcon className="w-5 h-5" />
      <span>取消任务</span>
    </button>
    
    <button 
      className="px-4 py-3 bg-gray-400 text-white rounded-lg cursor-not-allowed flex items-center justify-center space-x-2"
      disabled
    >
      <ArrowPathIcon className="w-5 h-5" />
      <span>重新处理</span>
    </button>
    
    <button 
      className="px-4 py-3 bg-gray-400 text-white rounded-lg cursor-not-allowed flex items-center justify-center space-x-2"
      disabled
    >
      <DocumentTextIcon className="w-5 h-5" />
      <span>查看结果</span>
    </button>
  </div>
  
  <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
    <div className="flex items-start space-x-3">
      <InformationCircleIcon className="w-5 h-5 text-blue-600 mt-0.5" />
      <div>
        <h5 className="text-sm font-medium text-blue-800">处理提示</h5>
        <p className="text-sm text-blue-700 mt-1">
          当前正在进行数据验证，预计还需要2分30秒完成。验证完成后将自动开始收入计算。
          如需暂停或取消处理，请使用上方控制按钮。
        </p>
      </div>
    </div>
  </div>
</div>
```
</example>
</examples>

请确保界面能够实时反映处理状态，提供清晰的进度指示，并具备良好的错误处理和用户交互体验。 