# 分销员关系管理界面 - Bolt.new 提示词

## 核心提示词

请帮我创建一个公会主播奖励分成系统的**分销员关系管理界面**。这是一个用于管理分销员信息和上下级关系网络的管理页面，支持可视化的关系调整和团队结构管理。

### 界面要求

创建一个直观的关系管理界面，包含以下核心功能：

1. **分销员列表区域**
   - 搜索和筛选功能（按姓名、ID、等级、状态）
   - 分销员基本信息列表（ID、姓名、等级、状态、直属下级数量）
   - 批量操作功能（批量分配、状态变更）
   - 添加新分销员按钮

2. **关系树状图可视化区域**
   - 可交互的树状组织架构图
   - 支持展开/折叠节点
   - 拖拽功能调整上下级关系
   - 节点颜色区分不同等级
   - 缩放和平移功能

3. **分销员详情侧边栏**
   - 选中分销员的详细信息
   - 个人基本信息编辑
   - 直属下级列表展示
   - 上级信息显示
   - 等级变更历史

4. **关系调整工具**
   - 上级分配功能（搜索选择新上级）
   - 批量调整工具
   - 关系变更历史记录
   - 操作权限控制

5. **新人分配管理**
   - 新人自动分配规则设置
   - 手动分配新人功能
   - 分配负载均衡显示
   - 分配历史记录

### 技术要求
- 使用React + TypeScript + Tailwind CSS
- 集成D3.js或类似图形库用于关系树可视化
- 拖拽功能使用react-dnd或类似库
- 响应式设计，支持桌面端
- 实时数据同步和状态管理
- 操作权限控制和审计日志

### 设计风格
- 采用左右分栏布局（列表+可视化）
- 使用现代化的卡片和面板设计
- 关系图采用清晰的层级颜色编码
- 交互操作提供清晰的视觉反馈

<examples>
<example>
输入：分销员列表区域
输出：
```tsx
<div className="w-1/3 bg-white border-r border-gray-200 flex flex-col h-full">
  <div className="p-4 border-b border-gray-200">
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-lg font-semibold text-gray-900">分销员管理</h3>
      <button className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm">
        + 添加分销员
      </button>
    </div>
    
    <div className="space-y-3">
      <div className="relative">
        <input
          type="text"
          placeholder="搜索分销员姓名或ID..."
          className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
        />
        <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
      </div>
      
      <div className="grid grid-cols-2 gap-2">
        <select className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">全部等级</option>
          <option value="gold">金牌</option>
          <option value="silver">银牌</option>
          <option value="bronze">铜牌</option>
        </select>
        
        <select className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">非活跃</option>
          <option value="suspended">暂停</option>
        </select>
      </div>
    </div>
  </div>
  
  <div className="flex-1 overflow-y-auto">
    <div className="p-2 space-y-2">
      <div className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer bg-blue-50 border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-yellow-600">张</span>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">张小明</div>
              <div className="text-xs text-gray-500">DS001</div>
            </div>
          </div>
          <div className="text-right">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              金牌
            </span>
            <div className="text-xs text-gray-500 mt-1">12个下级</div>
          </div>
        </div>
      </div>
      
      <div className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-600">李</span>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">李四</div>
              <div className="text-xs text-gray-500">DS025</div>
            </div>
          </div>
          <div className="text-right">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              银牌
            </span>
            <div className="text-xs text-gray-500 mt-1">8个下级</div>
          </div>
        </div>
      </div>
      
      <div className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-orange-600">王</span>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">王五</div>
              <div className="text-xs text-gray-500">DS031</div>
            </div>
          </div>
          <div className="text-right">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
              铜牌
            </span>
            <div className="text-xs text-gray-500 mt-1">3个下级</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div className="p-4 border-t border-gray-200">
    <div className="grid grid-cols-2 gap-2">
      <button className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm">
        批量分配
      </button>
      <button className="px-3 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 text-sm">
        状态管理
      </button>
    </div>
  </div>
</div>
```
</example>

<example>
输入：关系树状图可视化区域
输出：
```tsx
<div className="flex-1 bg-gray-50 flex flex-col">
  <div className="p-4 bg-white border-b border-gray-200">
    <div className="flex items-center justify-between">
      <h3 className="text-lg font-semibold text-gray-900">组织架构图</h3>
      <div className="flex items-center space-x-2">
        <button className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600">
          展开全部
        </button>
        <button className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600">
          折叠全部
        </button>
        <button className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
          重置视图
        </button>
      </div>
    </div>
  </div>
  
  <div className="flex-1 relative overflow-hidden">
    <div className="absolute inset-0" id="organization-chart">
      {/* 这里是D3.js渲染的组织架构图区域 */}
      <svg className="w-full h-full">
        {/* 示例节点 */}
        <g transform="translate(400, 50)">
          <circle cx="0" cy="0" r="20" fill="#fbbf24" stroke="#f59e0b" strokeWidth="2"/>
          <text x="0" y="5" textAnchor="middle" className="text-xs font-medium fill-white">张</text>
          <text x="0" y="35" textAnchor="middle" className="text-xs fill-gray-600">张小明</text>
          <text x="0" y="48" textAnchor="middle" className="text-xs fill-gray-500">DS001</text>
        </g>
        
        <g transform="translate(200, 150)">
          <circle cx="0" cy="0" r="18" fill="#9ca3af" stroke="#6b7280" strokeWidth="2"/>
          <text x="0" y="5" textAnchor="middle" className="text-xs font-medium fill-white">李</text>
          <text x="0" y="32" textAnchor="middle" className="text-xs fill-gray-600">李四</text>
        </g>
        
        <g transform="translate(600, 150)">
          <circle cx="0" cy="0" r="18" fill="#fb923c" stroke="#ea580c" strokeWidth="2"/>
          <text x="0" y="5" textAnchor="middle" className="text-xs font-medium fill-white">王</text>
          <text x="0" y="32" textAnchor="middle" className="text-xs fill-gray-600">王五</text>
        </g>
        
        {/* 连接线 */}
        <line x1="400" y1="70" x2="200" y2="132" stroke="#9ca3af" strokeWidth="2"/>
        <line x1="400" y1="70" x2="600" y2="132" stroke="#9ca3af" strokeWidth="2"/>
      </svg>
      
      {/* 操作提示 */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-md p-3 text-sm">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
            <span className="text-gray-600">金牌分销员</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
            <span className="text-gray-600">银牌分销员</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-400 rounded-full"></div>
            <span className="text-gray-600">铜牌分销员</span>
          </div>
        </div>
        <div className="mt-2 pt-2 border-t border-gray-200 text-xs text-gray-500">
          <p>• 拖拽节点调整关系</p>
          <p>• 点击节点查看详情</p>
          <p>• 滚轮缩放视图</p>
        </div>
      </div>
    </div>
  </div>
</div>
```
</example>

<example>
输入：分销员详情侧边栏
输出：
```tsx
<div className="w-80 bg-white border-l border-gray-200 flex flex-col h-full">
  <div className="p-4 border-b border-gray-200">
    <div className="flex items-center space-x-3">
      <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
        <span className="text-lg font-medium text-yellow-600">张</span>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-900">张小明</h3>
        <p className="text-sm text-gray-500">DS001</p>
      </div>
    </div>
  </div>
  
  <div className="flex-1 overflow-y-auto">
    <div className="p-4 space-y-6">
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">基本信息</h4>
        <div className="space-y-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">当前等级</label>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              金牌分销员
            </span>
          </div>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">状态</label>
            <select className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="active">活跃</option>
              <option value="inactive">非活跃</option>
              <option value="suspended">暂停</option>
            </select>
          </div>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">加入时间</label>
            <p className="text-sm text-gray-600">2024-01-15</p>
          </div>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">联系方式</label>
            <input
              type="email"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value="<EMAIL>"
            />
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">上级信息</h4>
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-blue-600">刘</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">刘总监</p>
                <p className="text-xs text-gray-500">DS000</p>
              </div>
            </div>
            <button className="text-xs text-blue-600 hover:text-blue-800">
              变更上级
            </button>
          </div>
        </div>
      </div>
      
      <div>
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium text-gray-900">直属下级 (12人)</h4>
          <button className="text-xs text-blue-600 hover:text-blue-800">
            管理下级
          </button>
        </div>
        <div className="space-y-2 max-h-40 overflow-y-auto">
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">李</span>
              </div>
              <div>
                <p className="text-xs font-medium text-gray-900">李四</p>
                <p className="text-xs text-gray-500">DS025</p>
              </div>
            </div>
            <span className="text-xs text-gray-500">银牌</span>
          </div>
          
          <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-orange-600">王</span>
              </div>
              <div>
                <p className="text-xs font-medium text-gray-900">王五</p>
                <p className="text-xs text-gray-500">DS031</p>
              </div>
            </div>
            <span className="text-xs text-gray-500">铜牌</span>
          </div>
          
          <div className="text-center py-2">
            <span className="text-xs text-gray-500">... 还有10个下级</span>
          </div>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">等级变更历史</h4>
        <div className="space-y-2">
          <div className="text-xs text-gray-600 p-2 bg-gray-50 rounded">
            <p className="font-medium">2024-01-20: 升级为金牌分销员</p>
            <p className="text-gray-500">原因: 达成直属下级10人目标</p>
          </div>
          <div className="text-xs text-gray-600 p-2 bg-gray-50 rounded">
            <p className="font-medium">2024-01-15: 加入为铜牌分销员</p>
            <p className="text-gray-500">初始等级</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div className="p-4 border-t border-gray-200">
    <div className="space-y-2">
      <button className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm">
        保存修改
      </button>
      <button className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm">
        重置
      </button>
    </div>
  </div>
</div>
```
</example>
</examples>

请确保界面支持直观的拖拽操作，提供清晰的可视化反馈，并具备完善的权限控制和操作审计功能。 