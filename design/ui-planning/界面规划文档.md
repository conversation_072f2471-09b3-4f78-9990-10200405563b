# 公会主播奖励分成系统 - UI界面规划文档

## 概述
基于PRD分析，除已存在的登录界面和上传EXCEL界面外，系统还需要以下核心UI界面来实现完整的业务功能。

## 已存在界面
- ✅ 登录界面 
- ✅ 上传EXCEL界面

## 需要开发的界面列表

### 1. 系统配置管理界面
**功能描述**: 
- 管理员设置和修改所有系统配置项
- 包括预算设置、汇率管理、门槛设置、等级规则配置、提成比例设置、拉新奖励阶梯设置
- 支持配置项的历史版本查看和回滚

**核心功能模块**:
- 基础参数配置（预算、支出上限比例、基础门槛等）
- 汇率历史管理（月份-汇率对应关系）
- 分销员等级规则配置（金/银/铜牌条件和分成比例）
- 多级提成比例设置（L1/L2/L3提成比例）
- 拉新奖励阶梯配置

### 2. 财务总览仪表板
**功能描述**:
- 显示当月财务关键指标的总览
- 实时对比预算目标与实际支出
- 提供财务风险预警和趋势分析

**核心数据展示**:
- 公会月总收入（预算）vs 实际总支出
- 支出上限 vs 当前支出状态
- 支出/收入比例的可视化图表
- 预算状态（预算内/超出预算）警示
- 超出预算金额提醒

### 3. 分销员收入明细报表界面
**功能描述**:
- 展示所有分销员的详细收入计算结果
- 支持按多维度筛选和排序
- 提供收入计算明细的钻取功能

**核心功能**:
- 分销员列表展示（ID、姓名、等级、收入等）
- 收入明细弹窗（等级分成、下三级提成、拉新奖励分解）
- 下级网络树状图展示
- 数据导出功能（Excel/PDF）
- 多维度筛选器（等级、收入范围、拉新人数等）

### 4. 分销员关系管理界面  
**功能描述**:
- 管理分销员的上下级关系网络
- 可视化展示团队层级结构
- 支持关系调整和新人分配

**核心功能**:
- 分销员信息管理（基本信息、状态、等级）
- 上下级关系可视化树状图
- 关系调整工具（拖拽分配、批量调整）
- 新人自动/手动分配规则设置
- 团队结构统计分析

### 5. 数据处理状态监控界面
**功能描述**:
- 监控Excel数据导入和处理状态
- 显示计算进度和错误日志
- 提供数据质量检查报告

**核心功能**:
- 文件上传进度显示
- 数据解析状态监控
- 计算任务进度跟踪
- 错误日志和异常处理
- 数据质量报告（缺失字段、格式错误等）

### 6. 月度佣金计算执行界面
**功能描述**:
- 选择指定月份并执行月度佣金计算
- 实时监控计算进度和状态
- 显示计算结果总览和历史记录

**核心功能**:
- 月份选择和前置条件检查
- 计算操作控制（预览/执行/重新计算）
- 实时计算进度监控
- 计算结果总览展示
- 历史计算记录管理

### 7. 历史数据查询界面
**功能描述**:
- 查询和对比历史月份的计算结果
- 支持趋势分析和数据对比
- 提供历史数据的导出功能

**核心功能**:
- 月份选择器和数据查询
- 历史财务总览对比
- 分销员历史收入趋势
- 团队发展历史追踪
- 数据对比和趋势图表

## 界面优先级
1. **高优先级**: 系统配置管理界面、财务总览仪表板
2. **中优先级**: 分销员收入明细报表界面、数据处理状态监控界面  
3. **低优先级**: 分销员关系管理界面、历史数据查询界面

## 技术要求
- 响应式设计，支持桌面和平板访问
- 数据实时更新，支持WebSocket推送
- 支持大数据量表格的虚拟滚动
- 提供数据导出（Excel、PDF、CSV）功能
- 具备权限控制，区分管理员和普通用户视图 