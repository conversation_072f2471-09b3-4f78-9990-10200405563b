# 历史数据导入时间策略测试用例

## 测试场景

### 场景1：默认策略测试（推荐）
**目标**：验证默认策略不影响新人资格判定

#### 测试步骤
1. 准备3月份的历史数据Excel文件
2. 调用默认导入接口：`POST /h/historical/import`
3. 检查新创建的creator的创建时间
4. 执行7月份的新人资格判定
5. 验证结果

#### 预期结果
- 新creator的创建时间为当前时间（非3月）
- 7月份新人资格判定时，这些creator符合新人标准
- 日志显示：`"历史数据导入：为creator {} 设置创建时间为当前时间，避免影响新人资格判定"`

### 场景2：历史时间策略测试
**目标**：验证历史时间策略的行为

#### 测试步骤
1. 准备3月份的历史数据Excel文件
2. 调用历史时间导入接口：`POST /h/historical/import-with-historical-time`
3. 检查新创建的creator的创建时间
4. 执行7月份的新人资格判定
5. 验证结果

#### 预期结果
- 新creator的创建时间为3月份
- 7月份新人资格判定时，这些creator不符合新人标准（因为超过60天）
- 日志显示：`"历史数据导入：为creator {} 设置创建时间为历史月份 {}"`

## 测试数据准备

### Excel测试文件格式
```
数据月份 | 主播ID | 昵称 | Handle | ... 其他字段
202503  | 12345  | 测试1 | test1  | ...
202503  | 12346  | 测试2 | test2  | ...
```

### 数据库状态检查
```sql
-- 检查creator创建时间
SELECT id, nickname, handle, create_time 
FROM creators 
WHERE id IN (12345, 12346);

-- 检查新人资格判定配置
SELECT setting_key, setting_value 
FROM commission_settings 
WHERE setting_key IN ('new_recruit_tenure_days', 'new_recruit_min_valid_days');
```

## API测试用例

### 1. 默认导入接口测试
```bash
curl -X POST "http://localhost:8080/h/historical/import" \
  -H "Authorization: Bearer {token}" \
  -F "file=@test_data_202503.xlsx"
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "rawImportCount": 2,
    "creatorCount": 2,
    "monthlyPerformanceCount": 2,
    "errorCount": 0,
    "errorMessages": [],
    "useHistoricalCreateTime": false
  }
}
```

### 2. 历史时间导入接口测试
```bash
curl -X POST "http://localhost:8080/h/historical/import-with-historical-time" \
  -H "Authorization: Bearer {token}" \
  -F "file=@test_data_202503.xlsx"
```

**预期响应**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "rawImportCount": 2,
    "creatorCount": 2,
    "monthlyPerformanceCount": 2,
    "errorCount": 0,
    "errorMessages": [],
    "useHistoricalCreateTime": true
  }
}
```

## 新人资格判定测试

### 测试接口
```bash
# 检查单个creator的新人资格
curl -X GET "http://localhost:8080/business/new-recruit-qualification/check/12345/2025-07" \
  -H "Authorization: Bearer {token}"
```

### 预期结果对比

#### 默认策略结果
```json
{
  "code": 200,
  "data": {
    "success": true,
    "qualified": true,
    "tenureQualified": true,
    "validDaysQualified": true,
    "tenureDaysConfig": 60,
    "createdAt": "2025-07-03 10:30:00",
    "qualificationReason": "符合拉新资格：账号创建时间符合要求且60天内有效直播天数达标"
  }
}
```

#### 历史时间策略结果
```json
{
  "code": 200,
  "data": {
    "success": true,
    "qualified": false,
    "tenureQualified": false,
    "validDaysQualified": true,
    "tenureDaysConfig": 60,
    "createdAt": "2025-03-01 00:00:00",
    "qualificationReason": "不符合拉新资格：账号创建时间超过60天限制"
  }
}
```

## 边界情况测试

### 1. 更新现有creator
**测试目标**：验证更新现有creator时不修改创建时间

#### 测试步骤
1. 先导入一批数据（使用默认策略）
2. 再次导入相同的creator数据（使用历史时间策略）
3. 检查creator的创建时间是否保持不变

#### 预期结果
- creator的创建时间保持第一次导入时的值
- 其他信息（如昵称）正常更新

### 2. 数据月份解析异常
**测试目标**：验证数据月份格式错误时的处理

#### 测试数据
```
数据月份 | 主播ID | 昵称 | Handle
invalid | 12347  | 测试3 | test3
```

#### 预期结果
- 解析失败时使用当前时间作为创建时间
- 日志记录警告信息

### 3. 空数据月份
**测试目标**：验证数据月份为空时的处理

#### 测试数据
```
数据月份 | 主播ID | 昵称 | Handle
        | 12348  | 测试4 | test4
```

#### 预期结果
- 使用当前时间作为创建时间
- 正常完成导入

## 性能测试

### 大批量数据导入
**测试目标**：验证两种策略的性能差异

#### 测试数据
- 10,000条creator记录
- 分别使用两种策略导入

#### 监控指标
- 导入耗时
- 内存使用
- 数据库连接数
- 错误率

## 回归测试

### 现有功能验证
**确保新功能不影响现有功能**

#### 测试项目
1. 原有导入接口仍然正常工作
2. 新人资格判定功能正常
3. 佣金计算功能正常
4. 虚拟上级创建功能正常
5. 关系重建功能正常

## 测试检查清单

- [ ] 默认策略导入测试通过
- [ ] 历史时间策略导入测试通过
- [ ] 新人资格判定结果符合预期
- [ ] 更新现有creator时间不变
- [ ] 异常数据处理正常
- [ ] 大批量数据导入性能正常
- [ ] 现有功能回归测试通过
- [ ] API文档更新完成
- [ ] 日志记录清晰准确
