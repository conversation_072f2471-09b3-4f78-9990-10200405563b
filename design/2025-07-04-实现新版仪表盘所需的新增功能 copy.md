<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<requirements>

# 需求

## 设计详案 


### 1. 概述与前提
本方案的目标是，在您现有的“一键计算”功能基础上，集成所有新设计的业务逻辑，以支撑新版仪表盘的数据展示。

实施前提:
本方案假设您的数据库已经应用了我们之前讨论并确定的所有结构变更。这包括：

creators 表: 已增加 current_distributor_level_id, level_updated_at, first_qualified_month, qualified_by_recruiter_id 字段。

commission_level_rules 表: 已将 min_direct_downlines 修改为 min_qualified_recruits，并增加了 payout_depth 字段。

### 2. 第一步：数据库层面的准备 (一次性操作)
在修改业务逻辑之前，请确保数据库已完成以下准备工作。

#### 2.1. 新增 team_events 表
此表是“团队动态/月度成就摘要”功能的核心，必须新建。

目标: 创建一个新表，用于存储所有值得展示的团队成就和动态事件。

SQL语句:

SQL
```
CREATE TABLE `team_events` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `actor_creator_id` bigint(20) unsigned NOT NULL COMMENT '事件关联人ID (如: 团队领导)',
  `target_creator_id` bigint(20) unsigned DEFAULT NULL COMMENT '事件目标者ID (如: 新人, 晋升者)',
  `event_type` varchar(50) NOT NULL COMMENT '事件类型 (LEVEL_UP, NEW_RECRUIT, PERFORMANCE_MILESTONE, APPROACHING_MILESTONE)',
  `event_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '事件发生的确切时间',
  `details` json DEFAULT NULL COMMENT '事件详情 (如: {"new_level": "Gold", "current_value": 22, "target_value": 24})',
  PRIMARY KEY (`id`),
  KEY `idx_actor_timestamp` (`actor_creator_id`, `event_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队动态与成就事件日志表';
```

#### 2.2. 配置系统参数
将所有业务规则中的可变数值，作为参数插入到 commission_settings 表中。

目标: 确保业务规则的灵活性，便于未来调整。

SQL语句:

SQL
```
INSERT INTO `commission_settings` (`setting_key`, `setting_value`, `description`) VALUES
('new_recruit_tenure_days', '60', '新人资格-账号最大创建天数'),
('new_recruit_min_valid_days', '24', '新人资格-月度最低有效直播天数'),
('retention_target_avg_growth_rate', '0.15', '业绩保级-目标平均增长率(15%)'),
('retention_growth_from_zero', '1.00', '业绩保级-从零增长的定义值(100%)'),
('retention_growth_rate_cap', '3.00', '业绩保级-单月增长率上限(300%)'),
('new_user_grace_period_months', '4', '新手保护期月数'),
('upgrade_protection_period_months', '2', '升级保护期月数'),
-- 为“即将达标”功能新增的参数
('approaching_recruit_min_days', '20', '即将达标(拉新)-最低有效天数'),
('approaching_performance_min_ratio', '0.80', '即将达标(业绩)-最低完成比例(80%)')
ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`), `description` = VALUES(`description`);
```


#### 3. 第二步：修改“一键计算”的业务逻辑


您现有的“一键计算”功能需要被重构为一个包含多个顺序步骤的任务管道（Pipeline）。

触发: 管理员选择月份（例如“2025年7月”），点击【开始计算】。

#### 任务A:  执行“合格拉新”资格判定
目标: 找出本月所有首次达标的新人，并“盖章”记录。

输入: 考核月份（“2025-07”）。

处理逻辑:

从 commission_settings读取 new_recruit_tenure_days (60) 和 new_recruit_min_valid_days (24)。

SELECT 所有 creators 表中 first_qualified_month IS NULL 的用户。

对这些用户，JOIN monthly_performance 表，筛选出满足“60天内创建”和“有效直播天数 ≥ 24天”的用户列表。

批量 UPDATE 这些用户的 creators 记录，将 first_qualified_month 设为考核月份，并将当时的 parent_id 填入 qualified_by_recruiter_id。

输出: creators 表中的拉新资格被更新。

#### 任务B: 执行“全量用户等级评定”
目标: 基于“进攻”和“防守”模型，计算出每个分销员在当月的最终等级。

输入: 考核月份，任务A的成果。

处理逻辑:

遍历所有分销员。对每一个人：

计算“防守等级”:

获取其 creators.current_distributor_level_id 作为“基础等级”。

检查其是否满足“新手保护期”或“升级保护期”条件。若满足，则“防守等级”=“基础等级”。

若不满足，则获取过去4个月的“个人+L1团队”业绩，执行“月度环比增长率平均值”算法（包含100%和300%的风控规则），得出“保级”或“降级”的结果，确定其“防守等级”。

计算“进攻等级”:

COUNT 该用户在 creators 表中有多少下线的 qualified_by_recruiter_id 是自己，且 first_qualified_month 是本月。

用此计数值去 commission_level_rules 表匹配，得出其“进攻等级”。

确定最终等级:

当月最终等级 = MAX(进攻等级, 防守等级)。

将结果写入 commission_distributor_qualifications 表，记录用户当月的达成等级、业绩等详细信息。

输出: 所有分销员的当月最终等级已确定并存储。

#### 任务C: 执行佣金计算
目标: 基于新评定的等级，计算所有佣金。

输入: 考核月份，任务B的结果。

处理逻辑:

此部分为您现有逻辑的修改。

在计算任何多层级佣金时，必须从 commission_distributor_qualifications 表中获取分销员当月的最终等级。

根据该等级在 commission_level_rules 中对应的 payout_depth 值，来决定佣金计算的深度。

输出: 所有佣金数据计算完成，存入 commission_payouts 和 commission_payout_breakdowns 表。

#### 任务D: [新增] 执行“月度成就摘要”生成
目标: 为“团队动态”界面生成数据，存入 team_events 表。

输入: 考核月份，任务A, B, C的完整结果。

处理逻辑:

生成“等级提升”事件: 对比本月和上月的最终等级，找出所有等级提升的用户，并为他们及其上级插入LEVEL_UP事件。

生成“新人加入”事件: 查询任务A中所有被“盖章”的新人，为他们的上级插入NEW_RECRUIT事件。

生成“业绩里程碑”事件: 遍历所有用户，对比其本月和上月的团队总业绩，找出所有在本月“跨越”了某个预设里程碑金额的用户，插入PERFORMANCE_MILESTONE事件。

生成“即将达标”事件:

筛选未达标新人，如果valid_days落在approaching_recruit_min_days和24天之间，则生成“即将达标(拉新)”事件。

筛选未跨越业绩里程碑的用户，如果其业绩完成度落在approaching_performance_min_ratio和100%之间，则生成“即将达标(业绩)”事件。

输出: team_events 表中写入了本月所有的成就事件。

#### 任务E: [新增] 更新用户“基础等级”
目标: 将本月的最终结果，固化为下一个月的考核基准。

输入: 任务B的结果。

处理逻辑:

批量 UPDATE creators 表。

将每个用户在任务B中计算出的“当月最终等级”，写入 current_distributor_level_id 字段。

同时更新 level_updated_at 字段为当前时间。

输出: creators 表中的长期等级状态被更新。

</requirements>


<source_files>
@CommissionCalculationController.java
</source_files>

<data_structure>
@database.sql
</data_structure>


- 先读需求内容requirements， 了解后. 读数据库结构 data_structure,  了解相关的数据修改data_changes， 
- 逐步思考, 本次的修改内容， 及修改方案， 列出修改计划
- 实现修改现有代码逻辑

- 所有的代码实现后， 为其它大模型生成一个review这次修改内容的提示词，生成到本地的/design/test/ 目录中。 提示词要求： 提供本次修改的文件及对应的行数范围， 修改的内容，需求， 及要求review 的内容。 


# 注意
需求中的
- 任务A, 任务B, 任务C, 已经实现， 
- 新增需要实现的任务：  任务D: [新增] 执行“月度成就摘要”生成
- 需要确认是否已经实现的任务， 任务E， 已经实现就不需要再修改了。 


