<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<data_structure>
@database.sql
</data_structure>

<requirements>
@PRD.md
</requirements>

- 根据我的需求PRD.md文档， 及数据结构 database.sql， 先了解需求总体的实现要求。 
- 逐步思考如何完成相关的实现API工作， 根据思考后的结果， 列出需要实现的API接口， 及具体实施细节
- 逐一实现对应的接口
- 现在需要新添加几个接口， 接口的主要目的主播作为用户登录到系统中。  调用对应的接口获取 
-- 查询主播月度数据树状关系图
-- 获取主播信息详细信息
-- 获取单个分销员的详细收入信息。 
- 接口先会从已经登录的用户信息中获取CreatorID, 方法： 先获取到已经登录的用户信息,再获取 SysUser.userName，  就是 CreatorID
示例：
```
LoginUser loginUser = getLoginUser();
SysUser user = loginUser.getUser();
```
- 这几个接口需要参考的代码都在下面的参考代码<code></code>中， 不同的是不用再次传入 CreatorID 都由已经登录的用户中获取出来

<code>

@CreatorController.java

    /**
     * 查询主播月度数据树状关系图 h:creator
     */
    @ApiOperation(value = "查询主播月度数据树状关系图 h:creator", notes = "获取包含月度业绩数据的主播树状关系图 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping("/monthlyTree")
    public AjaxResult monthlyTree(
            @RequestParam("dataMonth") @DateTimeFormat(pattern = "yyyy-MM") Date dataMonth,
            @RequestParam(value = "creatorId", required = false) Long creatorId,
            @RequestParam(value = "nickname", required = false) String nickname)
    {
        List<CreatorMonthlyTreeNode> tree = creatorService.selectCreatorMonthlyTree(dataMonth, creatorId, nickname);
        return AjaxResult.success("查询成功", tree);
    }



    /**
     * 获取主播信息详细信息 h:creator
     */
    @ApiOperation(value = "获取主播信息详细信息 h:creator", notes = "根据ID获取主播信息详细信息 h:creator")
    @PreAuthorize("@ss.hasPermi('h:creator')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(creatorService.selectCreatorById(id));
    }


@CommissionReportController.java

    /**
     * 获取单个分销员详细信息 h:commission_report
     */
    @ApiOperation(value = "获取分销员详细信息 h:commission_report", notes = "获取单个分销员的详细收入信息 h:commission_report")
    @PreAuthorize("@ss.hasPermi('h:commission_report')")
    @GetMapping("/distributor/{creatorId}/{dataMonth}")
    public AjaxResult getDistributorDetail(
            @ApiParam(value = "分销员ID", required = true, example = "1001") 
            @PathVariable("creatorId") Long creatorId,
            @ApiParam(value = "数据月份，格式：yyyy-MM", required = true, example = "2025-06") 
            @PathVariable("dataMonth") String dataMonth)
</code>



