<role>
你是一名经验丰富的后端开发工程师，对若依框架有深入的理解。请按照若依的风格进行开发, 当前是ruoyi管理后台系统 ， 数据库使用mysql5.7 
</role>

<requirements>
<basic_rules>
0. 中文回答， 代码中的中文使用 UTF-8 编码 
1. 参考 @SysUserController 实现 新的服务
2. 新的服务 最好是 单独生成一个Controller
3. 不要修改任何与 SysXXXController 的文件， 包含 SysXXXXXX 的文件
</basic_rules>

<permission_rules>
4. 新的服务的权限， 一个Controller内的方法的权限都是一个，  只加2级， h:XXX 不需要区分添加删除等。 如：  PreAuthorize("@ss.hasPermi('h:oss')")
</permission_rules>

<service_rules>
5. 新的服务的CRUD 及查询功能， 查询参数， 按数据结构，时间倒序排序 
6. 用户实现访问数据 Service  要注意数据源问题，在类的上面添加，  @DataSource(value = DataSourceType.SLAVE)， SLAVE 使用的是逻辑数据库streamer_distribution_system, 
MASTER 使用的ry的数据库， 并不是主从库。 
</service_rules>

<controller_rules>
7. Controller 注释 添加 后台-XXXX
   如：
```
/**
* 后台-新闻内容管理控制器 h:news
*
* <AUTHOR>
*/
```
8. 新增加的业务 Controller 可以放到business目录下
9. 生成的方法描述 及注释中添加 权限描述，  注释内容  h:XXX， 例如：  @ApiOperation(value = "获取Banner列表 h:oss", notes = "获取Banner分页列表 h:oss")
</controller_rules>

<data_source_example>
```java
@DataSource(value = DataSourceType.SLAVE)
public class AdInfoServiceImpl implements IAdInfoService 
public enum DataSourceType
{
    /**
     * 主库
     */
    MASTER,

    /**
     * 从库
     */
    SLAVE,
    
    /**
     * 认证服务库
     */
    AUTH
}
```
</data_source_example>

<mapper_rules>
10. 查询数据库的Mapper XML文件，让它们的列表查询按照指定的排序规则返回结果：如果有order相关字段则按order字段倒序排序，如果没有order。 找时间字段倒序排序。 

<mapper_example>
``` ProductTagMapper.xml
    <select id="selectProductTagList" parameterType="com.ruoyi.system.domain.ProductTag" resultMap="ProductTagResult">
        <include refid="selectProductTagVo"/>
        <where>  
            <if test="tagId != null and tagId != ''">
                and tag_id = #{tagId}
            </if>
            <if test="tagName != null and tagName != ''">
                and tag_name like concat('%', #{tagName}, '%')
            </if>
            <if test="tagType != null">
                and tag_type = #{tagType}
            </if>
            <if test="isDisplay != null">
                and is_display = #{isDisplay}
            </if>
        </where>
        order by created_at desc
    </select>
```
</mapper_example>

<validation_rules>
- 完成后比对 mapper.xml 文件 与数据库字段是否对应。 确认没有添加多余的时间字段 
</validation_rules>
</mapper_rules>
</requirements>

<project_architecture>
<module_structure>
系统采用Maven多模块架构，主要包含以下核心模块：

<ruoyi_admin>
ruoyi-admin：系统启动模块
- 包含应用程序入口类RuoYiApplication
- 包含Web控制器层，负责处理HTTP请求
- 按功能领域划分控制器（system、monitor、tool等）
</ruoyi_admin>

<ruoyi_framework>
ruoyi-framework：核心框架模块
- 包含系统框架配置
- 安全框架实现（基于Spring Security）
- 数据源配置
- AOP切面实现
- 拦截器配置
- Web相关配置
</ruoyi_framework>

<ruoyi_system>
ruoyi-system：系统功能模块
遵循经典三层架构：
- domain：领域模型层
- mapper：数据访问层
- service：业务逻辑层
</ruoyi_system>

<ruoyi_common>
ruoyi-common：通用工具模块
- 包含注解、常量定义
- 核心基础类
- 工具类库
- 异常处理
- XSS防护
- 枚举定义
</ruoyi_common>

<ruoyi_quartz>
ruoyi-quartz：定时任务模块
- 基于Quartz实现的任务调度功能
</ruoyi_quartz>

<ruoyi_generator>
ruoyi-generator：代码生成模块
- 用于自动生成代码，提高开发效率
</ruoyi_generator>
</module_structure>

<technology_stack>
<basic_framework>
基础框架：
- Spring Boot 2.5.15
- Spring Framework 5.3.33
- Spring Security 5.7.12
</basic_framework>

<data_access>
数据访问：
- MyBatis（通过PageHelper实现分页）
- Druid数据库连接池
</data_access>

<security_framework>
安全框架：
- Spring Security
- JWT令牌认证
</security_framework>

<api_documentation>
API文档：
- Swagger 3.0
</api_documentation>

<other_components>
其他技术组件：
- Kaptcha（验证码）
- POI（Excel处理）
- Velocity（模板引擎，用于代码生成）
- Fastjson（JSON处理）
- Lombok（简化代码）
</other_components>
</technology_stack>

<architecture_features>
<layered_architecture>
分层架构：
- 表现层（Controller）
- 业务层（Service）
- 数据访问层（Mapper）
- 领域模型层（Domain）
</layered_architecture>

<modular_design>
模块化设计：
- 功能模块清晰分离
- 依赖关系明确
</modular_design>

<security_design>
安全性设计：
- 基于Spring Security的认证授权
- XSS防护机制
- 数据过滤
</security_design>

<extensibility>
扩展性：
- 通过模块化设计支持功能扩展
- 代码生成器支持快速开发
</extensibility>

<maintainability>
可维护性：
- 统一的异常处理
- 规范的代码结构
- 通用工具类封装
- 开发规范
</maintainability>
</architecture_features>
</project_architecture>

<data_structure>
@database.sql
</data_structure>

<requirements>
@PRD.md
</requirements>

关联的数据库结构 

<TABLE>
-- Create syntax for TABLE 'creator_relationships'
CREATE TABLE `creator_relationships` (
  `ancestor_id` bigint(20) unsigned NOT NULL COMMENT '祖先用户ID (关联 creators.id)',
  `descendant_id` bigint(20) unsigned NOT NULL COMMENT '后代用户ID (关联 creators.id)',
  `depth` int(11) NOT NULL COMMENT '层级深度 (0表示用户自身)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ancestor_id`,`descendant_id`),
  KEY `idx_cr_descendant_depth` (`descendant_id`,`depth`),
  KEY `idx_cr_ancestor_depth` (`ancestor_id`,`depth`),
  CONSTRAINT `fk_rel_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rel_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `creators` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主播层级关系闭包表';

-- Create syntax for TABLE 'creators'
CREATE TABLE `creators` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主播的唯一ID (Creator ID)',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '直接上级主播的ID, 顶级为NULL',
  `first_qualified_month` date DEFAULT NULL COMMENT '首次被确认为合格拉新的月份 (YYYY-MM-01), 用于确保唯一性',
  `qualified_by_recruiter_id` bigint(20) unsigned DEFAULT NULL COMMENT '确认其合格时的直接上级(parent_id)快照, 用于永久锁定业绩归属',
  `nickname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '主播昵称',
  `handle` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主播的Handle',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `first_active_month` date DEFAULT NULL COMMENT '首次活跃月份，用于新人识别',
  `recruited_by` bigint(20) unsigned DEFAULT NULL COMMENT '招募人ID',
  `current_distributor_level_id` int(10) unsigned DEFAULT NULL COMMENT '当前基础等级ID, 关联commission_level_rules.id, 由“防守”体系更新',
  `level_updated_at` timestamp NULL DEFAULT NULL COMMENT '基础等级最后更新时间, 用于判断升级保护期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_handle` (`handle`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_recruited_by` (`recruited_by`),
  KEY `idx_first_active` (`first_active_month`),
  KEY `idx_qualified_by_month` (`qualified_by_recruiter_id`,`first_qualified_month`),
  KEY `idx_creators_current_level` (`current_distributor_level_id`),
  KEY `idx_creators_qualified_by_month` (`qualified_by_recruiter_id`,`first_qualified_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播信息与直接关系表';

-- Create syntax for TABLE 'group_creator_relation'
CREATE TABLE `group_creator_relation` (
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '组名',
  `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建者ID, 关联creators表的主键',
  `group_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '组类型：0-分销组，1-非分销组',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`group_name`),
  UNIQUE KEY `uk_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组名与CreatorID关系表';
</TABLE>

# 需求
 
- 按设计详案修改相关代码 
- 设计详案：拉新用户资格判定与应用 (V1.1)

版本: 1.1
日期: 2025年7月1日
关联需求: “拉新用户 规则 需要再确认。 规则： （自然月60天， 24天直播）”
修订说明 (V1.1): 根据最新确认，本方案中获得“拉新”业绩归属的招募人，被明确定义为新用户在合格时的直接上级（parent_id）。本文档中的相关逻辑已全部更新，以反映这一重要规则。

1. 需求背景

本次系统升级的核心之一，是需要建立一个清晰、无歧义且可量化的“合格拉新”标准。这个标准将作为分销员“进攻等级”评定的唯一依据，直接影响其等级晋升和佣金收益。
原始需求: 定义一个“合格拉新”用户，需满足两个条件：
账号创建时间在 60 天内。
在考核的自然月内，有效直播天数达到 24 天。

2. 核心挑战与设计原则

在将此业务需求转化为技术方案时，我们识别出以下核心挑战，并确立了相应的设计原则：
挑战一：唯一性（Uniqueness）
问题: 如何确保一个新用户只被其直接上级作为“拉新业绩”计算一次？
设计原则: “一次合格，永久标记”。一个用户作为“合格拉新”的身份，只能被确认一次。系统必须有机制能够永久记录这一成就归属。
挑战二：准确性（Accuracy）
问题: 规则中的“60天内”和“自然月”必须有精准的计算口径。
设计原则: “规则定义代码化”。所有时间窗口和条件判断都必须在代码和数据库查询中有明确、统一的定义。
挑战三：可控性（Controllability）
问题: 何时进行资格的判定？
设计原则: “流程可控，结果一致”。资格判定作为“一键算薪”流程的前置任务，确保了所有用户的资格都在同一时间点、基于同一份完整的数据集进行计算。

3. 最终解决方案

为应对上述挑战，我们设计了一套包含“精确定义”、“数据库标记”和“流程整合”的三位一体的解决方案。

3.1. 规则的精确定义

系统将按以下细则来判定资格（以计算2025年7月数据为例）：
账号创建时间在60天内:
取考核月的最后一天，即 2025-07-31。
用户的账号创建日期 creators.created_at 必须大于或等于 DATE_SUB('2025-07-31', INTERVAL 60 DAY)。
60天内有效直播24天:
统计用户在过去60天内（从 DATE_SUB('2025-07-31', INTERVAL 60 DAY) 到 2025-07-31）的总有效直播天数。
查询该时间范围内所有月份的 monthly_performance 记录，累计 valid_days 字段值。
累计的有效直播天数必须 ≥ 24 天。
参数化建议: 上述规则中的 60 天和 24 天，都将作为系统参数（new_recruit_tenure_days 和 new_recruit_min_valid_days）进行配置，方便未来调整。

3.2. 唯一性保证机制：数据库“盖章”

这是解决“唯一性”与“历史业绩准确性”挑战的核心。
数据库字段: 我们将在 creators 表中新增两个关键字段：
first_qualified_month (DATE): 用于记录该用户首次被确认为“合格拉新”的月份。默认值为 NULL。
qualified_by_recruiter_id (BIGINT): 用于永久记录在用户合格那一刻，其对应的直接上级ID (parent_id)。
为何仍需此字段: 即使业绩归属于parent_id，用户的parent_id也可能因为团队架构调整而变更。通过qualified_by_recruiter_id字段，我们将合格当时的上级关系**“快照”**并永久保存下来，确保即使未来parent_id变更，历史的拉新业绩归属也不会发生错乱。
判定逻辑: 在执行判定的批处理任务时，系统只会查询 first_qualified_month IS NULL 的用户。
“盖章”操作: 一旦系统确认某个用户在本月达标，就会立即更新该用户的记录，将 first_qualified_month 设置为当前考核月份，并将其当时的 parent_id 值填入 qualified_by_recruiter_id 字段。此操作不可逆。

3.3. 应用与执行流程

资格判定逻辑被整合为“一键计算”流程的第一个前置任务。
触发: 管理员在后台点击计算7月份的佣金。
执行: 系统启动“全量‘合格拉新’资格判定”任务。
产出: 任务执行完毕后，所有在7月份首次达标的新人都已在数据库中被清晰标记，其业绩也已准确归属到合格当时的直接上级。这个标记是后续“进攻等级”评定的直接数据源。

4. 数据库设计详述

为支持此功能，需对 creators 表进行如下修改：
SQL

```
-- 在 creators 表中增加以下字段
ALTER TABLE `creators`
ADD COLUMN `first_qualified_month` DATE DEFAULT NULL 
    COMMENT '首次被确认为合格拉新的月份 (YYYY-MM-01), 用于确保唯一性' AFTER `parent_id`,
ADD COLUMN `qualified_by_recruiter_id` BIGINT(20) UNSIGNED DEFAULT NULL 
    COMMENT '确认其合格时的直接上级(parent_id)快照, 用于永久锁定业绩归属' AFTER `first_qualified_month`;

-- 为新字段建立索引以优化查询性能
ALTER TABLE `creators`
ADD INDEX `idx_qualified_by_month` (`qualified_by_recruiter_id`, `first_qualified_month`);
```

# 相关代码

@CommissionCalculationController.java




