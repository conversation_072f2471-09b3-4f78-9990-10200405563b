## **产品需求文档 (PRD) - 公会主播奖励分成系统 V1.3**


### 1. 项目概述与目标

**概述**: 本项目为TikTok公会设计一套精细化的主播奖励与分成系统。系统旨在通过自动化的计算和透明的规则，激励主播创造更多收入（直播）并扩大公会规模（社交裂变），同时确保公会的总支出控制在预设的预算范围内。

**核心目标**:
1.  **自动化激励**: 自动计算并分配三种核心奖励：等级分成、多级提成、拉新奖励。
2.  **预算控制**: 严格遵守管理员设定的月度支出上限（默认为月度预算的30%），并对超支风险进行控制。
3.  **透明化**: 为管理员提供清晰的财务报表，为主播（未来）提供明确的收入构成。

### 2. 核心概念与角色定义

| 术语/角色 | 定义 |
| :--- | :--- |
| **公会 (Guild)** | 系统的所有者和管理者，负责配置规则、输入预算、导入数据和监控财务。 |
| **主播 (Streamer)** | 系统的基础用户，通过直播获取钻石收入。 |
| **分销员 (Distributor)** | 一种特殊的主播，可以发展下级形成自己的团队，并有资格获得额外分成。 |
| **下级网络** | 以分销员为根的树状结构。**L1**: 直属下级；**L2**: 下级的下级；**L3**: L2的下级。 |
| **新人 (New Recruit)** | 在本系统历史记录中，首次被添加并建立上级关系的主播。 |
| **钻石 (Diamond)** | 主播直播收入的计量单位，是计算部分成的基础。 |
| **美元 (USD)** | 系统进行财务预算、成本控制和最终支付的唯一货币单位。 |

### 3. 系统配置项 (Admin)

管理员需对以下参数进行配置。其中，月度预算为每月输入，其余为长期配置。

| 配置项 | 变量名示例 | 数据类型 | 描述 |
| :--- | :--- | :--- | :--- |
| **公会月总收入 (预算)** | `manual_monthly_income_usd` | 浮点数 (USD) | **每月输入**。此为预算基数，非系统计算值。 |
| **公会支出上限比例** | `payout_cap_rate` | 百分比 (e.g., 30%) | `支出上限 = 公会月总收入 * 此比例`。 |
| **钻石兑换美元比例** | `diamond_to_usd_rate` | 浮点数 (e.g., 0.005) | 核心汇率：`1 钻石 = 0.005 USD`。 |
| **基础分成钻石门槛** | `base_diamond_threshold` | 整数 (e.g., 5000) | 分销员个人业绩的基础要求。 |
| **钻石门槛月度上浮比例**| `threshold_increase_rate` | 百分比 (e.g., 1.2%) | 动态调整业绩门槛的比例。 |
| **分销员等级规则** | `distributor_levels` | 对象数组 | 定义金/银/铜牌的达成条件和分成比例。 |
| **下三级提成比例** | `multilevel_commission_rates`| 对象数组 | 定义对L1, L2, L3下级收入的提成比例。 |
| **分销员拉新奖励** | `recruitment_bonus_tiers` | 对象数组 | 定义月度拉新人数对应的USD奖励阶梯。 |

### 4. 数据导入要求

系统需支持按月份导入绩效数据，每条记录代表一个主播/分销员，并包含以下字段：
* `user_id`: 用户唯一标识
* `parent_id`: 上级用户的 `user_id` (顶级分销员则为空)
* `diamond_income`: 当月直播获得的钻石总数
* `new_recruits_count`: 当月发展的“新人”数量

### 5. 核心计算引擎：分步详解

这是系统的核心，严格按照以下顺序执行月度结算。

#### **第1步: 定义本月财务边界**
系统首先根据管理员的配置，计算出本月总的支出上限。
* **支出上限 (USD)**: `payout_cap_usd`
    $$
    \text{payout\_cap\_usd} = \text{manual\_monthly\_income\_usd} \times \text{payout\_cap\_rate}
    $$

#### **第2步: 计算每个分销员的理论收入**
系统遍历所有分销员，为每个人独立计算其理论上应得的三部分收入。

* **2.1: 资格判定**
    * 首先，计算该分销员当月必须达成的**动态钻石门槛** (`current_dynamic_threshold`)。
        $$
        \text{current\_dynamic\_threshold} = \max(\text{上月个人钻石收入} \times (1 + \text{threshold\_increase\_rate}), \text{base\_diamond\_threshold})
        $$
    * 然后，根据该分销员的`当月个人钻石收入`和`当月直属下级人数`，对照`distributor_levels`配置，判断其是否满足`current_dynamic_threshold`和人数要求，从而获得分销员等级（金/银/铜）。
    * **如果未获得任何等级，则此人本月无资格获得“等级分成”和“下三级提成”，直接跳到2.3。**

* **2.2: 计算理论钻石分成** (若有资格)
    * **等级分成(钻)**: (`个人钻石收入` + `所有L1下级钻石收入之和`) * `对应等级分成比例`
    * **下三级提成(钻)**:
        * (`L1下级钻石总收入` * `L1提成比例`) +
        * (`L2下级钻石总收入` * `L2提成比例`) +
        * (`L3下级钻石总收入` * `L3提成比例`)
    * 将以上两项相加，得到该分销员的 **`理论钻石总收入`**。

* **2.3: 计算理论美元奖励**
    * 根据分销员的 `new_recruits_count`，匹配`recruitment_bonus_tiers`配置，得到其 **`理论美元奖励`**。此项奖励与分销员等级无关。

#### **第3步: 系统总支出聚合与风控**
汇总所有人的理论收入，并与支出上限进行比较。

* **3.1: 聚合理论总支出**
    1.  汇总所有人的`理论钻石总收入`，得到 `total_diamond_commissions`。
    2.  汇总所有人的`理论美元奖励`，得到 `total_usd_bonuses`。
    3.  全部转换为USD并相加，得到理论上的总支出：
        $$
        \text{total\_theoretical\_payout\_usd} = (\text{total\_diamond\_commissions} \times \text{diamond\_to\_usd\_rate}) + \text{total\_usd\_bonuses}
        $$

* **3.2: 执行风控逻辑**
    * **情况A (预算内)**: 如果 `total_theoretical_payout_usd <= payout_cap_usd`，则无需风控。
    * **情况B (超预算)**: 如果 `total_theoretical_payout_usd > payout_cap_usd`，则启动风控：
        * **B1 (极端超支)**: 如果仅拉新奖励就已超支 (`total_usd_bonuses >= payout_cap_usd`)，则：
            * 拉新奖励(USD)按理论值**足额发放**。
            * 所有人的钻石分成（等级分成和三级提成）**全部作废（归零）**。
            * 系统记录一笔亏损：`Loss = total_usd_bonuses - payout_cap_usd`。
        * **B2 (普通超支)**: 如果拉新奖励未超支，但总和超支，则：
            * 拉新奖励(USD)按理论值**足额发放**。
            * 计算剩余可用额度：`scalable_usd_pool = payout_cap_usd - total_usd_bonuses`。
            * 计算理论钻石分成的总USD价值：`total_diamond_commissions_in_usd = total_diamond_commissions * diamond_to_usd_rate`。
            * 计算一个**缩减系数**: `Scaling_Factor = scalable_usd_pool / total_diamond_commissions_in_usd`。

#### **第4步: 计算并分配最终个人收入**
根据第3步的风控结果，计算每个分销员的最终实收美元金额。

* **对于情况A (预算内)**:
    $$
    \text{最终收入} = (\text{个人理论钻石收入} \times \text{diamond\_to\_usd\_rate}) + \text{个人理论美元奖励}
    $$
* **对于情况B1 (极端超支)**:
    $$
    \text{最终收入} = \text{个人理论美元奖励}
    $$
* **对于情况B2 (普通超支)**:
    $$
    \text{最终收入} = (\text{个人理论钻石收入} \times \text{diamond\_to\_usd\_rate} \times \text{Scaling\_Factor}) + \text{个人理论美元奖励}
    $$

### 6. 报表与分析模块 (Admin)

管理员后台需提供以下月度报表：

1.  **财务总览 (Dashboard)**
    * **公会月总收入 (预算)**: `manual_monthly_income_usd`
    * **总支出上限 (USD)**: `payout_cap_usd`
    * **实际总支出 (USD)**: 所有分销员最终收入的总和。
    * **支出/收入比例**: `(实际总支出 / 公会月总收入)`，并高亮显示是否 > 30%。
    * **系统亏损 (USD)**: 仅在触发B1极端超支时显示。
    * **风控状态**: (正常 / 普通缩减 / 极端超支)

2.  **分销员收入明细 (Table)**
    * 一个可排序、可搜索的表格，列出所有分销员。
    * **列**: User ID, 等级, 个人钻石收入, 理论钻石分成, 理论美元奖励, **最终实收总额(USD)**, 收入构成明细。

### 7. 运营策略建议

为了实现“规则稳定6个月”的业务目标，建议公会在系统上线前：
* **进行沙盘推演**: 使用过去几个月的真实数据，代入本PRD的计算模型进行模拟。
* **压力测试**: 调整各项分成比例，观察在业绩最好的月份，是否会频繁触发“普通超支”或“极端超支”。
* **设定合理预期**: 寻找一个平衡点，使得在大多数月份，实际总支出能控制在预算的 70-80% 左右，为业绩爆发留足缓冲空间，从而避免因频繁缩减分成而打击主播积极性。