## **产品需求文档 (PRD): 公会主播奖励分成系统 (计算与报表版)**

* **文档版本**: 1.6 (最终存档版)
* **创建日期**: 2025年6月17日
* **状态**: 已确认

---

### **1. 概述**

本项目旨在为TikTok公会建立一套自动化、精细化的主播奖励分成计算与报表系统。该系统通过对个人业绩、团队规模、拉新能力等多维度激励规则进行精确计算，自动生成所有分销员的理论应得收入。

系统的核心是**计算**与**报告**：它为公会管理者提供清晰、详尽的财务数据，直观地展示理论总支出与预算目标的对比，辅助公会进行手动、策略性的财务管理。

### **2. 全局规则与定义**

#### **2.1. 核心定义**

| 术语/角色 | 定义 |
| :--- | :--- |
| **公会 (Guild)** | 系统的所有者和管理者，负责配置规则、输入预算、导入数据和监控财务。 |
| **分销员 (Distributor)** | 有资格发展下级并参与团队分成的特殊主播。 |
| **下级网络** | 以分销员为根的树状结构。**L1**: 直属下级；**L2**: L1的下级；**L3**: L2的下级。 |
| **新人 (New Recruit)** | 在系统历史数据中，首次被添加并建立上级关系的主播。 |
| **钻石 (Diamond)** | 主播直播收入的计量单位，是计算部分成的基础。 |
| **美元 (USD)** | 系统进行财务预算、成本控制和最终支付的唯一货币单位。 |

#### **2.2. 精度与舍入规则**

* **内部计算精度**: 所有涉及汇率换算、百分比计算的中间过程，必须以**小数点后四 (4) 位**的精度进行。
* **最终结算精度**: 向分销员支付的最终金额，必须为**小数点后两 (2) 位**的财务格式。
* **舍入方法**: 统一采用 **“四舍五入” (Round Half Up)**。

### **3. 系统配置项 (由公会管理员设置)**

| 配置项 | 变量名示例 | 数据类型 | 描述 |
| :--- | :--- | :--- | :--- |
| **公会月总收入 (预算)** | `manual_monthly_income_usd` | 浮点数 (USD) | **每月手动输入**。此为预算基数，用于报表对比。 |
| **公会支出上限比例** | `payout_cap_rate` | 百分比 (e.g., 30%) | 定义支出上限，用于报表对比。 |
| **钻石兑美元汇率表** | `diamond_to_usd_rates` | **历史记录表** | 格式为 `(生效月份, 汇率)`。结算时自动匹配对应月份的汇率。 |
| **基础分成钻石门槛** | `base_diamond_threshold` | 整数 (e.g., 5000) | 个人业绩的基础要求。 |
| **钻石门槛月度上浮比例**| `threshold_increase_rate` | 百分比 (e.g., 1.2%) | 动态调整业绩门槛的比例。 |
| **分销员等级规则** | `distributor_levels` | 对象数组 | 定义金/银/铜牌的达成条件和分成比例。 |
| **下三级提成比例** | `multilevel_commission_rates`| 对象数组 | 定义对L1, L2, L3下级收入的提成比例。 |
| **分销员拉新奖励** | `recruitment_bonus_tiers` | 对象数组 | 定义月度拉新人数对应的USD奖励阶梯。 |

### **4. 详细分成规则与收入来源 (核心规则详解)**

本章节详细说明了分销员所有收入的来源、计算规则和获取条件。

#### **4.1. 分成规则一：分销员等级分成**

* **目的**: 激励分销员自身及其直属团队创造更高的直播收入。
* **收入来源**: `分销员个人当月钻石收入` + `所有直属下级(L1)当月钻石收入之和`。
* **获取条件**:
    1.  必须达到当月的**动态钻石门槛** (详见4.4)。
    2.  必须满足`分销员等级规则`中对应等级的**直属下级人数**要求。
* **计算公式 (钻石)**:
    $$
    \text{等级分成 (钻)} = (\text{个人钻石收入} + \sum \text{L1下级钻石收入}) \times \text{对应等级分成比例}
    $$

#### **4.2. 分成规则二：下三级提成**

* **目的**: 激励分销员发展更深、更广的下级网络。
* **收入来源**: 其下级网络中，第一、二、三层所有成员的钻石收入。
* **获取条件**: 只要分销员获得了任一等级（金/银/铜），就有资格获得此项提成。
* **计算公式 (钻石)**:
    $$
    \text{下三级提成 (钻)} = (\sum \text{L1收入} \times \text{L1比例}) + (\sum \text{L2收入} \times \text{L2比例}) + (\sum \text{L3收入} \times \text{L3比例})
    $$

#### **4.3. 分成规则三：分销员拉新奖励**

* **目的**: 直接奖励分销员为公会招募新成员的行为。
* **收入来源**: `当月成功招募的新人数量`。
* **获取条件**: 无需任何等级或业绩门槛。
* **计算规则**: 按月度拉新人数匹配奖励阶梯，**该奖励直接以美元(USD)计算**。

#### **4.4. 核心资格规则：动态钻石门槛**

* **目的**: 确保分销员保持持续的个人业绩贡献。
* **规则描述**: 这是获取“等级分成”和“下三级提成”的**先决条件**。
* **计算公式**:
    * 获取该分销员**上个月**的个人钻石收入 (`last_month_income`)。若无历史数据（新人）或上月收入为0，则 `last_month_income` 视为 **0**。
    * 应用公式计算当月门槛 `current_dynamic_threshold`:
        $$
        \text{current\_dynamic\_threshold} = \max(\text{last\_month\_income} \times (1 + \text{threshold\_increase\_rate}), \text{base\_diamond\_threshold})
        $$

### **5. 核心计算流程**

系统严格按照以下顺序执行月度结算，计算出每个人的最终收入。

1.  **初始化**: 获取当月管理员设置的`生效汇率`。
2.  **计算理论收入**: 遍历所有分销员，根据第4章的详细规则，为每个人计算出：
    * `理论钻石总收入` (由等级分成和三级提成构成)
    * `理论美元奖励` (由拉新奖励构成)
3.  **最终收入计算**: 将理论收入换算为最终支付金额。
    * 对于每个分销员，其最终收入等于其理论收入。计算公式见第6章。
4.  **聚合总数**: 将所有分销员的最终收入相加，得到`实际总支出(USD)`，用于后续报表分析。

### **6. 最终个人收入计算**

由于系统不执行自动风控，分销员的最终收入始终等于其根据所有规则计算出的理论收入。

* **最终收入计算公式**:
    $$
    \text{最终收入(USD)} = (\text{个人理论钻石总收入} \times \text{当月汇率}) + \text{个人理论美元奖励}
    $$
* **舍入**: 计算结果需遵循第2.2节的规则，四舍五入至小数点后两位。

### **7. 报表与分析模块**

这是本系统的核心产出，为公会决策提供数据支持。

#### **7.1. 财务总览报表**

| 指标 | 描述 | 计算方式 |
| :--- | :--- | :--- |
| **公会月总收入 (预算)** | 管理员当月输入的预算目标。 | `manual_monthly_income_usd` |
| **支出上限 (USD)** | 根据预算和比例计算出的财务红线。 | `manual_monthly_income_usd` * `payout_cap_rate` |
| **实际总支出 (USD)** | 所有分销员最终收入的总和。 | $\sum \text{每个人的最终收入(USD)}$ |
| **支出/收入比例** | 实际支出占预算的百分比。 | `实际总支出` / `公会月总收入 (预算)` |
| **预算状态** | 直观展示支出是否在预算内。 | `(预算内 / 超出预算)` |
| **超出预算金额 (USD)** | 若超出预算，则显示超出的具体金额。 | `max(0, 实际总支出 - 支出上限)` |

#### **7.2. 分销员收入明细报表**

一个可交互的表格，列出所有分销员的收入详情。

* **主表视图**:
    * `分销员ID`, `姓名/昵称`, `当月等级`, `个人钻石收入`, `团队总钻石收入`, `拉新人数`, **`最终实收总额(USD)`**

* **点击详情视图**: 点击任意分销员，可展开查看更多细节：
    1.  **收入明细 (Income Breakdown)**:
        * 等级分成: `4,200 钻` (`$21.00`)
        * 下三级提成: `14,000 钻` (`$70.00`)
        * 拉新奖励: `$20.00`
        * **合计**: `$111.00`
    2.  **下级列表 (Downline List)**:
        * 一个可展开的树状视图或列表，展示其所有直属（L1）下级。
        * 每行可显示下级的ID、姓名、及其当月贡献的钻石收入。