-- 修复重复虚拟上级问题的SQL脚本
-- 问题：存在两个名为"Jason-虚拟"的虚拟上级

-- 1. 首先查看当前的重复虚拟上级
SELECT 
    id, 
    parent_id, 
    nickname, 
    handle, 
    created_at, 
    remark 
FROM creators 
WHERE nickname = 'Jason-虚拟' 
    AND id LIKE '888%'
ORDER BY id;

-- 2. 查看Jason组的关系情况
SELECT 
    gcr.group_name,
    gcr.creator_id,
    c.nickname,
    c.handle,
    gcr.created_at
FROM group_creator_relation gcr
LEFT JOIN creators c ON gcr.creator_id = c.id
WHERE gcr.group_name = 'Jason';

-- 3. 查看是否有其他creator依赖这些虚拟上级
SELECT 
    child.id as child_id,
    child.nickname as child_nickname,
    child.parent_id,
    parent.nickname as parent_nickname
FROM creators child
LEFT JOIN creators parent ON child.parent_id = parent.id
WHERE child.parent_id IN (88807478951575539, 88808167815058363);

-- 4. 执行修复：保留第一个(88807478951575539)，删除第二个(88808167815058363)
-- 4.1 将第二个虚拟上级的所有下级转移到第一个
UPDATE creators 
SET parent_id = 88807478951575539, 
    updated_at = NOW(),
    update_by = 'system_fix'
WHERE parent_id = 88808167815058363;

-- 4.2 如果Jason组指向第二个虚拟上级，改为指向第一个
UPDATE group_creator_relation 
SET creator_id = 88807478951575539,
    updated_at = NOW(),
    update_by = 'system_fix'
WHERE group_name = 'Jason' 
    AND creator_id = 88808167815058363;

-- 4.3 删除creator_relationships表中第二个虚拟上级的关系
DELETE FROM creator_relationships 
WHERE ancestor_id = 88808167815058363 
    OR descendant_id = 88808167815058363;

-- 4.4 删除第二个重复的虚拟上级
DELETE FROM creators 
WHERE id = 88808167815058363;

-- 5. 验证修复结果
-- 5.1 确认只剩下一个Jason-虚拟上级
SELECT 
    id, 
    parent_id, 
    nickname, 
    handle, 
    created_at 
FROM creators 
WHERE nickname = 'Jason-虚拟' 
    AND id LIKE '888%';

-- 5.2 确认Jason组的关系正确
SELECT 
    gcr.group_name,
    gcr.creator_id,
    c.nickname,
    c.handle
FROM group_creator_relation gcr
LEFT JOIN creators c ON gcr.creator_id = c.id
WHERE gcr.group_name = 'Jason';

-- 5.3 确认没有孤儿关系
SELECT COUNT(*) as orphan_relationships
FROM creator_relationships cr
LEFT JOIN creators c1 ON cr.ancestor_id = c1.id
LEFT JOIN creators c2 ON cr.descendant_id = c2.id
WHERE c1.id IS NULL OR c2.id IS NULL;

-- 注意：执行前请备份数据库！
-- 建议分步执行，每步都检查结果再继续下一步 