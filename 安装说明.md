# RuoYi 后台管理系统安装说明

## 项目简介

RuoYi v3.8.8 是基于SpringBoot+Vue前后端分离的Java快速开发框架，毫无保留给个人及企业免费使用。

- **前端技术栈**: Vue + Element UI
- **后端技术栈**: Spring Boot + Spring Security + MyBatis + Redis + JWT
- **数据库**: MySQL 5.7+
- **Java版本**: JDK 1.8+

## 系统要求

### 服务器环境
- **操作系统**: Windows 10+ / Linux / macOS
- **Java**: JDK 1.8 或更高版本
- **Maven**: 3.6.0+
- **MySQL**: 5.7+ (推荐8.0+)
- **Redis**: 3.0+
- **Node.js**: 14.0+ (前端构建)

### 开发环境推荐
- **IDE**: IntelliJ IDEA / Eclipse / VS Code
- **Git**: 版本控制工具

## 安装步骤

### 1. 环境准备

#### 1.1 安装Java JDK
```bash
# 检查Java版本
java -version

# 如果没有安装，请下载安装JDK 1.8+
# Windows: 从Oracle官网下载安装包
# Linux: 
sudo apt update
sudo apt install openjdk-8-jdk

# macOS:
brew install openjdk@8
```

#### 1.2 安装Maven
```bash
# 检查Maven版本
mvn -version

# 如果没有安装
# Windows: 下载并配置环境变量
# Linux:
sudo apt install maven

# macOS:
brew install maven
```

#### 1.3 安装MySQL
```bash
# 下载并安装MySQL 5.7+
# 创建数据库用户和数据库
mysql -u root -p
CREATE DATABASE ry_vue DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE USER 'ruoyi'@'%' IDENTIFIED BY 'ruoyi123';
GRANT ALL PRIVILEGES ON ry_vue.* TO 'ruoyi'@'%';
FLUSH PRIVILEGES;
```

#### 1.4 安装Redis
```bash
# Linux:
sudo apt install redis-server
sudo systemctl start redis
sudo systemctl enable redis

# macOS:
brew install redis
brew services start redis

# Windows: 下载Redis for Windows
```

### 2. 获取项目代码

```bash
# 克隆项目（如果使用Git）
git clone [项目地址]
cd ruoyi-admin

# 或者直接下载解压源码包
```

### 3. 数据库初始化

#### 3.1 导入数据库脚本
```bash
# 进入项目sql目录
cd sql

# 导入主数据库脚本
mysql -u ruoyi -p ry_vue < ry_20240629.sql

# 导入定时任务数据库脚本（可选）
mysql -u ruoyi -p ry_vue < quartz.sql
```

#### 3.2 验证数据导入
```sql
USE ry_vue;
SHOW TABLES;
-- 应该看到sys_user, sys_menu, sys_role等系统表
```

### 4. 后端配置

#### 4.1 修改数据库配置
编辑 `ruoyi-admin/src/main/resources/application-druid.yml`:

```yaml
# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 主库数据源
      master:
        url: ***********************************************************************************************************************************************
        username: ruoyi
        password: ruoyi123
        driver-class-name: com.mysql.cj.jdbc.Driver
```

#### 4.2 修改Redis配置
编辑 `ruoyi-admin/src/main/resources/application.yml`:

```yaml
spring:
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码（如果有的话）
    password: 
    # 连接超时时间
    timeout: 10s
```

#### 4.3 修改文件上传路径
```yaml
# 项目相关配置
ruoyi:
  # 文件路径（根据实际情况修改）
  profile: /home/<USER>/uploadPath
```

### 5. 编译和启动后端

```bash
# 进入项目根目录
cd /path/to/ruoyi-project

# 清理并编译项目
mvn clean package -Dmaven.test.skip=true

# 启动应用
java -jar ruoyi-admin/target/ruoyi-admin.jar

# 或者在IDE中直接运行RuoYiApplication.java主类
```

### 6. 前端安装和启动

```bash
# 进入前端项目目录（如果前后端分离）
cd ruoyi-ui

# 安装依赖
npm install

# 或者使用yarn
yarn install

# 启动开发服务器
npm run dev

# 构建生产环境
npm run build:prod
```

### 7. 访问系统

#### 7.1 后端接口
- **地址**: http://localhost:6080
- **API文档**: http://localhost:6080/swagger-ui/index.html

#### 7.2 前端页面
- **地址**: http://localhost:80 (具体端口看前端配置)
- **默认账号**: admin
- **默认密码**: admin123

## 常见问题解决

### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查数据库用户权限
mysql -u ruoyi -p
```

### 2. Redis连接失败
```bash
# 检查Redis服务状态
systemctl status redis
redis-cli ping
```

### 3. 端口冲突
- 修改 `application.yml` 中的 `server.port` 配置
- 默认端口是6080，可改为其他未占用端口

### 4. 文件上传失败
- 检查 `ruoyi.profile` 配置的目录是否存在且有写权限
- 创建目录: `mkdir -p /home/<USER>/uploadPath`
- 设置权限: `chmod 755 /home/<USER>/uploadPath`

### 5. Maven依赖下载慢
配置国内镜像源，编辑 `~/.m2/settings.xml`:
```xml
<mirrors>
  <mirror>
    <id>alimaven</id>
    <name>aliyun maven</name>
    <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
    <mirrorOf>central</mirrorOf>
  </mirror>
</mirrors>
```

## 生产环境部署

### 1. 使用Docker部署
```dockerfile
# Dockerfile示例
FROM openjdk:8-jre-slim
COPY ruoyi-admin/target/ruoyi-admin.jar app.jar
EXPOSE 6080
ENTRYPOINT ["java","-jar","/app.jar"]
```

### 2. 使用脚本部署
项目提供了启动脚本:
- Windows: `ry.bat`
- Linux/macOS: `ry.sh`

```bash
# 给脚本执行权限
chmod +x ry.sh

# 启动
./ry.sh start

# 停止
./ry.sh stop

# 重启
./ry.sh restart

# 最简单的运行方式 
或使用 运行
nohup java -jar -Xms256m -Xmx1024m  ruoyi-admin.jar --server.port=6131  > admin-app.log 2>&1 &

```

### 3. Nginx反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:6080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 功能模块说明

系统包含以下主要功能模块：

1. **用户管理** - 系统用户配置
2. **部门管理** - 组织机构管理
3. **岗位管理** - 用户职务配置
4. **菜单管理** - 系统菜单和权限配置
5. **角色管理** - 角色权限分配
6. **字典管理** - 系统字典维护
7. **参数管理** - 系统参数配置
8. **通知公告** - 系统公告管理
9. **操作日志** - 系统操作记录
10. **登录日志** - 用户登录记录
11. **在线用户** - 在线用户监控
12. **定时任务** - 任务调度管理
13. **代码生成** - 代码自动生成
14. **系统接口** - API接口文档
15. **服务监控** - 系统性能监控
16. **缓存监控** - Redis缓存监控

## 开发指南

### 1. 代码生成器使用
1. 登录系统后访问"系统工具" -> "代码生成"
2. 导入数据库表
3. 配置生成参数
4. 生成并下载代码

### 2. 新增菜单权限
1. 访问"系统管理" -> "菜单管理"
2. 新增菜单项
3. 配置权限标识
4. 分配给相应角色

### 3. 自定义配置
- 修改 `application.yml` 进行系统配置
- 在 `RuoYiConfig.java` 中添加自定义配置类
- 使用 `@ConfigurationProperties` 注解绑定配置

## 技术支持

- **官方文档**: http://doc.ruoyi.vip
- **在线演示**: http://vue.ruoyi.vip
- **问题反馈**: 提交Issue到项目仓库
- **QQ群**: 请查看官方文档获取最新群号

## 版权说明

本项目基于MIT协议开源，可自由使用、修改和分发。

---

**安装成功后，请及时修改默认密码，确保系统安全！**
