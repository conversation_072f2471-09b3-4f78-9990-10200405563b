import pandas as pd

# 读取Excel文件
df = pd.read_excel('design/testdata0612.xlsx')

# 查找zamzam.z行
zamzam_row = df[df['Handle'] == 'zamzam.z'].iloc[0]

print('Excel文件中zamzam.z行的详细数据:')
print('=' * 50)
for col in df.columns:
    value = zamzam_row[col]
    print(f'{col}: {value}')

print('\n\n对比分析:')
print('=' * 50)

# 日志中的数据
log_data = {
    'dataMonth': '202504',
    'creatorId': '7498351234567890123',
    'creatorNickname': 'Zamzam',
    'handle': 'zamzam.z',
    'creatorNetworkManager': '',
    'groupName': '🦋🤍Maryama-7497918567634403345',
    'groupManager': 'stz6660808',
    'isViolativeCreators': 'Jason',
    'theCreatorWasRookie': 'false',
    'diamonds': 'No',
    'validDays': '4600',
    'liveDurationH': '16',
    'estimatedBonus': '46',
    'estBonusRookieM1Retention': '4.6',
    'estBonusRookieM2': 'Did not participate',
    'estBonusRookieHalfMilestone': '0',
    'estBonusRookieM1': '0',
    'estBonusActivenessTask': '0',
    'estBonusRevenueScaleTask': '4.8',
    'estBonusNewCreatorNetworkTask': '0'
}

# Excel列名到Java字段的映射
excel_to_java = {
    'Data Month': 'dataMonth',
    'Creator ID': 'creatorId', 
    'Creator nickname': 'creatorNickname',
    'Handle': 'handle',
    'Creator Network manager': 'creatorNetworkManager',
    'Group': 'groupName',
    'Group manager': 'groupManager',
    'Is violative creators': 'isViolativeCreators',
    'The creator was Rookie at the time of first joining': 'theCreatorWasRookie',
    'Diamonds': 'diamonds',
    'Valid days(d)': 'validDays',
    'LIVE duration(h)': 'liveDurationH',
    'Estimated bonus': 'estimatedBonus',
    'Estimated bonus - Rookie milestone 1 retention bonus task': 'estBonusRookieM1Retention',
    'Estimated bonus - Rookie milestone 2 bonus task': 'estBonusRookieM2',
    'Estimated bonus - Rookie half-milestone bonus task': 'estBonusRookieHalfMilestone',
    'Estimated bonus - Rookie milestone 1 bonus task': 'estBonusRookieM1',
    'Estimated bonus - Activeness task task': 'estBonusActivenessTask',
    'Estimated bonus - Revenue scale task task': 'estBonusRevenueScaleTask',
    'Estimated bonus - New Creator Network task task': 'estBonusNewCreatorNetworkTask'
}

print('字段对比分析:')
for excel_col, java_field in excel_to_java.items():
    if excel_col in zamzam_row.index:
        excel_value = str(zamzam_row[excel_col])
        log_value = str(log_data.get(java_field, 'N/A'))
        
        # 处理NaN值
        if excel_value == 'nan':
            excel_value = ''
        
        match = '✅' if excel_value == log_value else '❌'
        print(f'{match} {java_field}:')
        print(f'    Excel: "{excel_value}"')
        print(f'    Log:   "{log_value}"')
        if excel_value != log_value:
            print(f'    >>> 不匹配！')
        print() 